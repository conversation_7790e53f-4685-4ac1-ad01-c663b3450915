/**
 * INTEL UNISON++ MODERN MESSAGING INTERFACE
 * WhatsApp/Telegram inspired JavaScript interactions
 * 60fps smooth animations and modern UX
 */

const { ipcRenderer } = require('electron');

class ModernMessagingInterface {
    constructor() {
        this.currentConversation = null;
        this.messageInput = null;
        this.sendButton = null;
        this.messagesList = null;
        this.conversationsList = null;
        this.typingTimer = null;
        this.lastMessageTime = 0;
        this.conversations = [];
        this.contacts = [];
        
        this.initializeElements();
        this.setupEventListeners();
        this.setupIPCListeners();
        this.loadInitialData();
        this.startRealtimeSync();
    }

    initializeElements() {
        this.messageInput = document.getElementById('message-input');
        this.sendButton = document.querySelector('.send-btn');
        this.messagesList = document.getElementById('messages-list');
        this.conversationsList = document.getElementById('conversations-list');
        
        console.log('🚀 Intel Unison++ Modern Messaging Interface initialized');
    }

    setupIPCListeners() {
        // Listen for new messages
        ipcRenderer.on('new-message', (event, message) => {
            this.handleNewMessage(message);
        });

        // Listen for conversation updates
        ipcRenderer.on('conversations-updated', (event, data) => {
            this.conversations = data;
            this.renderConversations();
        });

        // Listen for message status updates
        ipcRenderer.on('message-status-updated', (event, data) => {
            this.updateMessageStatus(data);
        });

        // Listen for typing indicators
        ipcRenderer.on('typing-indicator', (event, data) => {
            this.showTypingIndicator(data);
        });

        // Listen for VM Bridge messages
        ipcRenderer.on('vm-messages-received', (event, messages) => {
            this.handleVMMessages(messages);
        });

        ipcRenderer.on('vm-new-message', (event, message) => {
            this.handleVMNewMessage(message);
        });
    }

    async loadInitialData() {
        await this.loadConversations();
        await this.loadContacts();
    }

    setupEventListeners() {
        // Message input handling
        if (this.messageInput) {
            this.messageInput.addEventListener('input', (e) => {
                this.handleTyping(e);
                this.updateSendButton();
            });
            
            this.messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
            
            this.messageInput.addEventListener('focus', () => {
                this.scrollToBottom();
            });
        }

        // Send button handling
        if (this.sendButton) {
            this.sendButton.addEventListener('click', () => {
                this.sendMessage();
            });
        }

        // Conversation selection
        if (this.conversationsList) {
            this.conversationsList.addEventListener('click', (e) => {
                const conversationItem = e.target.closest('.conversation-item');
                if (conversationItem) {
                    this.selectConversation(conversationItem);
                }
            });
        }

        // Message context menu
        if (this.messagesList) {
            this.messagesList.addEventListener('contextmenu', (e) => {
                const messageBubble = e.target.closest('.message-bubble');
                if (messageBubble) {
                    e.preventDefault();
                    this.showContextMenu(e, messageBubble);
                }
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Window resize handling
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    async loadConversations() {
        try {
            const result = await ipcRenderer.invoke('get-conversations');
            if (result.success) {
                this.conversations = result.data || [];
                this.renderConversations();
            } else {
                // Try VM Bridge if available
                const vmResult = await ipcRenderer.invoke('vm-get-messages');
                if (vmResult.success) {
                    this.conversations = this.processVMMessages(vmResult.data);
                    this.renderConversations();
                } else {
                    // No more demo data - wait for real iPhone connections
                    this.conversations = [];
                    this.renderConversations();
                    this.showNoDataMessage();
                }
            }
        } catch (error) {
            console.error('Failed to load conversations:', error);
            this.conversations = [];
            this.renderConversations();
            this.showNoDataMessage();
        }
    }

    async loadContacts() {
        try {
            const result = await ipcRenderer.invoke('get-contacts');
            if (result.success) {
                this.contacts = result.data || [];
            } else {
                // Try VM Bridge contacts
                const vmResult = await ipcRenderer.invoke('vm-get-contacts');
                if (vmResult.success) {
                    this.contacts = vmResult.data || [];
                } else {
                    this.contacts = [];
                }
            }
        } catch (error) {
            console.error('Failed to load contacts:', error);
            this.contacts = [];
        }
    }

    showNoDataMessage() {
        const conversationsList = document.getElementById('conversations-list');
        if (conversationsList) {
            conversationsList.innerHTML = `
                <div class="no-data-message" style="text-align: center; padding: 40px; color: #666;">
                    <h3>📱 No Messages Yet</h3>
                    <p>Connect your iPhone to see real messages:</p>
                    <ul style="text-align: left; display: inline-block; margin: 20px 0;">
                        <li>📺 Test AirPlay screen mirroring</li>
                        <li>📱 Install iOS companion app</li>
                        <li>📡 Connect via WebSocket</li>
                        <li>⚡ Setup iOS shortcuts</li>
                    </ul>
                    <p><strong>No more demo data - only real iPhone messages!</strong></p>
                </div>
            `;
        }
    }

    renderConversations() {
        const container = document.getElementById('conversations-list');
        if (!container) return;

        container.innerHTML = '';

        if (this.conversations.length === 0) {
            container.innerHTML = `
                <div class="empty-conversations">
                    <div class="empty-icon">💬</div>
                    <p>No conversations yet</p>
                    <button onclick="newMessage()" class="start-conversation-btn">Start a conversation</button>
                </div>
            `;
            return;
        }

        this.conversations.forEach(convo => {
            const div = document.createElement('div');
            div.className = 'conversation-item';
            if (this.currentConversation && this.currentConversation.id === convo.id) {
                div.classList.add('active');
            }

            const lastMessagePreview = convo.lastMessage ?
                (convo.lastMessage.length > 50 ? convo.lastMessage.substring(0, 50) + '...' : convo.lastMessage) :
                'No messages';

            div.innerHTML = `
                <div class="conversation-avatar">${this.getInitials(convo.contactName)}</div>
                <div class="conversation-info">
                    <div class="conversation-name">${this.escapeHtml(convo.contactName)}</div>
                    <div class="conversation-preview">${this.escapeHtml(lastMessagePreview)}</div>
                </div>
                <div class="conversation-meta">
                    <div class="conversation-time">${this.formatTime(convo.lastMessageTime)}</div>
                    ${convo.unreadCount > 0 ? `<div class="conversation-badge">${convo.unreadCount}</div>` : ''}
                </div>
            `;

            div.onclick = () => this.selectConversation(convo);
            container.appendChild(div);
        });
    }

    getInitials(name) {
        return name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    selectConversation(convo) {
        this.currentConversation = convo;
        
        // Update UI
        document.getElementById('no-conversation').style.display = 'none';
        document.getElementById('conversation-view').style.display = 'flex';
        document.getElementById('contact-name').textContent = convo.contactName;
        document.getElementById('contact-phone').textContent = convo.phoneNumber;
        
        // Mark as read
        ipcRenderer.invoke('mark-messages-read', convo.id);
        
        // Render messages
        this.renderMessages(convo.messages);
        
        // Update conversation list
        this.renderConversations();
        
        // Focus input
        this.messageInput.focus();
    }

    renderMessages(messages) {
        const container = document.getElementById('messages-list');
        if (!container) return;

        // Clear existing messages except typing indicator
        const typingIndicator = container.querySelector('.typing-indicator');
        container.innerHTML = '';

        if (!messages || messages.length === 0) {
            container.innerHTML = `
                <div class="no-messages">
                    <div class="no-messages-icon">💬</div>
                    <p>No messages in this conversation</p>
                    <p class="no-messages-subtitle">Send a message to get started</p>
                </div>
            `;
            return;
        }

        let lastDate = null;

        messages.forEach((msg, index) => {
            const messageDate = new Date(msg.timestamp).toDateString();

            // Add date separator if needed
            if (messageDate !== lastDate) {
                const dateSeparator = document.createElement('div');
                dateSeparator.className = 'date-divider';
                dateSeparator.innerHTML = `<div class="date-divider-text">${this.formatDate(msg.timestamp)}</div>`;
                container.appendChild(dateSeparator);
                lastDate = messageDate;
            }

            const messageBubble = this.createMessageBubble(msg.text, !msg.isIncoming);
            container.appendChild(messageBubble);
        });

        // Restore typing indicator if it existed
        if (typingIndicator) {
            container.appendChild(typingIndicator);
        }

        // Scroll to bottom
        this.scrollToBottom();
    }

    // Additional methods for message handling, animations, etc.
    handleTyping(e) {
        if (this.typingTimer) {
            clearTimeout(this.typingTimer);
        }
        
        this.typingTimer = setTimeout(() => {
            this.hideTypingIndicator();
        }, 2000);
    }

    updateSendButton() {
        const hasContent = this.messageInput.value.trim().length > 0;
        this.sendButton.disabled = !hasContent;
        
        if (hasContent) {
            this.sendButton.style.transform = 'scale(1.05)';
        } else {
            this.sendButton.style.transform = 'scale(1)';
        }
    }

    createMessageBubble(text, isSent = false) {
        const bubble = document.createElement('div');
        bubble.className = `message-bubble ${isSent ? 'sent' : 'received'}`;
        
        const content = document.createElement('div');
        content.className = 'message-content';
        content.textContent = text;
        
        const meta = document.createElement('div');
        meta.className = 'message-meta';
        
        const time = document.createElement('span');
        time.className = 'message-time';
        time.textContent = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        meta.appendChild(time);
        
        if (isSent) {
            const status = document.createElement('div');
            status.className = 'message-status';
            status.textContent = '✓';
            meta.appendChild(status);
        }
        
        bubble.appendChild(content);
        bubble.appendChild(meta);
        
        return bubble;
    }

    async sendMessage() {
        const messageText = this.messageInput.value.trim();
        if (!messageText || !this.currentConversation) return;

        // Create message bubble with animation
        const messageBubble = this.createMessageBubble(messageText, true);
        this.appendMessageWithAnimation(messageBubble);

        // Clear input
        this.messageInput.value = '';
        this.updateSendButton();

        // Send via Intel Unison++ protocol
        try {
            const result = await this.sendViaUnisonProtocol(messageText);
            this.updateMessageStatus(messageBubble, result.delivered ? 'delivered' : 'sent');
        } catch (error) {
            console.error('Failed to send message:', error);
            this.updateMessageStatus(messageBubble, 'failed');
        }
    }

    appendMessageWithAnimation(messageBubble) {
        const typingIndicator = document.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }

        messageBubble.style.opacity = '0';
        messageBubble.style.transform = 'translateY(20px)';
        
        this.messagesList.appendChild(messageBubble);
        
        requestAnimationFrame(() => {
            messageBubble.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            messageBubble.style.opacity = '1';
            messageBubble.style.transform = 'translateY(0)';
        });

        this.scrollToBottom();
    }

    updateMessageStatus(messageBubble, status) {
        const statusElement = messageBubble.querySelector('.message-status');
        if (statusElement) {
            switch (status) {
                case 'sent':
                    statusElement.textContent = '✓';
                    statusElement.className = 'message-status';
                    break;
                case 'delivered':
                    statusElement.textContent = '✓✓';
                    statusElement.className = 'message-status delivered';
                    break;
                case 'read':
                    statusElement.textContent = '✓✓';
                    statusElement.className = 'message-status read';
                    break;
                case 'failed':
                    statusElement.textContent = '✗';
                    statusElement.className = 'message-status failed';
                    statusElement.style.color = '#ff5f5f';
                    break;
            }
        }
    }

    showContextMenu(e, messageBubble) {
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.style.left = e.pageX + 'px';
        menu.style.top = e.pageY + 'px';
        
        const menuItems = [
            { label: 'Copy', action: () => this.copyMessage(messageBubble) },
            { label: 'Forward', action: () => this.forwardMessage(messageBubble) },
            { label: 'Reply', action: () => this.replyToMessage(messageBubble) },
            { label: 'Delete', action: () => this.deleteMessage(messageBubble), danger: true }
        ];
        
        menuItems.forEach(item => {
            const menuItem = document.createElement('div');
            menuItem.className = `context-menu-item ${item.danger ? 'danger' : ''}`;
            menuItem.textContent = item.label;
            menuItem.addEventListener('click', () => {
                item.action();
                menu.remove();
            });
            menu.appendChild(menuItem);
        });
        
        document.body.appendChild(menu);
        
        document.addEventListener('click', (e) => {
            if (!menu.contains(e.target)) {
                menu.remove();
            }
        }, { once: true });
    }

    copyMessage(messageBubble) {
        const content = messageBubble.querySelector('.message-content').textContent;
        navigator.clipboard.writeText(content);
    }

    forwardMessage(messageBubble) {
        console.log('Forward feature coming soon');
    }

    replyToMessage(messageBubble) {
        console.log('Reply feature coming soon');
    }

    deleteMessage(messageBubble) {
        messageBubble.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        messageBubble.style.opacity = '0';
        messageBubble.style.transform = 'translateX(-20px)';
        
        setTimeout(() => {
            messageBubble.remove();
        }, 300);
    }

    handleKeyboardShortcuts(e) {
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            this.sendMessage();
        }
        
        if (e.key === 'Escape') {
            const contextMenu = document.querySelector('.context-menu');
            if (contextMenu) {
                contextMenu.remove();
            }
        }
    }

    handleResize() {
        const isMobile = window.innerWidth <= 768;
        const conversationsPanel = document.querySelector('.conversations-panel');
        
        if (isMobile) {
            conversationsPanel.classList.add('mobile');
        } else {
            conversationsPanel.classList.remove('mobile');
        }
    }

    scrollToBottom() {
        if (this.messagesList) {
            requestAnimationFrame(() => {
                this.messagesList.scrollTop = this.messagesList.scrollHeight;
            });
        }
    }

    showTypingIndicator() {
        const existingIndicator = document.querySelector('.typing-indicator');
        if (existingIndicator) return;
        
        const indicator = document.createElement('div');
        indicator.className = 'typing-indicator';
        indicator.innerHTML = `
            <span>Someone is typing</span>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        `;
        
        this.messagesList.appendChild(indicator);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const indicator = document.querySelector('.typing-indicator');
        if (indicator) {
            indicator.style.opacity = '0';
            indicator.style.transform = 'translateY(10px)';
            
            setTimeout(() => {
                indicator.remove();
            }, 300);
        }
    }

    formatTime(timestamp) {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    formatDate(timestamp) {
        const date = new Date(timestamp);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        if (date.toDateString() === today.toDateString()) {
            return 'Today';
        } else if (date.toDateString() === yesterday.toDateString()) {
            return 'Yesterday';
        } else {
            return date.toLocaleDateString();
        }
    }

    async sendViaUnisonProtocol(messageText) {
        try {
            let result = await ipcRenderer.invoke('vm-send-message', {
                phoneNumber: this.currentConversation.phoneNumber,
                text: messageText
            });

            if (!result.success) {
                result = await ipcRenderer.invoke('send-message', {
                    phoneNumber: this.currentConversation.phoneNumber,
                    text: messageText
                });
            }

            return result;
        } catch (error) {
            console.error('Failed to send via Unison protocol:', error);
            return { success: false, error: error.message };
        }
    }

    startRealtimeSync() {
        setInterval(() => {
            this.syncMessages();
        }, 1000);
    }

    async syncMessages() {
        // Real-time sync would happen here
        // For demo purposes, we'll occasionally add a message
        if (Math.random() < 0.005) {
            const demoMessages = [
                "This is a demo message from Intel Unison++",
                "The new UI looks amazing! 🎉",
                "60fps animations are so smooth",
                "WhatsApp-style bubbles working perfectly"
            ];
            
            const randomMessage = demoMessages[Math.floor(Math.random() * demoMessages.length)];
            const messageBubble = this.createMessageBubble(randomMessage, false);
            this.appendMessageWithAnimation(messageBubble);
        }
    }

    // Handle IPC events
    handleNewMessage(message) {
        if (!this.currentConversation || this.currentConversation.id !== message.conversationId) {
            new Notification(message.contactName, {
                body: message.text,
                icon: '../../assets/icon.png'
            });
        }
        
        this.loadInitialData();
    }

    handleVMMessages(messages) {
        console.log('VM messages received:', messages);
        this.loadInitialData();
    }

    handleVMNewMessage(message) {
        console.log('VM new message:', message);
        this.loadInitialData();
    }

    processVMMessages(data) {
        return data || [];
    }
}

// Initialize the modern messaging interface when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.modernMessaging = new ModernMessagingInterface();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModernMessagingInterface;
}

// Legacy function for compatibility
function newMessage() {
    console.log('New message feature coming soon!');
}