const WebSocket = require('ws');
const { EventEmitter } = require('events');
const { exec } = require('child_process');

class VMBridge extends EventEmitter {
  constructor() {
    super();
    this.ws = null;
    this.vmIP = null;
    this.reconnectInterval = null;
    this.isConnected = false;
  }

  async findVM() {
    console.log('🔍 Scanning network for macOS VM...');
    
    // Common VM network ranges
    const ranges = [
      '192.168.1.',   // Common home network
      '192.168.0.',   // Alternative home network
      '10.0.0.',      // Corporate network
      '172.16.0.',    // VMware default
      '192.168.56.',  // VirtualBox default
      '192.168.122.'  // QEMU default
    ];
    
    for (const prefix of ranges) {
      for (let i = 1; i < 255; i++) {
        const ip = prefix + i;
        try {
          const ws = new WebSocket(`ws://${ip}:8888`, { 
            timeout: 200,
            handshakeTimeout: 200
          });
          
          const found = await new Promise((resolve) => {
            const timer = setTimeout(() => resolve(false), 200);
            
            ws.on('open', () => {
              clearTimeout(timer);
              console.log('✅ Found macOS VM at:', ip);
              this.vmIP = ip;
              ws.close();
              resolve(true);
            });
            
            ws.on('error', () => {
              clearTimeout(timer);
              resolve(false);
            });
          });
          
          if (found) return this.vmIP;
        } catch (e) {
          // Continue scanning
        }
      }
    }
    
    // Try localhost as fallback
    console.log('🔄 Trying localhost...');
    this.vmIP = 'localhost';
    return this.vmIP;
  }

  async connect() {
    if (!this.vmIP) {
      await this.findVM();
    }
    
    console.log(`🔗 Connecting to VM at ${this.vmIP}...`);
    this.setupConnection();
  }

  setupConnection() {
    try {
      this.ws = new WebSocket(`ws://${this.vmIP}:8888`);
      
      this.ws.on('open', () => {
        console.log('✅ Connected to macOS VM!');
        this.isConnected = true;
        this.emit('connected');
        
        // Request initial sync
        this.ws.send(JSON.stringify({ 
          type: 'SYNC_ALL',
          timestamp: Date.now()
        }));
      });
      
      this.ws.on('message', (data) => {
        try {
          const msg = JSON.parse(data);
          console.log('📨 VM data:', msg.type);
          this.emit('data', msg);
        } catch (e) {
          console.log('❌ Invalid VM message:', e.message);
        }
      });
      
      this.ws.on('close', () => {
        console.log('🔄 VM disconnected, reconnecting in 5s...');
        this.isConnected = false;
        setTimeout(() => this.connect(), 5000);
      });
      
      this.ws.on('error', (error) => {
        console.log('❌ VM connection error:', error.message);
      });
    } catch (e) {
      console.log('❌ Failed to connect to VM:', e.message);
    }
  }

  sendCommand(command) {
    if (this.isConnected && this.ws) {
      this.ws.send(JSON.stringify(command));
    }
  }
}

module.exports = { VMBridge };