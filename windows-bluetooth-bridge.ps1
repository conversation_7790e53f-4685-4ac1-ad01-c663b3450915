# Windows Bluetooth Bridge for WSL
# This script runs on Windows and provides Bluetooth access to WSL

param(
    [string]$Action = "scan",
    [string]$DeviceAddress = "",
    [int]$Port = 26820
)

Add-Type -AssemblyName System.Runtime.WindowsRuntime
Add-Type -Path "C:\Windows\Microsoft.NET\Framework64\v4.0.30319\System.Runtime.WindowsRuntime.dll"

# Bluetooth LE functions
function Get-BluetoothDevices {
    try {
        Write-Host "🔍 Scanning for Bluetooth devices..."
        
        # Use Windows.Devices.Bluetooth namespace
        $bluetoothAdapter = [Windows.Devices.Bluetooth.BluetoothAdapter]::GetDefaultAsync().GetAwaiter().GetResult()
        
        if ($null -eq $bluetoothAdapter) {
            Write-Host "❌ No Bluetooth adapter found"
            return @()
        }
        
        Write-Host "✅ Bluetooth adapter found: $($bluetoothAdapter.DeviceId)"
        
        # Scan for LE devices
        $deviceWatcher = [Windows.Devices.Bluetooth.Advertisement.BluetoothLEAdvertisementWatcher]::new()
        $devices = @()
        
        # Set up event handler for advertisements
        $deviceWatcher.add_Received({
            param($sender, $args)
            
            $device = @{
                Address = $args.BluetoothAddress.ToString("X12")
                LocalName = $args.LocalName
                TxPowerLevel = $args.TxPowerLevel
                RSSI = $args.RawSignalStrengthInDBm
                Timestamp = $args.Timestamp
                ServiceUuids = $args.Advertisement.ServiceUuids
            }
            
            # Check if this is an iPhone (look for Apple services)
            $isAppleDevice = $false
            foreach ($uuid in $args.Advertisement.ServiceUuids) {
                if ($uuid.ToString() -match "FE2C" -or $uuid.ToString() -match "180F") {
                    $isAppleDevice = $true
                    break
                }
            }
            
            if ($isAppleDevice -or $args.LocalName -match "iPhone") {
                Write-Host "📱 iPhone detected: $($args.LocalName) - $($device.Address)"
                $script:devices += $device
            }
        })
        
        # Start scanning
        $deviceWatcher.Start()
        Write-Host "🔍 Bluetooth LE scan started... (10 second timeout)"
        
        # Scan for 10 seconds
        Start-Sleep -Seconds 10
        
        $deviceWatcher.Stop()
        Write-Host "✅ Bluetooth scan completed. Found $($devices.Count) Apple devices"
        
        return $devices
        
    } catch {
        Write-Host "❌ Bluetooth scan error: $($_.Exception.Message)"
        return @()
    }
}

function Connect-ToiPhone {
    param([string]$DeviceAddress)
    
    try {
        Write-Host "🔗 Attempting to connect to iPhone: $DeviceAddress"
        
        # Convert address to UInt64
        $addressUInt64 = [Convert]::ToUInt64($DeviceAddress, 16)
        
        # Get Bluetooth LE device
        $device = [Windows.Devices.Bluetooth.BluetoothLEDevice]::FromBluetoothAddressAsync($addressUInt64).GetAwaiter().GetResult()
        
        if ($null -eq $device) {
            Write-Host "❌ Could not find device with address: $DeviceAddress"
            return $false
        }
        
        Write-Host "✅ iPhone found: $($device.Name)"
        
        # Try to get GATT services
        $gattResult = $device.GetGattServicesAsync().GetAwaiter().GetResult()
        
        if ($gattResult.Status -eq [Windows.Devices.Bluetooth.GenericAttributeProfile.GattCommunicationStatus]::Success) {
            Write-Host "✅ GATT services accessible. Services found: $($gattResult.Services.Count)"
            
            foreach ($service in $gattResult.Services) {
                Write-Host "📋 Service: $($service.Uuid)"
            }
            
            return $true
        } else {
            Write-Host "❌ Could not access GATT services: $($gattResult.Status)"
            return $false
        }
        
    } catch {
        Write-Host "❌ Connection error: $($_.Exception.Message)"
        return $false
    }
}

function Start-BluetoothBridge {
    Write-Host "🌉 Starting Bluetooth Bridge on port $Port"
    
    # Create TCP server to communicate with WSL
    $listener = [System.Net.Sockets.TcpListener]::new([System.Net.IPAddress]::Any, $Port)
    $listener.Start()
    
    Write-Host "✅ Bluetooth Bridge listening on port $Port"
    
    while ($true) {
        try {
            $client = $listener.AcceptTcpClient()
            Write-Host "🔗 WSL client connected"
            
            $stream = $client.GetStream()
            $reader = [System.IO.StreamReader]::new($stream)
            $writer = [System.IO.StreamWriter]::new($stream)
            
            while ($client.Connected) {
                $command = $reader.ReadLine()
                
                if ($null -eq $command) { break }
                
                Write-Host "📨 Command from WSL: $command"
                
                switch ($command) {
                    "scan" {
                        $devices = Get-BluetoothDevices
                        $result = @{
                            action = "scan_result"
                            devices = $devices
                            success = $true
                        }
                        $writer.WriteLine(($result | ConvertTo-Json -Compress))
                        $writer.Flush()
                    }
                    
                    { $_ -match "connect:(.*)" } {
                        $deviceAddr = $matches[1]
                        $success = Connect-ToiPhone -DeviceAddress $deviceAddr
                        $result = @{
                            action = "connect_result"
                            address = $deviceAddr
                            success = $success
                        }
                        $writer.WriteLine(($result | ConvertTo-Json -Compress))
                        $writer.Flush()
                    }
                    
                    "status" {
                        $result = @{
                            action = "status"
                            bluetooth_available = $true
                            adapter_count = 1
                        }
                        $writer.WriteLine(($result | ConvertTo-Json -Compress))
                        $writer.Flush()
                    }
                }
            }
            
            $client.Close()
            Write-Host "🔌 WSL client disconnected"
            
        } catch {
            Write-Host "❌ Bridge error: $($_.Exception.Message)"
        }
    }
}

# Main execution
switch ($Action) {
    "scan" {
        $devices = Get-BluetoothDevices
        $devices | ConvertTo-Json
    }
    
    "connect" {
        if ($DeviceAddress -eq "") {
            Write-Host "❌ Device address required for connect action"
            exit 1
        }
        $result = Connect-ToiPhone -DeviceAddress $DeviceAddress
        @{ success = $result } | ConvertTo-Json
    }
    
    "bridge" {
        Start-BluetoothBridge
    }
    
    default {
        Write-Host "Usage: .\windows-bluetooth-bridge.ps1 -Action [scan|connect|bridge] [-DeviceAddress <addr>] [-Port <port>]"
    }
}