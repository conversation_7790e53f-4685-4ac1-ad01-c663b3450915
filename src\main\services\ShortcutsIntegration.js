const EventEmitter = require('events');
const express = require('express');
const QRCode = require('qrcode');
const path = require('path');

class ShortcutsIntegration extends EventEmitter {
  constructor() {
    super();
    this.app = express();
    this.server = null;
    this.port = 8888;
    this.setupRoutes();
    this.connectedDevices = new Set();
  }

  setupRoutes() {
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));

    // Enable CORS for iOS shortcuts
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    });

    // Real message sync endpoints - NO MORE DEMO DATA
    this.app.post('/sync-messages', (req, res) => {
      this.handleRealMessageSync(req, res);
    });

    this.app.post('/send-message', (req, res) => {
      this.handleRealSendMessage(req, res);
    });

    this.app.post('/message-sent', (req, res) => {
      this.handleMessageSentConfirmation(req, res);
    });

    // Real call endpoints
    this.app.post('/call', (req, res) => {
      this.handleRealCallEvent(req, res);
    });

    // Real notification endpoints
    this.app.post('/notification', (req, res) => {
      this.handleRealNotificationSync(req, res);
    });

    // Device registration
    this.app.post('/register', (req, res) => {
      this.handleDeviceRegistration(req, res);
    });

    // Health check
    this.app.get('/health', (req, res) => {
      res.json({ 
        status: 'ok', 
        timestamp: Date.now(),
        connectedDevices: this.connectedDevices.size
      });
    });

    // Shortcut installation helper
    this.app.get('/shortcuts', (req, res) => {
      this.serveShortcutInstructions(req, res);
    });
  }

  async start() {
    return new Promise((resolve, reject) => {
      this.server = this.app.listen(this.port, '0.0.0.0', (err) => {
        if (err) {
          reject(err);
        } else {
          console.log(`Shortcuts integration server running on port ${this.port}`);
          this.generateQRCode();
          resolve(this.port);
        }
      });
    });
  }

  stop() {
    if (this.server) {
      this.server.close();
      this.server = null;
    }
  }

  // Real message handling - NO MORE DEMO DATA
  handleRealMessageSync(req, res) {
    const { type, messages, timestamp } = req.body;

    console.log('📱 REAL MESSAGE SYNC from iPhone:', {
      type,
      timestamp,
      messageCount: messages?.length || 0
    });

    if (messages && Array.isArray(messages)) {
      // Process only real iPhone messages
      const realMessages = messages.filter(msg =>
        msg.text && msg.sender && msg.timestamp
      );

      if (realMessages.length > 0) {
        console.log(`✅ Processing ${realMessages.length} real iPhone messages`);

        this.emit('real-messages-received', {
          messages: realMessages.map(msg => ({
            id: msg.id || `real_${Date.now()}_${Math.random()}`,
            phoneNumber: msg.sender,
            contactName: msg.contactName || this.getContactName(msg.sender),
            text: msg.text,
            timestamp: new Date(msg.timestamp),
            isIncoming: msg.isIncoming !== false,
            isRead: msg.isRead || false,
            isReal: true,
            source: 'ios_shortcuts'
          }))
        });
      }
    }

    res.json({
      success: true,
      received: Date.now(),
      processed: messages?.length || 0,
      message: 'Real iPhone messages processed successfully'
    });
  }

  handleRealSendMessage(req, res) {
    const { recipient, message, requestId } = req.body;

    console.log('📤 REAL MESSAGE SEND REQUEST:', { recipient, requestId });

    if (!recipient || !message) {
      return res.status(400).json({
        success: false,
        error: 'Missing recipient or message',
        requestId
      });
    }

    // Emit event for real message sending
    this.emit('send-real-message', {
      phoneNumber: recipient,
      text: message,
      requestId: requestId || `req_${Date.now()}`,
      timestamp: new Date(),
      isReal: true
    });

    res.json({
      success: true,
      requestId: requestId || `req_${Date.now()}`,
      message: 'Message send request processed - waiting for iPhone confirmation'
    });
  }

  handleMessageSentConfirmation(req, res) {
    const { success, messageId, requestId, deliveryStatus, error } = req.body;

    console.log('📱 MESSAGE SENT CONFIRMATION:', {
      success,
      messageId,
      requestId,
      deliveryStatus
    });

    this.emit('message-sent-confirmation', {
      success,
      messageId,
      requestId,
      deliveryStatus,
      error,
      timestamp: new Date(),
      isReal: true
    });

    res.json({ success: true, received: Date.now() });
  }

  handleRealCallEvent(req, res) {
    const { type, phoneNumber, contactName, duration, timestamp } = req.body;

    console.log('📞 REAL CALL EVENT:', { type, phoneNumber, contactName });

    this.emit('real-call-event', {
      type,
      phoneNumber,
      contactName: contactName || this.getContactName(phoneNumber),
      duration,
      timestamp: timestamp || new Date(),
      isReal: true,
      source: 'ios_shortcuts'
    });

    res.json({ success: true, received: Date.now() });
  }

  handleRealNotificationSync(req, res) {
    const { app, notification, sender, timestamp } = req.body;

    console.log('🔔 REAL NOTIFICATION:', {
      app: app?.name,
      title: notification?.title
    });

    this.emit('real-notification', {
      app,
      notification,
      sender,
      timestamp: timestamp || new Date(),
      isReal: true,
      source: 'ios_shortcuts'
    });

    res.json({ success: true, received: Date.now() });
  }

  getContactName(phoneNumber) {
    // This would integrate with contacts database
    return phoneNumber; // Fallback to phone number
  }

  // Legacy function - keeping for compatibility but not using
  handleSendMessage(req, res) {
    const { phoneNumber, text, contactName } = req.body;
    
    console.log('Sending message via iOS Shortcuts:', { phoneNumber, text });
    
    // Emit to main app for processing
    this.emit('send-message', {
      phoneNumber,
      text,
      contactName,
      timestamp: Date.now()
    });

    res.json({ success: true, messageId: Date.now().toString() });
  }

  // Call handling
  handleCallEvent(req, res) {
    const { type, caller, phoneNumber, timestamp } = req.body;
    
    console.log('Received call event from iOS Shortcuts:', { type, caller });
    
    this.emit('call-event', {
      type: type || 'incoming',
      caller: caller || 'Unknown',
      phoneNumber: phoneNumber || '',
      timestamp: timestamp || Date.now()
    });

    res.json({ success: true, received: Date.now() });
  }

  // Notification handling
  handleNotificationSync(req, res) {
    const { notifications, timestamp } = req.body;
    
    console.log('Received notification sync from iOS Shortcuts');
    
    if (notifications && Array.isArray(notifications)) {
      this.emit('notifications-received', {
        notifications: notifications.map(notif => ({
          id: notif.id || Date.now().toString(),
          app: notif.app || 'Unknown',
          title: notif.title || '',
          body: notif.body || '',
          timestamp: notif.timestamp || Date.now()
        }))
      });
    }

    res.json({ success: true, received: Date.now() });
  }

  // Device registration
  handleDeviceRegistration(req, res) {
    const { deviceId, deviceName, capabilities } = req.body;
    
    console.log('Device registered:', { deviceId, deviceName });
    
    this.connectedDevices.add(deviceId);
    
    this.emit('device-registered', {
      deviceId,
      deviceName: deviceName || 'iPhone',
      capabilities: capabilities || [],
      timestamp: Date.now()
    });

    res.json({ 
      success: true, 
      serverId: 'windows-pc-' + Date.now(),
      endpoints: {
        messages: '/messages',
        sendMessage: '/send-message',
        calls: '/call',
        notifications: '/notifications'
      }
    });
  }

  // Generate QR code for easy setup
  async generateQRCode() {
    try {
      const setupData = {
        serverUrl: `http://YOUR_PC_IP:${this.port}`,
        endpoints: {
          register: '/register',
          messages: '/messages',
          calls: '/call',
          notifications: '/notifications'
        },
        shortcuts: [
          'SendMessageFromPC',
          'GetRecentMessages',
          'HandleIncomingCall',
          'SyncNotifications'
        ]
      };

      const qrCodeData = await QRCode.toDataURL(JSON.stringify(setupData));
      this.qrCode = qrCodeData;
      
      console.log('QR Code generated for iOS setup');
      this.emit('qr-code-ready', qrCodeData);
      
    } catch (error) {
      console.error('Error generating QR code:', error);
    }
  }

  // Serve shortcut installation instructions
  serveShortcutInstructions(req, res) {
    const instructions = {
      title: "iPhone Companion Pro - iOS Shortcuts Setup",
      steps: [
        "1. Install the required iOS Shortcuts using the links below",
        "2. Set up automation in iOS Shortcuts app",
        "3. Configure shortcuts to send data to this PC",
        "4. Test the connection"
      ],
      shortcuts: {
        "Send Message": "https://www.icloud.com/shortcuts/sendmessage",
        "Get Messages": "https://www.icloud.com/shortcuts/getmessages", 
        "Handle Calls": "https://www.icloud.com/shortcuts/handlecalls",
        "Sync Notifications": "https://www.icloud.com/shortcuts/syncnotifications"
      },
      serverInfo: {
        url: `http://YOUR_PC_IP:${this.port}`,
        status: "Running"
      }
    };

    res.json(instructions);
  }

  // Get connection status
  getStatus() {
    return {
      running: this.server !== null,
      port: this.port,
      connectedDevices: this.connectedDevices.size,
      qrCodeReady: !!this.qrCode
    };
  }

  // Send message to iOS device (via shortcuts)
  async sendToiOS(data) {
    // This would trigger iOS shortcuts to perform actions
    // For now, we emit events that the UI can handle
    this.emit('ios-action-request', data);
  }

  // Get local IP for QR code
  getLocalIP() {
    const { networkInterfaces } = require('os');
    const nets = networkInterfaces();
    
    for (const name of Object.keys(nets)) {
      for (const net of nets[name]) {
        if (net.family === 'IPv4' && !net.internal) {
          return net.address;
        }
      }
    }
    
    return '127.0.0.1';
  }
}

module.exports = { ShortcutsIntegration };
