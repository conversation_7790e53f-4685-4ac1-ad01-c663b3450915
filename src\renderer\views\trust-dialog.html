<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0a0a;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
        }
        
        .trust-container {
            text-align: center;
            padding: 40px;
            background: #1a1a1a;
            border-radius: 20px;
            max-width: 500px;
        }
        
        .phone-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }
        
        h2 {
            margin-bottom: 20px;
        }
        
        .steps {
            text-align: left;
            margin: 30px 0;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
        }
        
        .steps li {
            margin: 10px 0;
        }
        
        .trust-image {
            width: 300px;
            margin: 20px auto;
            border: 1px solid #3a3a3a;
            border-radius: 10px;
        }
        
        button {
            padding: 12px 30px;
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="trust-container">
        <div class="phone-icon">📱</div>
        <h2>Trust This Computer</h2>
        <p>To connect your iPhone, you need to trust this computer.</p>
        
        <div class="steps">
            <ol>
                <li>Unlock your iPhone</li>
                <li>You should see a popup asking "Trust This Computer?"</li>
                <li>Tap "Trust"</li>
                <li>Enter your passcode if prompted</li>
            </ol>
        </div>
        
        <p>If you don't see the popup, unplug and reconnect your iPhone.</p>
        
        <button onclick="window.close()">I've Trusted This Computer</button>
    </div>
</body>
</html>