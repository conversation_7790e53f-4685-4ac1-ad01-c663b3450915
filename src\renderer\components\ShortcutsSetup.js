class ShortcutsSetup {
  constructor() {
    this.container = null;
    this.qrCode = null;
    this.status = {
      serverRunning: false,
      devicesConnected: 0,
      shortcutsInstalled: false
    };
    
    this.init();
  }

  init() {
    this.createContainer();
    this.setupEventListeners();
    this.checkStatus();
  }

  createContainer() {
    this.container = document.createElement('div');
    this.container.className = 'shortcuts-setup';
    this.container.innerHTML = `
      <div class="shortcuts-setup-content">
        <div class="setup-header">
          <h2>🔗 iOS Shortcuts Integration</h2>
          <p>Connect your iPhone using iOS Shortcuts for real message and call integration</p>
        </div>

        <div class="setup-status">
          <div class="status-item">
            <span class="status-label">Server Status:</span>
            <span class="status-value" id="server-status">Checking...</span>
          </div>
          <div class="status-item">
            <span class="status-label">Connected Devices:</span>
            <span class="status-value" id="device-count">0</span>
          </div>
        </div>

        <div class="setup-steps">
          <div class="step-section">
            <h3>📱 Step 1: Install iOS Shortcuts</h3>
            <p>Install these shortcuts on your iPhone:</p>
            <div class="shortcuts-list">
              <div class="shortcut-item">
                <span class="shortcut-name">Send Message from PC</span>
                <button class="install-btn" data-shortcut="send-message">Install</button>
              </div>
              <div class="shortcut-item">
                <span class="shortcut-name">Get Recent Messages</span>
                <button class="install-btn" data-shortcut="get-messages">Install</button>
              </div>
              <div class="shortcut-item">
                <span class="shortcut-name">Handle Incoming Calls</span>
                <button class="install-btn" data-shortcut="handle-calls">Install</button>
              </div>
              <div class="shortcut-item">
                <span class="shortcut-name">Sync Notifications</span>
                <button class="install-btn" data-shortcut="sync-notifications">Install</button>
              </div>
            </div>
          </div>

          <div class="step-section">
            <h3>📋 Step 2: Scan QR Code</h3>
            <p>Scan this QR code with your iPhone to get server details:</p>
            <div class="qr-code-container">
              <div id="qr-code-display">
                <div class="qr-placeholder">Generating QR Code...</div>
              </div>
              <button id="refresh-qr" class="refresh-btn">🔄 Refresh QR Code</button>
            </div>
          </div>

          <div class="step-section">
            <h3>⚙️ Step 3: Set Up Automation</h3>
            <p>Configure iOS automation for real-time sync:</p>
            <div class="automation-instructions">
              <ol>
                <li>Open iOS <strong>Shortcuts</strong> app</li>
                <li>Go to <strong>Automation</strong> tab</li>
                <li>Tap <strong>+</strong> to create new automation</li>
                <li>Choose <strong>App</strong> trigger</li>
                <li>Select <strong>Messages</strong> app</li>
                <li>Choose <strong>Is Opened</strong></li>
                <li>Add <strong>Get Recent Messages</strong> shortcut</li>
                <li>Disable <strong>Ask Before Running</strong></li>
                <li>Save automation</li>
              </ol>
            </div>
          </div>

          <div class="step-section">
            <h3>🧪 Step 4: Test Connection</h3>
            <div class="test-controls">
              <button id="test-connection" class="test-btn">Test Connection</button>
              <button id="send-test-message" class="test-btn">Send Test Message</button>
              <div id="test-results" class="test-results"></div>
            </div>
          </div>
        </div>

        <div class="setup-actions">
          <button id="restart-server" class="action-btn">🔄 Restart Server</button>
          <button id="show-advanced" class="action-btn">⚙️ Advanced Settings</button>
          <button id="close-setup" class="action-btn secondary">✕ Close</button>
        </div>

        <div class="advanced-settings" id="advanced-settings" style="display: none;">
          <h3>Advanced Settings</h3>
          <div class="setting-item">
            <label>Server Port:</label>
            <input type="number" id="server-port" value="8888" min="1024" max="65535">
          </div>
          <div class="setting-item">
            <label>Local IP:</label>
            <input type="text" id="local-ip" readonly>
          </div>
          <div class="setting-item">
            <label>Auto-start Server:</label>
            <input type="checkbox" id="auto-start" checked>
          </div>
        </div>
      </div>
    `;
  }

  setupEventListeners() {
    // Install shortcut buttons
    this.container.querySelectorAll('.install-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const shortcut = e.target.dataset.shortcut;
        this.installShortcut(shortcut);
      });
    });

    // QR code refresh
    this.container.querySelector('#refresh-qr').addEventListener('click', () => {
      this.refreshQRCode();
    });

    // Test buttons
    this.container.querySelector('#test-connection').addEventListener('click', () => {
      this.testConnection();
    });

    this.container.querySelector('#send-test-message').addEventListener('click', () => {
      this.sendTestMessage();
    });

    // Action buttons
    this.container.querySelector('#restart-server').addEventListener('click', () => {
      this.restartServer();
    });

    this.container.querySelector('#show-advanced').addEventListener('click', () => {
      this.toggleAdvancedSettings();
    });

    this.container.querySelector('#close-setup').addEventListener('click', () => {
      this.close();
    });

    // Listen for shortcuts events using ipcRenderer
    const { ipcRenderer } = require('electron');

    ipcRenderer.on('shortcuts-qr-code', (event, qrCode) => {
      this.displayQRCode(qrCode);
    });

    ipcRenderer.on('shortcuts-device-registered', (event, device) => {
      this.updateDeviceCount();
      this.showTestResults(`Device registered: ${device.deviceName}`, 'success');
    });
  }

  async checkStatus() {
    try {
      const { ipcRenderer } = require('electron');
      const result = await ipcRenderer.invoke('get-shortcuts-status');
      if (result.success) {
        this.updateStatus(result.data);
      }
    } catch (error) {
      console.error('Error checking shortcuts status:', error);
    }
  }

  updateStatus(status) {
    this.status = { ...this.status, ...status };
    
    const serverStatus = this.container.querySelector('#server-status');
    const deviceCount = this.container.querySelector('#device-count');
    
    serverStatus.textContent = status.running ? 'Running' : 'Stopped';
    serverStatus.className = status.running ? 'status-success' : 'status-error';
    
    deviceCount.textContent = status.connectedDevices || 0;
  }

  async refreshQRCode() {
    try {
      const { ipcRenderer } = require('electron');
      const result = await ipcRenderer.invoke('get-shortcuts-qr-code');
      if (result.success) {
        this.displayQRCode(result.data);
      } else {
        this.showQRError('QR code not ready');
      }
    } catch (error) {
      this.showQRError('Error loading QR code');
    }
  }

  displayQRCode(qrCodeData) {
    const qrDisplay = this.container.querySelector('#qr-code-display');
    qrDisplay.innerHTML = `<img src="${qrCodeData}" alt="Setup QR Code" class="qr-code-image">`;
  }

  showQRError(message) {
    const qrDisplay = this.container.querySelector('#qr-code-display');
    qrDisplay.innerHTML = `<div class="qr-error">${message}</div>`;
  }

  installShortcut(shortcutType) {
    // Generate installation URLs based on shortcut type
    const urls = {
      'send-message': 'https://www.icloud.com/shortcuts/your-send-message-shortcut',
      'get-messages': 'https://www.icloud.com/shortcuts/your-get-messages-shortcut',
      'handle-calls': 'https://www.icloud.com/shortcuts/your-handle-calls-shortcut',
      'sync-notifications': 'https://www.icloud.com/shortcuts/your-sync-notifications-shortcut'
    };

    const url = urls[shortcutType];
    if (url) {
      window.open(url, '_blank');
    }
  }

  async testConnection() {
    this.showTestResults('Testing connection...', 'info');

    try {
      const { ipcRenderer } = require('electron');
      const result = await ipcRenderer.invoke('get-shortcuts-status');
      if (result.success && result.data.running) {
        this.showTestResults('✅ Server is running and ready for connections', 'success');
      } else {
        this.showTestResults('❌ Server is not running', 'error');
      }
    } catch (error) {
      this.showTestResults('❌ Connection test failed', 'error');
    }
  }

  async sendTestMessage() {
    this.showTestResults('Sending test message...', 'info');

    try {
      const { ipcRenderer } = require('electron');
      const result = await ipcRenderer.invoke('send-message-via-shortcuts', {
        phoneNumber: '+1234567890',
        text: 'Test message from iPhone Companion Pro',
        contactName: 'Test Contact'
      });

      if (result.success) {
        this.showTestResults('✅ Test message sent successfully', 'success');
      } else {
        this.showTestResults('❌ Failed to send test message', 'error');
      }
    } catch (error) {
      this.showTestResults('❌ Test message failed', 'error');
    }
  }

  showTestResults(message, type) {
    const results = this.container.querySelector('#test-results');
    results.innerHTML = `<div class="test-result ${type}">${message}</div>`;
    
    // Auto-clear after 5 seconds
    setTimeout(() => {
      results.innerHTML = '';
    }, 5000);
  }

  async restartServer() {
    try {
      const { ipcRenderer } = require('electron');
      const result = await ipcRenderer.invoke('restart-shortcuts-server');
      if (result.success) {
        this.showTestResults('✅ Server restarted successfully', 'success');
        this.checkStatus();
      } else {
        this.showTestResults('❌ Failed to restart server', 'error');
      }
    } catch (error) {
      this.showTestResults('❌ Server restart failed', 'error');
    }
  }

  toggleAdvancedSettings() {
    const advanced = this.container.querySelector('#advanced-settings');
    const isVisible = advanced.style.display !== 'none';
    advanced.style.display = isVisible ? 'none' : 'block';
  }

  updateDeviceCount() {
    this.checkStatus();
  }

  show() {
    document.body.appendChild(this.container);
    this.refreshQRCode();
  }

  close() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }
}

// Export for use in other modules
window.ShortcutsSetup = ShortcutsSetup;
