@echo off
echo ��� iPhone Companion Pro - Connection Test Suite
echo ================================================
echo.

echo [1/4] Testing Node.js environment...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js not found! Please install Node.js
    pause
    exit /b 1
)
echo ✅ Node.js found

echo.
echo [2/4] Installing required dependencies...
npm install bonjour-service ws @abandonware/noble
if %errorlevel% neq 0 (
    echo ⚠️  Some dependencies failed to install
)

echo.
echo [3/4] Running comprehensive connection test...
node complete-connection-test.js
if %errorlevel% neq 0 (
    echo ❌ Connection test failed
    pause
    exit /b 1
)

echo.
echo [4/4] Opening connection dashboard...
start real-data-dashboard.html

echo.
echo ✅ Test suite completed!
echo Check connection-config.json for detailed results
pause
=======
@echo off
title iPhone Companion Pro - Connection Testing Suite
color 0A

echo.
echo ========================================
echo  iPhone Companion Pro Connection Test
echo ========================================
echo.

echo 🔥 Testing ALL iPhone connection methods...
echo.

REM Check Node.js
echo [1/6] Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found - install from nodejs.org
    pause
    exit /b 1
) else (
    echo ✅ Node.js found
)

REM Install dependencies if needed
echo.
echo [2/6] Installing dependencies...
if not exist node_modules (
    echo Installing npm packages...
    npm install ws sqlite3 better-sqlite3 bonjour-service @abandonware/noble
)

REM Check iTunes/Apple Mobile Device Support
echo.
echo [3/6] Checking iTunes drivers...
reg query "HKLM\SOFTWARE\Apple Inc.\Apple Mobile Device Support" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ iTunes not installed - download from Apple
    echo    Required for USB connection
) else (
    echo ✅ iTunes drivers found
)

REM Check Phone Link
echo.
echo [4/6] Checking Phone Link...
tasklist | findstr YourPhone >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Phone Link not running
    echo    Start it from Start Menu
) else (
    echo ✅ Phone Link is running
)

REM Check Bluetooth service
echo.
echo [5/6] Checking Bluetooth...
sc query bthserv | findstr RUNNING >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Bluetooth service not running
    echo    Starting Bluetooth service...
    net start bthserv >nul 2>&1
) else (
    echo ✅ Bluetooth service running
)

REM Run comprehensive test
echo.
echo [6/6] Running comprehensive connection test...
echo.
node complete-connection-test.js

echo.
echo ========================================
echo  iPhone Setup Instructions
echo ========================================
echo.
echo On your iPhone:
echo 📱 1. Connect via USB cable
echo 📱 2. Tap "Trust This Computer" when prompted
echo 📱 3. Enable Personal Hotspot in Settings
echo 📱 4. Open Control Center ^> Screen Mirroring
echo 📱 5. Look for "iPhone Companion Pro" in AirPlay list
echo 📱 6. Ensure Bluetooth is enabled
echo.

echo ========================================
echo  Next Steps
echo ========================================
echo.
echo If connections are working:
echo   npm start
echo.
echo If you have a macOS VM:
echo   1. Copy macos-vm-bridge.sh to your VM
echo   2. Run: chmod +x macos-vm-bridge.sh
echo   3. Run: ./macos-vm-bridge.sh
echo.
echo For troubleshooting:
echo   Check the generated log files
echo   Ensure firewall allows the app
echo   Restart iPhone and try again
echo.

pause
>>>>>>>
