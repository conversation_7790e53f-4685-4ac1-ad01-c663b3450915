#!/bin/bash
# macos-bridge-setup.sh - Setup macOS VM Bridge for iPhone Messages
# Run this script inside your macOS Virtual Machine

echo "🍎 iPhone Companion Pro - macOS VM Bridge Setup"
echo "================================================"
echo ""

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ This script must run on macOS (in VM)"
    echo "   Transfer this file to your macOS VM and run it there"
    exit 1
fi

echo "✅ Running on macOS - proceeding with setup..."
echo ""

# Check for Homebrew
if ! command -v brew &> /dev/null; then
    echo "📦 Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
else
    echo "✅ Homebrew already installed"
fi

# Install Node.js if not present
if ! command -v node &> /dev/null; then
    echo "📦 Installing Node.js..."
    brew install node
else
    echo "✅ Node.js already installed: $(node --version)"
fi

# Create bridge directory
BRIDGE_DIR="$HOME/iphone-bridge"
mkdir -p "$BRIDGE_DIR"
cd "$BRIDGE_DIR"

echo "📁 Created bridge directory: $BRIDGE_DIR"

# Initialize npm project
if [ ! -f "package.json" ]; then
    echo "📦 Initializing npm project..."
    npm init -y
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install ws sqlite3 chokidar

# Create the main bridge script
echo "📝 Creating bridge script..."
cat > bridge.js << 'EOF'
const WebSocket = require('ws');
const sqlite3 = require('sqlite3');
const fs = require('fs');
const path = require('path');
const os = require('os');
const chokidar = require('chokidar');

class MacOSBridge {
  constructor() {
    this.wss = null;
    this.clients = new Set();
    this.messagesDB = null;
    this.dbPath = path.join(os.homedir(), 'Library/Messages/chat.db');
    this.lastSync = 0;
  }

  start() {
    console.log('🚀 Starting macOS Bridge Server...');
    
    // Create WebSocket server
    this.wss = new WebSocket.Server({ 
      port: 8888,
      host: '0.0.0.0'
    });
    
    console.log('✅ WebSocket server running on port 8888');
    console.log(`🌐 Connect from Windows: ws://${this.getLocalIP()}:8888`);
    
    this.wss.on('connection', (ws, req) => {
      const clientIP = req.socket.remoteAddress;
      console.log(`🔗 Windows client connected from: ${clientIP}`);
      
      this.clients.add(ws);
      
      ws.on('message', async (message) => {
        try {
          const command = JSON.parse(message);
          await this.handleCommand(ws, command);
        } catch (error) {
          console.error('❌ Invalid command:', error.message);
        }
      });
      
      ws.on('close', () => {
        console.log('🔌 Windows client disconnected');
        this.clients.delete(ws);
      });
      
      // Send welcome message
      ws.send(JSON.stringify({
        type: 'WELCOME',
        message: 'macOS Bridge connected',
        timestamp: Date.now()
      }));
    });
    
    // Setup Messages database monitoring
    this.setupDatabaseMonitoring();
    
    // Initial sync
    setTimeout(() => this.syncAllData(), 2000);
  }

  getLocalIP() {
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
      for (const interface of interfaces[name]) {
        if (interface.family === 'IPv4' && !interface.internal) {
          return interface.address;
        }
      }
    }
    return 'localhost';
  }

  setupDatabaseMonitoring() {
    if (!fs.existsSync(this.dbPath)) {
      console.log('⚠️ Messages database not found at:', this.dbPath);
      console.log('   Make sure Messages app has been opened at least once');
      return;
    }
    
    console.log('👀 Monitoring Messages database for changes...');
    
    // Watch for database changes
    const watcher = chokidar.watch(this.dbPath, {
      persistent: true,
      usePolling: true,
      interval: 1000
    });
    
    watcher.on('change', () => {
      console.log('📨 Messages database changed - syncing...');
      this.syncNewMessages();
    });
  }

  async handleCommand(ws, command) {
    console.log('📨 Command received:', command.type);
    
    switch (command.type) {
      case 'SYNC_ALL':
        await this.syncAllData();
        break;
        
      case 'SYNC_MESSAGES':
        await this.syncMessages(command.limit || 100);
        break;
        
      case 'SEND_MESSAGE':
        await this.sendMessage(command.phone, command.text);
        break;
        
      case 'GET_CONTACTS':
        await this.syncContacts();
        break;
        
      default:
        ws.send(JSON.stringify({
          type: 'ERROR',
          message: `Unknown command: ${command.type}`
        }));
    }
  }

  async syncAllData() {
    console.log('🔄 Syncing all iPhone data...');
    
    await this.syncMessages(200);
    await this.syncContacts();
    
    this.broadcast({
      type: 'SYNC_COMPLETE',
      timestamp: Date.now()
    });
  }

  async syncMessages(limit = 100) {
    if (!fs.existsSync(this.dbPath)) {
      console.log('❌ Messages database not accessible');
      return;
    }
    
    try {
      const db = new sqlite3.Database(this.dbPath, sqlite3.OPEN_READONLY);
      
      const query = `
        SELECT 
          m.ROWID as id,
          m.text,
          m.date,
          m.is_from_me,
          m.is_read,
          h.id as phone_number,
          h.service,
          datetime(m.date/1000000000 + strftime('%s', '2001-01-01'), 'unixepoch') as readable_date
        FROM message m
        LEFT JOIN handle h ON m.handle_id = h.ROWID
        WHERE m.text IS NOT NULL
        ORDER BY m.date DESC 
        LIMIT ?
      `;
      
      db.all(query, [limit], (err, rows) => {
        if (err) {
          console.error('❌ Database query error:', err.message);
          return;
        }
        
        console.log(`📨 Found ${rows.length} messages`);
        
        this.broadcast({
          type: 'MESSAGES',
          data: rows,
          count: rows.length,
          timestamp: Date.now()
        });
        
        db.close();
      });
      
    } catch (error) {
      console.error('❌ Messages sync error:', error.message);
    }
  }

  async syncNewMessages() {
    // Sync only messages newer than last sync
    const currentTime = Date.now();
    
    if (currentTime - this.lastSync < 1000) {
      return; // Avoid too frequent syncs
    }
    
    this.lastSync = currentTime;
    await this.syncMessages(50);
  }

  async syncContacts() {
    // Note: Contacts access requires additional permissions
    console.log('📇 Contacts sync not implemented yet (requires permissions)');
    
    this.broadcast({
      type: 'CONTACTS',
      data: [],
      message: 'Contacts sync requires additional setup',
      timestamp: Date.now()
    });
  }

  async sendMessage(phoneNumber, text) {
    console.log(`📤 Sending message to ${phoneNumber}: ${text}`);
    
    // Use AppleScript to send message
    const script = `
      tell application "Messages"
        set targetService to 1st service whose service type = iMessage
        set targetBuddy to buddy "${phoneNumber}" of targetService
        send "${text}" to targetBuddy
      end tell
    `;
    
    const { exec } = require('child_process');
    exec(`osascript -e '${script}'`, (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Send message error:', error.message);
        this.broadcast({
          type: 'SEND_ERROR',
          error: error.message,
          timestamp: Date.now()
        });
      } else {
        console.log('✅ Message sent successfully');
        this.broadcast({
          type: 'SEND_SUCCESS',
          phone: phoneNumber,
          text: text,
          timestamp: Date.now()
        });
      }
    });
  }

  broadcast(data) {
    const message = JSON.stringify(data);
    this.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }
}

// Start the bridge
const bridge = new MacOSBridge();
bridge.start();

console.log('');
console.log('🎉 macOS Bridge is running!');
console.log('');
console.log('📱 iPhone Setup:');
console.log('   1. Make sure Messages app is signed in with Apple ID');
console.log('   2. Enable "Text Message Forwarding" in Messages settings');
console.log('   3. Allow this Mac to send/receive messages');
console.log('');
console.log('💻 Windows Setup:');
console.log('   1. Run: node test-all-connections.js');
console.log('   2. Look for "VM Bridge" connection success');
console.log('');
console.log('🔄 To stop: Press Ctrl+C');
EOF

# Create startup script
echo "📝 Creating startup script..."
cat > start-bridge.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
echo "🍎 Starting iPhone Bridge..."
node bridge.js
EOF

chmod +x start-bridge.sh

# Create auto-start plist for launchd
echo "📝 Creating auto-start configuration..."
cat > com.iphonecompanion.bridge.plist << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.iphonecompanion.bridge</string>
    <key>ProgramArguments</key>
    <array>
        <string>$BRIDGE_DIR/start-bridge.sh</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>WorkingDirectory</key>
    <string>$BRIDGE_DIR</string>
</dict>
</plist>
EOF

echo ""
echo "🎉 macOS Bridge setup complete!"
echo ""
echo "🚀 To start the bridge:"
echo "   ./start-bridge.sh"
echo ""
echo "🔄 To auto-start on boot:"
echo "   cp com.iphonecompanion.bridge.plist ~/Library/LaunchAgents/"
echo "   launchctl load ~/Library/LaunchAgents/com.iphonecompanion.bridge.plist"
echo ""
echo "📊 To test connection:"
echo "   Run 'node test-all-connections.js' on Windows"
echo ""
