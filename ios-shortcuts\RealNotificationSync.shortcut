# Real Notification Sync Shortcut
# This shortcut captures actual iPhone notifications and sends them to PC

# Shortcut Name: "Sync Real Notifications"
# Description: Forwards actual iPhone notifications to Windows PC
# Trigger: Automation when notification received

# Actions:
1. Get Current Notifications
   - Access notification center
   - Filter for relevant apps (Messages, Calls, etc.)
   - Extract notification details

2. Process Notification Data
   - App name and bundle ID
   - Notification title and body
   - Timestamp and priority
   - Action buttons available

3. Send to PC
   - URL: http://YOUR_PC_IP:8888/notification
   - Method: POST
   - Real-time delivery

# Notification Format:
{
  "type": "notification",
  "timestamp": "2024-01-01T12:00:00Z",
  "app": {
    "name": "Messages",
    "bundleId": "com.apple.MobileSMS",
    "icon": "base64_icon_data"
  },
  "notification": {
    "title": "New Message",
    "body": "Actual message preview",
    "category": "message",
    "threadId": "conversation_id",
    "actions": ["Reply", "Mark as Read"]
  },
  "sender": {
    "name": "Real Contact Name",
    "phoneNumber": "+1234567890"
  }
}

# Supported Apps:
- Messages (SMS/iMessage)
- Phone (Calls, Voicemail)
- Mail (Email notifications)
- Calendar (Event reminders)
- Third-party messaging apps

# Automation Setup:
1. Open Shortcuts app
2. Go to Automation tab
3. Create "When I receive a notification"
4. Select relevant apps
5. Add this shortcut as action
6. Enable "Run Immediately"

# Privacy & Permissions:
- Requires notification access permission
- User can filter which apps to sync
- Sensitive content can be masked
- Option to sync only notification counts
