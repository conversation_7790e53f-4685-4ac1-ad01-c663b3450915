#!/usr/bin/env node

/**
 * Intel Unison Messaging Test
 * Tests the core messaging functionality
 */

async function testIntelUnisonMessaging() {
  console.log('🧪 Testing Intel Unison Messaging System...\n');
  
  try {
    // Test database setup
    const { Database } = require('./src/main/unison-core/database/Database');
    const db = new Database();
    await db.initialize();
    console.log('✅ Database test passed');
    
    // Test message storage
    const testMessage = {
      id: 'test_msg_' + Date.now(),
      thread_id: 'test_thread_1',
      sender: 'self',
      recipients: JSON.stringify(['+1234567890']),
      body: 'Test message from Intel Unison++',
      timestamp: Date.now(),
      is_read: 0,
      is_delivered: 0,
      is_sent: 1,
      message_type: 'sms',
      metadata: JSON.stringify({ test: true })
    };
    
    await db.storeMessage(testMessage);
    console.log('✅ Message storage test passed');
    
    // Test message retrieval
    const retrievedMessage = await db.getMessage(testMessage.id);
    if (retrievedMessage && retrievedMessage.body === testMessage.body) {
      console.log('✅ Message retrieval test passed');
    } else {
      throw new Error('Message retrieval failed');
    }
    
    // Test conversation listing
    const conversations = await db.getConversations(10);
    console.log(`✅ Conversation listing test passed (${conversations.length} conversations)`);
    
    // Test message search
    const searchResults = await db.searchMessages('Test message');
    console.log(`✅ Message search test passed (${searchResults.length} results)`);
    
    // Test database stats
    const stats = await db.getStats();
    console.log('✅ Database stats test passed');
    console.log(`   📊 Total messages: ${stats.totalMessages}`);
    console.log(`   📊 Total conversations: ${stats.totalConversations}`);
    console.log(`   📊 Database size: ${stats.databaseSize} bytes`);
    
    await db.cleanup();
    
    console.log('\n🎉 ALL INTEL UNISON MESSAGING TESTS PASSED!');
    console.log('📱 The core messaging system is ready for iPhone connection');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Test API endpoint simulation
async function testAPIEndpoints() {
  console.log('\n🌐 Testing API endpoints...');
  
  const axios = require('axios');
  
  try {
    // Wait a bit for server to start
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test status endpoint
    const statusResponse = await axios.get('http://localhost:3000/api/status');
    console.log('✅ Status endpoint working');
    console.log(`   📊 Status: ${statusResponse.data.status}`);
    console.log(`   📊 Version: ${statusResponse.data.version}`);
    
    // Test devices endpoint
    const devicesResponse = await axios.get('http://localhost:3000/api/devices');
    console.log('✅ Devices endpoint working');
    console.log(`   📱 Devices found: ${devicesResponse.data.devices.length}`);
    
    console.log('✅ API tests passed');
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('⚠️ Server not running - skipping API tests');
    } else {
      console.error('❌ API test failed:', error.message);
    }
  }
}

// Run tests
if (require.main === module) {
  testIntelUnisonMessaging()
    .then(() => testAPIEndpoints())
    .then(() => process.exit(0))
    .catch(error => {
      console.error('❌ Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { testIntelUnisonMessaging, testAPIEndpoints };