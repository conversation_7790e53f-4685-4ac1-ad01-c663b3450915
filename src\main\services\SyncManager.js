const { EventEmitter } = require('events');
const fs = require('fs');
const path = require('path');
const Store = require('electron-store');

class SyncManager extends EventEmitter {
  constructor(airPlayServer = null, webBridge = null, messageService = null, callManager = null) {
    super();
    this.airPlayServer = airPlayServer;
    this.webBridge = webBridge;
    this.messageService = messageService;
    this.callManager = callManager;
    
    this.store = new Store({ name: 'sync-data' });
    this.syncInterval = null;
    this.lastSyncTime = null;
    this.syncQueue = [];
    this.isOnline = false;
    
    this.syncData = {
      contacts: new Map(),
      messages: new Map(),
      callLogs: [],
      notifications: [],
      photos: [],
      files: []
    };
    
    this.syncStatus = {
      contacts: { lastSync: null, count: 0, enabled: false },
      messages: { lastSync: null, count: 0, enabled: false },
      calls: { lastSync: null, count: 0, enabled: false },
      notifications: { lastSync: null, count: 0, enabled: false },
      photos: { lastSync: null, count: 0, enabled: false },
      files: { lastSync: null, count: 0, enabled: false }
    };
  }

  async initialize() {
    console.log('Initializing sync manager...');
    
    // Load existing sync data
    this.loadSyncData();
    
    // Set up integration event handlers
    this.setupIntegrationHandlers();
    
    // Start sync monitoring
    this.startSyncMonitoring();
    
    console.log('Sync manager initialized');
  }

  loadSyncData() {
    try {
      const savedData = this.store.get('syncData', {});
      const savedStatus = this.store.get('syncStatus', {});
      
      // Load contacts
      if (savedData.contacts) {
        this.syncData.contacts = new Map(Object.entries(savedData.contacts));
      }
      
      // Load other data
      this.syncData.messages = new Map(Object.entries(savedData.messages || {}));
      this.syncData.callLogs = savedData.callLogs || [];
      this.syncData.notifications = savedData.notifications || [];
      this.syncData.photos = savedData.photos || [];
      this.syncData.files = savedData.files || [];
      
      // Load sync status
      Object.assign(this.syncStatus, savedStatus);
      
      console.log('Sync data loaded:', {
        contacts: this.syncData.contacts.size,
        messages: this.syncData.messages.size,
        callLogs: this.syncData.callLogs.length,
        notifications: this.syncData.notifications.length
      });
      
    } catch (error) {
      console.error('Error loading sync data:', error);
    }
  }

  saveSyncData() {
    try {
      const dataToSave = {
        contacts: Object.fromEntries(this.syncData.contacts),
        messages: Object.fromEntries(this.syncData.messages),
        callLogs: this.syncData.callLogs,
        notifications: this.syncData.notifications,
        photos: this.syncData.photos,
        files: this.syncData.files
      };
      
      this.store.set('syncData', dataToSave);
      this.store.set('syncStatus', this.syncStatus);
      
    } catch (error) {
      console.error('Error saving sync data:', error);
    }
  }

  setupIntegrationHandlers() {
    // AirPlay integration
    if (this.airPlayServer) {
      this.airPlayServer.on('mirror-start', () => {
        this.isOnline = true;
        this.enableSync(['contacts', 'notifications']);
        this.requestFullSync();
      });

      this.airPlayServer.on('mirror-stop', () => {
        this.isOnline = false;
        this.disableSync(['contacts', 'notifications']);
      });
    }

    // Web bridge integration
    if (this.webBridge) {
      this.webBridge.on('device-connected', () => {
        this.isOnline = true;
        this.enableSync(['contacts', 'messages', 'calls', 'notifications', 'photos', 'files']);
        this.requestFullSync();
      });

      this.webBridge.on('device-disconnected', () => {
        this.isOnline = false;
        this.disableAllSync();
      });

      this.webBridge.on('sync-data', (data) => {
        this.handleIncomingSyncData(data);
      });
    }

    // Message service integration
    if (this.messageService) {
      this.messageService.on('new-message', (message) => {
        this.syncMessage(message);
      });

      this.messageService.on('conversations-updated', (conversations) => {
        this.syncConversations(conversations);
      });
    }

    // Call manager integration
    if (this.callManager) {
      this.callManager.on('call-ended', (call) => {
        this.syncCallLog(call);
      });
    }
  }

  startSyncMonitoring() {
    // Sync every 30 seconds when online
    this.syncInterval = setInterval(() => {
      if (this.isOnline) {
        this.performIncrementalSync();
      }
    }, 30000);

    // Process sync queue every 5 seconds
    setInterval(() => {
      this.processSyncQueue();
    }, 5000);
  }

  enableSync(types) {
    types.forEach(type => {
      if (this.syncStatus[type]) {
        this.syncStatus[type].enabled = true;
        console.log(`Enabled sync for ${type}`);
      }
    });
    
    this.emit('sync-status-changed', this.syncStatus);
  }

  disableSync(types) {
    types.forEach(type => {
      if (this.syncStatus[type]) {
        this.syncStatus[type].enabled = false;
        console.log(`Disabled sync for ${type}`);
      }
    });
    
    this.emit('sync-status-changed', this.syncStatus);
  }

  disableAllSync() {
    Object.keys(this.syncStatus).forEach(type => {
      this.syncStatus[type].enabled = false;
    });
    
    this.emit('sync-status-changed', this.syncStatus);
  }

  requestFullSync() {
    if (!this.isOnline) return;

    const syncRequest = {
      type: 'full_sync_request',
      timestamp: Date.now(),
      lastSyncTimes: Object.fromEntries(
        Object.entries(this.syncStatus).map(([key, status]) => [key, status.lastSync])
      )
    };

    this.sendSyncMessage(syncRequest);
  }

  performIncrementalSync() {
    if (!this.isOnline) return;

    const enabledTypes = Object.entries(this.syncStatus)
      .filter(([_, status]) => status.enabled)
      .map(([type, _]) => type);

    if (enabledTypes.length === 0) return;

    const syncRequest = {
      type: 'incremental_sync_request',
      timestamp: Date.now(),
      types: enabledTypes,
      lastSyncTimes: Object.fromEntries(
        enabledTypes.map(type => [type, this.syncStatus[type].lastSync])
      )
    };

    this.sendSyncMessage(syncRequest);
  }

  handleIncomingSyncData(data) {
    try {
      switch (data.type) {
        case 'contacts_sync':
          this.handleContactsSync(data.contacts);
          break;
          
        case 'messages_sync':
          this.handleMessagesSync(data.messages);
          break;
          
        case 'calls_sync':
          this.handleCallsSync(data.calls);
          break;
          
        case 'notifications_sync':
          this.handleNotificationsSync(data.notifications);
          break;
          
        case 'photos_sync':
          this.handlePhotosSync(data.photos);
          break;
          
        case 'files_sync':
          this.handleFilesSync(data.files);
          break;
          
        default:
          console.log('Unknown sync data type:', data.type);
      }
    } catch (error) {
      console.error('Error handling incoming sync data:', error);
    }
  }

  handleContactsSync(contacts) {
    let newCount = 0;
    let updatedCount = 0;

    contacts.forEach(contact => {
      const existing = this.syncData.contacts.get(contact.id);
      
      if (!existing) {
        newCount++;
      } else if (existing.lastModified < contact.lastModified) {
        updatedCount++;
      }
      
      this.syncData.contacts.set(contact.id, contact);
    });

    this.syncStatus.contacts.lastSync = Date.now();
    this.syncStatus.contacts.count = this.syncData.contacts.size;

    console.log(`Contacts sync: ${newCount} new, ${updatedCount} updated`);
    this.emit('contacts-synced', { new: newCount, updated: updatedCount, total: this.syncData.contacts.size });
    
    this.saveSyncData();
  }

  handleMessagesSync(messages) {
    let newCount = 0;

    messages.forEach(message => {
      if (!this.syncData.messages.has(message.id)) {
        this.syncData.messages.set(message.id, message);
        newCount++;
        
        // Forward to message service
        if (this.messageService) {
          this.messageService.addMessage(message);
        }
      }
    });

    this.syncStatus.messages.lastSync = Date.now();
    this.syncStatus.messages.count = this.syncData.messages.size;

    console.log(`Messages sync: ${newCount} new messages`);
    this.emit('messages-synced', { new: newCount, total: this.syncData.messages.size });
    
    this.saveSyncData();
  }

  handleCallsSync(calls) {
    let newCount = 0;

    calls.forEach(call => {
      const exists = this.syncData.callLogs.some(existing => existing.id === call.id);
      
      if (!exists) {
        this.syncData.callLogs.unshift(call);
        newCount++;
      }
    });

    // Keep only last 1000 calls
    if (this.syncData.callLogs.length > 1000) {
      this.syncData.callLogs = this.syncData.callLogs.slice(0, 1000);
    }

    this.syncStatus.calls.lastSync = Date.now();
    this.syncStatus.calls.count = this.syncData.callLogs.length;

    console.log(`Calls sync: ${newCount} new calls`);
    this.emit('calls-synced', { new: newCount, total: this.syncData.callLogs.length });
    
    this.saveSyncData();
  }

  handleNotificationsSync(notifications) {
    let newCount = 0;

    notifications.forEach(notification => {
      const exists = this.syncData.notifications.some(existing => existing.id === notification.id);
      
      if (!exists) {
        this.syncData.notifications.unshift(notification);
        newCount++;
        
        // Emit notification event for UI
        this.emit('new-notification', notification);
      }
    });

    // Keep only last 100 notifications
    if (this.syncData.notifications.length > 100) {
      this.syncData.notifications = this.syncData.notifications.slice(0, 100);
    }

    this.syncStatus.notifications.lastSync = Date.now();
    this.syncStatus.notifications.count = this.syncData.notifications.length;

    console.log(`Notifications sync: ${newCount} new notifications`);
    this.emit('notifications-synced', { new: newCount, total: this.syncData.notifications.length });
    
    this.saveSyncData();
  }

  handlePhotosSync(photos) {
    // Handle photo metadata sync
    let newCount = 0;

    photos.forEach(photo => {
      const exists = this.syncData.photos.some(existing => existing.id === photo.id);
      
      if (!exists) {
        this.syncData.photos.unshift(photo);
        newCount++;
      }
    });

    this.syncStatus.photos.lastSync = Date.now();
    this.syncStatus.photos.count = this.syncData.photos.length;

    console.log(`Photos sync: ${newCount} new photos`);
    this.emit('photos-synced', { new: newCount, total: this.syncData.photos.length });
    
    this.saveSyncData();
  }

  handleFilesSync(files) {
    // Handle file metadata sync
    let newCount = 0;

    files.forEach(file => {
      const exists = this.syncData.files.some(existing => existing.id === file.id);
      
      if (!exists) {
        this.syncData.files.unshift(file);
        newCount++;
      }
    });

    this.syncStatus.files.lastSync = Date.now();
    this.syncStatus.files.count = this.syncData.files.length;

    console.log(`Files sync: ${newCount} new files`);
    this.emit('files-synced', { new: newCount, total: this.syncData.files.length });
    
    this.saveSyncData();
  }

  // Outgoing sync methods
  syncMessage(message) {
    this.addToSyncQueue({
      type: 'message_sync',
      data: message,
      timestamp: Date.now()
    });
  }

  syncConversations(conversations) {
    this.addToSyncQueue({
      type: 'conversations_sync',
      data: conversations,
      timestamp: Date.now()
    });
  }

  syncCallLog(call) {
    this.addToSyncQueue({
      type: 'call_sync',
      data: call,
      timestamp: Date.now()
    });
  }

  addToSyncQueue(item) {
    this.syncQueue.push(item);
    
    // Limit queue size
    if (this.syncQueue.length > 1000) {
      this.syncQueue = this.syncQueue.slice(-1000);
    }
  }

  processSyncQueue() {
    if (!this.isOnline || this.syncQueue.length === 0) return;

    const batch = this.syncQueue.splice(0, 10); // Process 10 items at a time
    
    batch.forEach(item => {
      this.sendSyncMessage(item);
    });
  }

  sendSyncMessage(message) {
    if (this.webBridge && this.webBridge.connectedClients.size > 0) {
      this.webBridge.broadcast(message);
    }
  }

  // Public API methods
  getContacts() {
    return Array.from(this.syncData.contacts.values());
  }

  getMessages() {
    return Array.from(this.syncData.messages.values());
  }

  getCallLogs() {
    return this.syncData.callLogs;
  }

  getNotifications() {
    return this.syncData.notifications;
  }

  getPhotos() {
    return this.syncData.photos;
  }

  getFiles() {
    return this.syncData.files;
  }

  getSyncStatus() {
    return { ...this.syncStatus, isOnline: this.isOnline };
  }

  forceSyncType(type) {
    if (this.syncStatus[type] && this.syncStatus[type].enabled) {
      const syncRequest = {
        type: `${type}_sync_request`,
        timestamp: Date.now(),
        force: true
      };
      
      this.sendSyncMessage(syncRequest);
    }
  }

  stop() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    
    this.saveSyncData();
    console.log('Sync manager stopped');
  }
}

module.exports = { SyncManager };
