const express = require('express');
const cors = require('cors');

class CRMBridge {
  constructor(messageService, phoneLinkBridge) {
    this.app = express();
    this.app.use(cors());
    this.app.use(express.json());
    
    this.messageService = messageService;
    this.phoneLinkBridge = phoneLinkBridge;
    this.server = null;
    
    this.setupEndpoints();
  }

  setupEndpoints() {
    // Get all messages for a contact (for your CRM)
    this.app.get('/api/crm/messages/:phone', async (req, res) => {
      try {
        const messages = await this.phoneLinkBridge.getMessagesForPhone(req.params.phone);
        res.json({
          success: true,
          phone: req.params.phone,
          messages: messages,
          count: messages.length
        });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Send message (uses YOUR phone number)
    this.app.post('/api/crm/send', async (req, res) => {
      try {
        const { phone, text, campaignId } = req.body;
        
        if (!phone || !text) {
          return res.status(400).json({ 
            success: false, 
            error: 'Phone number and text are required' 
          });
        }
        
        // Send via your iPhone through MessageService
        await this.messageService.sendMessage(phone, text);
        
        // Also save to local Unison-style database
        await this.phoneLinkBridge.sendMessage(phone, text);
        
        // Log for CRM
        console.log(`📊 CRM Campaign ${campaignId || 'manual'}: Sent to ${phone}`);
        
        res.json({ 
          success: true, 
          phone, 
          campaignId: campaignId || 'manual',
          timestamp: Date.now()
        });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Get all contacts
    this.app.get('/api/crm/contacts', async (req, res) => {
      try {
        // Get from both Phone Link and local database
        const phoneLinkContacts = await this.phoneLinkBridge.getContacts();
        const localContacts = await this.phoneLinkBridge.getLocalContacts();
        
        // Merge and deduplicate
        const allContacts = [...phoneLinkContacts];
        localContacts.forEach(local => {
          if (!allContacts.find(c => c.phoneNumber === local.phone_number)) {
            allContacts.push({
              id: local.phone_number,
              name: local.display_name,
              phoneNumber: local.phone_number
            });
          }
        });
        
        res.json({
          success: true,
          contacts: allContacts,
          count: allContacts.length
        });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Get all messages (for CRM dashboard)
    this.app.get('/api/crm/messages', async (req, res) => {
      try {
        const messages = await this.phoneLinkBridge.getMessagesForCRM();
        res.json({
          success: true,
          messages: messages,
          count: messages.length
        });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Get conversation threads
    this.app.get('/api/crm/conversations', async (req, res) => {
      try {
        const conversations = this.messageService.getConversations();
        res.json({
          success: true,
          conversations: conversations,
          count: conversations.length
        });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Webhook for CRM to register
    this.app.post('/api/crm/webhook', (req, res) => {
      console.log('📊 CRM Webhook registered:', req.body.url);
      res.json({ 
        registered: true, 
        url: req.body.url,
        timestamp: Date.now()
      });
    });

    // Health check endpoint
    this.app.get('/api/crm/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: Date.now(),
        integrations: this.messageService.getIntegrationStatus()
      });
    });

    // Get integration status
    this.app.get('/api/crm/status', (req, res) => {
      res.json({
        success: true,
        status: this.messageService.getIntegrationStatus(),
        timestamp: Date.now()
      });
    });
  }

  start(port = 7777) {
    return new Promise((resolve, reject) => {
      const tryPort = (currentPort) => {
        this.server = this.app.listen(currentPort, (err) => {
          if (err) {
            if (err.code === 'EADDRINUSE' && currentPort < port + 10) {
              console.log(`⚠️ Port ${currentPort} in use, trying ${currentPort + 1}...`);
              tryPort(currentPort + 1);
            } else {
              reject(err);
            }
          } else {
            console.log('🚀 CRM API running on http://localhost:' + currentPort);
            console.log('📊 Endpoints:');
            console.log('   GET  /api/crm/messages/:phone - Get messages for specific contact');
            console.log('   POST /api/crm/send - Send message via iPhone');
            console.log('   GET  /api/crm/contacts - Get all contacts');
            console.log('   GET  /api/crm/messages - Get all messages');
            console.log('   GET  /api/crm/conversations - Get conversation threads');
            console.log('   GET  /api/crm/health - Health check');
            console.log('   GET  /api/crm/status - Integration status');
            console.log('   POST /api/crm/webhook - Register webhook');
            resolve();
          }
        });

        this.server.on('error', (error) => {
          if (error.code === 'EADDRINUSE' && currentPort < port + 10) {
            console.log(`⚠️ Port ${currentPort} in use, trying ${currentPort + 1}...`);
            tryPort(currentPort + 1);
          } else {
            reject(error);
          }
        });
      };

      tryPort(port);
    });
  }

  stop() {
    if (this.server) {
      this.server.close();
      console.log('🛑 CRM API server stopped');
    }
  }
}

module.exports = CRMBridge;
