const { TestRunner, TestUtils, PerformanceTest } = require('./test-runner');
const path = require('path');

// Mock the services for testing
const { AirPlayServer } = require('../src/main/services/AirPlayServer');
const { MessageService } = require('../src/main/services/MessageService');
const { CallManager } = require('../src/main/services/CallManager');
const { SyncManager } = require('../src/main/services/SyncManager');
const { PerformanceMonitor } = require('../src/main/services/PerformanceMonitor');

async function runIntegrationTests() {
  const runner = new TestRunner();

  // AirPlay Server Tests
  runner.addTest('AirPlay Server Initialization', async () => {
    const airPlayServer = new AirPlayServer();
    TestUtils.assert(airPlayServer !== null, 'AirPlay server should be created');
    TestUtils.assert(typeof airPlayServer.start === 'function', 'Should have start method');
    TestUtils.assert(typeof airPlayServer.stop === 'function', 'Should have stop method');
  }, { category: 'AirPlay' });

  runner.addTest('AirPlay Server Start/Stop', async () => {
    const airPlayServer = new AirPlayServer();
    
    // Test starting the server
    await airPlayServer.start();
    TestUtils.assert(airPlayServer.isRunning, 'Server should be running after start');
    
    // Test stopping the server
    await airPlayServer.stop();
    TestUtils.assert(!airPlayServer.isRunning, 'Server should not be running after stop');
  }, { category: 'AirPlay' });

  runner.addTest('AirPlay Video Processing', async () => {
    const airPlayServer = new AirPlayServer();
    
    // Test H.264 data detection
    const h264Data = Buffer.from([0x00, 0x00, 0x00, 0x01, 0x67]); // H.264 NAL unit
    TestUtils.assert(airPlayServer.isH264Data(h264Data), 'Should detect H.264 data');
    
    // Test JPEG data detection
    const jpegData = Buffer.from([0xFF, 0xD8, 0xFF, 0xE0]); // JPEG header
    TestUtils.assert(!airPlayServer.isH264Data(jpegData), 'Should not detect JPEG as H.264');
  }, { category: 'AirPlay' });

  // Message Service Tests
  runner.addTest('Message Service Initialization', async () => {
    const messageService = new MessageService();
    TestUtils.assert(messageService !== null, 'Message service should be created');
    TestUtils.assert(Array.isArray(messageService.conversations), 'Should have conversations array');
  }, { category: 'Messages' });

  runner.addTest('Message Service Add Message', async () => {
    const messageService = new MessageService();
    await messageService.initialize();
    
    const testMessage = {
      id: 'test-123',
      phoneNumber: '+1234567890',
      text: 'Test message',
      timestamp: Date.now(),
      isIncoming: true
    };
    
    messageService.addMessage(testMessage);
    
    const conversations = messageService.getConversations();
    TestUtils.assert(conversations.length > 0, 'Should have at least one conversation');
    
    const conversation = conversations.find(c => c.phoneNumber === testMessage.phoneNumber);
    TestUtils.assert(conversation !== undefined, 'Should find conversation for phone number');
    TestUtils.assert(conversation.messages.length > 0, 'Conversation should have messages');
  }, { category: 'Messages' });

  // Call Manager Tests
  runner.addTest('Call Manager Initialization', async () => {
    const callManager = new CallManager();
    TestUtils.assert(callManager !== null, 'Call manager should be created');
    TestUtils.assert(Array.isArray(callManager.callHistory), 'Should have call history array');
  }, { category: 'Calls' });

  runner.addTest('Call Manager Make Call', async () => {
    const callManager = new CallManager();
    await callManager.initialize();
    
    const result = await callManager.makeCall('+1234567890', 'Test Contact');
    TestUtils.assert(result.success === true, 'Make call should succeed');
    TestUtils.assert(typeof result.callId === 'string', 'Should return call ID');
    TestUtils.assert(callManager.isInCall(), 'Should be in call after making call');
  }, { category: 'Calls' });

  // Sync Manager Tests
  runner.addTest('Sync Manager Initialization', async () => {
    const syncManager = new SyncManager();
    TestUtils.assert(syncManager !== null, 'Sync manager should be created');
    TestUtils.assert(typeof syncManager.syncData === 'object', 'Should have sync data object');
  }, { category: 'Sync' });

  runner.addTest('Sync Manager Contact Sync', async () => {
    const syncManager = new SyncManager();
    await syncManager.initialize();
    
    const testContacts = [
      { id: '1', name: 'John Doe', phoneNumber: '+1234567890', lastModified: Date.now() },
      { id: '2', name: 'Jane Smith', phoneNumber: '+0987654321', lastModified: Date.now() }
    ];
    
    syncManager.handleContactsSync(testContacts);
    
    const contacts = syncManager.getContacts();
    TestUtils.assert(contacts.length === 2, 'Should have 2 contacts after sync');
    TestUtils.assert(contacts[0].name === 'John Doe', 'Should have correct contact name');
  }, { category: 'Sync' });

  // Performance Monitor Tests
  runner.addTest('Performance Monitor Initialization', async () => {
    const perfMonitor = new PerformanceMonitor();
    TestUtils.assert(perfMonitor !== null, 'Performance monitor should be created');
    TestUtils.assert(typeof perfMonitor.metrics === 'object', 'Should have metrics object');
  }, { category: 'Performance' });

  runner.addTest('Performance Monitor Metrics Collection', async () => {
    const perfMonitor = new PerformanceMonitor();
    perfMonitor.start();
    
    // Wait a bit for metrics to be collected
    await TestUtils.delay(1000);
    
    const metrics = perfMonitor.getMetrics();
    TestUtils.assert(typeof metrics.cpu.usage === 'number', 'Should have CPU usage metric');
    TestUtils.assert(typeof metrics.memory.percentage === 'number', 'Should have memory percentage');
    TestUtils.assert(metrics.uptime > 0, 'Should have positive uptime');
    
    perfMonitor.stop();
  }, { category: 'Performance' });

  // Integration Tests
  runner.addTest('AirPlay and Message Service Integration', async () => {
    const airPlayServer = new AirPlayServer();
    const messageService = new MessageService(null, null, airPlayServer);
    
    await airPlayServer.start();
    await messageService.initialize();
    
    // Test that message service can receive AirPlay events
    let eventReceived = false;
    messageService.on('airplay-connected', () => {
      eventReceived = true;
    });
    
    airPlayServer.emit('mirror-start', 'test-session');
    
    await TestUtils.delay(100);
    TestUtils.assert(eventReceived, 'Message service should receive AirPlay events');
    
    await airPlayServer.stop();
  }, { category: 'Integration' });

  runner.addTest('Sync Manager and Call Manager Integration', async () => {
    const callManager = new CallManager();
    const syncManager = new SyncManager(null, null, null, callManager);
    
    await callManager.initialize();
    await syncManager.initialize();
    
    // Test that sync manager receives call events
    let callSynced = false;
    syncManager.on('calls-synced', () => {
      callSynced = true;
    });
    
    // Simulate a call ending
    const testCall = {
      id: 'test-call-123',
      phoneNumber: '+1234567890',
      duration: 120,
      endTime: new Date()
    };
    
    callManager.emit('call-ended', testCall);
    
    await TestUtils.delay(100);
    // Note: In a real test, we would check if the call was added to sync queue
    TestUtils.assert(true, 'Integration test completed');
  }, { category: 'Integration' });

  // Performance Tests
  runner.addTest('Message Processing Performance', async () => {
    const messageService = new MessageService();
    await messageService.initialize();
    
    const results = await PerformanceTest.stressTest(async () => {
      const message = {
        id: `test-${Date.now()}-${Math.random()}`,
        phoneNumber: '+1234567890',
        text: 'Performance test message',
        timestamp: Date.now(),
        isIncoming: true
      };
      messageService.addMessage(message);
    }, 100, 10);
    
    TestUtils.assert(results.averageTime < 10, 'Message processing should be fast (< 10ms average)');
    TestUtils.assert(results.maxTime < 50, 'No message should take more than 50ms to process');
  }, { category: 'Performance' });

  runner.addTest('AirPlay Frame Processing Performance', async () => {
    const airPlayServer = new AirPlayServer();
    
    const results = await PerformanceTest.stressTest(async () => {
      const fakeFrameData = Buffer.alloc(1024 * 100); // 100KB fake frame
      await airPlayServer.handleScreenData(fakeFrameData, 'test-session');
    }, 50, 5);
    
    TestUtils.assert(results.averageTime < 100, 'Frame processing should be fast (< 100ms average)');
  }, { category: 'Performance' });

  runner.addTest('Memory Usage Test', async () => {
    const messageService = new MessageService();
    await messageService.initialize();
    
    const memoryUsage = await PerformanceTest.measureMemoryUsage(async () => {
      // Add many messages to test memory usage
      for (let i = 0; i < 1000; i++) {
        const message = {
          id: `test-${i}`,
          phoneNumber: '+1234567890',
          text: `Test message ${i}`,
          timestamp: Date.now(),
          isIncoming: i % 2 === 0
        };
        messageService.addMessage(message);
      }
    });
    
    // Memory usage should be reasonable (less than 50MB for 1000 messages)
    TestUtils.assert(memoryUsage.heapUsedDelta < 50 * 1024 * 1024, 
      'Memory usage should be reasonable for 1000 messages');
  }, { category: 'Performance' });

  // Error Handling Tests
  runner.addTest('AirPlay Server Error Handling', async () => {
    const airPlayServer = new AirPlayServer();
    
    // Test handling invalid data
    const invalidData = Buffer.from([0x00, 0x01, 0x02]); // Invalid data
    
    // Should not throw error
    await airPlayServer.handleScreenData(invalidData, 'test-session');
    TestUtils.assert(true, 'Should handle invalid data gracefully');
  }, { category: 'Error Handling' });

  runner.addTest('Message Service Error Handling', async () => {
    const messageService = new MessageService();
    await messageService.initialize();
    
    // Test adding invalid message
    const invalidMessage = { /* missing required fields */ };
    
    // Should not throw error
    messageService.addMessage(invalidMessage);
    TestUtils.assert(true, 'Should handle invalid message gracefully');
  }, { category: 'Error Handling' });

  // Run all tests
  const results = await runner.runAllTests();
  const report = runner.generateReport();
  
  return { results, report };
}

// Export for use in other test files
module.exports = {
  runIntegrationTests
};

// Run tests if this file is executed directly
if (require.main === module) {
  runIntegrationTests()
    .then(({ results, report }) => {
      console.log('\n✅ Integration tests completed');
      process.exit(results.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    });
}
