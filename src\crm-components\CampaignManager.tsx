import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  Send, 
  Users, 
  MessageSquare, 
  Calendar, 
  Target, 
  BarChart3,
  Play,
  Pause,
  Settings,
  Plus,
  Edit3,
  Trash2,
  Clock,
  CheckCircle,
  AlertCircle,
  Filter,
  Download,
  Upload
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';

interface Campaign {
  id: string;
  name: string;
  status: 'draft' | 'active' | 'paused' | 'completed';
  type: 'immediate' | 'scheduled' | 'drip' | 'trigger-based';
  message: string;
  recipients: {
    total: number;
    sent: number;
    delivered: number;
    failed: number;
    opened: number;
    replied: number;
  };
  schedule: {
    startDate: Date;
    endDate?: Date;
    timeZone: string;
    sendTimes: string[];
  };
  createdAt: Date;
  lastActivity: Date;
  metrics: {
    deliveryRate: number;
    openRate: number;
    responseRate: number;
    conversionRate: number;
  };
  segments: string[];
  template: string;
}

interface Template {
  id: string;
  name: string;
  content: string;
  variables: string[];
  category: 'sales' | 'marketing' | 'support' | 'follow-up';
  isActive: boolean;
  usageCount: number;
  createdAt: Date;
}

interface Segment {
  id: string;
  name: string;
  criteria: {
    leadScore?: { min: number; max: number };
    contactType?: 'client' | 'lead';
    lastActivity?: { days: number };
    tags?: string[];
    location?: string;
  };
  contactCount: number;
  isActive: boolean;
}

interface CampaignManagerProps {
  height?: string;
}

export const CampaignManager: React.FC<CampaignManagerProps> = ({ height = '800px' }) => {
  const { user } = useUser();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [segments, setSegments] = useState<Segment[]>([]);
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [activeTab, setActiveTab] = useState('campaigns');
  const [isLoading, setIsLoading] = useState(false);
  
  // Campaign creation state
  const [newCampaign, setNewCampaign] = useState({
    name: '',
    type: 'immediate' as Campaign['type'],
    message: '',
    template: '',
    segments: [] as string[],
    schedule: {
      startDate: new Date(),
      timeZone: 'UTC',
      sendTimes: ['09:00', '14:00']
    }
  });

  // Template creation state
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    content: '',
    category: 'sales' as Template['category'],
    variables: [] as string[]
  });

  useEffect(() => {
    loadCampaignData();
  }, [user]);

  const loadCampaignData = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const [campaignsRes, templatesRes, segmentsRes] = await Promise.all([
        fetch('/api/campaigns', {
          headers: { 'Authorization': `Bearer ${user.id}` }
        }),
        fetch('/api/templates', {
          headers: { 'Authorization': `Bearer ${user.id}` }
        }),
        fetch('/api/segments', {
          headers: { 'Authorization': `Bearer ${user.id}` }
        })
      ]);

      const campaignData = await campaignsRes.json();
      const templateData = await templatesRes.json();
      const segmentData = await segmentsRes.json();

      if (campaignData.success) setCampaigns(campaignData.campaigns);
      if (templateData.success) setTemplates(templateData.templates);
      if (segmentData.success) setSegments(segmentData.segments);
    } catch (error) {
      console.error('Error loading campaign data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const createCampaign = async () => {
    if (!user) return;

    try {
      const response = await fetch('/api/campaigns', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.id}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newCampaign)
      });

      const data = await response.json();
      
      if (data.success) {
        setCampaigns(prev => [...prev, data.campaign]);
        setIsCreating(false);
        setNewCampaign({
          name: '',
          type: 'immediate',
          message: '',
          template: '',
          segments: [],
          schedule: {
            startDate: new Date(),
            timeZone: 'UTC',
            sendTimes: ['09:00', '14:00']
          }
        });
      }
    } catch (error) {
      console.error('Error creating campaign:', error);
    }
  };

  const createTemplate = async () => {
    if (!user) return;

    try {
      const response = await fetch('/api/templates', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.id}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newTemplate)
      });

      const data = await response.json();
      
      if (data.success) {
        setTemplates(prev => [...prev, data.template]);
        setNewTemplate({
          name: '',
          content: '',
          category: 'sales',
          variables: []
        });
      }
    } catch (error) {
      console.error('Error creating template:', error);
    }
  };

  const toggleCampaignStatus = async (campaignId: string, action: 'play' | 'pause' | 'stop') => {
    if (!user) return;

    try {
      const response = await fetch(`/api/campaigns/${campaignId}/${action}`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${user.id}` }
      });

      const data = await response.json();
      
      if (data.success) {
        setCampaigns(prev => prev.map(c => 
          c.id === campaignId ? { ...c, status: data.status } : c
        ));
      }
    } catch (error) {
      console.error(`Error ${action}ing campaign:`, error);
    }
  };

  const getStatusColor = (status: Campaign['status']) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: Campaign['type']) => {
    switch (type) {
      case 'immediate': return 'bg-red-100 text-red-800';
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'drip': return 'bg-purple-100 text-purple-800';
      case 'trigger-based': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderCampaignList = () => (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Campaigns</CardTitle>
          <Button onClick={() => setIsCreating(true)} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            New Campaign
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-96">
          <div className="space-y-3">
            {campaigns.map((campaign) => (
              <div
                key={campaign.id}
                className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                  selectedCampaign?.id === campaign.id
                    ? 'bg-blue-50 border-blue-200'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => setSelectedCampaign(campaign)}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <h4 className="font-medium">{campaign.name}</h4>
                    <Badge className={getStatusColor(campaign.status)}>
                      {campaign.status}
                    </Badge>
                    <Badge variant="outline" className={getTypeColor(campaign.type)}>
                      {campaign.type}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {campaign.status === 'active' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleCampaignStatus(campaign.id, 'pause');
                        }}
                      >
                        <Pause className="h-4 w-4" />
                      </Button>
                    )}
                    {campaign.status === 'paused' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleCampaignStatus(campaign.id, 'play');
                        }}
                      >
                        <Play className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
                
                <div className="grid grid-cols-4 gap-4 text-sm text-gray-600 mb-2">
                  <div>
                    <span className="font-medium">Recipients:</span> {campaign.recipients.total}
                  </div>
                  <div>
                    <span className="font-medium">Sent:</span> {campaign.recipients.sent}
                  </div>
                  <div>
                    <span className="font-medium">Delivered:</span> {campaign.recipients.delivered}
                  </div>
                  <div>
                    <span className="font-medium">Response Rate:</span> {campaign.metrics.responseRate}%
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Progress 
                      value={(campaign.recipients.sent / campaign.recipients.total) * 100} 
                      className="w-32"
                    />
                    <span className="text-sm text-gray-500">
                      {Math.round((campaign.recipients.sent / campaign.recipients.total) * 100)}% sent
                    </span>
                  </div>
                  
                  <span className="text-sm text-gray-500">
                    {formatDistanceToNow(campaign.lastActivity, { addSuffix: true })}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );

  const renderCampaignDetails = () => {
    if (!selectedCampaign) return null;

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Campaign Details</CardTitle>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Edit3 className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="recipients">Recipients</TabsTrigger>
              <TabsTrigger value="schedule">Schedule</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Campaign Name</Label>
                  <p className="text-lg font-medium">{selectedCampaign.name}</p>
                </div>
                <div>
                  <Label>Type</Label>
                  <Badge className={getTypeColor(selectedCampaign.type)}>
                    {selectedCampaign.type}
                  </Badge>
                </div>
              </div>
              
              <div>
                <Label>Message Content</Label>
                <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm">{selectedCampaign.message}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">{selectedCampaign.recipients.total}</p>
                  <p className="text-sm text-gray-500">Total Recipients</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{selectedCampaign.recipients.sent}</p>
                  <p className="text-sm text-gray-500">Sent</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-yellow-600">{selectedCampaign.recipients.delivered}</p>
                  <p className="text-sm text-gray-500">Delivered</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">{selectedCampaign.recipients.replied}</p>
                  <p className="text-sm text-gray-500">Replied</p>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="performance" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Delivery Rate</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <Progress value={selectedCampaign.metrics.deliveryRate} className="flex-1" />
                    <span className="text-sm font-medium">{selectedCampaign.metrics.deliveryRate}%</span>
                  </div>
                </div>
                
                <div>
                  <Label>Open Rate</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <Progress value={selectedCampaign.metrics.openRate} className="flex-1" />
                    <span className="text-sm font-medium">{selectedCampaign.metrics.openRate}%</span>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Response Rate</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <Progress value={selectedCampaign.metrics.responseRate} className="flex-1" />
                    <span className="text-sm font-medium">{selectedCampaign.metrics.responseRate}%</span>
                  </div>
                </div>
                
                <div>
                  <Label>Conversion Rate</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <Progress value={selectedCampaign.metrics.conversionRate} className="flex-1" />
                    <span className="text-sm font-medium">{selectedCampaign.metrics.conversionRate}%</span>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="recipients" className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>Target Segments</Label>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </div>
              
              <div className="space-y-2">
                {selectedCampaign.segments.map((segmentId) => {
                  const segment = segments.find(s => s.id === segmentId);
                  return segment ? (
                    <div key={segmentId} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span>{segment.name}</span>
                      <span className="text-sm text-gray-500">{segment.contactCount} contacts</span>
                    </div>
                  ) : null;
                })}
              </div>
            </TabsContent>
            
            <TabsContent value="schedule" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Start Date</Label>
                  <p className="text-sm">{format(selectedCampaign.schedule.startDate, 'PPP')}</p>
                </div>
                <div>
                  <Label>Time Zone</Label>
                  <p className="text-sm">{selectedCampaign.schedule.timeZone}</p>
                </div>
              </div>
              
              <div>
                <Label>Send Times</Label>
                <div className="flex space-x-2 mt-2">
                  {selectedCampaign.schedule.sendTimes.map((time, index) => (
                    <Badge key={index} variant="outline">{time}</Badge>
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    );
  };

  const renderCampaignCreator = () => (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Create New Campaign</CardTitle>
          <Button variant="outline" onClick={() => setIsCreating(false)}>
            Cancel
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <Label>Campaign Name</Label>
            <Input
              value={newCampaign.name}
              onChange={(e) => setNewCampaign(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter campaign name"
            />
          </div>
          
          <div>
            <Label>Campaign Type</Label>
            <select
              value={newCampaign.type}
              onChange={(e) => setNewCampaign(prev => ({ ...prev, type: e.target.value as Campaign['type'] }))}
              className="w-full p-2 border rounded"
            >
              <option value="immediate">Send Immediately</option>
              <option value="scheduled">Schedule for Later</option>
              <option value="drip">Drip Campaign</option>
              <option value="trigger-based">Trigger-based</option>
            </select>
          </div>
          
          <div>
            <Label>Message Template</Label>
            <select
              value={newCampaign.template}
              onChange={(e) => setNewCampaign(prev => ({ ...prev, template: e.target.value }))}
              className="w-full p-2 border rounded"
            >
              <option value="">Select a template</option>
              {templates.map(template => (
                <option key={template.id} value={template.id}>{template.name}</option>
              ))}
            </select>
          </div>
          
          <div>
            <Label>Message Content</Label>
            <Textarea
              value={newCampaign.message}
              onChange={(e) => setNewCampaign(prev => ({ ...prev, message: e.target.value }))}
              placeholder="Enter your message or select a template"
              rows={4}
            />
          </div>
          
          <div>
            <Label>Target Segments</Label>
            <div className="space-y-2 mt-2">
              {segments.map(segment => (
                <div key={segment.id} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={newCampaign.segments.includes(segment.id)}
                    onChange={(e) => {
                      setNewCampaign(prev => ({
                        ...prev,
                        segments: e.target.checked 
                          ? [...prev.segments, segment.id]
                          : prev.segments.filter(s => s !== segment.id)
                      }));
                    }}
                  />
                  <span>{segment.name}</span>
                  <span className="text-sm text-gray-500">({segment.contactCount} contacts)</span>
                </div>
              ))}
            </div>
          </div>
          
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsCreating(false)}>
              Cancel
            </Button>
            <Button onClick={createCampaign}>
              Create Campaign
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderTemplateManager = () => (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Message Templates</CardTitle>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            New Template
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <Label>Template Name</Label>
            <Input
              value={newTemplate.name}
              onChange={(e) => setNewTemplate(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter template name"
            />
          </div>
          
          <div>
            <Label>Category</Label>
            <select
              value={newTemplate.category}
              onChange={(e) => setNewTemplate(prev => ({ ...prev, category: e.target.value as Template['category'] }))}
              className="w-full p-2 border rounded"
            >
              <option value="sales">Sales</option>
              <option value="marketing">Marketing</option>
              <option value="support">Support</option>
              <option value="follow-up">Follow-up</option>
            </select>
          </div>
          
          <div>
            <Label>Content</Label>
            <Textarea
              value={newTemplate.content}
              onChange={(e) => setNewTemplate(prev => ({ ...prev, content: e.target.value }))}
              placeholder="Enter template content (use {{variable}} for dynamic content)"
              rows={4}
            />
          </div>
          
          <Button onClick={createTemplate}>
            Save Template
          </Button>
        </div>
        
        <div className="mt-6">
          <Label>Existing Templates</Label>
          <ScrollArea className="h-48 mt-2">
            <div className="space-y-2">
              {templates.map(template => (
                <div key={template.id} className="p-3 border rounded">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{template.name}</h4>
                    <Badge variant="outline">{template.category}</Badge>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{template.content}</p>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-xs text-gray-500">Used {template.usageCount} times</span>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <Edit3 className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6" style={{ height }}>
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Campaign Manager</h1>
        <div className="flex items-center space-x-2">
          <Badge variant="outline">
            {campaigns.filter(c => c.status === 'active').length} Active
          </Badge>
          <Badge variant="outline">
            {campaigns.filter(c => c.status === 'draft').length} Draft
          </Badge>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="segments">Segments</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="campaigns" className="space-y-6">
          {isCreating ? (
            renderCampaignCreator()
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {renderCampaignList()}
              {renderCampaignDetails()}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="templates">
          {renderTemplateManager()}
        </TabsContent>
        
        <TabsContent value="segments">
          <Card>
            <CardHeader>
              <CardTitle>Contact Segments</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Segment management coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Campaign Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Analytics dashboard coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CampaignManager;