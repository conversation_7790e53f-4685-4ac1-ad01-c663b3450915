const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const fs = require('fs');
const path = require('path');

let files = [];
let filteredFiles = [];
let currentPath = '/';
let currentCategory = 'all';
let viewMode = 'grid';
let selectedFiles = [];
let contextFile = null;

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    loadDeviceInfo();
    loadFiles();
    setupEventListeners();
    
    // Listen for file transfer updates
    ipcRenderer.on('file-transfer-progress', (event, data) => {
        updateTransferProgress(data);
    });
    
    ipcRenderer.on('file-transfer-complete', (event, data) => {
        handleTransferComplete(data);
    });
    
    ipcRenderer.on('files-updated', (event, data) => {
        files = data;
        applyCurrentFilter();
    });
});

function setupEventListeners() {
    // File search
    const searchInput = document.getElementById('file-search');
    if (searchInput) {
        searchInput.addEventListener('input', searchFiles);
    }
    
    // Context menu
    document.addEventListener('click', hideContextMenu);
    document.addEventListener('contextmenu', (e) => e.preventDefault());
    
    // Keyboard shortcuts
    document.addEventListener('keydown', (event) => {
        if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
                case 'a':
                    event.preventDefault();
                    selectAllFiles();
                    break;
                case 'r':
                    event.preventDefault();
                    refreshFiles();
                    break;
                case 'u':
                    event.preventDefault();
                    uploadFiles();
                    break;
                case 'd':
                    event.preventDefault();
                    downloadSelected();
                    break;
            }
        }
        
        if (event.key === 'Delete') {
            deleteSelectedFiles();
        }
        
        if (event.key === 'Escape') {
            clearSelection();
        }
    });
}

async function loadDeviceInfo() {
    try {
        const result = await ipcRenderer.invoke('get-device-info');
        if (result.success) {
            const deviceInfo = result.data;
            document.getElementById('device-name').textContent = deviceInfo.name || 'iPhone';
            
            const usedGB = (deviceInfo.usedStorage / (1024 * 1024 * 1024)).toFixed(1);
            const totalGB = (deviceInfo.totalStorage / (1024 * 1024 * 1024)).toFixed(1);
            const usedPercentage = (deviceInfo.usedStorage / deviceInfo.totalStorage) * 100;
            
            document.getElementById('device-storage').textContent = `${usedGB} GB of ${totalGB} GB used`;
            document.getElementById('storage-used').style.width = `${usedPercentage}%`;
        } else {
            // Use sample data
            document.getElementById('device-name').textContent = 'iPhone 14 Pro';
            document.getElementById('device-storage').textContent = '45.2 GB of 128 GB used';
            document.getElementById('storage-used').style.width = '35%';
        }
    } catch (error) {
        console.error('Failed to load device info:', error);
    }
}

async function loadFiles() {
    try {
        const result = await ipcRenderer.invoke('get-files', { path: currentPath });
        if (result.success) {
            files = result.data || [];
        } else {
            // Generate sample files for demo
            files = generateSampleFiles();
        }
        
        applyCurrentFilter();
        updateBreadcrumb();
        
    } catch (error) {
        console.error('Failed to load files:', error);
        files = generateSampleFiles();
        applyCurrentFilter();
    }
}

function generateSampleFiles() {
    return [
        {
            id: '1',
            name: 'Photos',
            type: 'folder',
            size: 0,
            dateModified: Date.now() - 86400000,
            path: '/Photos',
            category: 'photos'
        },
        {
            id: '2',
            name: 'Documents',
            type: 'folder',
            size: 0,
            dateModified: Date.now() - 172800000,
            path: '/Documents',
            category: 'documents'
        },
        {
            id: '3',
            name: 'IMG_1234.jpg',
            type: 'image',
            size: 2048576, // 2MB
            dateModified: Date.now() - 3600000,
            path: '/IMG_1234.jpg',
            category: 'photos',
            thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjNDQ0Ii8+Cjx0ZXh0IHg9IjIwIiB5PSIyNCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1zaXplPSIyNCI+🖼️PC90ZXh0Pgo8L3N2Zz4K'
        },
        {
            id: '4',
            name: 'Video_001.mp4',
            type: 'video',
            size: 15728640, // 15MB
            dateModified: Date.now() - 7200000,
            path: '/Video_001.mp4',
            category: 'videos'
        },
        {
            id: '5',
            name: 'Report.pdf',
            type: 'document',
            size: 1048576, // 1MB
            dateModified: Date.now() - 10800000,
            path: '/Report.pdf',
            category: 'documents'
        },
        {
            id: '6',
            name: 'Song.mp3',
            type: 'audio',
            size: 5242880, // 5MB
            dateModified: Date.now() - 14400000,
            path: '/Song.mp3',
            category: 'music'
        },
        {
            id: '7',
            name: 'Notes.txt',
            type: 'text',
            size: 2048, // 2KB
            dateModified: Date.now() - 18000000,
            path: '/Notes.txt',
            category: 'documents'
        }
    ];
}

function renderFiles() {
    const container = document.getElementById('file-list');
    if (!container) return;
    
    container.innerHTML = '';
    container.className = `file-list ${viewMode}-view`;
    
    if (filteredFiles.length === 0) {
        container.innerHTML = `
            <div class="empty-files">
                <div class="empty-icon">📁</div>
                <h3>No files found</h3>
                <p>This folder is empty or no files match your search</p>
            </div>
        `;
        return;
    }
    
    filteredFiles.forEach(file => {
        const fileElement = createFileElement(file);
        container.appendChild(fileElement);
    });
}

function createFileElement(file) {
    const div = document.createElement('div');
    div.className = `file-item ${file.type}`;
    div.dataset.fileId = file.id;
    
    const isSelected = selectedFiles.includes(file.id);
    if (isSelected) {
        div.classList.add('selected');
    }
    
    const fileIcon = getFileIcon(file);
    const fileSize = file.type === 'folder' ? '' : formatFileSize(file.size);
    
    if (viewMode === 'grid') {
        div.innerHTML = `
            <div class="file-icon">
                ${file.thumbnail ? `<img src="${file.thumbnail}" alt="${file.name}">` : fileIcon}
                <div class="file-type-badge">${getFileTypeBadge(file.type)}</div>
            </div>
            <div class="file-name" title="${escapeHtml(file.name)}">${escapeHtml(file.name)}</div>
            <div class="file-size">${fileSize}</div>
            <div class="file-checkbox">
                <input type="checkbox" ${isSelected ? 'checked' : ''} onchange="toggleFileSelection('${file.id}')">
            </div>
        `;
    } else {
        div.innerHTML = `
            <div class="file-checkbox">
                <input type="checkbox" ${isSelected ? 'checked' : ''} onchange="toggleFileSelection('${file.id}')">
            </div>
            <div class="file-icon-small">${fileIcon}</div>
            <div class="file-details">
                <div class="file-name">${escapeHtml(file.name)}</div>
                <div class="file-meta">
                    <span class="file-size">${fileSize}</span>
                    <span class="file-date">${formatDate(file.dateModified)}</span>
                </div>
            </div>
            <div class="file-actions">
                <button class="action-btn small" onclick="downloadSingleFile('${file.id}')" title="Download">📥</button>
                <button class="action-btn small" onclick="showFileDetails('${file.id}')" title="Details">ℹ️</button>
            </div>
        `;
    }
    
    // Add event listeners
    div.addEventListener('click', (e) => {
        if (e.target.type !== 'checkbox' && !e.target.closest('.action-btn')) {
            if (file.type === 'folder') {
                navigateToFolder(file.path);
            } else {
                showFileDetails(file.id);
            }
        }
    });
    
    div.addEventListener('dblclick', (e) => {
        if (file.type === 'folder') {
            navigateToFolder(file.path);
        } else {
            downloadSingleFile(file.id);
        }
    });
    
    div.addEventListener('contextmenu', (e) => {
        e.preventDefault();
        showContextMenu(e, file);
    });
    
    return div;
}

function getFileIcon(file) {
    const iconMap = {
        folder: '📁',
        image: '🖼️',
        video: '🎥',
        audio: '🎵',
        document: '📄',
        text: '📝',
        pdf: '📕',
        zip: '📦',
        app: '📱'
    };
    
    return iconMap[file.type] || '📄';
}

function getFileTypeBadge(type) {
    const badgeMap = {
        folder: '',
        image: 'IMG',
        video: 'VID',
        audio: 'AUD',
        document: 'DOC',
        text: 'TXT',
        pdf: 'PDF',
        zip: 'ZIP',
        app: 'APP'
    };
    
    return badgeMap[type] || '';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function formatDate(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 86400000) { // Less than 24 hours
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diff < 604800000) { // Less than 7 days
        return date.toLocaleDateString([], { weekday: 'short' });
    } else {
        return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
}

function filterByCategory(category) {
    currentCategory = category;
    
    // Update category button states
    document.querySelectorAll('.category-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-category="${category}"]`).classList.add('active');
    
    applyCurrentFilter();
}

function applyCurrentFilter() {
    if (currentCategory === 'all') {
        filteredFiles = [...files];
    } else {
        filteredFiles = files.filter(file => file.category === currentCategory);
    }
    
    // Apply search filter if active
    const searchTerm = document.getElementById('file-search').value.toLowerCase();
    if (searchTerm) {
        filteredFiles = filteredFiles.filter(file => 
            file.name.toLowerCase().includes(searchTerm)
        );
    }
    
    // Apply sorting
    sortFiles();
    renderFiles();
}

function searchFiles() {
    applyCurrentFilter();
}

function sortFiles() {
    const sortBy = document.getElementById('sort-by').value;
    
    filteredFiles.sort((a, b) => {
        // Folders always come first
        if (a.type === 'folder' && b.type !== 'folder') return -1;
        if (a.type !== 'folder' && b.type === 'folder') return 1;
        
        switch (sortBy) {
            case 'name':
                return a.name.localeCompare(b.name);
            case 'date':
                return b.dateModified - a.dateModified;
            case 'size':
                return b.size - a.size;
            case 'type':
                return a.type.localeCompare(b.type);
            default:
                return 0;
        }
    });
    
    renderFiles();
}

function setViewMode(mode) {
    viewMode = mode;
    
    // Update view button states
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-view="${mode}"]`).classList.add('active');
    
    renderFiles();
}

function toggleFileSelection(fileId) {
    const index = selectedFiles.indexOf(fileId);
    if (index > -1) {
        selectedFiles.splice(index, 1);
    } else {
        selectedFiles.push(fileId);
    }
    
    updateFileSelectionUI();
}

function updateFileSelectionUI() {
    // Update checkboxes
    document.querySelectorAll('.file-item').forEach(item => {
        const fileId = item.dataset.fileId;
        const checkbox = item.querySelector('input[type="checkbox"]');
        const isSelected = selectedFiles.includes(fileId);
        
        if (checkbox) {
            checkbox.checked = isSelected;
        }
        
        item.classList.toggle('selected', isSelected);
    });
}

function selectAllFiles() {
    selectedFiles = filteredFiles.map(file => file.id);
    updateFileSelectionUI();
}

function clearSelection() {
    selectedFiles = [];
    updateFileSelectionUI();
}

function navigateToFolder(folderPath) {
    currentPath = folderPath;
    loadFiles();
}

function navigateToPath(targetPath) {
    currentPath = targetPath;
    loadFiles();
}

function updateBreadcrumb() {
    const breadcrumb = document.getElementById('breadcrumb');
    if (!breadcrumb) return;
    
    const pathParts = currentPath.split('/').filter(part => part);
    
    let html = '<span class="breadcrumb-item" onclick="navigateToPath(\'/\')">iPhone</span>';
    
    let currentPathBuild = '';
    pathParts.forEach((part, index) => {
        currentPathBuild += '/' + part;
        const isLast = index === pathParts.length - 1;
        
        html += ` <span class="breadcrumb-separator">></span> `;
        html += `<span class="breadcrumb-item ${isLast ? 'active' : ''}" onclick="navigateToPath('${currentPathBuild}')">${escapeHtml(part)}</span>`;
    });
    
    breadcrumb.innerHTML = html;
}

// File operations
function uploadFiles() {
    document.getElementById('upload-modal').style.display = 'flex';
}

function closeUploadModal() {
    document.getElementById('upload-modal').style.display = 'none';
}

function handleFileDrop(event) {
    event.preventDefault();
    const uploadArea = document.getElementById('upload-area');
    uploadArea.classList.remove('drag-over');
    
    const files = Array.from(event.dataTransfer.files);
    startFileUpload(files);
}

function handleDragOver(event) {
    event.preventDefault();
    document.getElementById('upload-area').classList.add('drag-over');
}

function handleDragLeave(event) {
    event.preventDefault();
    document.getElementById('upload-area').classList.remove('drag-over');
}

function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    startFileUpload(files);
}

async function startFileUpload(files) {
    closeUploadModal();
    showTransferProgress('Uploading files...');
    
    for (const file of files) {
        try {
            const result = await ipcRenderer.invoke('upload-file', {
                filePath: file.path,
                fileName: file.name,
                destinationPath: currentPath,
                overwrite: document.getElementById('overwrite-existing').checked,
                createBackup: document.getElementById('create-backup').checked
            });
            
            if (result.success) {
                addTransferItem(file.name, 'upload', 'completed');
            } else {
                addTransferItem(file.name, 'upload', 'failed', result.error);
            }
        } catch (error) {
            addTransferItem(file.name, 'upload', 'failed', error.message);
        }
    }
    
    // Refresh file list
    setTimeout(() => {
        loadFiles();
    }, 1000);
}

async function downloadSelected() {
    if (selectedFiles.length === 0) {
        alert('Please select files to download');
        return;
    }
    
    showTransferProgress('Downloading files...');
    
    for (const fileId of selectedFiles) {
        const file = files.find(f => f.id === fileId);
        if (file) {
            await downloadSingleFile(fileId, false);
        }
    }
    
    clearSelection();
}

async function downloadSingleFile(fileId, showProgress = true) {
    const file = files.find(f => f.id === fileId);
    if (!file) return;
    
    if (showProgress) {
        showTransferProgress('Downloading file...');
    }
    
    try {
        const result = await ipcRenderer.invoke('download-file', {
            fileId: file.id,
            filePath: file.path,
            fileName: file.name
        });
        
        if (result.success) {
            addTransferItem(file.name, 'download', 'completed');
        } else {
            addTransferItem(file.name, 'download', 'failed', result.error);
        }
    } catch (error) {
        addTransferItem(file.name, 'download', 'failed', error.message);
    }
}

function showTransferProgress(title) {
    document.getElementById('transfer-title').textContent = title;
    document.getElementById('transfer-progress').style.display = 'block';
    document.getElementById('progress-list').innerHTML = '';
}

function hideTransferProgress() {
    document.getElementById('transfer-progress').style.display = 'none';
}

function addTransferItem(fileName, type, status, error = null) {
    const progressList = document.getElementById('progress-list');
    
    const item = document.createElement('div');
    item.className = `transfer-item ${status}`;
    
    const icon = type === 'upload' ? '📤' : '📥';
    const statusIcon = status === 'completed' ? '✅' : status === 'failed' ? '❌' : '⏳';
    
    item.innerHTML = `
        <div class="transfer-info">
            <span class="transfer-icon">${icon}</span>
            <span class="transfer-name">${escapeHtml(fileName)}</span>
        </div>
        <div class="transfer-status">
            <span class="status-icon">${statusIcon}</span>
            ${error ? `<span class="error-message">${escapeHtml(error)}</span>` : ''}
        </div>
    `;
    
    progressList.appendChild(item);
}

async function refreshFiles() {
    const refreshBtn = document.querySelector('[onclick="refreshFiles()"]');
    if (refreshBtn) {
        refreshBtn.innerHTML = '<span>🔄</span> Refreshing...';
        refreshBtn.disabled = true;
    }
    
    await loadFiles();
    
    if (refreshBtn) {
        refreshBtn.innerHTML = '<span>🔄</span> Refresh';
        refreshBtn.disabled = false;
    }
}

function createFolder() {
    document.getElementById('create-folder-modal').style.display = 'flex';
    document.getElementById('folder-name').focus();
}

function closeCreateFolderModal() {
    document.getElementById('create-folder-modal').style.display = 'none';
    document.getElementById('folder-name').value = '';
}

async function createNewFolder() {
    const folderName = document.getElementById('folder-name').value.trim();
    if (!folderName) return;
    
    try {
        const result = await ipcRenderer.invoke('create-folder', {
            folderName,
            parentPath: currentPath
        });
        
        if (result.success) {
            closeCreateFolderModal();
            loadFiles();
        } else {
            alert('Failed to create folder: ' + result.error);
        }
    } catch (error) {
        alert('Failed to create folder: ' + error.message);
    }
}

// Context menu
function showContextMenu(event, file) {
    contextFile = file;
    const contextMenu = document.getElementById('context-menu');
    
    contextMenu.style.display = 'block';
    contextMenu.style.left = event.pageX + 'px';
    contextMenu.style.top = event.pageY + 'px';
}

function hideContextMenu() {
    document.getElementById('context-menu').style.display = 'none';
    contextFile = null;
}

function downloadContextFile() {
    if (contextFile) {
        downloadSingleFile(contextFile.id);
    }
    hideContextMenu();
}

function shareContextFile() {
    if (contextFile) {
        // Implement file sharing
        console.log('Sharing file:', contextFile.name);
    }
    hideContextMenu();
}

function renameContextFile() {
    if (contextFile) {
        const newName = prompt('Enter new name:', contextFile.name);
        if (newName && newName !== contextFile.name) {
            renameFile(contextFile.id, newName);
        }
    }
    hideContextMenu();
}

function copyContextFile() {
    if (contextFile) {
        // Implement file copying
        console.log('Copying file:', contextFile.name);
    }
    hideContextMenu();
}

function moveContextFile() {
    if (contextFile) {
        // Implement file moving
        console.log('Moving file:', contextFile.name);
    }
    hideContextMenu();
}

function showContextFileDetails() {
    if (contextFile) {
        showFileDetails(contextFile.id);
    }
    hideContextMenu();
}

function deleteContextFile() {
    if (contextFile) {
        deleteFile(contextFile.id);
    }
    hideContextMenu();
}

// File details modal
function showFileDetails(fileId) {
    const file = files.find(f => f.id === fileId);
    if (!file) return;
    
    const modal = document.getElementById('file-details-modal');
    const content = document.getElementById('file-details-content');
    
    content.innerHTML = `
        <div class="file-details-header">
            <div class="file-icon-large">${getFileIcon(file)}</div>
            <div class="file-info">
                <h3>${escapeHtml(file.name)}</h3>
                <p class="file-path">${escapeHtml(file.path)}</p>
            </div>
        </div>
        <div class="file-properties">
            <div class="property-row">
                <span class="property-label">Type:</span>
                <span class="property-value">${file.type}</span>
            </div>
            <div class="property-row">
                <span class="property-label">Size:</span>
                <span class="property-value">${formatFileSize(file.size)}</span>
            </div>
            <div class="property-row">
                <span class="property-label">Modified:</span>
                <span class="property-value">${new Date(file.dateModified).toLocaleString()}</span>
            </div>
            <div class="property-row">
                <span class="property-label">Category:</span>
                <span class="property-value">${file.category}</span>
            </div>
        </div>
    `;
    
    modal.style.display = 'flex';
}

function closeFileDetailsModal() {
    document.getElementById('file-details-modal').style.display = 'none';
}

async function renameFile(fileId, newName) {
    try {
        const result = await ipcRenderer.invoke('rename-file', {
            fileId,
            newName
        });

        if (result.success) {
            loadFiles();
        } else {
            alert('Failed to rename file: ' + result.error);
        }
    } catch (error) {
        alert('Failed to rename file: ' + error.message);
    }
}

async function deleteFile(fileId) {
    const file = files.find(f => f.id === fileId);
    if (!file) return;

    if (confirm(`Are you sure you want to delete "${file.name}"?`)) {
        try {
            const result = await ipcRenderer.invoke('delete-file', {
                fileId,
                filePath: file.path
            });

            if (result.success) {
                loadFiles();
            } else {
                alert('Failed to delete file: ' + result.error);
            }
        } catch (error) {
            alert('Failed to delete file: ' + error.message);
        }
    }
}

async function deleteSelectedFiles() {
    if (selectedFiles.length === 0) return;

    if (confirm(`Are you sure you want to delete ${selectedFiles.length} selected file(s)?`)) {
        for (const fileId of selectedFiles) {
            await deleteFile(fileId);
        }
        clearSelection();
    }
}

function shareFile() {
    if (contextFile) {
        // Implement file sharing functionality
        console.log('Sharing file:', contextFile.name);
        alert('File sharing feature coming soon!');
    }
}

// Utility functions
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
