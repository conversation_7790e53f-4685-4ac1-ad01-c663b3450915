const { EventEmitter } = require('events');
const os = require('os');
const { performance } = require('perf_hooks');

class PerformanceMonitor extends EventEmitter {
  constructor() {
    super();
    this.metrics = {
      cpu: { usage: 0, history: [] },
      memory: { used: 0, total: 0, percentage: 0, history: [] },
      network: { bytesReceived: 0, bytesSent: 0, packetsReceived: 0, packetsSent: 0 },
      airplay: { frameRate: 0, latency: 0, droppedFrames: 0, bandwidth: 0 },
      sync: { operationsPerSecond: 0, queueSize: 0, lastSyncTime: 0 },
      calls: { activeConnections: 0, audioQuality: 0, callDuration: 0 },
      messages: { messagesPerSecond: 0, deliveryRate: 0, queueSize: 0 }
    };
    
    this.monitoringInterval = null;
    this.isMonitoring = false;
    this.startTime = Date.now();
    this.frameTimestamps = [];
    this.syncOperations = [];
    this.networkStats = { received: 0, sent: 0 };
  }

  start() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.startTime = Date.now();
    
    // Monitor system metrics every 5 seconds
    this.monitoringInterval = setInterval(() => {
      this.updateSystemMetrics();
      this.updateCustomMetrics();
      this.emit('metrics-updated', this.getMetrics());
    }, 5000);
    
    console.log('Performance monitoring started');
  }

  stop() {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    console.log('Performance monitoring stopped');
  }

  updateSystemMetrics() {
    // CPU Usage
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;
    
    cpus.forEach(cpu => {
      for (let type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    });
    
    const idle = totalIdle / cpus.length;
    const total = totalTick / cpus.length;
    const usage = 100 - ~~(100 * idle / total);
    
    this.metrics.cpu.usage = usage;
    this.metrics.cpu.history.push({ timestamp: Date.now(), value: usage });
    
    // Keep only last 60 data points (5 minutes at 5-second intervals)
    if (this.metrics.cpu.history.length > 60) {
      this.metrics.cpu.history.shift();
    }

    // Memory Usage
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryPercentage = (usedMemory / totalMemory) * 100;
    
    this.metrics.memory.used = usedMemory;
    this.metrics.memory.total = totalMemory;
    this.metrics.memory.percentage = memoryPercentage;
    this.metrics.memory.history.push({ timestamp: Date.now(), value: memoryPercentage });
    
    if (this.metrics.memory.history.length > 60) {
      this.metrics.memory.history.shift();
    }
  }

  updateCustomMetrics() {
    // Update AirPlay metrics
    this.updateAirPlayMetrics();
    
    // Update sync metrics
    this.updateSyncMetrics();
    
    // Update network metrics
    this.updateNetworkMetrics();
  }

  updateAirPlayMetrics() {
    const now = Date.now();
    
    // Calculate frame rate (frames per second)
    const recentFrames = this.frameTimestamps.filter(timestamp => now - timestamp < 1000);
    this.metrics.airplay.frameRate = recentFrames.length;
    
    // Calculate average latency
    if (this.frameTimestamps.length > 1) {
      const latencies = [];
      for (let i = 1; i < this.frameTimestamps.length; i++) {
        latencies.push(this.frameTimestamps[i] - this.frameTimestamps[i - 1]);
      }
      this.metrics.airplay.latency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
    }
    
    // Clean old frame timestamps
    this.frameTimestamps = this.frameTimestamps.filter(timestamp => now - timestamp < 10000);
  }

  updateSyncMetrics() {
    const now = Date.now();
    
    // Calculate sync operations per second
    const recentOps = this.syncOperations.filter(timestamp => now - timestamp < 1000);
    this.metrics.sync.operationsPerSecond = recentOps.length;
    
    // Clean old sync operations
    this.syncOperations = this.syncOperations.filter(timestamp => now - timestamp < 10000);
  }

  updateNetworkMetrics() {
    // Network metrics would be updated by the services that handle network traffic
    // This is a placeholder for network statistics
  }

  // Methods for services to report metrics
  recordFrame(timestamp = Date.now()) {
    this.frameTimestamps.push(timestamp);
  }

  recordDroppedFrame() {
    this.metrics.airplay.droppedFrames++;
  }

  recordSyncOperation(timestamp = Date.now()) {
    this.syncOperations.push(timestamp);
  }

  recordNetworkTraffic(bytesReceived = 0, bytesSent = 0) {
    this.metrics.network.bytesReceived += bytesReceived;
    this.metrics.network.bytesSent += bytesSent;
    this.networkStats.received += bytesReceived;
    this.networkStats.sent += bytesSent;
  }

  recordCallMetrics(activeConnections, audioQuality, callDuration) {
    this.metrics.calls.activeConnections = activeConnections;
    this.metrics.calls.audioQuality = audioQuality;
    this.metrics.calls.callDuration = callDuration;
  }

  recordMessageMetrics(messagesPerSecond, deliveryRate, queueSize) {
    this.metrics.messages.messagesPerSecond = messagesPerSecond;
    this.metrics.messages.deliveryRate = deliveryRate;
    this.metrics.messages.queueSize = queueSize;
  }

  setSyncQueueSize(size) {
    this.metrics.sync.queueSize = size;
  }

  setLastSyncTime(timestamp) {
    this.metrics.sync.lastSyncTime = timestamp;
  }

  setBandwidth(bandwidth) {
    this.metrics.airplay.bandwidth = bandwidth;
  }

  getMetrics() {
    return {
      ...this.metrics,
      uptime: Date.now() - this.startTime,
      timestamp: Date.now()
    };
  }

  getSystemInfo() {
    return {
      platform: os.platform(),
      arch: os.arch(),
      cpus: os.cpus().length,
      totalMemory: os.totalmem(),
      hostname: os.hostname(),
      nodeVersion: process.version,
      electronVersion: process.versions.electron
    };
  }

  getPerformanceReport() {
    const metrics = this.getMetrics();
    const systemInfo = this.getSystemInfo();
    
    return {
      timestamp: Date.now(),
      uptime: metrics.uptime,
      system: systemInfo,
      performance: {
        cpu: {
          current: metrics.cpu.usage,
          average: this.calculateAverage(metrics.cpu.history),
          peak: this.calculatePeak(metrics.cpu.history)
        },
        memory: {
          current: metrics.memory.percentage,
          average: this.calculateAverage(metrics.memory.history),
          peak: this.calculatePeak(metrics.memory.history),
          used: this.formatBytes(metrics.memory.used),
          total: this.formatBytes(metrics.memory.total)
        },
        airplay: {
          frameRate: metrics.airplay.frameRate,
          averageLatency: Math.round(metrics.airplay.latency),
          droppedFrames: metrics.airplay.droppedFrames,
          bandwidth: this.formatBytes(metrics.airplay.bandwidth) + '/s'
        },
        sync: {
          operationsPerSecond: metrics.sync.operationsPerSecond,
          queueSize: metrics.sync.queueSize,
          lastSync: metrics.sync.lastSyncTime ? new Date(metrics.sync.lastSyncTime).toLocaleString() : 'Never'
        },
        network: {
          totalReceived: this.formatBytes(metrics.network.bytesReceived),
          totalSent: this.formatBytes(metrics.network.bytesSent),
          packetsReceived: metrics.network.packetsReceived,
          packetsSent: metrics.network.packetsSent
        }
      },
      recommendations: this.generateRecommendations(metrics)
    };
  }

  calculateAverage(history) {
    if (history.length === 0) return 0;
    const sum = history.reduce((acc, item) => acc + item.value, 0);
    return Math.round(sum / history.length);
  }

  calculatePeak(history) {
    if (history.length === 0) return 0;
    return Math.max(...history.map(item => item.value));
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  generateRecommendations(metrics) {
    const recommendations = [];
    
    // CPU recommendations
    if (metrics.cpu.usage > 80) {
      recommendations.push({
        type: 'warning',
        category: 'CPU',
        message: 'High CPU usage detected. Consider reducing video quality or frame rate.',
        action: 'reduce_quality'
      });
    }
    
    // Memory recommendations
    if (metrics.memory.percentage > 85) {
      recommendations.push({
        type: 'warning',
        category: 'Memory',
        message: 'High memory usage detected. Consider restarting the application.',
        action: 'restart_app'
      });
    }
    
    // AirPlay recommendations
    if (metrics.airplay.frameRate < 15 && metrics.airplay.frameRate > 0) {
      recommendations.push({
        type: 'info',
        category: 'AirPlay',
        message: 'Low frame rate detected. Check network connection or reduce video quality.',
        action: 'check_network'
      });
    }
    
    if (metrics.airplay.droppedFrames > 100) {
      recommendations.push({
        type: 'warning',
        category: 'AirPlay',
        message: 'Many dropped frames detected. Network may be unstable.',
        action: 'check_network'
      });
    }
    
    // Sync recommendations
    if (metrics.sync.queueSize > 50) {
      recommendations.push({
        type: 'info',
        category: 'Sync',
        message: 'Large sync queue detected. Sync may be slow.',
        action: 'check_connection'
      });
    }
    
    return recommendations;
  }

  // Performance optimization methods
  optimizeForLowEnd() {
    return {
      airplay: {
        maxFrameRate: 15,
        videoQuality: 'medium',
        enableCompression: true
      },
      sync: {
        batchSize: 5,
        syncInterval: 60000
      },
      ui: {
        animationsEnabled: false,
        updateInterval: 10000
      }
    };
  }

  optimizeForHighEnd() {
    return {
      airplay: {
        maxFrameRate: 60,
        videoQuality: 'high',
        enableCompression: false
      },
      sync: {
        batchSize: 20,
        syncInterval: 5000
      },
      ui: {
        animationsEnabled: true,
        updateInterval: 1000
      }
    };
  }

  getOptimizationSettings() {
    const metrics = this.getMetrics();
    
    // Determine if system is low-end or high-end based on performance
    const isLowEnd = metrics.cpu.usage > 70 || metrics.memory.percentage > 80;
    
    return isLowEnd ? this.optimizeForLowEnd() : this.optimizeForHighEnd();
  }
}

module.exports = { PerformanceMonitor };
