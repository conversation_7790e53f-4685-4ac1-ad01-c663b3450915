-- iPhone Companion Pro - Supabase CRM Integration Schema
-- Phase 4: Intel Unison-style CRM Communication Tracking

-- Main communications table for Intel Unison-style sync
CREATE TABLE communications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  clerk_user_id TEXT NOT NULL,
  client_id UUID REFERENCES active_clients(id),
  lead_id UUID REFERENCES leads(id),
  phone_number TEXT NOT NULL,
  direction TEXT CHECK (direction IN ('inbound', 'outbound')),
  type TEXT CHECK (type IN ('sms', 'call')),
  content TEXT,
  duration INTEGER,
  status TEXT DEFAULT 'completed',
  external_id TEXT, -- Phone Link message ID
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_communications_user ON communications(clerk_user_id);
CREATE INDEX idx_communications_phone ON communications(phone_number);
CREATE INDEX idx_communications_created ON communications(created_at DESC);
CREATE INDEX idx_communications_type ON communications(type);
CREATE INDEX idx_communications_direction ON communications(direction);
CREATE INDEX idx_communications_client ON communications(client_id);
CREATE INDEX idx_communications_lead ON communications(lead_id);

-- Row Level Security (RLS) for user isolation
ALTER TABLE communications ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access their own communications
CREATE POLICY "Users can only access their own communications" ON communications
  FOR ALL USING (auth.uid()::text = clerk_user_id);

-- Create a function to get contact info by phone number
CREATE OR REPLACE FUNCTION get_contact_by_phone(user_id TEXT, phone TEXT)
RETURNS TABLE(
  contact_id UUID,
  contact_name TEXT,
  contact_type TEXT
) AS $$
BEGIN
  -- First check active_clients
  RETURN QUERY
  SELECT 
    ac.id as contact_id,
    ac.name as contact_name,
    'client'::TEXT as contact_type
  FROM active_clients ac
  WHERE ac.clerk_user_id = user_id 
    AND ac.phone = phone
  LIMIT 1;
  
  -- If no client found, check leads
  IF NOT FOUND THEN
    RETURN QUERY
    SELECT 
      l.id as contact_id,
      l.name as contact_name,
      'lead'::TEXT as contact_type
    FROM leads l
    WHERE l.clerk_user_id = user_id 
      AND l.phone = phone
    LIMIT 1;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger function to auto-populate client_id or lead_id
CREATE OR REPLACE FUNCTION auto_populate_contact_ids()
RETURNS TRIGGER AS $$
DECLARE
  contact_info RECORD;
BEGIN
  -- Get contact info by phone number
  SELECT * INTO contact_info 
  FROM get_contact_by_phone(NEW.clerk_user_id, NEW.phone_number);
  
  IF contact_info.contact_type = 'client' THEN
    NEW.client_id := contact_info.contact_id;
    NEW.lead_id := NULL;
  ELSIF contact_info.contact_type = 'lead' THEN
    NEW.lead_id := contact_info.contact_id;
    NEW.client_id := NULL;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-populate contact IDs
CREATE TRIGGER auto_populate_contact_ids_trigger
  BEFORE INSERT ON communications
  FOR EACH ROW
  EXECUTE FUNCTION auto_populate_contact_ids();

-- Create real-time subscription table for webhooks
CREATE TABLE communication_webhooks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  clerk_user_id TEXT NOT NULL,
  webhook_url TEXT NOT NULL,
  events TEXT[] DEFAULT ARRAY['sms.received', 'sms.sent', 'call.received', 'call.made'],
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for webhooks
ALTER TABLE communication_webhooks ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only manage their own webhooks" ON communication_webhooks
  FOR ALL USING (auth.uid()::text = clerk_user_id);

-- Create function to trigger webhooks
CREATE OR REPLACE FUNCTION trigger_communication_webhook()
RETURNS TRIGGER AS $$
DECLARE
  webhook_record RECORD;
  event_type TEXT;
  payload JSON;
BEGIN
  -- Determine event type
  IF TG_OP = 'INSERT' THEN
    IF NEW.type = 'sms' AND NEW.direction = 'inbound' THEN
      event_type := 'sms.received';
    ELSIF NEW.type = 'sms' AND NEW.direction = 'outbound' THEN
      event_type := 'sms.sent';
    ELSIF NEW.type = 'call' AND NEW.direction = 'inbound' THEN
      event_type := 'call.received';
    ELSIF NEW.type = 'call' AND NEW.direction = 'outbound' THEN
      event_type := 'call.made';
    END IF;
    
    -- Create payload
    payload := json_build_object(
      'event', event_type,
      'communication_id', NEW.id,
      'phone_number', NEW.phone_number,
      'content', NEW.content,
      'direction', NEW.direction,
      'type', NEW.type,
      'created_at', NEW.created_at,
      'client_id', NEW.client_id,
      'lead_id', NEW.lead_id
    );
    
    -- Send webhook notifications (this would be handled by Edge Functions in real implementation)
    FOR webhook_record IN 
      SELECT * FROM communication_webhooks 
      WHERE clerk_user_id = NEW.clerk_user_id 
        AND is_active = true 
        AND event_type = ANY(events)
    LOOP
      -- In a real implementation, this would trigger an Edge Function
      -- For now, we'll just insert into a notifications table
      INSERT INTO realtime_notifications (
        user_id, 
        type, 
        payload, 
        webhook_url
      ) VALUES (
        NEW.clerk_user_id,
        'webhook',
        payload,
        webhook_record.webhook_url
      );
    END LOOP;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for webhook notifications
CREATE TRIGGER trigger_communication_webhook_trigger
  AFTER INSERT ON communications
  FOR EACH ROW
  EXECUTE FUNCTION trigger_communication_webhook();

-- Realtime notifications table (for debugging and queue management)
CREATE TABLE realtime_notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id TEXT NOT NULL,
  type TEXT NOT NULL,
  payload JSON NOT NULL,
  webhook_url TEXT,
  sent_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable realtime for communications table
ALTER TABLE communications REPLICA IDENTITY FULL;

-- Views for easy querying
CREATE VIEW communication_summary AS
SELECT 
  c.*,
  CASE 
    WHEN c.client_id IS NOT NULL THEN ac.name
    WHEN c.lead_id IS NOT NULL THEN l.name
    ELSE 'Unknown Contact'
  END as contact_name,
  CASE 
    WHEN c.client_id IS NOT NULL THEN 'client'
    WHEN c.lead_id IS NOT NULL THEN 'lead'
    ELSE 'unknown'
  END as contact_type
FROM communications c
LEFT JOIN active_clients ac ON c.client_id = ac.id
LEFT JOIN leads l ON c.lead_id = l.id;

-- View for conversation threads
CREATE VIEW conversation_threads AS
SELECT 
  clerk_user_id,
  phone_number,
  MAX(contact_name) as contact_name,
  MAX(contact_type) as contact_type,
  COUNT(*) as message_count,
  MAX(created_at) as last_activity,
  COUNT(CASE WHEN direction = 'inbound' AND type = 'sms' THEN 1 END) as unread_count
FROM communication_summary
WHERE type = 'sms'
GROUP BY clerk_user_id, phone_number
ORDER BY last_activity DESC;

-- Performance optimization: Partitioning by date (for high-volume users)
-- This would be implemented if the table grows very large
CREATE TABLE communications_archive (
  LIKE communications INCLUDING ALL
);

-- Cleanup function for old communications (optional)
CREATE OR REPLACE FUNCTION cleanup_old_communications(days_old INTEGER DEFAULT 365)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  WITH deleted AS (
    DELETE FROM communications 
    WHERE created_at < NOW() - (days_old || ' days')::INTERVAL
    RETURNING *
  )
  SELECT COUNT(*) INTO deleted_count FROM deleted;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON communications TO authenticated;
GRANT ALL ON communication_webhooks TO authenticated;
GRANT SELECT ON communication_summary TO authenticated;
GRANT SELECT ON conversation_threads TO authenticated;