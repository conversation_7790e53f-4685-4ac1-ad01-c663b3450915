const { EventEmitter } = require('events');
const WebSocket = require('ws');
const winston = require('winston');
const axios = require('axios');

class IntelUnisonSyncService extends EventEmitter {
  constructor(messageService, persistence, supabaseCRM) {
    super();
    this.messageService = messageService;
    this.persistence = persistence;
    this.supabaseCRM = supabaseCRM;
    this.isRunning = false;
    this.syncInterval = null;
    this.wsServer = null;
    this.connectedDevices = new Map();
    this.syncQueue = [];
    this.lastSyncTimestamp = null;
    
    // Configure logger
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ level, message, timestamp }) => {
          return `${timestamp} [${level.toUpperCase()}] IntelUnisonSync: ${message}`;
        })
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'intel-unison-sync.log' })
      ]
    });

    this.syncMethods = {
      phoneLink: { enabled: true, priority: 1, lastSync: null },
      companionApp: { enabled: true, priority: 2, lastSync: null },
      airPlay: { enabled: true, priority: 3, lastSync: null },
      webBridge: { enabled: true, priority: 4, lastSync: null },
      bluetooth: { enabled: true, priority: 5, lastSync: null },
      wifiDirect: { enabled: true, priority: 6, lastSync: null },
      usbDirect: { enabled: true, priority: 7, lastSync: null }
    };
  }

  async initialize() {
    this.logger.info('🔥 INITIALIZING INTEL UNISON++ SYNC SERVICE 🔥');
    
    // Set up WebSocket server for real-time sync
    await this.initializeWebSocketServer();
    
    // Set up sync event handlers
    this.setupEventHandlers();
    
    // Load last sync state
    await this.loadSyncState();
    
    this.logger.info('✅ Intel Unison++ Sync Service initialized');
    return true;
  }

  async initializeWebSocketServer() {
    // Try to find an available port for WebSocket server
    const ports = [8765, 8766, 8767, 8768, 8769];
    
    for (const port of ports) {
      try {
        this.wsServer = new WebSocket.Server({ 
          port, 
          perMessageDeflate: false 
        });
        
        this.wsServer.on('connection', (ws, req) => {
          const deviceId = this.generateDeviceId(req);
          this.handleDeviceConnection(ws, deviceId);
        });
        
        this.wsServer.on('error', (error) => {
          if (error.code !== 'EADDRINUSE') {
            this.logger.error(`WebSocket server error: ${error.message}`);
          }
        });
        
        this.logger.info(`✅ Real-time sync WebSocket server started on port ${port}`);
        break;
        
      } catch (error) {
        if (error.code === 'EADDRINUSE') {
          continue; // Try next port
        }
        this.logger.error(`Failed to start WebSocket server: ${error.message}`);
      }
    }
  }

  setupEventHandlers() {
    // Listen for new messages from message service
    this.messageService.on('message-received', (message) => {
      this.queueForSync(message, 'message-received');
    });

    this.messageService.on('message-sent', (message) => {
      this.queueForSync(message, 'message-sent');
    });

    // Listen for extraction events
    if (this.messageService.messageExtractor) {
      this.messageService.messageExtractor.on('message-extracted', (message) => {
        this.queueForSync(message, 'message-extracted');
      });
    }

    // Listen for Supabase events
    if (this.supabaseCRM) {
      this.supabaseCRM.on('communication-received', (communication) => {
        this.queueForSync(communication, 'crm-communication');
      });
    }
  }

  async startRealtimeSync() {
    if (this.isRunning) {
      this.logger.warn('Real-time sync already running');
      return;
    }

    this.logger.info('🚀 Starting Intel Unison++ real-time sync...');
    this.isRunning = true;

    // Start sync queue processor
    this.startSyncQueueProcessor();

    // Start periodic sync for missed data
    this.startPeriodicSync();

    // Start connection health monitoring
    this.startConnectionMonitoring();

    // Emit sync started event
    this.emit('sync-started');
    
    this.logger.info('✅ Intel Unison++ real-time sync started');
  }

  startSyncQueueProcessor() {
    // Process sync queue every 500ms for near real-time sync
    this.syncInterval = setInterval(async () => {
      if (this.syncQueue.length > 0) {
        const batch = this.syncQueue.splice(0, 10); // Process up to 10 items at once
        await this.processSyncBatch(batch);
      }
    }, 500);
  }

  startPeriodicSync() {
    // Full sync every 30 seconds to catch any missed data
    setInterval(async () => {
      if (this.isRunning) {
        await this.performFullSync();
      }
    }, 30000);
  }

  startConnectionMonitoring() {
    // Monitor connections every 10 seconds
    setInterval(() => {
      this.monitorConnections();
    }, 10000);
  }

  queueForSync(data, eventType) {
    const syncItem = {
      id: this.generateSyncId(),
      data,
      eventType,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: 3
    };

    this.syncQueue.push(syncItem);
    this.logger.info(`📤 Queued for sync: ${eventType} - ${syncItem.id}`);
  }

  async processSyncBatch(batch) {
    this.logger.info(`🔄 Processing sync batch of ${batch.length} items`);

    for (const item of batch) {
      try {
        await this.processSyncItem(item);
      } catch (error) {
        this.logger.error(`❌ Failed to sync item ${item.id}: ${error.message}`);
        
        // Retry logic
        if (item.retryCount < item.maxRetries) {
          item.retryCount++;
          this.syncQueue.push(item); // Re-queue for retry
          this.logger.info(`🔄 Re-queued item ${item.id} for retry (${item.retryCount}/${item.maxRetries})`);
        } else {
          this.logger.error(`❌ Item ${item.id} failed after ${item.maxRetries} retries`);
          this.emit('sync-failed', item);
        }
      }
    }
  }

  async processSyncItem(item) {
    const { data, eventType } = item;

    switch (eventType) {
      case 'message-received':
        await this.syncMessageReceived(data);
        break;
      
      case 'message-sent':
        await this.syncMessageSent(data);
        break;
      
      case 'message-extracted':
        await this.syncExtractedMessage(data);
        break;
      
      case 'crm-communication':
        await this.syncCRMCommunication(data);
        break;
      
      default:
        this.logger.warn(`Unknown sync event type: ${eventType}`);
    }

    // Broadcast to connected devices
    this.broadcastToDevices(item);
    
    // Update sync status
    await this.updateSyncStatus(eventType, 'completed');
  }

  async syncMessageReceived(message) {
    // Save to local database
    await this.persistence.saveMessage({
      threadId: message.phoneNumber || message.conversationId,
      phoneNumber: message.phoneNumber,
      contactName: message.contactName || 'Unknown',
      messageText: message.text || message.content,
      timestamp: message.timestamp || Date.now(),
      isOutgoing: false,
      isDelivered: true,
      isRead: false,
      source: 'realtime_sync'
    });

    // Sync to CRM if available
    if (this.supabaseCRM) {
      await this.supabaseCRM.saveCommunication('default-user', {
        phone_number: message.phoneNumber,
        direction: 'inbound',
        type: 'sms',
        content: message.text || message.content,
        metadata: {
          source: 'intel_unison_sync',
          sync_timestamp: Date.now()
        }
      });
    }

    this.logger.info(`📬 Synced incoming message from ${message.phoneNumber}`);
  }

  async syncMessageSent(message) {
    // Save to local database
    await this.persistence.saveMessage({
      threadId: message.phoneNumber || message.conversationId,
      phoneNumber: message.phoneNumber,
      contactName: message.contactName || 'Unknown',
      messageText: message.text || message.content,
      timestamp: message.timestamp || Date.now(),
      isOutgoing: true,
      isDelivered: true,
      isRead: true,
      source: 'realtime_sync'
    });

    // Sync to CRM if available
    if (this.supabaseCRM) {
      await this.supabaseCRM.saveCommunication('default-user', {
        phone_number: message.phoneNumber,
        direction: 'outbound',
        type: 'sms',
        content: message.text || message.content,
        metadata: {
          source: 'intel_unison_sync',
          sync_timestamp: Date.now()
        }
      });
    }

    this.logger.info(`📤 Synced outgoing message to ${message.phoneNumber}`);
  }

  async syncExtractedMessage(message) {
    // Message already saved by extraction service, just broadcast
    this.logger.info(`🔍 Synced extracted message from ${message.phoneNumber}`);
  }

  async syncCRMCommunication(communication) {
    // Convert CRM communication to local message format
    const message = {
      threadId: communication.phone_number,
      phoneNumber: communication.phone_number,
      contactName: 'CRM Contact',
      messageText: communication.content,
      timestamp: new Date(communication.created_at).getTime(),
      isOutgoing: communication.direction === 'outbound',
      isDelivered: true,
      isRead: true,
      source: 'crm_sync'
    };

    await this.persistence.saveMessage(message);
    this.logger.info(`💼 Synced CRM communication for ${communication.phone_number}`);
  }

  broadcastToDevices(syncItem) {
    const message = JSON.stringify({
      type: 'sync-update',
      eventType: syncItem.eventType,
      data: syncItem.data,
      timestamp: syncItem.timestamp
    });

    let broadcastCount = 0;
    this.connectedDevices.forEach((device, deviceId) => {
      if (device.ws.readyState === WebSocket.OPEN) {
        device.ws.send(message);
        broadcastCount++;
      }
    });

    if (broadcastCount > 0) {
      this.logger.info(`📡 Broadcasted sync update to ${broadcastCount} devices`);
    }
  }

  handleDeviceConnection(ws, deviceId) {
    this.logger.info(`📱 Device connected: ${deviceId}`);
    
    const device = {
      id: deviceId,
      ws,
      connectedAt: Date.now(),
      lastPing: Date.now(),
      syncState: 'connected'
    };

    this.connectedDevices.set(deviceId, device);

    // Set up device message handlers
    ws.on('message', (data) => {
      this.handleDeviceMessage(deviceId, data);
    });

    ws.on('close', () => {
      this.logger.info(`📱 Device disconnected: ${deviceId}`);
      this.connectedDevices.delete(deviceId);
    });

    ws.on('error', (error) => {
      this.logger.error(`📱 Device ${deviceId} error: ${error.message}`);
      this.connectedDevices.delete(deviceId);
    });

    // Send initial sync data
    this.sendInitialSync(deviceId);

    // Emit device connected event
    this.emit('device-connected', { deviceId, device });
  }

  async handleDeviceMessage(deviceId, data) {
    try {
      const message = JSON.parse(data.toString());
      const device = this.connectedDevices.get(deviceId);
      
      if (!device) return;

      switch (message.type) {
        case 'ping':
          device.lastPing = Date.now();
          device.ws.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
          break;

        case 'sync-request':
          await this.handleSyncRequest(deviceId, message);
          break;

        case 'message-update':
          await this.handleMessageUpdate(deviceId, message);
          break;

        case 'status-update':
          this.handleStatusUpdate(deviceId, message);
          break;

        default:
          this.logger.warn(`Unknown message type from device ${deviceId}: ${message.type}`);
      }
    } catch (error) {
      this.logger.error(`Error handling device message from ${deviceId}: ${error.message}`);
    }
  }

  async sendInitialSync(deviceId) {
    const device = this.connectedDevices.get(deviceId);
    if (!device) return;

    try {
      // Send recent messages (last 100)
      const recentMessages = await this.persistence.loadAllMessages(100);
      const syncData = {
        type: 'initial-sync',
        messages: recentMessages,
        timestamp: Date.now()
      };

      device.ws.send(JSON.stringify(syncData));
      this.logger.info(`📤 Sent initial sync to device ${deviceId} (${recentMessages.length} messages)`);
    } catch (error) {
      this.logger.error(`Failed to send initial sync to device ${deviceId}: ${error.message}`);
    }
  }

  async handleSyncRequest(deviceId, message) {
    const { since, limit = 50 } = message;
    
    try {
      // Get messages since specified timestamp
      let messages;
      if (since) {
        messages = await this.persistence.loadMessagesAfter(since, limit);
      } else {
        messages = await this.persistence.loadAllMessages(limit);
      }

      const response = {
        type: 'sync-response',
        requestId: message.requestId,
        messages,
        timestamp: Date.now()
      };

      const device = this.connectedDevices.get(deviceId);
      if (device && device.ws.readyState === WebSocket.OPEN) {
        device.ws.send(JSON.stringify(response));
        this.logger.info(`📤 Sent sync response to device ${deviceId} (${messages.length} messages)`);
      }
    } catch (error) {
      this.logger.error(`Failed to handle sync request from device ${deviceId}: ${error.message}`);
    }
  }

  async handleMessageUpdate(deviceId, message) {
    // Handle message updates from device (read status, etc.)
    const { messageId, updates } = message;
    
    try {
      if (updates.isRead !== undefined) {
        // Update read status in local database
        await this.persistence.updateMessageReadStatus(messageId, updates.isRead);
        this.logger.info(`📖 Updated read status for message ${messageId}`);
      }
    } catch (error) {
      this.logger.error(`Failed to handle message update from device ${deviceId}: ${error.message}`);
    }
  }

  handleStatusUpdate(deviceId, message) {
    const device = this.connectedDevices.get(deviceId);
    if (device) {
      device.status = message.status;
      device.lastStatusUpdate = Date.now();
      this.logger.info(`📱 Device ${deviceId} status updated: ${message.status}`);
    }
  }

  async performFullSync() {
    this.logger.info('🔄 Performing full sync...');
    
    try {
      // Get sync statistics
      const stats = await this.persistence.getMessageStats();
      
      // Update last sync timestamp
      this.lastSyncTimestamp = Date.now();
      
      // Save sync status
      await this.updateSyncStatus('full-sync', 'completed', stats.total_messages);
      
      this.logger.info(`✅ Full sync completed - ${stats.total_messages} messages in database`);
      this.emit('full-sync-completed', stats);
    } catch (error) {
      this.logger.error(`❌ Full sync failed: ${error.message}`);
      await this.updateSyncStatus('full-sync', 'failed', 0, error.message);
    }
  }

  monitorConnections() {
    const now = Date.now();
    const timeoutThreshold = 60000; // 60 seconds

    this.connectedDevices.forEach((device, deviceId) => {
      if (now - device.lastPing > timeoutThreshold) {
        this.logger.warn(`📱 Device ${deviceId} appears to be inactive, removing...`);
        device.ws.terminate();
        this.connectedDevices.delete(deviceId);
      }
    });

    // Log connection status
    if (this.connectedDevices.size > 0) {
      this.logger.info(`📱 ${this.connectedDevices.size} devices connected`);
    }
  }

  async updateSyncStatus(syncType, status, recordsSynced = 0, errorMessage = null) {
    try {
      await this.persistence.updateSyncStatus(syncType, status, null, recordsSynced, errorMessage);
      
      if (this.syncMethods[syncType]) {
        this.syncMethods[syncType].lastSync = Date.now();
      }
    } catch (error) {
      this.logger.error(`Failed to update sync status: ${error.message}`);
    }
  }

  async loadSyncState() {
    try {
      // Load last sync timestamps for each method
      for (const method of Object.keys(this.syncMethods)) {
        const lastSync = await this.persistence.getLastSync(method);
        if (lastSync) {
          this.syncMethods[method].lastSync = lastSync.last_sync;
        }
      }
      
      this.logger.info('✅ Sync state loaded');
    } catch (error) {
      this.logger.error(`Failed to load sync state: ${error.message}`);
    }
  }

  generateDeviceId(req) {
    const userAgent = req.headers['user-agent'] || 'unknown';
    const ip = req.socket.remoteAddress || 'unknown';
    return `device_${Buffer.from(`${ip}_${userAgent}`).toString('base64').substring(0, 16)}`;
  }

  generateSyncId() {
    return `sync_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  getSyncStatus() {
    return {
      isRunning: this.isRunning,
      lastSyncTimestamp: this.lastSyncTimestamp,
      queueSize: this.syncQueue.length,
      connectedDevices: this.connectedDevices.size,
      syncMethods: this.syncMethods,
      devices: Array.from(this.connectedDevices.entries()).map(([id, device]) => ({
        id,
        connectedAt: device.connectedAt,
        lastPing: device.lastPing,
        status: device.status || 'connected'
      }))
    };
  }

  async stopSync() {
    this.logger.info('⏹️ Stopping Intel Unison++ sync service...');
    this.isRunning = false;

    // Clear intervals
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    // Close WebSocket server
    if (this.wsServer) {
      this.wsServer.close();
    }

    // Disconnect all devices
    this.connectedDevices.forEach((device) => {
      device.ws.terminate();
    });
    this.connectedDevices.clear();

    this.emit('sync-stopped');
    this.logger.info('✅ Intel Unison++ sync service stopped');
  }
}

module.exports = { IntelUnisonSyncService };