const { EventEmitter } = require('events');
const fs = require('fs');
const path = require('path');
const Store = require('electron-store');
const { PhoneLinkBridge } = require('./PhoneLinkBridge');
const WebSocket = require('ws');
const { BeastPersistence } = require('./BeastPersistence');
const { MessageExtractionService } = require('./MessageExtractionService');
const { UnisonProtocolService } = require('./UnisonProtocolService');
const { IntelUnisonMessageExtractor } = require('./IntelUnisonMessageExtractor');
const logger = require('../utils/Logger');

class MessageService extends EventEmitter {
  constructor(airPlayServer = null, webBridge = null) {
    super();
    this.store = new Store({ name: 'messages' });
    this.conversations = new Map();
    this.syncInterval = null;
    this.airPlayServer = airPlayServer;
    this.webBridge = webBridge;
    this.companionAppConnected = false;
    this.wsServer = null;
    this.connectedClients = new Set();
    this.messageQueue = [];

    // INTEL UNISON++ ADDITIONS
    this.persistence = new BeastPersistence();
    this.messageExtractor = new MessageExtractionService(this.persistence);
    this.unisonProtocol = new UnisonProtocolService(this.persistence, this);
    this.autoReconnectInterval = null;
    this.connectionMethods = new Map();
    this.phoneLinkBridge = new PhoneLinkBridge();
    this.intelUnisonExtractor = null;

    this.integrationMethods = {
      phoneLink: false,
      airPlay: false,
      companionApp: false,
      webBridge: false,
      vmBridge: false,
      usbDirect: false,
      bluetooth: false,
      wifiDirect: false,
      shortcuts: false
    };
  }




  async initialize() {
    logger.iphone('INFO', 'Initializing Intel Unison++ Message Service');

    // Initialize BEAST persistence first
    await this.persistence.initialize();
    logger.database('INFO', 'BeastPersistence initialized successfully');

    // Initialize Message Extraction Service
    await this.messageExtractor.initialize();
    logger.sync('INFO', 'MessageExtractionService initialized successfully');

    // Set up extraction event handlers
    this.messageExtractor.on('message-extracted', (message) => {
      logger.sync('INFO', `New message extracted from ${message.phoneNumber}`, { preview: message.messageText.substring(0, 50) + '...' });
      this.emit('message-received', message);
    });

    // Start message extraction
    await this.messageExtractor.startExtraction();
    logger.sync('INFO', 'Message extraction started successfully');

    // Initialize Intel Unison++ Message Extractor
    this.intelUnisonExtractor = new IntelUnisonMessageExtractor(this.persistence, this.phoneLinkBridge);
    await this.intelUnisonExtractor.initialize();
    console.log('✅ Intel Unison++ Extractor initialized');

    // Set up Intel Unison extractor event handlers
    this.intelUnisonExtractor.on('message-extracted', (message) => {
      console.log(`📬 Intel Unison++: New message from ${message.contactName}`);
      this.addMessageToConversation(message);
      this.emit('message-received', message);
    });

    // Initialize Intel Unison++ Protocol
    await this.unisonProtocol.initialize();
    console.log('✅ Intel Unison++ Protocol initialized');
    
    // Set up Unison protocol event handlers
    this.unisonProtocol.on('message-received', (message) => {
      console.log(`📬 Unison Protocol: New message from ${message.from}`);
      this.emit('message-received', message);
    });
    
    this.unisonProtocol.on('bluetooth-connected', (device) => {
      console.log(`🔵 Bluetooth connected: ${device.name}`);
      this.integrationMethods.bluetooth = true;
    });
    
    this.unisonProtocol.on('wifi-direct-connected', (device) => {
      console.log(`📡 WiFi Direct connected: ${device.name}`);
      this.integrationMethods.wifiDirect = true;
    });
    
    // Start real-time sync
    await this.unisonProtocol.startRealtimeSync();
    console.log('✅ Real-time sync started');

    // Load all persisted data
    await this.loadPersistedData();

    // Initialize WebSocket server for companion app
    this.initializeWebSocketServer();

    // Set up AirPlay integration if available
    if (this.airPlayServer) {
      this.setupAirPlayIntegration();
    }

    // Set up web bridge integration if available
    if (this.webBridge) {
      this.setupWebBridgeIntegration();
    }

    // Try to load Phone Link messages first
    await this.initializePhoneLinkIntegration();

    // Initialize Unison-style features
    await this.initializeUnison();

    // Start BEAST mode syncing with auto-reconnect
    this.startBeastSync();

    // DISABLED: Auto-reconnect spam - user can manually connect
    // this.startAutoReconnect();

    console.log('✅ INTEL UNISON++ MESSAGE SERVICE READY!');
    console.log('Integration methods:', this.integrationMethods);
    console.log('Extraction status:', this.messageExtractor.getStatus());
    console.log('Unison Protocol status:', this.unisonProtocol.getStatus());
  }

  // Add this to MessageService.js initialize() method
  async initializeUnison() {
    console.log('🔥 Initializing Unison-style message sync...');

    // Connect to enhanced Phone Link bridge
    if (this.phoneLinkBridge) {
      this.phoneLinkBridge.on('data-synced', async (stats) => {
        console.log('✅ Synced:', stats);

        // Load messages for UI
        const messages = await this.phoneLinkBridge.getMessagesForCRM();

        // Update conversations
        for (const msg of messages) {
          const convoId = msg.phone_number;

          if (!this.conversations.has(convoId)) {
            this.conversations.set(convoId, {
              id: convoId,
              phoneNumber: msg.phone_number,
              contactName: msg.contact_name || msg.phone_number,
              messages: [],
              lastMessage: '',
              lastTimestamp: 0,
              unreadCount: 0
            });
          }

          const conversation = this.conversations.get(convoId);

          // Add message if not already exists
          const exists = conversation.messages.some(m =>
            m.text === msg.message_text && m.timestamp === msg.timestamp
          );

          if (!exists) {
            conversation.messages.push({
              id: msg.id,
              text: msg.message_text,
              timestamp: msg.timestamp,
              isIncoming: !msg.is_outgoing,
              sent: true,
              delivered: true,
              isRead: true
            });
          }
        }

        // Sort messages and update UI
        this.conversations.forEach(conv => {
          conv.messages.sort((a, b) => a.timestamp - b.timestamp);
          if (conv.messages.length > 0) {
            const last = conv.messages[conv.messages.length - 1];
            conv.lastMessage = last.text;
            conv.lastTimestamp = last.timestamp;
          }
        });

        // Save to persistence
        await this.saveConversations();

        // Emit to UI
        this.emit('conversations-updated', this.getConversations());
      });
    }
  }

  async initializePhoneLinkIntegration() {
    try {
      console.log('🔌 Initializing Phone Link Bridge...');
      this.phoneLinkBridge = new PhoneLinkBridge();
      await this.phoneLinkBridge.connect();

      // Load contacts
      const contacts = await this.phoneLinkBridge.getContacts();
      console.log(`✅ Loaded ${contacts.length} contacts from Phone Link`);

      // Load call history
      const calls = await this.phoneLinkBridge.getCallHistory();
      console.log(`✅ Loaded ${calls.length} calls from Phone Link`);

      this.integrationMethods.phoneLink = true;
      this.emit('phonelink-connected', { contacts, calls });
    } catch (error) {
      console.error('Phone Link integration failed:', error);
      this.integrationMethods.phoneLink = false;
    }
  }

  initializeWebSocketServer() {
    // Try multiple ports for WebSocket server
    const ports = [8081, 8082, 8083, 8084, 8085];

    const tryPort = (portIndex = 0) => {
      if (portIndex >= ports.length) {
        console.log('⚠️ Could not start WebSocket server on any available port');
        return;
      }

      const port = ports[portIndex];

      try {
        this.wsServer = new WebSocket.Server({ port });

        this.wsServer.on('connection', (ws, req) => {
          console.log('📱 Companion app connected from:', req.socket.remoteAddress);
          this.connectedClients.add(ws);
          this.companionAppConnected = true;
          this.integrationMethods.companionApp = true;

          // Send any queued messages
          this.messageQueue.forEach(msg => {
            ws.send(JSON.stringify(msg));
          });
          this.messageQueue = [];

          ws.on('message', (data) => {
            this.handleCompanionAppMessage(JSON.parse(data.toString()));
          });

          ws.on('close', () => {
            this.connectedClients.delete(ws);
            if (this.connectedClients.size === 0) {
              this.companionAppConnected = false;
              this.integrationMethods.companionApp = false;
            }
          });

          ws.on('error', (error) => {
            console.error('Companion app WebSocket error:', error);
            this.connectedClients.delete(ws);
          });
        });

        this.wsServer.on('error', (error) => {
          if (error.code === 'EADDRINUSE') {
            console.log(`⚠️ Port ${port} in use, trying next port...`);
            tryPort(portIndex + 1);
          } else {
            console.error('WebSocket server error:', error);
          }
        });

        console.log(`✅ WebSocket server for companion app started on port ${port}`);

      } catch (error) {
        console.log(`⚠️ Failed to start WebSocket server on port ${port}:`, error.message);
        tryPort(portIndex + 1);
      }
    };

    tryPort();
  }

  setupAirPlayIntegration() {
    if (!this.airPlayServer) return;

    this.airPlayServer.on('mirror-start', () => {
      this.integrationMethods.airPlay = true;
      console.log('AirPlay integration enabled');
    });

    this.airPlayServer.on('mirror-stop', () => {
      this.integrationMethods.airPlay = false;
      console.log('AirPlay integration disabled');
    });

    // Listen for message overlay requests
    this.airPlayServer.on('message-overlay-request', (data) => {
      this.handleAirPlayMessageRequest(data);
    });
  }

  setupWebBridgeIntegration() {
    if (!this.webBridge) return;

    this.webBridge.on('device-connected', () => {
      this.integrationMethods.webBridge = true;
      console.log('Web bridge integration enabled');
    });

    this.webBridge.on('message-received', (message) => {
      this.handleWebBridgeMessage(message);
    });
  }

  startSync() {
    // Initial sync
    this.syncMessages();
    
    // Sync every 5 seconds
    this.syncInterval = setInterval(() => {
      this.syncMessages();
    }, 5000);
  }

  async syncMessages() {
    // Try to sync with Phone Link periodically
    try {
      if (this.phoneLinkBridge) {
        // Trigger a sync from Phone Link
        await this.phoneLinkBridge.syncFromPhoneLink();
      }
    } catch (error) {
      console.log('Phone Link sync failed, waiting for real data sources:', error.message);
      // NO MORE MOCK DATA - only process real iPhone data
    }
  }

  // generateMockMessage() REMOVED - NO MORE DEMO DATA

  async loadPersistedData() {
    console.log('📂 Loading persisted data from Intel Unison++ database...');

    try {
      // Load all messages from enhanced BEAST persistence
      const messages = await this.persistence.loadAllMessages(1000);
      console.log(`📬 Loaded ${messages.length} persisted messages`);

      // Load message threads (conversations)
      const threads = await this.persistence.loadMessageThreads();
      console.log(`💬 Loaded ${threads.length} message threads`);

      // Load calls
      const calls = await this.persistence.loadCalls(100);
      console.log(`📞 Loaded ${calls.length} calls`);

      // Load contacts
      const contacts = await this.persistence.loadContacts();
      console.log(`👥 Loaded ${contacts.length} contacts`);

      // Get database statistics
      const stats = await this.persistence.getMessageStats();
      console.log('📊 Database stats:', stats);

      // Update local state
      this.updateLocalState(messages, threads, calls, contacts);

      // Emit data loaded event
      this.emit('data-loaded', {
        messages: messages.length,
        threads: threads.length,
        calls: calls.length,
        contacts: contacts.length,
        stats
      });

    } catch (error) {
      console.error('❌ Error loading persisted data:', error);
      this.emit('error', error);
    }
  }

  updateLocalState(messages, threads, calls, contacts) {
    // Update conversations map with message threads
    this.conversations.clear();
    
    for (const thread of threads) {
      this.conversations.set(thread.threadId, {
        id: thread.threadId,
        phoneNumber: thread.phoneNumber,
        contactName: thread.contactName,
        lastMessage: thread.lastMessage,
        lastTimestamp: thread.lastTimestamp,
        messageCount: thread.messageCount,
        unreadCount: thread.unreadCount,
        photoPath: thread.photoPath,
        isGroup: thread.isGroup,
        messages: []
      });
    }
    
    // Group messages by thread ID
    for (const message of messages) {
      const threadId = message.threadId;
      
      if (this.conversations.has(threadId)) {
        const convo = this.conversations.get(threadId);
        convo.messages.push(message);
      }
    }
    
    // Sort messages in each conversation by timestamp
    for (const [threadId, convo] of this.conversations) {
      if (convo.messages) {
        convo.messages.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
      }
    }
    
    console.log(`✅ Updated ${this.conversations.size} conversations in local state`);
    console.log(`✅ Loaded ${messages.length} messages, ${calls.length} calls, ${contacts.length} contacts`);
  }

  async addMessage(message) {
    const convoId = message.conversationId;

    if (!this.conversations.has(convoId)) {
      this.conversations.set(convoId, {
        id: convoId,
        contactName: message.contactName,
        phoneNumber: message.phoneNumber,
        messages: [],
        lastMessage: null,
        lastMessageTime: null,
        unreadCount: 0
      });
    }

    const conversation = this.conversations.get(convoId);
    conversation.messages.push(message);
    conversation.lastMessage = message.text;
    conversation.lastMessageTime = message.timestamp;

    if (message.isIncoming && !message.isRead) {
      conversation.unreadCount++;
    }

    // BEAST MODE: Save to persistent database
    await this.persistence.saveMessage(message);
    await this.persistence.saveConversation(conversation);

    // Also save to store for compatibility
    this.saveConversations();

    // Emit events
    this.emit('new-message', message);
    this.emit('conversations-updated', this.getConversations());
  }

  sendMessage(phoneNumber, text) {
    const message = {
      id: Date.now().toString(),
      conversationId: phoneNumber,
      contactName: this.getContactName(phoneNumber),
      phoneNumber: phoneNumber,
      text: text,
      timestamp: new Date(),
      isIncoming: false,
      isRead: true,
      attachments: []
    };
    
    this.addMessage(message);
    
    // Simulate sending (in real app, would send via iPhone)
    setTimeout(() => {
      this.emit('message-sent', message);
    }, 500);
    
    return message;
  }

  getConversations() {
    return Array.from(this.conversations.values())
      .sort((a, b) => {
        const timeA = a.lastMessageTime ? new Date(a.lastMessageTime).getTime() : 0;
        const timeB = b.lastMessageTime ? new Date(b.lastMessageTime).getTime() : 0;
        return timeB - timeA;
      });
  }

  getConversation(phoneNumber) {
    return this.conversations.get(phoneNumber);
  }

  markAsRead(phoneNumber) {
    const conversation = this.conversations.get(phoneNumber);
    if (conversation) {
      conversation.messages.forEach(msg => {
        if (msg.isIncoming) {
          msg.isRead = true;
        }
      });
      conversation.unreadCount = 0;
      this.saveConversations();
      this.emit('conversations-updated', this.getConversations());
    }
  }

  searchMessages(query) {
    const results = [];
    for (const conversation of this.conversations.values()) {
      const matchingMessages = conversation.messages.filter(msg => 
        msg.text.toLowerCase().includes(query.toLowerCase()) ||
        conversation.contactName.toLowerCase().includes(query.toLowerCase())
      );
      if (matchingMessages.length > 0) {
        results.push({
          conversation,
          messages: matchingMessages
        });
      }
    }
    return results;
  }

  getContactName(phoneNumber) {
    const conversation = this.conversations.get(phoneNumber);
    return conversation ? conversation.contactName : phoneNumber;
  }

  saveConversations() {
    const toSave = {};
    for (const [key, value] of this.conversations) {
      toSave[key] = value;
    }
    this.store.set('conversations', toSave);
  }

  // loadDemoData() REMOVED - NO MORE DEMO DATA

  // Handle messages from companion app
  handleCompanionAppMessage(data) {
    console.log('Received message from companion app:', data);

    switch (data.type) {
      case 'new_message':
        this.addRealMessage(data.message);
        break;
      case 'message_sent':
        this.handleMessageSent(data.message);
        break;
      case 'conversations_sync':
        this.syncConversationsFromCompanion(data.conversations);
        break;
      case 'read_receipt':
        this.handleReadReceipt(data.phoneNumber, data.messageIds);
        break;
      default:
        console.log('Unknown companion app message type:', data.type);
    }
  }

  // Handle AirPlay message overlay requests
  handleAirPlayMessageRequest(data) {
    if (data.action === 'show_messages') {
      // Send message overlay to AirPlay stream
      const recentMessages = this.getRecentMessages(5);
      this.sendMessageOverlay(recentMessages);
    } else if (data.action === 'send_message') {
      this.sendMessageViaAirPlay(data.phoneNumber, data.text);
    }
  }

  // Handle web bridge messages
  handleWebBridgeMessage(message) {
    console.log('Received message from web bridge:', message);
    this.addRealMessage(message);
  }

  // Add real message from iPhone
  addRealMessage(message) {
    const enhancedMessage = {
      ...message,
      id: message.id || Date.now().toString(),
      timestamp: new Date(message.timestamp || Date.now()),
      isReal: true, // Mark as real iPhone message
      source: message.source || 'companion_app'
    };

    this.addMessage(enhancedMessage);
  }

  // Send message via different methods
  async sendMessageViaIntegration(phoneNumber, text) {
    const message = {
      id: Date.now().toString(),
      conversationId: phoneNumber,
      contactName: this.getContactName(phoneNumber),
      phoneNumber: phoneNumber,
      text: text,
      timestamp: new Date(),
      isIncoming: false,
      isRead: true,
      attachments: [],
      isReal: false
    };

    // Try companion app first
    if (this.integrationMethods.companionApp && this.companionAppConnected) {
      this.sendViaCompanionApp(phoneNumber, text);
      message.isReal = true;
      message.source = 'companion_app';
    }
    // Try AirPlay integration
    else if (this.integrationMethods.airPlay) {
      this.sendViaAirPlay(phoneNumber, text);
      message.source = 'airplay';
    }
    // Try web bridge
    else if (this.integrationMethods.webBridge) {
      this.sendViaWebBridge(phoneNumber, text);
      message.source = 'web_bridge';
    }
    // Fallback to simulation
    else {
      message.source = 'simulation';
    }

    this.addMessage(message);
    return message;
  }

  sendViaCompanionApp(phoneNumber, text) {
    const messageData = {
      type: 'send_message',
      phoneNumber: phoneNumber,
      text: text,
      timestamp: Date.now()
    };

    this.connectedClients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(messageData));
      }
    });
  }

  sendViaAirPlay(phoneNumber, text) {
    if (this.airPlayServer && this.airPlayServer.reverseConnection) {
      const messageData = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>category</key>
  <string>message</string>
  <key>action</key>
  <string>send</string>
  <key>phoneNumber</key>
  <string>${phoneNumber}</string>
  <key>text</key>
  <string>${text}</string>
</dict>
</plist>`;

      try {
        this.airPlayServer.reverseConnection.write(`POST /message HTTP/1.1\r\n`);
        this.airPlayServer.reverseConnection.write(`Content-Type: text/x-apple-plist+xml\r\n`);
        this.airPlayServer.reverseConnection.write(`Content-Length: ${Buffer.byteLength(messageData)}\r\n`);
        this.airPlayServer.reverseConnection.write(`\r\n`);
        this.airPlayServer.reverseConnection.write(messageData);
      } catch (error) {
        console.error('Error sending message via AirPlay:', error);
      }
    }
  }

  sendViaWebBridge(phoneNumber, text) {
    if (this.webBridge) {
      this.webBridge.sendMessage(phoneNumber, text);
    }
  }

  // Get recent messages for overlay
  getRecentMessages(count = 5) {
    const allMessages = [];
    for (const conversation of this.conversations.values()) {
      allMessages.push(...conversation.messages);
    }

    return allMessages
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, count);
  }

  // Send message overlay to AirPlay
  sendMessageOverlay(messages) {
    if (!this.airPlayServer) return;

    const overlayData = {
      type: 'message_overlay',
      messages: messages.map(msg => ({
        contactName: msg.contactName,
        text: msg.text,
        timestamp: msg.timestamp,
        isIncoming: msg.isIncoming
      }))
    };

    this.airPlayServer.emit('overlay-data', overlayData);
  }

  // Handle message sent confirmation
  handleMessageSent(message) {
    const conversation = this.conversations.get(message.conversationId);
    if (conversation) {
      const existingMessage = conversation.messages.find(m => m.id === message.id);
      if (existingMessage) {
        existingMessage.sent = true;
        existingMessage.delivered = message.delivered || false;
        this.saveConversations();
        this.emit('message-status-updated', message);
      }
    }
  }

  // Handle read receipts
  handleReadReceipt(phoneNumber, messageIds) {
    const conversation = this.conversations.get(phoneNumber);
    if (conversation) {
      conversation.messages.forEach(msg => {
        if (messageIds.includes(msg.id)) {
          msg.isRead = true;
        }
      });
      this.saveConversations();
      this.emit('conversations-updated', this.getConversations());
    }
  }

  // Sync conversations from companion app
  syncConversationsFromCompanion(conversations) {
    let hasUpdates = false;

    conversations.forEach(conv => {
      const existing = this.conversations.get(conv.phoneNumber);
      if (!existing || existing.messages.length < conv.messages.length) {
        this.conversations.set(conv.phoneNumber, conv);
        hasUpdates = true;
      }
    });

    if (hasUpdates) {
      this.saveConversations();
      this.emit('conversations-updated', this.getConversations());
    }
  }

  // Override sendMessage to use Intel Unison++ Protocol
  async sendMessage(phoneNumber, text, contactName = null) {
    console.log(`📤 Sending message via Intel Unison++ Protocol to ${phoneNumber}: ${text}`);
    
    // First try the Unison Protocol service
    try {
      const result = await this.unisonProtocol.sendMessage(phoneNumber, text);
      if (result.success) {
        console.log('✅ Message sent successfully via Unison Protocol');
        
        // Add to local conversations
        const message = {
          threadId: phoneNumber,
          phoneNumber,
          contactName: contactName || phoneNumber,
          messageText: text,
          timestamp: new Date(),
          isOutgoing: true,
          isDelivered: true,
          isRead: true,
          hasAttachment: false,
          source: 'unison_protocol'
        };
        
        this.addMessageToConversation(message);
        this.emit('message-sent', message);
        return message;
      }
    } catch (error) {
      console.error('❌ Unison Protocol send failed:', error);
    }
    
    // Fallback to old integration method
    return this.sendMessageViaIntegration(phoneNumber, text);
  }

  // Get integration status
  getIntegrationStatus() {
    return {
      ...this.integrationMethods,
      companionAppConnected: this.companionAppConnected,
      connectedClients: this.connectedClients.size
    };
  }

  // BEAST MODE: Enhanced sync with multiple methods
  startBeastSync() {
    // Initial sync
    this.syncMessages();

    // Sync every 30 seconds to avoid spam (only when real data is available)
    this.syncInterval = setInterval(() => {
      if (this.integrationMethods.phoneLink || this.integrationMethods.companionApp) {
        this.syncMessages();
      }
    }, 30000);
  }

  // BEAST MODE: Auto-reconnect to all methods
  startAutoReconnect() {
    // Much less aggressive reconnection - every 2 minutes instead of 10 seconds
    this.autoReconnectInterval = setInterval(() => {
      this.attemptReconnections();
    }, 120000); // Try reconnecting every 2 minutes
    
    logger.sync('INFO', 'Auto-reconnect started (2-minute intervals)');
  }

  async attemptReconnections() {
    try {
      logger.sync('DEBUG', 'Attempting reconnections...');
      
      let reconnectionAttempted = false;
      
      // Try Phone Link
      if (!this.integrationMethods.phoneLink) {
        logger.sync('DEBUG', 'Attempting Phone Link reconnection');
        await this.initializePhoneLinkIntegration();
        reconnectionAttempted = true;
      }

      // Try companion app reconnection
      if (!this.companionAppConnected && !this.wsServer) {
        logger.sync('DEBUG', 'Attempting WebSocket server reconnection');
        this.initializeWebSocketServer();
        reconnectionAttempted = true;
      }

      // Only update database if we actually attempted something
      if (reconnectionAttempted) {
        await this.persistence.updateSyncStatus('auto-reconnect', 'attempted', null, 0, null);
        logger.sync('DEBUG', 'Reconnection attempts completed');
      }
    } catch (error) {
      logger.sync('ERROR', 'Error during reconnection attempt', error);
      try {
        await this.persistence.updateSyncStatus('auto-reconnect', 'failed', null, 0, error.message);
      } catch (dbError) {
        logger.database('ERROR', 'Failed to update sync status after reconnection error', dbError);
      }
    }
  }

  // BEAST MODE: Handle VM messages
  async handleVMMessages(messages) {
    console.log('📱 Processing VM messages:', messages.length);

    for (const message of messages) {
      await this.addRealMessage({
        ...message,
        source: 'vm_bridge',
        isReal: true
      });
    }

    this.integrationMethods.vmBridge = true;
    await this.persistence.updateSyncStatus('vm-bridge', 'connected');
  }

  async handleVMNewMessage(message) {
    console.log('📱 New VM message received');
    await this.addRealMessage({
      ...message,
      source: 'vm_bridge',
      isReal: true
    });
  }

  // BEAST MODE: Enhanced real message handling
  async addRealMessage(message) {
    const enhancedMessage = {
      ...message,
      id: message.id || Date.now().toString(),
      timestamp: new Date(message.timestamp || Date.now()),
      isReal: true, // Mark as real iPhone message
      source: message.source || 'companion_app'
    };

    await this.addMessage(enhancedMessage);

    // Update sync status
    await this.persistence.updateSyncStatus(message.source, 'message-received');
  }

  // Intel Unison++ Message Conversation Handler
  async addMessageToConversation(message) {
    try {
      const threadId = message.threadId || message.phoneNumber;
      
      // Create or update conversation
      let conversation = this.conversations.get(threadId);
      if (!conversation) {
        conversation = {
          threadId,
          phoneNumber: message.phoneNumber,
          contactName: message.contactName,
          messages: [],
          lastMessage: null,
          lastMessageTime: null,
          unreadCount: 0
        };
        this.conversations.set(threadId, conversation);
      }
      
      // Add message to conversation
      conversation.messages.push(message);
      conversation.lastMessage = message.messageText;
      conversation.lastMessageTime = message.timestamp;
      
      // Update unread count
      if (!message.isOutgoing && !message.isRead) {
        conversation.unreadCount++;
      }
      
      // Sort messages by timestamp
      conversation.messages.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
      
      // Save to persistence
      await this.persistence.saveMessage(message);
      
      // Emit conversation update
      this.emit('conversations-updated', this.getConversations());
      
      console.log(`📬 Added message to conversation: ${message.contactName} - ${message.messageText.substring(0, 50)}...`);
    } catch (error) {
      console.error('❌ Error adding message to conversation:', error);
    }
  }

  stop() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    if (this.autoReconnectInterval) {
      clearInterval(this.autoReconnectInterval);
    }

    if (this.wsServer) {
      this.wsServer.close();
    }

    if (this.persistence) {
      this.persistence.close();
    }

    this.connectedClients.clear();
  }
}

module.exports = { MessageService };