<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>iPhone Companion Pro</title>
    <!-- Professional Polish CSS -->
    <link rel="stylesheet" href="../styles/professional-polish.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            margin: 0;
            padding: 0;
            user-select: none;
            overflow: hidden;
        }

        .titlebar {
            height: 40px;
            background: rgba(20, 20, 20, 0.95);
            backdrop-filter: blur(20px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            -webkit-app-region: drag;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .titlebar-title {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .titlebar-title::before {
            content: "📱";
            font-size: 16px;
        }

        .titlebar-controls {
            display: flex;
            gap: 8px;
            -webkit-app-region: no-drag;
        }

        .titlebar-button {
            width: 40px;
            height: 28px;
            border: none;
            background: transparent;
            color: #999;
            cursor: pointer;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .titlebar-button:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
        }
        
        .main-container {
            display: flex;
            height: calc(100vh - 40px);
        }

        .sidebar {
            width: 320px;
            background: rgba(20, 20, 20, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 24px;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .content {
            flex: 1;
            padding: 32px;
            background: rgba(10, 10, 10, 0.5);
            overflow-y: auto;
        }
        
        .connect-btn {
            width: 100%;
            padding: 16px 20px;
            background: linear-gradient(135deg, #007AFF 0%, #0051D5 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
        }

        .connect-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 122, 255, 0.4);
        }

        .connect-btn:disabled {
            background: linear-gradient(135deg, #555 0%, #444 100%);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .connect-btn.connecting {
            background: linear-gradient(135deg, #FF9500 0%, #FF6B00 100%);
            animation: pulse 2s infinite;
        }

        .connect-btn.connected {
            background: linear-gradient(135deg, #34C759 0%, #30A14E 100%);
        }

        @keyframes pulse {
            0% { box-shadow: 0 4px 20px rgba(255, 149, 0, 0.3); }
            50% { box-shadow: 0 8px 30px rgba(255, 149, 0, 0.6); }
            100% { box-shadow: 0 4px 20px rgba(255, 149, 0, 0.3); }
        }

        .connection-methods-grid {
            display: grid;
            gap: 12px;
        }

        .method-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .method-item:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(0, 122, 255, 0.3);
        }

        .method-item.available {
            border-color: rgba(52, 199, 89, 0.5);
            background: rgba(52, 199, 89, 0.1);
        }

        .method-item.connected {
            border-color: rgba(52, 199, 89, 0.8);
            background: rgba(52, 199, 89, 0.2);
            box-shadow: 0 0 20px rgba(52, 199, 89, 0.3);
        }

        .method-item.failed {
            border-color: rgba(255, 59, 48, 0.5);
            background: rgba(255, 59, 48, 0.1);
        }

        .method-icon {
            font-size: 20px;
            width: 32px;
            text-align: center;
        }

        .method-info {
            flex: 1;
        }

        .method-name {
            font-size: 14px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 2px;
        }

        .method-status {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
        }

        .method-item.available .method-status {
            color: rgba(52, 199, 89, 0.9);
        }

        .method-item.connected .method-status {
            color: rgba(52, 199, 89, 1);
            font-weight: 600;
        }

        .method-item.failed .method-status {
            color: rgba(255, 59, 48, 0.9);
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .status-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #007AFF 0%, #00D4FF 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .status-subtitle {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
        }
        
        h1 {
            font-size: 36px;
            font-weight: 800;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #ffffff 0%, #cccccc 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .subtitle {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 40px;
            font-weight: 500;
        }
        
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 24px;
            margin-top: 32px;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 28px;
            backdrop-filter: blur(20px);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.2);
        }
        
        .card h3 {
            margin: 0 0 20px 0;
            font-size: 20px;
        }
        
        .device-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        
        .info-value {
            font-size: 16px;
            font-weight: 500;
        }
        
        .battery-container {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .battery-visual {
            width: 100px;
            height: 40px;
            border: 3px solid #333;
            border-radius: 4px;
            position: relative;
            background: #1a1a1a;
        }
        
        .battery-fill {
            height: 100%;
            background: #4CAF50;
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 16px;
        }

        .action-button {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 24px 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            color: #ffffff;
            text-decoration: none;
        }

        .action-button:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-4px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .action-icon {
            font-size: 32px;
            margin-bottom: 12px;
            display: block;
        }

        .action-label {
            font-size: 14px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-indicator {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background: #666;
            box-shadow: 0 0 10px rgba(102, 102, 102, 0.3);
        }

        .status-indicator.connected {
            background: #4CAF50;
            animation: pulse-green 2s infinite;
            box-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
        }

        .status-indicator.connecting {
            background: #FF9800;
            animation: pulse-orange 1s infinite;
            box-shadow: 0 0 15px rgba(255, 152, 0, 0.5);
        }

        @keyframes pulse-green {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        @keyframes pulse-orange {
            0% { opacity: 1; }
            50% { opacity: 0.3; }
            100% { opacity: 1; }
        }

        .airplay-status {
            background: #1a1a1a;
            border: 1px solid #2a2a2a;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .airplay-status h4 {
            margin: 0 0 10px 0;
            color: #007AFF;
        }

        .notification-panel {
            background: #1a1a1a;
            border: 1px solid #2a2a2a;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
        }

        .notification-item {
            padding: 10px;
            border-bottom: 1px solid #2a2a2a;
            cursor: pointer;
            transition: background 0.2s;
        }

        .notification-item:hover {
            background: #2a2a2a;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-title {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .notification-body {
            font-size: 12px;
            color: #999;
        }

        .notification-time {
            font-size: 10px;
            color: #666;
            float: right;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .stat-value {
            font-size: 28px;
            font-weight: 800;
            background: linear-gradient(135deg, #007AFF 0%, #00D4FF 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 6px;
            font-weight: 500;
        }

        .feature-status {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.05);
            transition: all 0.3s ease;
        }

        .feature-status:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .feature-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .feature-icon.enabled {
            background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .feature-icon.disabled {
            background: linear-gradient(135deg, #666 0%, #555 100%);
        }

        .feature-info {
            flex: 1;
        }

        .feature-name {
            font-weight: 600;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }

        .feature-desc {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
            margin-top: 2px;
        }
    </style>
</head>
<body>
    <!-- Title Bar -->
    <div class="titlebar">
        <div class="titlebar-title">iPhone Companion Pro</div>
        <div class="titlebar-controls">
            <button class="titlebar-button" onclick="require('electron').ipcRenderer.send('minimize-window')">−</button>
            <button class="titlebar-button" onclick="require('electron').ipcRenderer.send('maximize-window')">□</button>
            <button class="titlebar-button" onclick="require('electron').ipcRenderer.send('close-window')">×</button>
        </div>
    </div>

    <!-- Main Layout -->
    <div class="main-container">
        <div class="sidebar">
            <div class="status-card">
                <div class="status-title" id="status-text">BEAST MODE</div>
                <div class="status-subtitle" id="status-subtitle">Ready for iPhone connection</div>
            </div>

            <button class="connect-btn" id="connect-btn">
                🚀 Connect iPhone
            </button>

            <div class="status-card">
                <h4 style="margin-bottom: 16px; font-weight: 600; color: rgba(255, 255, 255, 0.9);">Connection Status</h4>
                <div class="connection-status">
                    <div class="status-indicator" id="connection-indicator"></div>
                    <div>
                        <div style="font-size: 12px; color: rgba(255, 255, 255, 0.6);">Status</div>
                        <div style="font-size: 14px; font-weight: 600;" id="connection-type">Waiting for iPhone</div>
                    </div>
                </div>
            </div>

            <div class="status-card">
                <h4 style="margin-bottom: 16px; font-weight: 600; color: rgba(255, 255, 255, 0.9);">Real iPhone Connection Methods</h4>
                <div class="connection-methods-grid">
                    <div class="method-item" id="airplay-method">
                        <span class="method-icon">🎥</span>
                        <div class="method-info">
                            <div class="method-name">AirPlay Screen Mirror</div>
                            <div class="method-status" id="airplay-status">Checking...</div>
                        </div>
                    </div>
                    <div class="method-item" id="phonelink-method">
                        <span class="method-icon">📱</span>
                        <div class="method-info">
                            <div class="method-name">Windows Phone Link</div>
                            <div class="method-status" id="phonelink-status">Checking...</div>
                        </div>
                    </div>
                    <div class="method-item" id="usb-method">
                        <span class="method-icon">🔗</span>
                        <div class="method-info">
                            <div class="method-name">USB Connection</div>
                            <div class="method-status" id="usb-status">Checking...</div>
                        </div>
                    </div>
                    <div class="method-item" id="macosvm-method">
                        <span class="method-icon">🖥️</span>
                        <div class="method-info">
                            <div class="method-name">macOS VM Bridge</div>
                            <div class="method-status" id="macosvm-status">Checking...</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="status-card">
                <h4 style="margin-bottom: 16px; font-weight: 600; color: rgba(255, 255, 255, 0.9);">Live Stats</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="message-count">0</div>
                        <div class="stat-label">Messages</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="call-count">0</div>
                        <div class="stat-label">Calls</div>
                    </div>
                </div>
            </div>

            <div class="status-card">
                <h4 style="margin-bottom: 16px; font-weight: 600; color: rgba(255, 255, 255, 0.9);">Integration Methods</h4>

                <div class="feature-status">
                    <div class="feature-icon disabled" id="messages-icon">💬</div>
                    <div class="feature-info">
                        <div class="feature-name">Messages</div>
                        <div class="feature-desc" id="messages-status">Standby</div>
                    </div>
                </div>

                <div class="feature-status">
                    <div class="feature-icon disabled" id="calls-icon">📞</div>
                    <div class="feature-info">
                        <div class="feature-name">Calls</div>
                        <div class="feature-desc" id="calls-status">Standby</div>
                    </div>
                </div>

                <div class="feature-status">
                    <div class="feature-icon disabled" id="airplay-icon">📱</div>
                    <div class="feature-info">
                        <div class="feature-name">AirPlay Mirror</div>
                        <div class="feature-desc" id="mirror-status">Ready</div>
                    </div>
                </div>

                <div class="feature-status">
                    <div class="feature-icon disabled" id="vm-icon">🖥️</div>
                    <div class="feature-info">
                        <div class="feature-name">VM Bridge</div>
                        <div class="feature-desc" id="vm-status">Available</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="content">
            <h1>iPhone Companion Pro</h1>
            <p class="subtitle">Professional iPhone mirroring and control for Windows</p>
            
            <div class="cards-grid">
                <!-- Device Info Card -->
                <div class="card">
                    <h3>Device Information</h3>
                    <div class="device-info-grid">
                        <div class="info-item">
                            <span class="info-label">Device</span>
                            <span class="info-value" id="device-name">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Model</span>
                            <span class="info-value" id="device-model">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">iOS Version</span>
                            <span class="info-value" id="device-ios">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Storage</span>
                            <span class="info-value" id="device-storage">-</span>
                        </div>
                    </div>
                </div>
                
                <!-- Battery Card -->
                <div class="card">
                    <h3>Battery</h3>
                    <div class="battery-container">
                        <div class="battery-visual">
                            <div class="battery-fill" id="battery-fill" style="width: 0%"></div>
                        </div>
                        <div>
                            <div style="font-size: 24px; font-weight: bold;" id="battery-level">-</div>
                            <div style="font-size: 14px; color: #999;" id="battery-status">Unknown</div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions Card -->
                <div class="card" style="grid-column: span 2;">
                    <h3 style="margin-bottom: 24px; font-size: 24px; font-weight: 700;">iPhone Control Center</h3>
                    <div class="quick-actions">
                        <div class="action-button" onclick="startAirPlayMirror()">
                            <div class="action-icon">📱</div>
                            <div class="action-label">Screen Mirror</div>
                        </div>
                        <div class="action-button" onclick="require('electron').ipcRenderer.invoke('open-messages')">
                            <div class="action-icon">💬</div>
                            <div class="action-label">Messages</div>
                        </div>
                        <div class="action-button" onclick="require('electron').ipcRenderer.invoke('open-calls')">
                            <div class="action-icon">📞</div>
                            <div class="action-label">Calls</div>
                        </div>
                        <div class="action-button" onclick="require('electron').ipcRenderer.invoke('open-contacts')">
                            <div class="action-icon">👥</div>
                            <div class="action-label">Contacts</div>
                        </div>
                        <div class="action-button" onclick="openPhotoManager()">
                            <div class="action-icon">📷</div>
                            <div class="action-label">Photos</div>
                        </div>
                        <div class="action-button" onclick="openFileManager()">
                            <div class="action-icon">📁</div>
                            <div class="action-label">Files</div>
                        </div>
                        <div class="action-button" onclick="openSettings()">
                            <div class="action-icon">⚙️</div>
                            <div class="action-label">Settings</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include the app script -->
    <script src="../scripts/app.js"></script>

    <script>
    async function startAirPlayMirror() {
      const result = await require('electron').ipcRenderer.invoke('start-mirror');

      if (result.success) {
        // Show instructions
        const modal = document.createElement('div');
        modal.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0,0,0,0.9);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        `;

        modal.innerHTML = `
          <div style="background: #1a1a1a; padding: 40px; border-radius: 20px; text-align: center; max-width: 500px;">
            <h2 style="margin-bottom: 30px;">📱 AirPlay Screen Mirroring</h2>

            <div style="text-align: left; background: #2a2a2a; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
              <h3>Instructions:</h3>
              <ol style="margin: 10px 0; padding-left: 20px;">
                ${result.instructions.map(step => `<li style="margin: 10px 0;">${step}</li>`).join('')}
              </ol>
            </div>

            <p style="color: #007AFF; margin-bottom: 20px;">The mirror window is now open and waiting for your iPhone!</p>

            <button onclick="this.parentElement.parentElement.remove()" style="
              padding: 10px 30px;
              background: #007AFF;
              color: white;
              border: none;
              border-radius: 5px;
              cursor: pointer;
              font-size: 16px;
            ">Got it!</button>
          </div>
        `;

        document.body.appendChild(modal);
      } else {
        alert('Failed to start AirPlay: ' + result.error);
      }
    }

    // Additional iPhone control functions
    function openCallManager() {
      // Create call manager window
      showFeatureModal('📞 Call Manager', [
        'Make and receive calls through your iPhone',
        'View call history and contacts',
        'Manage voicemail and call settings',
        'Full integration with iPhone Phone app'
      ], 'This feature requires the companion iOS app to be installed.');
    }

    function openPhotoManager() {
      showFeatureModal('📷 Photo Manager', [
        'View and manage iPhone photos',
        'Transfer photos to PC automatically',
        'Access all photo albums and memories',
        'Edit and organize photos'
      ], 'Photo access requires iPhone to be connected via AirPlay or companion app.');
    }

    function openClipboardSync() {
      showFeatureModal('📋 Clipboard Sync', [
        'Sync clipboard between iPhone and PC',
        'Copy text, images, and files seamlessly',
        'Universal clipboard functionality',
        'Secure end-to-end encryption'
      ], 'Clipboard sync works automatically when iPhone is connected.');
    }

    function findMyiPhone() {
      showFeatureModal('📍 Find My iPhone', [
        'Locate your iPhone on a map',
        'Play sound to find nearby iPhone',
        'Enable lost mode remotely',
        'View battery level and last location'
      ], 'This feature integrates with Apple\'s Find My network.');
    }

    function toggleNotifications() {
      const panel = document.getElementById('notification-panel');
      if (panel.style.display === 'none') {
        panel.style.display = 'block';
        loadNotifications();
      } else {
        panel.style.display = 'none';
      }
    }

    function loadNotifications() {
      const list = document.getElementById('notifications-list');

      // Sample notifications
      const notifications = [
        { title: 'New Message', body: 'Mom: How was your day?', time: '2 min ago' },
        { title: 'Missed Call', body: 'John Smith', time: '5 min ago' },
        { title: 'Calendar', body: 'Meeting in 15 minutes', time: '10 min ago' }
      ];

      list.innerHTML = '';
      notifications.forEach(notif => {
        const item = document.createElement('div');
        item.className = 'notification-item';
        item.innerHTML = `
          <div class="notification-time">${notif.time}</div>
          <div class="notification-title">${notif.title}</div>
          <div class="notification-body">${notif.body}</div>
        `;
        list.appendChild(item);
      });
    }

    function openFileManager() {
      showFeatureModal('📁 File Manager', [
        'Access iPhone files and documents',
        'Transfer files between iPhone and PC',
        'Manage app documents and data',
        'Backup and restore capabilities'
      ], 'File access requires companion app for security compliance.');
    }

    function showFeatureModal(title, features, note) {
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
      `;

      modal.innerHTML = `
        <div style="background: #1a1a1a; padding: 40px; border-radius: 20px; text-align: center; max-width: 500px;">
          <h2 style="margin-bottom: 30px;">${title}</h2>

          <div style="text-align: left; background: #2a2a2a; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
            <h3>Features:</h3>
            <ul style="margin: 10px 0; padding-left: 20px;">
              ${features.map(feature => `<li style="margin: 10px 0;">${feature}</li>`).join('')}
            </ul>
          </div>

          <p style="color: #007AFF; margin-bottom: 20px; font-style: italic;">${note}</p>

          <div style="display: flex; gap: 15px; justify-content: center;">
            <button onclick="this.parentElement.parentElement.parentElement.remove()" style="
              padding: 10px 30px;
              background: #666;
              color: white;
              border: none;
              border-radius: 5px;
              cursor: pointer;
              font-size: 16px;
            ">Close</button>
            <button onclick="alert('Feature coming soon! Install companion iOS app for full functionality.')" style="
              padding: 10px 30px;
              background: #007AFF;
              color: white;
              border: none;
              border-radius: 5px;
              cursor: pointer;
              font-size: 16px;
            ">Learn More</button>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
    }

    function openSettings() {
      showSettingsModal();
    }

    function showSettingsModal() {
      const modal = document.createElement('div');
      modal.className = 'modal-overlay';
      modal.innerHTML = `
        <div class="modal-content">
          <div class="modal-header">
            <h2>⚙️ Settings</h2>
            <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">✕</button>
          </div>
          <div class="modal-body">
            <div class="settings-grid">
              <div class="setting-item" onclick="openGeneralSettings()">
                <div class="setting-icon">🔧</div>
                <div class="setting-info">
                  <h3>General Settings</h3>
                  <p>Configure connection preferences and interface options</p>
                </div>
              </div>
              <div class="setting-item" onclick="openVMBridgeSettings()">
                <div class="setting-icon">🖥️</div>
                <div class="setting-info">
                  <h3>macOS VM Bridge</h3>
                  <p>Advanced iPhone integration through macOS virtualization</p>
                </div>
              </div>
              <div class="setting-item" onclick="openNotificationSettings()">
                <div class="setting-icon">🔔</div>
                <div class="setting-info">
                  <h3>Notifications</h3>
                  <p>Manage notification settings and preferences</p>
                </div>
              </div>
              <div class="setting-item" onclick="openPerformanceSettings()">
                <div class="setting-icon">⚡</div>
                <div class="setting-info">
                  <h3>Performance</h3>
                  <p>Optimize performance and resource usage</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
    }

    function openVMBridgeSettings() {
      // Close current modal
      const modal = document.querySelector('.modal-overlay');
      if (modal) modal.remove();

      // Open VM Bridge configuration window
      const { ipcRenderer } = require('electron');
      const { BrowserWindow } = require('@electron/remote');

      const vmBridgeWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 1000,
        minHeight: 600,
        frame: false,
        backgroundColor: '#0a0a0a',
        webPreferences: {
          nodeIntegration: true,
          contextIsolation: false
        }
      });

      vmBridgeWindow.loadFile('src/renderer/vm-bridge.html');
    }

    function openGeneralSettings() {
      showFeatureModal('🔧 General Settings', [
        'Configure connection preferences',
        'Manage automatic features',
        'Customize interface options',
        'Set up backup and sync'
      ], 'General settings allow you to personalize your iPhone Companion Pro experience.');
    }

    function openNotificationSettings() {
      showFeatureModal('🔔 Notification Settings', [
        'Configure notification types',
        'Set notification sounds',
        'Manage do not disturb',
        'Customize notification appearance'
      ], 'Manage how notifications are displayed and handled.');
    }

    function openPerformanceSettings() {
      showFeatureModal('⚡ Performance Settings', [
        'Optimize resource usage',
        'Configure quality settings',
        'Manage background processes',
        'Monitor system performance'
      ], 'Optimize iPhone Companion Pro for your system.');
    }

    function showConnectionQR() {
      // Generate QR code for iPhone connection
      const qrData = `iphone-companion://connect?host=${window.location.hostname}&port=8080`;

      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
      `;

      modal.innerHTML = `
        <div style="background: #1a1a1a; padding: 40px; border-radius: 20px; text-align: center; max-width: 500px;">
          <h2 style="margin-bottom: 30px;">📲 Connect Your iPhone</h2>

          <div style="background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
            <div id="qr-code" style="width: 200px; height: 200px; margin: 0 auto; background: #f0f0f0; display: flex; align-items: center; justify-content: center; border-radius: 10px;">
              <div style="color: #666; font-size: 14px;">QR Code<br>Would appear here</div>
            </div>
          </div>

          <div style="text-align: left; background: #2a2a2a; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
            <h3>Instructions:</h3>
            <ol style="margin: 10px 0; padding-left: 20px;">
              <li style="margin: 10px 0;">Install iPhone Companion Pro app on your iPhone</li>
              <li style="margin: 10px 0;">Open the app and tap "Scan QR Code"</li>
              <li style="margin: 10px 0;">Scan this QR code with your iPhone</li>
              <li style="margin: 10px 0;">Grant necessary permissions when prompted</li>
              <li style="margin: 10px 0;">Your iPhone will connect automatically</li>
            </ol>
          </div>

          <p style="color: #007AFF; margin-bottom: 20px;">Keep both devices on the same WiFi network</p>

          <button onclick="this.parentElement.parentElement.remove()" style="
            padding: 10px 30px;
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
          ">Close</button>
        </div>
      `;

      document.body.appendChild(modal);
    }

    // Update status indicators
    function updateFeatureStatus(feature, enabled, status) {
      const icon = document.getElementById(`${feature}-icon`);
      const statusText = document.getElementById(`${feature}-status`);

      if (icon) {
        icon.className = `feature-icon ${enabled ? 'enabled' : 'disabled'}`;
      }

      if (statusText) {
        statusText.textContent = status;
      }
    }

    // Update statistics
    function updateStats() {
      // These would be updated with real data from the backend
      document.getElementById('message-count').textContent = Math.floor(Math.random() * 50);
      document.getElementById('call-count').textContent = Math.floor(Math.random() * 10);
    }

    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', () => {
      updateStats();

      // Update stats every 30 seconds
      setInterval(updateStats, 30000);

      // Simulate feature status updates
      setTimeout(() => {
        updateFeatureStatus('messages', true, 'Connected via companion app');
        updateFeatureStatus('calls', true, 'Ready for calls');
      }, 3000);
    });
    </script>

    <!-- Shortcuts Setup CSS -->
    <link rel="stylesheet" href="styles/shortcuts-setup.css">

    <!-- Shortcuts Setup Component -->
    <script src="components/ShortcutsSetup.js"></script>

    <script>
    // Initialize shortcuts setup
    let shortcutsSetup = null;

    // Add shortcuts setup button to the interface
    function addShortcutsButton() {
      const connectSection = document.querySelector('.connect-section');
      if (connectSection) {
        const shortcutsBtn = document.createElement('button');
        shortcutsBtn.className = 'connect-btn shortcuts-btn';
        shortcutsBtn.innerHTML = '🔗 iOS Shortcuts Setup';
        shortcutsBtn.onclick = () => {
          if (!shortcutsSetup) {
            shortcutsSetup = new ShortcutsSetup();
          }
          shortcutsSetup.show();
        };
        connectSection.appendChild(shortcutsBtn);
      }
    }

    // Add shortcuts button when DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(addShortcutsButton, 1000);
    });
    </script>

<script>
function openMessages() {
  console.log('Opening messages...');
  require('electron').ipcRenderer.invoke('open-messages');
}

function openCalls() {
  console.log('Opening calls...');
  require('electron').ipcRenderer.invoke('open-calls');
}

function openContacts() {
  console.log('Opening contacts...');
  require('electron').ipcRenderer.invoke('open-contacts');
}

function openScreenMirror() {
  console.log('Opening screen mirror...');
  require('electron').ipcRenderer.invoke('open-mirror');
}

function openPhotos() {
  console.log('Opening photos...');
  require('electron').ipcRenderer.invoke('open-photos');
}

function openFiles() {
  console.log('Opening files...');
  require('electron').ipcRenderer.invoke('open-files');
}

function openSettings() {
  console.log('Opening settings...');
  require('electron').ipcRenderer.invoke('open-settings');
}
</script>

</body>
</html>