const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

/**
 * Intel Unison Database Implementation
 * Matches Intel Unison's exact database schema for full compatibility
 */
class Database {
  constructor() {
    this.db = null;
    this.isInitialized = false;
    this.dbPath = null;
    
    // Database configuration
    this.DB_VERSION = 1;
    this.BACKUP_INTERVAL = 30 * 60 * 1000; // 30 minutes
    
    this.setupDatabasePath();
  }

  setupDatabasePath() {
    // Use the same path structure as Intel Unison
    const userDataPath = global.app ? global.app.getPath('userData') : 
                         path.join(require('os').homedir(), '.intel-unison-companion');
    
    if (!fs.existsSync(userDataPath)) {
      fs.mkdirSync(userDataPath, { recursive: true });
    }
    
    this.dbPath = path.join(userDataPath, 'messages.db');
    console.log(`💾 Database path: ${this.dbPath}`);
  }

  async initialize() {
    console.log('💾 Initializing Intel Unison Database...');
    
    try {
      // Open SQLite database
      await this.openDatabase();
      
      // Create tables with Intel Unison schema
      await this.createTables();
      
      // Set up indices for performance
      await this.createIndices();
      
      // Enable WAL mode for better performance
      await this.enableWALMode();
      
      // Set up automatic backups
      this.setupBackups();
      
      this.isInitialized = true;
      console.log('✅ Database initialized successfully');
      
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      throw error;
    }
  }

  async openDatabase() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE, (error) => {
        if (error) {
          reject(error);
        } else {
          console.log(`✅ Database opened: ${this.dbPath}`);
          resolve();
        }
      });
    });
  }

  async createTables() {
    console.log('📋 Creating Intel Unison database schema...');
    
    const tables = [
      // Messages table (exactly like Intel Unison)
      `CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY,
        thread_id TEXT NOT NULL,
        sender TEXT NOT NULL,
        recipients TEXT NOT NULL, -- JSON array
        body TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        is_read INTEGER DEFAULT 0,
        is_delivered INTEGER DEFAULT 0,
        is_sent INTEGER DEFAULT 0,
        message_type TEXT DEFAULT 'sms', -- sms, imessage, etc.
        attachments TEXT, -- JSON array
        metadata TEXT, -- JSON object
        created_at INTEGER DEFAULT (strftime('%s', 'now')),
        updated_at INTEGER DEFAULT (strftime('%s', 'now'))
      )`,
      
      // Conversations table
      `CREATE TABLE IF NOT EXISTS conversations (
        thread_id TEXT PRIMARY KEY,
        participants TEXT NOT NULL, -- JSON array
        last_message_id TEXT,
        last_message_text TEXT,
        last_message_time INTEGER,
        unread_count INTEGER DEFAULT 0,
        is_muted INTEGER DEFAULT 0,
        is_archived INTEGER DEFAULT 0,
        created_at INTEGER DEFAULT (strftime('%s', 'now')),
        updated_at INTEGER DEFAULT (strftime('%s', 'now')),
        FOREIGN KEY (last_message_id) REFERENCES messages(id)
      )`,
      
      // Contacts table
      `CREATE TABLE IF NOT EXISTS contacts (
        phone_number TEXT PRIMARY KEY,
        name TEXT,
        display_name TEXT,
        photo_url TEXT,
        is_favorite INTEGER DEFAULT 0,
        metadata TEXT, -- JSON
        created_at INTEGER DEFAULT (strftime('%s', 'now')),
        updated_at INTEGER DEFAULT (strftime('%s', 'now'))
      )`,
      
      // Call history
      `CREATE TABLE IF NOT EXISTS calls (
        id TEXT PRIMARY KEY,
        phone_number TEXT NOT NULL,
        contact_name TEXT,
        call_type TEXT CHECK(call_type IN ('incoming', 'outgoing', 'missed')),
        duration INTEGER DEFAULT 0,
        timestamp INTEGER NOT NULL,
        is_video INTEGER DEFAULT 0,
        metadata TEXT,
        created_at INTEGER DEFAULT (strftime('%s', 'now'))
      )`,
      
      // Failed messages for retry
      `CREATE TABLE IF NOT EXISTS failed_messages (
        id TEXT PRIMARY KEY,
        phone_number TEXT NOT NULL,
        message_text TEXT NOT NULL,
        error_message TEXT,
        retry_count INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 3,
        timestamp INTEGER NOT NULL,
        last_retry INTEGER,
        status TEXT DEFAULT 'pending' -- pending, retrying, failed, resolved
      )`,
      
      // Sync state tracking
      `CREATE TABLE IF NOT EXISTS sync_state (
        key TEXT PRIMARY KEY,
        value TEXT,
        timestamp INTEGER DEFAULT (strftime('%s', 'now'))
      )`,
      
      // Message search FTS table
      `CREATE VIRTUAL TABLE IF NOT EXISTS messages_fts USING fts5(
        id,
        body,
        sender,
        recipients,
        content='messages',
        content_rowid='rowid'
      )`
    ];
    
    for (const tableSQL of tables) {
      await this.runQuery(tableSQL);
    }
    
    console.log('✅ Database tables created');
  }

  async createIndices() {
    console.log('📊 Creating database indices...');
    
    const indices = [
      'CREATE INDEX IF NOT EXISTS idx_messages_thread_id ON messages(thread_id)',
      'CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp DESC)',
      'CREATE INDEX IF NOT EXISTS idx_messages_sender ON messages(sender)',
      'CREATE INDEX IF NOT EXISTS idx_messages_is_read ON messages(is_read)',
      'CREATE INDEX IF NOT EXISTS idx_conversations_last_message_time ON conversations(last_message_time DESC)',
      'CREATE INDEX IF NOT EXISTS idx_contacts_name ON contacts(name)',
      'CREATE INDEX IF NOT EXISTS idx_calls_timestamp ON calls(timestamp DESC)',
      'CREATE INDEX IF NOT EXISTS idx_calls_phone_number ON calls(phone_number)',
      'CREATE INDEX IF NOT EXISTS idx_failed_messages_status ON failed_messages(status)'
    ];
    
    for (const indexSQL of indices) {
      await this.runQuery(indexSQL);
    }
    
    console.log('✅ Database indices created');
  }

  async enableWALMode() {
    console.log('⚡ Enabling WAL mode for better performance...');
    
    await this.runQuery('PRAGMA journal_mode = WAL');
    await this.runQuery('PRAGMA synchronous = NORMAL');
    await this.runQuery('PRAGMA cache_size = 10000');
    await this.runQuery('PRAGMA temp_store = memory');
    
    console.log('✅ WAL mode enabled');
  }

  async storeMessage(messageData) {
    console.log(`💾 Storing message: ${messageData.id}`);
    
    try {
      // Insert message
      await this.runQuery(
        `INSERT OR REPLACE INTO messages 
         (id, thread_id, sender, recipients, body, timestamp, is_read, is_delivered, is_sent, message_type, attachments, metadata)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          messageData.id,
          messageData.thread_id,
          messageData.sender,
          messageData.recipients,
          messageData.body,
          messageData.timestamp,
          messageData.is_read,
          messageData.is_delivered,
          messageData.is_sent,
          messageData.message_type || 'sms',
          messageData.attachments,
          messageData.metadata
        ]
      );
      
      // Update conversation
      await this.updateConversation(messageData);
      
      // Update FTS index
      await this.updateFTSIndex(messageData);
      
      console.log(`✅ Message stored: ${messageData.id}`);
      
    } catch (error) {
      console.error(`❌ Failed to store message ${messageData.id}:`, error);
      throw error;
    }
  }

  async updateConversation(messageData) {
    const participants = typeof messageData.recipients === 'string' 
      ? messageData.recipients 
      : JSON.stringify(messageData.recipients);
    
    await this.runQuery(
      `INSERT OR REPLACE INTO conversations 
       (thread_id, participants, last_message_id, last_message_text, last_message_time, updated_at)
       VALUES (?, ?, ?, ?, ?, strftime('%s', 'now'))`,
      [
        messageData.thread_id,
        participants,
        messageData.id,
        messageData.body.substring(0, 100), // Truncate for preview
        messageData.timestamp
      ]
    );
    
    // Update unread count if it's an incoming message
    if (!messageData.is_sent && !messageData.is_read) {
      await this.runQuery(
        `UPDATE conversations 
         SET unread_count = unread_count + 1
         WHERE thread_id = ?`,
        [messageData.thread_id]
      );
    }
  }

  async updateFTSIndex(messageData) {
    try {
      await this.runQuery(
        `INSERT INTO messages_fts (id, body, sender, recipients)
         VALUES (?, ?, ?, ?)`,
        [
          messageData.id,
          messageData.body,
          messageData.sender,
          messageData.recipients
        ]
      );
    } catch (error) {
      // FTS errors are non-critical
      console.warn(`⚠️ FTS index update failed for ${messageData.id}:`, error.message);
    }
  }

  async getMessage(messageId) {
    const result = await this.getQuery(
      'SELECT * FROM messages WHERE id = ?',
      [messageId]
    );
    
    return result;
  }

  async getRecentMessages(limit = 100) {
    const results = await this.allQuery(
      `SELECT * FROM messages 
       ORDER BY timestamp DESC 
       LIMIT ?`,
      [limit]
    );
    
    return results;
  }

  async getConversationMessages(threadId, limit = 50) {
    const results = await this.allQuery(
      `SELECT * FROM messages 
       WHERE thread_id = ? 
       ORDER BY timestamp DESC 
       LIMIT ?`,
      [threadId, limit]
    );
    
    return results.reverse(); // Reverse to get chronological order
  }

  async getConversations(limit = 100) {
    const results = await this.allQuery(
      `SELECT c.*, 
              COUNT(CASE WHEN m.is_read = 0 AND m.is_sent = 0 THEN 1 END) as unread_count
       FROM conversations c
       LEFT JOIN messages m ON c.thread_id = m.thread_id
       GROUP BY c.thread_id
       ORDER BY c.last_message_time DESC
       LIMIT ?`,
      [limit]
    );
    
    return results;
  }

  async searchMessages(query, options = {}) {
    console.log(`🔍 Searching messages: "${query}"`);
    
    try {
      const limit = options.limit || 50;
      
      // Use FTS for text search
      const results = await this.allQuery(
        `SELECT m.* FROM messages_fts fts
         JOIN messages m ON fts.id = m.id
         WHERE messages_fts MATCH ?
         ORDER BY m.timestamp DESC
         LIMIT ?`,
        [query, limit]
      );
      
      console.log(`🔍 Found ${results.length} messages matching "${query}"`);
      return results;
      
    } catch (error) {
      console.error('❌ Message search failed:', error);
      
      // Fallback to LIKE search
      const results = await this.allQuery(
        `SELECT * FROM messages 
         WHERE body LIKE ? 
         ORDER BY timestamp DESC 
         LIMIT ?`,
        [`%${query}%`, options.limit || 50]
      );
      
      return results;
    }
  }

  async storeContact(contactData) {
    await this.runQuery(
      `INSERT OR REPLACE INTO contacts 
       (phone_number, name, display_name, photo_url, is_favorite, metadata, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, strftime('%s', 'now'))`,
      [
        contactData.phone_number,
        contactData.name,
        contactData.display_name,
        contactData.photo_url,
        contactData.is_favorite ? 1 : 0,
        contactData.metadata
      ]
    );
  }

  async getContact(phoneNumber) {
    return await this.getQuery(
      'SELECT * FROM contacts WHERE phone_number = ?',
      [phoneNumber]
    );
  }

  async storeCall(callData) {
    await this.runQuery(
      `INSERT OR REPLACE INTO calls 
       (id, phone_number, contact_name, call_type, duration, timestamp, is_video, metadata)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        callData.id,
        callData.phone_number,
        callData.contact_name,
        callData.call_type,
        callData.duration,
        callData.timestamp,
        callData.is_video ? 1 : 0,
        callData.metadata
      ]
    );
  }

  async getCallHistory(limit = 100) {
    return await this.allQuery(
      `SELECT * FROM calls 
       ORDER BY timestamp DESC 
       LIMIT ?`,
      [limit]
    );
  }

  async storeFailedMessage(failedData) {
    await this.runQuery(
      `INSERT OR REPLACE INTO failed_messages 
       (id, phone_number, message_text, error_message, retry_count, timestamp, status)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        failedData.id,
        failedData.phone_number,
        failedData.message_text,
        failedData.error_message,
        failedData.retry_count || 0,
        failedData.timestamp,
        failedData.status || 'pending'
      ]
    );
  }

  async getFailedMessages() {
    return await this.allQuery(
      `SELECT * FROM failed_messages 
       WHERE status IN ('pending', 'retrying')
       ORDER BY timestamp ASC`
    );
  }

  async setSyncState(key, value) {
    await this.runQuery(
      `INSERT OR REPLACE INTO sync_state (key, value, timestamp)
       VALUES (?, ?, strftime('%s', 'now'))`,
      [key, JSON.stringify(value)]
    );
  }

  async getSyncState(key) {
    const result = await this.getQuery(
      'SELECT value FROM sync_state WHERE key = ?',
      [key]
    );
    
    return result ? JSON.parse(result.value) : null;
  }

  async getStats() {
    const stats = {};
    
    // Message counts
    const messageCount = await this.getQuery('SELECT COUNT(*) as count FROM messages');
    stats.totalMessages = messageCount.count;
    
    const unreadCount = await this.getQuery('SELECT COUNT(*) as count FROM messages WHERE is_read = 0 AND is_sent = 0');
    stats.unreadMessages = unreadCount.count;
    
    // Conversation count
    const conversationCount = await this.getQuery('SELECT COUNT(*) as count FROM conversations');
    stats.totalConversations = conversationCount.count;
    
    // Contact count
    const contactCount = await this.getQuery('SELECT COUNT(*) as count FROM contacts');
    stats.totalContacts = contactCount.count;
    
    // Call count
    const callCount = await this.getQuery('SELECT COUNT(*) as count FROM calls');
    stats.totalCalls = callCount.count;
    
    // Failed message count
    const failedCount = await this.getQuery('SELECT COUNT(*) as count FROM failed_messages WHERE status = "pending"');
    stats.failedMessages = failedCount.count;
    
    // Database file size
    try {
      const dbStats = fs.statSync(this.dbPath);
      stats.databaseSize = dbStats.size;
    } catch (error) {
      stats.databaseSize = 0;
    }
    
    return stats;
  }

  setupBackups() {
    console.log('💾 Setting up automatic database backups...');
    
    setInterval(async () => {
      try {
        await this.createBackup();
      } catch (error) {
        console.error('❌ Backup failed:', error);
      }
    }, this.BACKUP_INTERVAL);
  }

  async createBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = this.dbPath.replace('.db', `_backup_${timestamp}.db`);
    
    try {
      fs.copyFileSync(this.dbPath, backupPath);
      console.log(`💾 Database backup created: ${backupPath}`);
      
      // Clean up old backups (keep last 5)
      await this.cleanupOldBackups();
      
    } catch (error) {
      console.error('❌ Backup creation failed:', error);
    }
  }

  async cleanupOldBackups() {
    const backupDir = path.dirname(this.dbPath);
    const files = fs.readdirSync(backupDir);
    
    const backupFiles = files
      .filter(file => file.includes('_backup_'))
      .map(file => ({
        name: file,
        path: path.join(backupDir, file),
        stats: fs.statSync(path.join(backupDir, file))
      }))
      .sort((a, b) => b.stats.mtime - a.stats.mtime);
    
    // Keep only the 5 most recent backups
    if (backupFiles.length > 5) {
      for (let i = 5; i < backupFiles.length; i++) {
        try {
          fs.unlinkSync(backupFiles[i].path);
          console.log(`🗑️ Deleted old backup: ${backupFiles[i].name}`);
        } catch (error) {
          console.error(`❌ Failed to delete backup ${backupFiles[i].name}:`, error);
        }
      }
    }
  }

  // Utility methods for database operations
  async runQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(error) {
        if (error) {
          reject(error);
        } else {
          resolve({ changes: this.changes, lastID: this.lastID });
        }
      });
    });
  }

  async getQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (error, row) => {
        if (error) {
          reject(error);
        } else {
          resolve(row);
        }
      });
    });
  }

  async allQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (error, rows) => {
        if (error) {
          reject(error);
        } else {
          resolve(rows);
        }
      });
    });
  }

  async close() {
    if (this.db) {
      return new Promise((resolve, reject) => {
        this.db.close((error) => {
          if (error) {
            reject(error);
          } else {
            console.log('✅ Database closed');
            resolve();
          }
        });
      });
    }
  }

  async cleanup() {
    console.log('🧹 Cleaning up database...');
    await this.close();
    this.db = null;
    this.isInitialized = false;
    console.log('✅ Database cleanup complete');
  }
}

module.exports = { Database };