/* Premium iPhone Companion Pro Design System */
:root {
    /* Premium Color Palette */
    --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-solid: #667eea;
    --primary-dark: #5a6fd8;
    --accent-blue: #007AFF;
    --accent-green: #32D74B;
    --accent-red: #FF453A;
    --accent-orange: #FF9F0A;
    --accent-purple: #BF5AF2;

    /* Glass Morphism */
    --glass: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-hover: rgba(255, 255, 255, 0.08);
    --glass-active: rgba(255, 255, 255, 0.12);

    /* Backgrounds */
    --bg-main: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    --bg-card: rgba(28, 28, 30, 0.8);
    --bg-card-hover: rgba(44, 44, 46, 0.9);
    --bg-sidebar: rgba(18, 18, 20, 0.95);
    --bg-header: rgba(28, 28, 30, 0.95);

    /* Text */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    --text-tertiary: rgba(255, 255, 255, 0.5);
    --text-accent: #667eea;

    /* Shadows */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.15);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.25);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.37);
    --shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.5);
    --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);

    /* Borders */
    --border: rgba(255, 255, 255, 0.1);
    --border-light: rgba(255, 255, 255, 0.15);
    --border-accent: rgba(102, 126, 234, 0.3);

    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;

    /* Border Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --radius-full: 50%;

    /* Transitions */
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Typography */
    --font-display: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    --font-text: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    --font-mono: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
}

/* Global Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-text);
    background: var(--bg-main);
    color: var(--text-primary);
    overflow: hidden;
    line-height: 1.6;
    font-size: 14px;
    position: relative;
}

/* Premium Background with Animated Gradient */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 122, 255, 0.05) 0%, transparent 50%);
    z-index: -1;
    animation: gradientShift 20s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--glass);
    border-radius: 3px;
    transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--glass-hover);
}

/* Premium Header with Glass Effect */
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--bg-header);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 100;
}

.app-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--glass);
    z-index: -1;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: var(--transition-smooth);
}

.logo:hover {
    transform: translateY(-1px);
}

.logo img {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-smooth);
}

.logo img:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.logo h1 {
    font-family: var(--font-display);
    font-size: 22px;
    font-weight: 700;
    background: var(--primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--glass);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    transition: var(--transition-smooth);
    cursor: pointer;
}

.connection-status:hover {
    background: var(--glass-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: var(--radius-full);
    background: var(--accent-red);
    position: relative;
    transition: var(--transition-smooth);
}

.status-dot::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: var(--radius-full);
    background: inherit;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: var(--accent-green);
}

.status-dot.connecting {
    background: var(--accent-orange);
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.5); opacity: 0.1; }
    100% { transform: scale(1); opacity: 0.3; }
}

#connection-text {
    font-weight: 500;
    font-size: 13px;
    color: var(--text-secondary);
}

/* Premium Layout */
.app-container {
    display: flex;
    height: calc(100vh - 85px);
    position: relative;
}

/* Premium Sidebar with Glass Effect */
.sidebar {
    width: 280px;
    background: var(--bg-sidebar);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-right: 1px solid var(--glass-border);
    padding: var(--spacing-xl) var(--spacing-lg);
    overflow-y: auto;
    position: relative;
    z-index: 50;
    box-shadow: var(--shadow-lg);
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--glass);
    z-index: -1;
}

/* Premium Navigation Sections */
.nav-section {
    margin-bottom: var(--spacing-2xl);
    position: relative;
}

.nav-section h3 {
    font-family: var(--font-display);
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-md);
    position: relative;
}

.nav-section h3::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 12px;
    background: var(--primary);
    border-radius: 2px;
}

/* Premium Navigation Buttons */
.nav-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    background: transparent;
    border: none;
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    font-family: var(--font-text);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-smooth);
    position: relative;
    margin-bottom: var(--spacing-xs);
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-smooth);
}

.nav-btn:hover::before {
    left: 100%;
}

.nav-btn:hover {
    background: var(--glass-hover);
    color: var(--text-primary);
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.nav-btn.active {
    background: var(--glass-active);
    color: var(--text-primary);
    box-shadow: var(--shadow-glow);
    border: 1px solid var(--border-accent);
}

.nav-btn.active::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary);
    border-radius: 0 2px 2px 0;
}

.nav-btn i {
    font-size: 18px;
    width: 20px;
    text-align: center;
    transition: var(--transition-smooth);
}

.nav-btn:hover i {
    transform: scale(1.1);
}

/* Premium Badge */
.badge {
    position: absolute;
    right: var(--spacing-md);
    background: var(--primary);
    color: white;
    font-size: 10px;
    font-weight: 700;
    padding: 4px 8px;
    border-radius: var(--radius-full);
    min-width: 20px;
    text-align: center;
    box-shadow: var(--shadow-sm);
    animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.badge:empty {
    display: none;
}

/* Premium Content Area */
.content {
    flex: 1;
    padding: var(--spacing-2xl);
    overflow-y: auto;
    position: relative;
    background: rgba(0, 0, 0, 0.1);
}

.content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 70% 30%, rgba(102, 126, 234, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 30% 70%, rgba(118, 75, 162, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.content > * {
    position: relative;
    z-index: 1;
}

/* Premium View Transitions */
.view {
    display: none;
    opacity: 0;
    transform: translateY(20px);
    transition: var(--transition-smooth);
}

.view.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
    animation: viewFadeIn 0.5s ease-out;
}

@keyframes viewFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Premium View Headers */
.view-header {
    margin-bottom: var(--spacing-2xl);
    text-align: center;
    position: relative;
}

.view-header h2 {
    font-family: var(--font-display);
    font-size: 36px;
    font-weight: 800;
    margin-bottom: var(--spacing-sm);
    background: var(--primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.view-header h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--primary);
    border-radius: 2px;
}

.view-header p {
    color: var(--text-secondary);
    font-size: 16px;
    font-weight: 400;
    max-width: 600px;
    margin: 0 auto;
}

/* Premium Cards with Glass Effect */
.card {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--glass);
    z-index: -1;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--border-light);
}

.card:hover::before {
    background: var(--glass-hover);
}

.card h3 {
    font-family: var(--font-display);
    font-size: 18px;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
    position: relative;
}

.card h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--primary);
    border-radius: 1px;
}

/* Premium Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
}

/* Premium Device Card */
.device-card {
    text-align: center;
    position: relative;
    overflow: hidden;
}

.device-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    animation: rotate 10s linear infinite;
    z-index: -1;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.device-icon {
    font-size: 80px;
    margin: var(--spacing-xl) 0;
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.3));
    animation: deviceFloat 3s ease-in-out infinite;
}

@keyframes deviceFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.device-details {
    text-align: left;
    margin-top: var(--spacing-lg);
}

.device-details p {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--glass-border);
    margin-bottom: var(--spacing-sm);
}

.device-details p:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.device-details strong {
    color: var(--text-secondary);
    font-weight: 500;
}

.device-details span {
    color: var(--text-primary);
    font-weight: 600;
}

.device-details p {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.device-details strong {
    color: var(--text-primary);
}

/* Premium Stats */
.stat-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.stat {
    text-align: center;
    position: relative;
    padding: var(--spacing-lg);
    background: var(--glass);
    border-radius: var(--radius-md);
    border: 1px solid var(--glass-border);
    transition: var(--transition-smooth);
}

.stat:hover {
    transform: translateY(-2px);
    background: var(--glass-hover);
    box-shadow: var(--shadow-md);
}

.stat-value {
    font-family: var(--font-display);
    font-size: 42px;
    font-weight: 800;
    background: var(--primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-sm);
    position: relative;
}

.stat-value::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background: var(--primary);
    border-radius: 1px;
}

.stat-label {
    font-size: 11px;
    color: var(--text-secondary);
    text-transform: uppercase;
    font-weight: 700;
    letter-spacing: 1px;
}

/* Premium Action Buttons */
.action-buttons {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--glass);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-smooth);
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    background: var(--glass-hover);
    border-color: var(--border-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.action-btn i {
    font-size: 24px;
    transition: var(--transition-smooth);
}

.action-btn:hover i {
    transform: scale(1.1);
}

.action-btn span {
    font-size: 14px;
    font-weight: 600;
}

/* Premium Connection Methods */
.connection-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.method-card {
    padding: var(--spacing-xl);
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 2px solid var(--glass-border);
    border-radius: var(--radius-lg);
    text-align: center;
    cursor: pointer;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.method-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--glass);
    z-index: -1;
}

.method-card:hover {
    border-color: var(--primary-solid);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.method-card:hover::before {
    background: var(--glass-hover);
}

.method-icon {
    font-size: 64px;
    margin-bottom: var(--spacing-lg);
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.2));
    transition: var(--transition-smooth);
}

.method-card:hover .method-icon {
    transform: scale(1.1);
    filter: drop-shadow(0 6px 12px rgba(102, 126, 234, 0.4));
}

.method-card h4 {
    font-family: var(--font-display);
    font-size: 18px;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.method-card p {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.5;
}

.method-status {
    font-size: 12px;
    font-weight: 600;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--glass);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-full);
    color: var(--text-secondary);
    display: inline-block;
    transition: var(--transition-smooth);
}

.method-status.connected {
    background: var(--accent-green);
    color: white;
    box-shadow: 0 0 12px rgba(50, 215, 75, 0.3);
}

.method-status.connecting {
    background: var(--accent-orange);
    color: white;
    animation: pulse 2s infinite;
}

/* Premium Connection Log */
.connection-log {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.connection-log::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--glass);
    z-index: -1;
}

.connection-log h3 {
    font-family: var(--font-display);
    font-size: 18px;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
    position: relative;
}

.connection-log h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--primary);
    border-radius: 1px;
}

#connection-log-content {
    max-height: 250px;
    overflow-y: auto;
    font-family: var(--font-mono);
    font-size: 13px;
    line-height: 1.6;
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-sm);
    padding: var(--spacing-md);
    border: 1px solid var(--glass-border);
}

.log-entry {
    padding: var(--spacing-xs) 0;
    color: var(--text-secondary);
    transition: var(--transition-fast);
    position: relative;
    padding-left: var(--spacing-md);
}

.log-entry::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--text-tertiary);
}

.log-entry.success {
    color: var(--accent-green);
}

.log-entry.success::before {
    content: '✓';
    color: var(--accent-green);
}

.log-entry.error {
    color: var(--accent-red);
}

.log-entry.error::before {
    content: '✗';
    color: var(--accent-red);
}

.log-entry.warning {
    color: var(--accent-orange);
}

.log-entry.warning::before {
    content: '⚠';
    color: var(--accent-orange);
}

/* Premium Loading Animation */
@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

.loading {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* ===== PREMIUM CALLS INTERFACE ===== */

/* Calls Container */
.calls-container {
    display: flex;
    height: calc(100vh - 200px);
    gap: var(--spacing-xl);
}

/* Calls Panel */
.calls-panel {
    flex: 1;
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    position: relative;
}

.calls-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--glass);
    z-index: -1;
}

.calls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--glass-border);
    background: var(--glass-hover);
}

.calls-header h3 {
    font-family: var(--font-display);
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.new-call-btn {
    width: 44px;
    height: 44px;
    border-radius: var(--radius-full);
    background: var(--primary);
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-md);
}

.new-call-btn:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-glow);
}

.new-call-btn:active {
    transform: scale(0.95);
}

/* Search Box */
.search-box {
    padding: var(--spacing-lg) var(--spacing-xl);
}

.search-box input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--glass);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-family: var(--font-text);
    font-size: 14px;
    transition: var(--transition-smooth);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-solid);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-box input::placeholder {
    color: var(--text-tertiary);
}

/* Calls List */
.calls-list {
    overflow-y: auto;
    max-height: calc(100vh - 400px);
}

.call-entry {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--glass-border);
    transition: var(--transition-smooth);
    cursor: pointer;
    position: relative;
}

.call-entry:hover {
    background: var(--glass-hover);
}

.call-entry:last-child {
    border-bottom: none;
}

/* Call Avatar */
.call-avatar {
    position: relative;
    width: 50px;
    height: 50px;
    border-radius: var(--radius-full);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.call-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 14px;
    height: 14px;
    background: var(--accent-green);
    border: 2px solid var(--bg-card);
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-sm);
}

/* Call Info */
.call-info {
    flex: 1;
    min-width: 0;
}

.call-name {
    font-family: var(--font-display);
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.call-details {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 13px;
    color: var(--text-secondary);
}

.call-type {
    font-weight: 500;
}

.call-entry.missed .call-type {
    color: var(--accent-red);
}

.call-entry.outgoing .call-type {
    color: var(--accent-blue);
}

.call-entry.incoming .call-type {
    color: var(--accent-green);
}

.call-time {
    opacity: 0.8;
}

/* Call Actions */
.call-actions {
    display: flex;
    gap: var(--spacing-sm);
    opacity: 0;
    transition: var(--transition-smooth);
}

.call-entry:hover .call-actions {
    opacity: 1;
}

.call-btn,
.video-btn {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-full);
    border: none;
    background: var(--glass);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-smooth);
}

.call-btn:hover {
    background: var(--accent-green);
    color: white;
    transform: scale(1.1);
}

.video-btn:hover {
    background: var(--accent-blue);
    color: white;
    transform: scale(1.1);
}

/* Premium Dialer Panel */
.dialer-panel {
    width: 350px;
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    position: relative;
}

.dialer-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--glass);
    z-index: -1;
}

.dialer-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--glass-border);
    background: var(--glass-hover);
}

.dialer-header h3 {
    font-family: var(--font-display);
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    text-align: center;
}

/* Phone Display */
.phone-display {
    padding: var(--spacing-xl);
    position: relative;
}

.phone-input {
    width: 100%;
    padding: var(--spacing-lg);
    background: var(--glass);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    letter-spacing: 2px;
    transition: var(--transition-smooth);
}

.phone-input:focus {
    outline: none;
    border-color: var(--primary-solid);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.clear-btn {
    position: absolute;
    right: calc(var(--spacing-xl) + var(--spacing-sm));
    top: 50%;
    transform: translateY(-50%);
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    border: none;
    background: var(--glass-hover);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-smooth);
    opacity: 0;
}

.clear-btn:hover {
    background: var(--accent-red);
    color: white;
    transform: translateY(-50%) scale(1.1);
}

.phone-input:not(:placeholder-shown) + .clear-btn {
    opacity: 1;
}

/* Dialer Grid */
.dialer-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
}

.dial-btn {
    aspect-ratio: 1;
    border: none;
    background: var(--glass);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.dial-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    transition: var(--transition-fast);
    transform: translate(-50%, -50%);
}

.dial-btn:active::before {
    width: 100%;
    height: 100%;
}

.dial-btn:hover {
    background: var(--glass-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.dial-btn .digit {
    font-family: var(--font-display);
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.dial-btn .letters {
    font-size: 10px;
    font-weight: 600;
    color: var(--text-secondary);
    letter-spacing: 1px;
}

/* Call Controls */
.call-controls {
    padding: var(--spacing-xl);
    border-top: 1px solid var(--glass-border);
}

.call-action-btn {
    width: 100%;
    padding: var(--spacing-lg);
    background: var(--accent-green);
    border: none;
    border-radius: var(--radius-lg);
    color: white;
    font-family: var(--font-display);
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-md);
}

.call-action-btn:hover {
    background: #28a745;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.call-action-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    text-align: center;
    padding: var(--spacing-2xl);
}

.empty-icon {
    font-size: 64px;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
    animation: float 3s ease-in-out infinite;
}

.empty-state h3 {
    font-family: var(--font-display);
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.empty-state p {
    color: var(--text-secondary);
    font-size: 16px;
    max-width: 400px;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Premium Notification System */
.notification {
    position: fixed;
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    transform: translateX(400px);
    opacity: 0;
    transition: var(--transition-smooth);
    min-width: 300px;
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.notification-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.notification-message {
    color: var(--text-primary);
    font-weight: 500;
    line-height: 1.4;
}

.notification.success {
    border-color: var(--accent-green);
    box-shadow: 0 8px 32px rgba(50, 215, 75, 0.2);
}

.notification.warning {
    border-color: var(--accent-orange);
    box-shadow: 0 8px 32px rgba(255, 159, 10, 0.2);
}

.notification.error {
    border-color: var(--accent-red);
    box-shadow: 0 8px 32px rgba(255, 69, 58, 0.2);
}

.notification.info {
    border-color: var(--accent-blue);
    box-shadow: 0 8px 32px rgba(0, 122, 255, 0.2);
}

/* Premium Messages Interface */
.messages-container {
    display: flex;
    height: calc(100vh - 200px);
    gap: 1px;
    background: var(--glass);
    border-radius: var(--radius-xl);
    overflow: hidden;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--glass-border);
}

.conversations-panel {
    width: 350px;
    background: var(--glass-hover);
    border-right: 1px solid var(--glass-border);
    display: flex;
    flex-direction: column;
}

.conversations-header {
    padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
    border-bottom: 1px solid var(--glass-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--glass);
}

.conversations-header h3 {
    color: var(--text-primary);
    font-family: var(--font-display);
    font-size: 20px;
    font-weight: 700;
    margin: 0;
}

.new-message-btn {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-md);
    background: var(--primary-gradient);
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-md);
}

.new-message-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-lg);
}

.conversations-list {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-sm);
}

.conversation-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xs);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-smooth);
    position: relative;
}

.conversation-item:hover {
    background: var(--glass);
    transform: translateX(4px);
}

.conversation-item.active {
    background: var(--primary-gradient);
    border: 1px solid var(--primary-solid);
    box-shadow: var(--shadow-md);
}

.conversation-avatar {
    position: relative;
    margin-right: var(--spacing-md);
}

.conversation-avatar img {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    object-fit: cover;
    border: 2px solid var(--glass-border);
}

.online-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 16px;
    height: 16px;
    background: var(--accent-green);
    border: 3px solid var(--bg-primary);
    border-radius: var(--radius-full);
    box-shadow: 0 0 8px rgba(50, 215, 75, 0.5);
}

.conversation-info {
    flex: 1;
    min-width: 0;
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.conversation-name {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.conversation-time {
    color: var(--text-secondary);
    font-size: 12px;
    white-space: nowrap;
}

.conversation-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-text {
    color: var(--text-secondary);
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.unread-badge {
    background: var(--accent-blue);
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: var(--radius-full);
    min-width: 18px;
    text-align: center;
    box-shadow: var(--shadow-sm);
}

/* Chat Area */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
}

.chat-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--glass-border);
    background: var(--glass);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chat-contact {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.chat-contact-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    object-fit: cover;
    border: 2px solid var(--glass-border);
}

.chat-contact-info h4 {
    color: var(--text-primary);
    font-weight: 600;
    margin: 0 0 2px 0;
    font-size: 16px;
}

.chat-contact-status {
    color: var(--text-secondary);
    font-size: 12px;
    margin: 0;
}

.chat-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.chat-action-btn {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-md);
    border: 1px solid var(--glass-border);
    background: var(--glass);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-smooth);
}

.chat-action-btn:hover {
    background: var(--glass-hover);
    color: var(--text-primary);
    transform: translateY(-1px);
}

/* Messages Area */
.messages-area {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.message {
    display: flex;
    align-items: flex-end;
    gap: var(--spacing-sm);
    max-width: 70%;
    animation: messageSlideIn 0.3s ease-out;
}

.message.sent {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-md);
    object-fit: cover;
    border: 1px solid var(--glass-border);
    flex-shrink: 0;
}

.message-content {
    background: var(--glass);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    position: relative;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.message.sent .message-content {
    background: var(--primary-gradient);
    border-color: var(--primary-solid);
    color: white;
}

.message-text {
    color: var(--text-primary);
    line-height: 1.4;
    margin: 0;
    word-wrap: break-word;
}

.message.sent .message-text {
    color: white;
}

.message-time {
    font-size: 11px;
    color: var(--text-tertiary);
    margin-top: var(--spacing-xs);
    text-align: right;
}

.message.sent .message-time {
    color: rgba(255, 255, 255, 0.7);
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Message Input */
.message-input-area {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-top: 1px solid var(--glass-border);
    background: var(--glass);
}

.message-input-container {
    display: flex;
    align-items: flex-end;
    gap: var(--spacing-md);
    background: var(--bg-primary);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-md);
    transition: var(--transition-smooth);
}

.message-input-container:focus-within {
    border-color: var(--primary-solid);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.message-input {
    flex: 1;
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    outline: none;
    min-height: 20px;
    max-height: 120px;
    font-family: var(--font-text);
}

.message-input::placeholder {
    color: var(--text-tertiary);
}

.input-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.input-action-btn {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-md);
    border: none;
    background: var(--glass);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-smooth);
}

.input-action-btn:hover {
    background: var(--glass-hover);
    color: var(--text-primary);
}

.send-btn {
    background: var(--primary-gradient);
    color: white;
}

.send-btn:hover {
    background: var(--primary-solid);
    transform: scale(1.05);
}

/* Premium Message Bubbles */
.message-bubble {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    max-width: 300px;
    position: relative;
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-smooth);
}

.message.sent .message-bubble {
    background: var(--primary-gradient);
    color: white;
    border-color: var(--primary-solid);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.message.received .message-bubble {
    background: var(--bg-card-hover);
}

.message-bubble:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.message.sent .message-bubble:hover {
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: var(--spacing-xs);
    word-wrap: break-word;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    text-align: right;
}

.message.received .message-time {
    color: var(--text-tertiary);
}

.message-status {
    font-size: 12px;
    color: var(--accent-blue);
    margin-top: var(--spacing-xs);
    text-align: right;
}

/* Consecutive Messages */
.message.consecutive {
    margin-top: 2px;
}

.message.consecutive .message-bubble {
    border-radius: var(--radius-md);
}

.message.sent.consecutive .message-bubble {
    border-bottom-right-radius: 4px;
}

.message.received.consecutive .message-bubble {
    border-bottom-left-radius: 4px;
}

/* Latest Message Animation */
.message.latest {
    animation: messageSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Typing Indicator */
.typing-indicator {
    animation: typingFadeIn 0.3s ease-out;
}

.message-bubble.typing {
    background: var(--bg-card-hover);
    padding: var(--spacing-md) var(--spacing-lg);
    min-width: 60px;
}

.typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: center;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--text-secondary);
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
    animation-delay: 0s;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingDot {
    0%, 60%, 100% {
        transform: scale(1);
        opacity: 0.5;
    }
    30% {
        transform: scale(1.2);
        opacity: 1;
    }
}

@keyframes typingFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Default Avatar for Calls */
.default-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 16px;
    text-transform: uppercase;
}

/* ===== SCREEN MIRROR INTERFACE ===== */
.mirror-container {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: var(--spacing-6);
    height: calc(100vh - 120px);
    padding: var(--spacing-6);
}

.mirror-controls {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    height: fit-content;
    box-shadow: var(--shadow-lg);
}

.connection-panel {
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
}

.connection-panel h3 {
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--spacing-4);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.connection-panel h3::before {
    content: "📡";
    font-size: 20px;
}

.connection-status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-4);
    padding: var(--spacing-3);
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    position: relative;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: var(--success-color);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
}

.status-dot.disconnected {
    background: var(--error-color);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
}

.status-dot.connecting {
    background: var(--warning-color);
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 currentColor;
    }
    70% {
        box-shadow: 0 0 0 10px transparent;
    }
    100% {
        box-shadow: 0 0 0 0 transparent;
    }
}

.connect-btn {
    width: 100%;
    padding: var(--spacing-4);
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.connect-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.connect-btn:active {
    transform: translateY(0);
}

.connect-btn.connecting {
    background: var(--warning-gradient);
    pointer-events: none;
}

.connect-btn.connected {
    background: var(--success-gradient);
}

.mirror-settings h4 {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    margin-bottom: var(--spacing-4);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.mirror-settings h4::before {
    content: "⚙️";
    font-size: 16px;
}

.setting-group {
    margin-bottom: var(--spacing-4);
}

.setting-group label {
    display: block;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: var(--spacing-2);
}

.setting-group select {
    width: 100%;
    padding: var(--spacing-3);
    background: var(--surface-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.setting-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.setting-group input[type="checkbox"] {
    margin-right: var(--spacing-2);
    accent-color: var(--primary-color);
}

.mirror-display {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.device-frame {
    width: 100%;
    max-width: 400px;
    aspect-ratio: 9/19.5;
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
    border-radius: 40px;
    padding: 20px;
    box-shadow:
        0 0 0 8px rgba(255, 255, 255, 0.1),
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 0 0 2px rgba(255, 255, 255, 0.1);
    position: relative;
}

.device-frame::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.device-frame::after {
    content: '';
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 140px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
}

.device-screen {
    width: 100%;
    height: 100%;
    background: #000;
    border-radius: 30px;
    overflow: hidden;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mirror-placeholder {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    padding: var(--spacing-6);
}

.placeholder-icon {
    font-size: 64px;
    margin-bottom: var(--spacing-4);
    opacity: 0.6;
}

.mirror-placeholder h3 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 24px;
    font-weight: 600;
    margin-bottom: var(--spacing-3);
}

.mirror-placeholder p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 16px;
    margin-bottom: var(--spacing-6);
    line-height: 1.5;
}

.connection-steps {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
    margin-top: var(--spacing-6);
}

.step {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.step-number {
    width: 24px;
    height: 24px;
    background: var(--primary-gradient);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    flex-shrink: 0;
}

.step span:last-child {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.device-controls {
    position: absolute;
    bottom: var(--spacing-6);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: var(--spacing-3);
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    padding: var(--spacing-3);
    border-radius: var(--radius-full);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.control-btn {
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: scale(1.1);
}

.control-btn:active {
    transform: scale(0.95);
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.control-btn:active::before {
    width: 100%;
    height: 100%;
}

/* Mirror Screen States */
.device-screen.connected {
    background: var(--surface-primary);
}

.device-screen.mirroring {
    background: transparent;
}

.mirror-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 30px;
}

/* Loading Animation for Mirror */
.mirror-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-4);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    font-weight: 500;
}

/* Quality Indicator */
.quality-indicator {
    position: absolute;
    top: var(--spacing-4);
    right: var(--spacing-4);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius-md);
    font-size: 12px;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

/* Responsive Design for Mirror */
@media (max-width: 1200px) {
    .mirror-container {
        grid-template-columns: 300px 1fr;
        gap: var(--spacing-4);
    }

    .device-frame {
        max-width: 350px;
    }
}

@media (max-width: 768px) {
    .mirror-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .mirror-controls {
        order: 2;
    }

    .mirror-display {
        order: 1;
        height: 60vh;
    }

    .device-frame {
        max-width: 280px;
    }
}

/* Premium Micro-Interactions & Enhanced Visual Feedback */
.nav-btn {
    position: relative;
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.nav-btn:active::before {
    width: 300px;
    height: 300px;
}

/* Enhanced Button Interactions */
.action-btn, .method-card, .card {
    position: relative;
    overflow: hidden;
}

.action-btn::after, .method-card::after, .card::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.action-btn:focus:not(:active)::after,
.method-card:focus:not(:active)::after,
.card:focus:not(:active)::after {
    animation: rippleEffect 1s ease-out;
}

@keyframes rippleEffect {
    0% {
        transform: scale(0, 0);
        opacity: 1;
    }
    20% {
        transform: scale(25, 25);
        opacity: 1;
    }
    100% {
        opacity: 0;
        transform: scale(40, 40);
    }
}

/* Premium Hover Effects */
.nav-btn:hover {
    transform: translateX(8px);
    background: var(--glass-hover);
}

.nav-btn:hover i {
    transform: scale(1.2);
    filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Enhanced Loading States */
.loading-skeleton {
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.05) 25%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Premium Focus States */
.nav-btn:focus,
.action-btn:focus,
.method-card:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* Enhanced Status Indicators */
.status-dot {
    position: relative;
}

.status-dot::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: inherit;
    transform: translate(-50%, -50%);
    animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

/* Premium Smooth Transitions */
* {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                background 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Premium Notification System */
.premium-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    min-width: 300px;
    max-width: 400px;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    animation: slideInRight 0.3s ease-out;
}

.premium-notification.success {
    border-left: 4px solid var(--accent-green);
}

.premium-notification.error {
    border-left: 4px solid var(--accent-red);
}

.premium-notification.info {
    border-left: 4px solid var(--accent-blue);
}

.notification-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.notification-message {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 20px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: var(--glass-hover);
    color: var(--text-primary);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Enhanced Fade In Animation */
.fade-in-up {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) both;
}

/* Premium Loading Spinner */
.premium-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--glass-border);
    border-top: 3px solid var(--primary-solid);
    border-radius: 50%;
    animation: premiumSpin 1s linear infinite;
}

@keyframes premiumSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Device Info Styling */
.device-value {
    color: var(--text-primary);
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 4px;
    background: var(--glass);
    border: 1px solid var(--glass-border);
    display: inline-block;
    min-width: 60px;
    text-align: center;
}

.battery-indicator {
    background: linear-gradient(135deg, var(--accent-green), #28a745);
    color: white;
    box-shadow: 0 2px 8px rgba(50, 215, 75, 0.3);
}

.quality-indicator {
    background: linear-gradient(135deg, var(--accent-blue), #0056b3);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

/* Premium Stats Enhancement */
.stat-value {
    background: var(--primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    text-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

/* Enhanced Action Buttons */
.action-btn i {
    font-size: 18px;
    margin-right: 8px;
    filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
}

/* Premium Card Headers */
.card h3 {
    display: flex;
    align-items: center;
    gap: 8px;
}

.card h3::before {
    content: '';
    width: 4px;
    height: 20px;
    background: var(--primary);
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

/* Enhanced Connection Status */
.connection-status {
    position: relative;
    padding: 12px;
    background: var(--glass);
    border-radius: var(--radius-md);
    border: 1px solid var(--glass-border);
}

.connection-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    border-radius: var(--radius-md);
    z-index: -1;
    animation: shimmer 3s infinite;
}

/* ===== WINDOW SCALING FIXES ===== */

/* Fix content overflow */
body {
    overflow-x: hidden;
    min-width: 1000px;
}

.container {
    max-width: 100%;
    padding: 20px;
    box-sizing: border-box;
}

/* Responsive grid */
.control-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

@media (max-width: 1200px) {
    .control-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
