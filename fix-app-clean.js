#!/usr/bin/env node

/**
 * Fix app-clean.js class structure properly
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'src/renderer/scripts/app-clean.js');

try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    console.log('🔧 Fixing app-clean.js class structure...');
    
    // Fix the constructor
    content = content.replace('function constructor() {', '    constructor() {');
    
    // Fix methods inside the class - they should not have 'function' keyword
    // and should be properly indented within the class
    const methodsToFix = [
        'init',
        'initializeWebSocket',
        'handleWebSocketMessage',
        'requestSystemStatus',
        'logConnection',
        'setupEventListeners',
        'updateUI',
        'startConnectionMonitoring',
        'loadRealConversations',
        'loadRealCallHistory',
        'showView',
        'updateConnectionStatus',
        'updateStats'
    ];
    
    methodsToFix.forEach(methodName => {
        // Fix methods that start with 'function methodName('
        const functionRegex = new RegExp(`^function ${methodName}\\(`, 'gm');
        content = content.replace(functionRegex, `    ${methodName}(`);
        
        // Fix methods that are standalone (not properly indented)
        const standaloneRegex = new RegExp(`^${methodName}\\(`, 'gm');
        content = content.replace(standaloneRegex, `    ${methodName}(`);
    });
    
    // Find where the class should end and add proper closing brace
    const lines = content.split('\n');
    let inClass = false;
    let braceCount = 0;
    let classEndLine = -1;
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        
        if (line.startsWith('class iPhoneCompanionApp')) {
            inClass = true;
            braceCount = 0;
        }
        
        if (inClass) {
            // Count braces
            braceCount += (line.match(/\{/g) || []).length;
            braceCount -= (line.match(/\}/g) || []).length;
            
            // If we've closed all braces, this is the end of the class
            if (braceCount === 0 && i > 0) {
                classEndLine = i;
                break;
            }
        }
    }
    
    // Add global app variable and instantiation after the class
    if (!content.includes('const app = new iPhoneCompanionApp();')) {
        const classEndIndex = content.indexOf('}', content.lastIndexOf('class iPhoneCompanionApp'));
        if (classEndIndex !== -1) {
            const beforeClass = content.substring(0, classEndIndex + 1);
            const afterClass = content.substring(classEndIndex + 1);
            
            content = beforeClass + '\n\n// Global app instance\nconst app = new iPhoneCompanionApp();\n' + afterClass;
        }
    }
    
    fs.writeFileSync(filePath, content);
    
    console.log('✅ app-clean.js structure fixed');
    
    // Test the syntax
    require('child_process').exec(`node -c "${filePath}"`, (error, stdout, stderr) => {
        if (error) {
            console.error('❌ Syntax errors still exist:', stderr);
        } else {
            console.log('✅ JavaScript syntax is now valid');
        }
    });
    
} catch (error) {
    console.error('❌ Failed to fix app-clean.js:', error);
}