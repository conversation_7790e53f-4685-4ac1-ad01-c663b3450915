/**
 * 🎨 PROFESSIONAL UI POLISH - CRITICAL STYLING
 * Making iPhone Companion Pro 10x better than Intel Unison
 * Professional, modern, and polished interface
 */

/* ========== GLOBAL ANIMATIONS & TRANSITIONS ========== */
:root {
    --primary-blue: #007AFF;
    --primary-blue-dark: #0051D5;
    --success-green: #34C759;
    --warning-orange: #FF9500;
    --error-red: #FF3B30;
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    --text-muted: rgba(255, 255, 255, 0.5);
}

* {
    box-sizing: border-box;
}

html, body {
    scroll-behavior: smooth;
}

/* Enhanced glassmorphism effects */
.glass-effect {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-effect:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

/* ========== ENHANCED BUTTON STYLES ========== */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 122, 255, 0.4);
    background: linear-gradient(135deg, var(--primary-blue-dark) 0%, #003F8F 100%);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-green) 0%, #30A14E 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(52, 199, 89, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(52, 199, 89, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, var(--error-red) 0%, #D70015 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(255, 59, 48, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(255, 59, 48, 0.4);
}

/* ========== ENHANCED INPUT STYLES ========== */
.form-input {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 14px 18px;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 500;
    outline: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.form-input:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    background: rgba(255, 255, 255, 0.08);
}

.form-input::placeholder {
    color: var(--text-muted);
}

/* ========== ENHANCED CARD STYLES ========== */
.card-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 24px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-modern:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

/* ========== ENHANCED STATUS INDICATORS ========== */
.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    position: relative;
    transition: all 0.3s ease;
}

.status-dot.connected {
    background: var(--success-green);
    box-shadow: 0 0 20px rgba(52, 199, 89, 0.5);
    animation: pulse-success 2s infinite;
}

.status-dot.connecting {
    background: var(--warning-orange);
    box-shadow: 0 0 20px rgba(255, 149, 0, 0.5);
    animation: pulse-warning 1s infinite;
}

.status-dot.disconnected {
    background: var(--error-red);
    box-shadow: 0 0 20px rgba(255, 59, 48, 0.5);
}

@keyframes pulse-success {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.1); }
}

@keyframes pulse-warning {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.4; transform: scale(1.15); }
}

/* ========== ENHANCED NOTIFICATIONS ========== */
.notification-modern {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 16px 20px;
    color: var(--text-primary);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    z-index: 10000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 300px;
    max-width: 400px;
}

.notification-modern.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-modern.success {
    border-left: 4px solid var(--success-green);
    box-shadow: 0 12px 40px rgba(52, 199, 89, 0.2);
}

.notification-modern.error {
    border-left: 4px solid var(--error-red);
    box-shadow: 0 12px 40px rgba(255, 59, 48, 0.2);
}

.notification-modern.warning {
    border-left: 4px solid var(--warning-orange);
    box-shadow: 0 12px 40px rgba(255, 149, 0, 0.2);
}

.notification-modern.info {
    border-left: 4px solid var(--primary-blue);
    box-shadow: 0 12px 40px rgba(0, 122, 255, 0.2);
}

/* ========== ENHANCED LOADING STATES ========== */
.loading-modern {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    text-align: center;
}

.spinner-modern {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top: 3px solid var(--primary-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

.loading-text {
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
}

.loading-text h3 {
    color: var(--text-primary);
    margin-bottom: 8px;
    font-weight: 600;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ========== ENHANCED MODAL STYLES ========== */
.modal-modern {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-modern.show {
    opacity: 1;
    pointer-events: all;
}

.modal-content-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(30px);
    border: 1px solid var(--glass-border);
    border-radius: 24px;
    padding: 32px;
    min-width: 400px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(40px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 24px 80px rgba(0, 0, 0, 0.5);
}

.modal-modern.show .modal-content-modern {
    transform: scale(1) translateY(0);
}

/* ========== ENHANCED SCROLLBARS ========== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* ========== ENHANCED TYPOGRAPHY ========== */
.heading-xl {
    font-size: 36px;
    font-weight: 800;
    background: linear-gradient(135deg, var(--text-primary) 0%, var(--text-secondary) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 1.2;
    margin-bottom: 16px;
}

.heading-lg {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.3;
    margin-bottom: 12px;
}

.heading-md {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.4;
    margin-bottom: 8px;
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-blue) 0%, #00D4FF 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
}

/* ========== ENHANCED GRID LAYOUTS ========== */
.grid-responsive {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.grid-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

@media (max-width: 768px) {
    .grid-2, .grid-3 {
        grid-template-columns: 1fr;
    }
}

/* ========== ENHANCED FLEX UTILITIES ========== */
.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.flex-start {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.flex-column {
    display: flex;
    flex-direction: column;
}

.flex-column-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* ========== ENHANCED SPACING ========== */
.p-8 { padding: 8px; }
.p-12 { padding: 12px; }
.p-16 { padding: 16px; }
.p-20 { padding: 20px; }
.p-24 { padding: 24px; }
.p-32 { padding: 32px; }

.m-8 { margin: 8px; }
.m-12 { margin: 12px; }
.m-16 { margin: 16px; }
.m-20 { margin: 20px; }
.m-24 { margin: 24px; }
.m-32 { margin: 32px; }

.gap-8 { gap: 8px; }
.gap-12 { gap: 12px; }
.gap-16 { gap: 16px; }
.gap-20 { gap: 20px; }
.gap-24 { gap: 24px; }

/* ========== ENHANCED BORDER RADIUS ========== */
.rounded-sm { border-radius: 6px; }
.rounded { border-radius: 12px; }
.rounded-lg { border-radius: 16px; }
.rounded-xl { border-radius: 20px; }
.rounded-2xl { border-radius: 24px; }
.rounded-full { border-radius: 50%; }

/* ========== ENHANCED SHADOWS ========== */
.shadow-sm {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.shadow {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.shadow-lg {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.shadow-xl {
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.4);
}

.shadow-blue {
    box-shadow: 0 8px 32px rgba(0, 122, 255, 0.3);
}

.shadow-green {
    box-shadow: 0 8px 32px rgba(52, 199, 89, 0.3);
}

/* ========== ENHANCED HOVER EFFECTS ========== */
.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
}

.hover-scale {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-glow:hover {
    box-shadow: 0 0 30px rgba(0, 122, 255, 0.5);
}

/* ========== ENHANCED BACKDROP BLUR EFFECTS ========== */
.backdrop-blur-sm {
    backdrop-filter: blur(8px);
}

.backdrop-blur {
    backdrop-filter: blur(16px);
}

.backdrop-blur-lg {
    backdrop-filter: blur(24px);
}

.backdrop-blur-xl {
    backdrop-filter: blur(32px);
}

/* ========== ENHANCED PROFESSIONAL POLISH ========== */
.professional-container {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.professional-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(0, 122, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(52, 199, 89, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.content-area {
    position: relative;
    z-index: 1;
}

/* ========== ENHANCED ANIMATIONS ========== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-fadeInRight {
    animation: fadeInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-scaleIn {
    animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ========== STAGGERED ANIMATION DELAYS ========== */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }

/* ========== RESPONSIVE UTILITIES ========== */
@media (max-width: 640px) {
    .heading-xl { font-size: 28px; }
    .heading-lg { font-size: 24px; }
    .heading-md { font-size: 18px; }
    
    .p-32 { padding: 20px; }
    .p-24 { padding: 16px; }
    
    .modal-content-modern {
        padding: 24px;
        min-width: auto;
        margin: 20px;
    }
}

@media (max-width: 480px) {
    .heading-xl { font-size: 24px; }
    .heading-lg { font-size: 20px; }
    
    .btn-primary,
    .btn-success,
    .btn-danger {
        padding: 10px 20px;
        font-size: 13px;
    }
    
    .form-input {
        padding: 12px 16px;
        font-size: 13px;
    }
}