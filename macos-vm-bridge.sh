#!/bin/bash
# macOS VM Bridge Setup Script - Enhanced for iPhone Companion Pro
# Run this in your macOS VM

echo "🍎 Setting up Enhanced macOS Bridge for iPhone Companion Pro..."
echo "This will create a bridge to access iPhone Messages, Calls, and more"
echo ""

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ This script must run on macOS"
    exit 1
fi

# Get VM IP address
VM_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
echo "🌐 VM IP Address: $VM_IP"

# Install Homebrew if not present
if ! command -v brew &> /dev/null; then
    echo "📦 Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
    eval "$(/opt/homebrew/bin/brew shellenv)"
fi

# Install Node.js if not present
if ! command -v node &> /dev/null; then
    echo "📦 Installing Node.js..."
    brew install node
fi

# Create bridge directory
echo "📁 Creating bridge directory..."
mkdir -p ~/iphone-bridge
cd ~/iphone-bridge

# Initialize npm project
npm init -y

# Install dependencies
echo "📦 Installing dependencies..."
npm install ws sqlite3 node-cron fs-extra

# Enable Full Disk Access for Terminal (required for Messages access)
echo "🔐 Setting up permissions..."
echo "⚠️  IMPORTANT: You need to grant Full Disk Access to Terminal"
echo "   1. Go to System Preferences > Security & Privacy > Privacy"
echo "   2. Click 'Full Disk Access' on the left"
echo "   3. Click the lock and enter your password"
echo "   4. Click '+' and add Terminal.app"
echo "   5. Restart Terminal after adding"
echo ""
read -p "Press Enter after granting Full Disk Access to Terminal..."

# Create the enhanced bridge server
echo "🔧 Creating bridge server..."
cat > bridge.js << 'EOF'
const WebSocket = require('ws');
const sqlite3 = require('sqlite3').verbose();
const os = require('os');
const path = require('path');
const { exec } = require('child_process');
const fs = require('fs');
const fsExtra = require('fs-extra');

class MacOSBridge {
  constructor() {
    this.wss = new WebSocket.Server({ port: 8888 });
    this.messagesDbPath = path.join(os.homedir(), 'Library/Messages/chat.db');
    this.db = null;
    this.lastMessageId = 0;
    
    console.log('🔥 macOS Bridge Starting...');
    console.log(`Messages DB: ${this.messagesDbPath}`);
    console.log('Listening on port 8888');
  }

  async initialize() {
    // Check if Messages database exists
    if (!fs.existsSync(this.messagesDbPath)) {
      console.log('❌ Messages database not found');
      console.log('Make sure Messages app has been opened at least once');
      return;
    }

    // Open Messages database (read-only)
    try {
      this.db = new sqlite3.Database(this.messagesDbPath, sqlite3.OPEN_READONLY);
      console.log('✅ Connected to Messages database');
    } catch (error) {
      console.log('❌ Failed to open Messages database:', error.message);
      return;
    }

    // Handle WebSocket connections
    this.wss.on('connection', (ws) => {
      console.log('✅ Windows app connected!');
      
      ws.on('message', async (data) => {
        try {
          const message = JSON.parse(data);
          
          if (message.type === 'SYNC_ALL') {
            await this.sendFullSync(ws);
          } else if (message.type === 'SEND_MESSAGE') {
            await this.sendMessage(message.phoneNumber, message.text);
          } else if (message.type === 'TEST') {
            ws.send(JSON.stringify({ type: 'TEST_RESPONSE', status: 'OK' }));
          }
        } catch (error) {
          console.log('Error handling message:', error.message);
        }
      });
      
      ws.on('close', () => {
        console.log('Windows app disconnected');
      });
    });

    // Start monitoring for new messages
    this.startMonitoring();
    
    console.log('✅ Bridge ready!');
  }

  async sendFullSync(ws) {
    console.log('📤 Sending full sync to Windows...');
    
    try {
      const messages = await this.getRecentMessages(500);
      const contacts = await this.getContacts();
      
      ws.send(JSON.stringify({
        type: 'FULL_SYNC',
        messages: messages,
        contacts: contacts,
        timestamp: Date.now()
      }));
      
      console.log(`✅ Sent ${messages.length} messages and ${contacts.length} contacts`);
    } catch (error) {
      console.log('Error during sync:', error.message);
    }
  }

  async getRecentMessages(limit = 100) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT 
          m.ROWID as id,
          m.text,
          m.date,
          m.is_from_me,
          h.id as phone_number,
          c.display_name as contact_name,
          m.service as service_type
        FROM message m
        LEFT JOIN handle h ON m.handle_id = h.ROWID
        LEFT JOIN chat_message_join cmj ON m.ROWID = cmj.message_id
        LEFT JOIN chat c ON cmj.chat_id = c.ROWID
        WHERE m.text IS NOT NULL AND m.text != ''
        ORDER BY m.date DESC
        LIMIT ?
      `;
      
      this.db.all(query, [limit], (err, rows) => {
        if (err) {
          console.error('Error reading messages:', err);
          resolve([]);
        } else {
          const messages = rows.map(row => ({
            id: `imessage-${row.id}`,
            text: row.text,
            phoneNumber: row.phone_number,
            contactName: row.contact_name || row.phone_number,
            timestamp: new Date(row.date / 1000000 + 978307200000), // Convert Apple timestamp
            isIncoming: !row.is_from_me,
            isRead: true,
            source: 'macOS-Messages',
            serviceType: row.service_type
          }));
          
          // Update last message ID
          if (messages.length > 0) {
            this.lastMessageId = Math.max(...messages.map(m => parseInt(m.id.split('-')[1])));
          }
          
          resolve(messages);
        }
      });
    });
  }

  async getContacts() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT DISTINCT
          h.id as phone_number,
          c.display_name as contact_name
        FROM handle h
        LEFT JOIN chat_handle_join chj ON h.ROWID = chj.handle_id
        LEFT JOIN chat c ON chj.chat_id = c.ROWID
        WHERE h.id IS NOT NULL AND h.id != ''
        ORDER BY c.display_name
      `;
      
      this.db.all(query, [], (err, rows) => {
        if (err) {
          console.error('Error reading contacts:', err);
          resolve([]);
        } else {
          resolve(rows.filter(row => row.phone_number));
        }
      });
    });
  }

  async sendMessage(phoneNumber, text) {
    console.log(`📤 Sending message to ${phoneNumber}: ${text}`);
    
    // Use AppleScript to send message
    const script = `
      tell application "Messages"
        set targetService to 1st service whose service type = iMessage
        set targetBuddy to buddy "${phoneNumber}" of targetService
        send "${text}" to targetBuddy
      end tell
    `;
    
    exec(`osascript -e '${script}'`, (error, stdout, stderr) => {
      if (error) {
        console.error('Failed to send message:', error.message);
      } else {
        console.log(`✅ Message sent to ${phoneNumber}`);
      }
    });
  }

  startMonitoring() {
    // Check for new messages every 5 seconds
    setInterval(async () => {
      try {
        const newMessages = await this.getNewMessages();
        
        if (newMessages.length > 0) {
          console.log(`📨 Found ${newMessages.length} new messages`);
          
          // Broadcast to all connected clients
          this.wss.clients.forEach((client) => {
            if (client.readyState === WebSocket.OPEN) {
              newMessages.forEach(msg => {
                client.send(JSON.stringify({
                  type: 'NEW_MESSAGE',
                  message: msg
                }));
              });
            }
          });
        }
      } catch (error) {
        console.log('Error monitoring messages:', error.message);
      }
    }, 5000);
  }

  async getNewMessages() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT 
          m.ROWID as id,
          m.text,
          m.date,
          m.is_from_me,
          h.id as phone_number,
          c.display_name as contact_name
        FROM message m
        LEFT JOIN handle h ON m.handle_id = h.ROWID
        LEFT JOIN chat_message_join cmj ON m.ROWID = cmj.message_id
        LEFT JOIN chat c ON cmj.chat_id = c.ROWID
        WHERE m.ROWID > ? AND m.text IS NOT NULL AND m.text != ''
        ORDER BY m.date DESC
      `;
      
      this.db.all(query, [this.lastMessageId], (err, rows) => {
        if (err) {
          resolve([]);
        } else {
          const messages = rows.map(row => ({
            id: `imessage-${row.id}`,
            text: row.text,
            phoneNumber: row.phone_number,
            contactName: row.contact_name || row.phone_number,
            timestamp: new Date(row.date / 1000000 + 978307200000),
            isIncoming: !row.is_from_me,
            isRead: true,
            source: 'macOS-Messages'
          }));
          
          // Update last message ID
          if (messages.length > 0) {
            this.lastMessageId = Math.max(...messages.map(m => parseInt(m.id.split('-')[1])));
          }
          
          resolve(messages);
        }
      });
    });
  }
}

// Start the bridge
const bridge = new MacOSBridge();
bridge.initialize().catch(console.error);

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down bridge...');
  process.exit(0);
});
EOF

# Create startup script
cat > start-bridge.sh << 'EOF'
#!/bin/bash
echo "🍎 Starting macOS Bridge..."
cd ~/iphone-bridge
node bridge.js
EOF

chmod +x start-bridge.sh

# Create auto-start plist for launchd
mkdir -p ~/Library/LaunchAgents

cat > ~/Library/LaunchAgents/com.iphonecompanion.bridge.plist << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.iphonecompanion.bridge</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/local/bin/node</string>
        <string>$HOME/iphone-bridge/bridge.js</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardOutPath</key>
    <string>$HOME/iphone-bridge/bridge.log</string>
    <key>StandardErrorPath</key>
    <string>$HOME/iphone-bridge/bridge.error.log</string>
</dict>
</plist>
EOF

echo "✅ macOS Bridge setup complete!"
echo ""
echo "To start the bridge:"
echo "  ./start-bridge.sh"
echo ""
echo "To auto-start on boot:"
echo "  launchctl load ~/Library/LaunchAgents/com.iphonecompanion.bridge.plist"
echo ""
echo "Bridge will be available at ws://[VM-IP]:8888"
echo "Check your VM's IP address with: ifconfig"
