/* Sync Manager Styles */
.sync-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.sync-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.sync-header h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.sync-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.sync-content section {
    margin-bottom: 2rem;
}

.sync-content h2 {
    margin: 0 0 1rem 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Status Grid */
.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.status-card {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.status-card:hover {
    border-color: var(--accent-color);
}

.status-icon {
    font-size: 2rem;
    margin-right: 1rem;
}

.status-info {
    flex: 1;
}

.status-info h3 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 500;
}

.status-text {
    margin: 0 0 0.25rem 0;
    font-weight: 500;
}

.status-text.status-synced {
    color: var(--success-color);
}

.status-text.status-syncing {
    color: var(--warning-color);
}

.status-text.status-paused {
    color: var(--text-secondary);
}

.status-text.status-error {
    color: var(--error-color);
}

.last-sync {
    margin: 0;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.status-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    background: none;
    border: none;
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background: var(--bg-hover);
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.setting-group {
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.setting-group h3 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    font-size: 0.9rem;
    color: var(--text-primary);
}

.setting-item select {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.8rem;
}

/* Switch */
.switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-hover);
    transition: 0.4s;
    border-radius: 20px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--accent-color);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* Data Types */
.data-types {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    cursor: pointer;
}

/* History */
.history-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.history-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
}

.history-item {
    display: flex;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
}

.history-item:last-child {
    border-bottom: none;
}

.history-time {
    width: 100px;
    font-size: 0.8rem;
    color: var(--text-secondary);
    flex-shrink: 0;
}

.history-message {
    flex: 1;
    font-size: 0.9rem;
}

/* Statistics */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-card {
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    text-align: center;
}

.stat-card h3 {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.stat-value {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--accent-color);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    min-width: 400px;
    max-width: 500px;
}

.modal-content h3 {
    margin: 0 0 1rem 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.progress-container {
    margin-bottom: 1.5rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: var(--accent-color);
    transition: width 0.3s ease;
}

.progress-text {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background: var(--accent-hover);
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-hover);
}

.icon {
    font-size: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .sync-content {
        padding: 1rem;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .data-types {
        grid-template-columns: 1fr;
    }
    
    .history-controls {
        flex-direction: column;
    }
}
