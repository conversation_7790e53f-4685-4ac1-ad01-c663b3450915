/* Logs View Styling */
.logs-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 180px);
    gap: 15px;
    padding: 20px;
    background: var(--background);
}

/* Log Controls */
.logs-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.log-filters {
    display: flex;
    gap: 8px;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 1px solid var(--border);
    background: var(--background);
    color: var(--text-secondary);
    border-radius: 8px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-btn:hover {
    background: var(--hover-bg);
    border-color: var(--primary);
}

.filter-btn.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.filter-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.filter-dot.all { background: #666; }
.filter-dot.info { background: #3b82f6; }
.filter-dot.warn { background: #f59e0b; }
.filter-dot.error { background: #ef4444; }
.filter-dot.debug { background: #8b5cf6; }

.log-actions {
    display: flex;
    gap: 8px;
}

.log-action-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 1px solid var(--border);
    background: var(--background);
    color: var(--text-secondary);
    border-radius: 8px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.log-action-btn:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
}

.log-action-btn.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

/* Log Search */
.log-search {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border);
}

#log-search-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid var(--border);
    background: var(--background);
    color: var(--text-primary);
    border-radius: 8px;
    font-size: 14px;
}

#log-search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-stats {
    font-size: 13px;
    color: var(--text-secondary);
    white-space: nowrap;
}

/* Log Display */
.log-display {
    flex: 1;
    background: #1e1e1e;
    border-radius: 12px;
    border: 1px solid var(--border);
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.log-entry {
    display: grid;
    grid-template-columns: 80px 60px 150px 1fr;
    gap: 12px;
    padding: 8px 15px;
    border-bottom: 1px solid #333;
    transition: background-color 0.1s ease;
}

.log-entry:hover {
    background: #2a2a2a;
}

.log-entry.hidden {
    display: none;
}

.log-entry.info { border-left: 3px solid #3b82f6; }
.log-entry.warn { border-left: 3px solid #f59e0b; }
.log-entry.error { border-left: 3px solid #ef4444; }
.log-entry.debug { border-left: 3px solid #8b5cf6; }
.log-entry.success { border-left: 3px solid #10b981; }

.log-time {
    color: #888;
    font-size: 12px;
    font-weight: 500;
}

.log-level {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 4px;
    text-align: center;
}

.log-level.info { 
    background: rgba(59, 130, 246, 0.2); 
    color: #60a5fa; 
}
.log-level.warn { 
    background: rgba(245, 158, 11, 0.2); 
    color: #fbbf24; 
}
.log-level.error { 
    background: rgba(239, 68, 68, 0.2); 
    color: #f87171; 
}
.log-level.debug { 
    background: rgba(139, 92, 246, 0.2); 
    color: #a78bfa; 
}

.log-source {
    color: #aaa;
    font-weight: 500;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.log-message {
    color: #fff;
    word-break: break-word;
}

/* Developer Console */
.developer-console {
    background: #1e1e1e;
    border-radius: 12px;
    border: 1px solid var(--border);
    margin-top: 15px;
    overflow: hidden;
}

.console-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #2a2a2a;
    border-bottom: 1px solid #333;
}

.console-header h3 {
    margin: 0;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
}

.console-controls {
    display: flex;
    gap: 8px;
}

.console-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    border: 1px solid #444;
    background: #333;
    color: #ccc;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.console-btn:hover {
    background: #404040;
    color: #fff;
}

.console-output {
    height: 200px;
    overflow-y: auto;
    padding: 10px 15px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.console-line {
    margin-bottom: 4px;
    color: #ccc;
}

.console-prompt {
    color: #10b981;
    font-weight: 600;
    margin-right: 8px;
}

.console-text {
    color: #fff;
}

.console-text.error {
    color: #ef4444;
}

.console-text.success {
    color: #10b981;
}

.console-text.warning {
    color: #f59e0b;
}

.console-input-container {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background: #2a2a2a;
    border-top: 1px solid #333;
}

.console-input {
    flex: 1;
    background: transparent;
    border: none;
    color: #fff;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    outline: none;
}

.console-input::placeholder {
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .logs-controls {
        flex-direction: column;
        gap: 10px;
    }
    
    .log-filters {
        flex-wrap: wrap;
    }
    
    .log-entry {
        grid-template-columns: 60px 50px 100px 1fr;
        gap: 8px;
        font-size: 12px;
    }
    
    .console-output {
        height: 150px;
    }
}

/* Scrollbar Styling */
.log-display::-webkit-scrollbar,
.console-output::-webkit-scrollbar {
    width: 8px;
}

.log-display::-webkit-scrollbar-track,
.console-output::-webkit-scrollbar-track {
    background: #2a2a2a;
}

.log-display::-webkit-scrollbar-thumb,
.console-output::-webkit-scrollbar-thumb {
    background: #444;
    border-radius: 4px;
}

.log-display::-webkit-scrollbar-thumb:hover,
.console-output::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Animation for new log entries */
@keyframes logEntryFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.log-entry.new {
    animation: logEntryFadeIn 0.3s ease;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-indicator.online { background: #10b981; }
.status-indicator.offline { background: #ef4444; }
.status-indicator.warning { background: #f59e0b; }
.status-indicator.processing { 
    background: #3b82f6;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}