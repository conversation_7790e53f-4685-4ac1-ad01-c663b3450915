<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sync Manager - iPhone Companion Pro</title>
    <link rel="stylesheet" href="styles/sync-manager.css">
    <link rel="stylesheet" href="styles/common.css">
</head>
<body>
    <div class="sync-container">
        <header class="sync-header">
            <h1>Sync Manager</h1>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="forceSyncAll()">
                    <span class="icon">🔄</span>
                    Sync All
                </button>
                <button class="close-btn" onclick="window.close()">×</button>
            </div>
        </header>

        <div class="sync-content">
            <!-- Sync Status Overview -->
            <section class="sync-overview">
                <h2>Sync Status</h2>
                <div class="status-grid">
                    <div class="status-card" data-type="messages">
                        <div class="status-icon">💬</div>
                        <div class="status-info">
                            <h3>Messages</h3>
                            <p class="status-text">Synced</p>
                            <p class="last-sync">Last: 2 minutes ago</p>
                        </div>
                        <div class="status-actions">
                            <button class="btn-icon" onclick="syncData('messages')" title="Sync Now">🔄</button>
                            <button class="btn-icon" onclick="toggleSync('messages')" title="Toggle Auto-sync">⏸️</button>
                        </div>
                    </div>

                    <div class="status-card" data-type="contacts">
                        <div class="status-icon">👥</div>
                        <div class="status-info">
                            <h3>Contacts</h3>
                            <p class="status-text">Synced</p>
                            <p class="last-sync">Last: 5 minutes ago</p>
                        </div>
                        <div class="status-actions">
                            <button class="btn-icon" onclick="syncData('contacts')" title="Sync Now">🔄</button>
                            <button class="btn-icon" onclick="toggleSync('contacts')" title="Toggle Auto-sync">⏸️</button>
                        </div>
                    </div>

                    <div class="status-card" data-type="calls">
                        <div class="status-icon">📞</div>
                        <div class="status-info">
                            <h3>Call History</h3>
                            <p class="status-text">Synced</p>
                            <p class="last-sync">Last: 1 minute ago</p>
                        </div>
                        <div class="status-actions">
                            <button class="btn-icon" onclick="syncData('calls')" title="Sync Now">🔄</button>
                            <button class="btn-icon" onclick="toggleSync('calls')" title="Toggle Auto-sync">⏸️</button>
                        </div>
                    </div>

                    <div class="status-card" data-type="photos">
                        <div class="status-icon">📷</div>
                        <div class="status-info">
                            <h3>Photos</h3>
                            <p class="status-text">Paused</p>
                            <p class="last-sync">Last: 1 hour ago</p>
                        </div>
                        <div class="status-actions">
                            <button class="btn-icon" onclick="syncData('photos')" title="Sync Now">🔄</button>
                            <button class="btn-icon" onclick="toggleSync('photos')" title="Toggle Auto-sync">▶️</button>
                        </div>
                    </div>

                    <div class="status-card" data-type="notifications">
                        <div class="status-icon">🔔</div>
                        <div class="status-info">
                            <h3>Notifications</h3>
                            <p class="status-text">Synced</p>
                            <p class="last-sync">Last: 30 seconds ago</p>
                        </div>
                        <div class="status-actions">
                            <button class="btn-icon" onclick="syncData('notifications')" title="Sync Now">🔄</button>
                            <button class="btn-icon" onclick="toggleSync('notifications')" title="Toggle Auto-sync">⏸️</button>
                        </div>
                    </div>

                    <div class="status-card" data-type="files">
                        <div class="status-icon">📁</div>
                        <div class="status-info">
                            <h3>Files</h3>
                            <p class="status-text">Syncing...</p>
                            <p class="last-sync">Progress: 45%</p>
                        </div>
                        <div class="status-actions">
                            <button class="btn-icon" onclick="syncData('files')" title="Sync Now">🔄</button>
                            <button class="btn-icon" onclick="toggleSync('files')" title="Toggle Auto-sync">⏸️</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Sync Settings -->
            <section class="sync-settings">
                <h2>Sync Settings</h2>
                <div class="settings-grid">
                    <div class="setting-group">
                        <h3>Auto-Sync</h3>
                        <div class="setting-item">
                            <label class="switch">
                                <input type="checkbox" id="autoSyncEnabled" checked>
                                <span class="slider"></span>
                            </label>
                            <span>Enable automatic synchronization</span>
                        </div>
                        <div class="setting-item">
                            <label for="syncInterval">Sync Interval:</label>
                            <select id="syncInterval">
                                <option value="realtime">Real-time</option>
                                <option value="30">30 seconds</option>
                                <option value="60">1 minute</option>
                                <option value="300">5 minutes</option>
                                <option value="900">15 minutes</option>
                            </select>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3>Data Types</h3>
                        <div class="data-types">
                            <label class="checkbox-item">
                                <input type="checkbox" checked> Messages
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" checked> Contacts
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" checked> Call History
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox"> Photos
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" checked> Notifications
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox"> Files
                            </label>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3>Conflict Resolution</h3>
                        <div class="setting-item">
                            <label for="conflictResolution">When conflicts occur:</label>
                            <select id="conflictResolution">
                                <option value="ask">Ask me</option>
                                <option value="phone">Prefer iPhone data</option>
                                <option value="pc">Prefer PC data</option>
                                <option value="newest">Use newest data</option>
                            </select>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Sync History -->
            <section class="sync-history">
                <h2>Sync History</h2>
                <div class="history-controls">
                    <button class="btn btn-secondary" onclick="clearHistory()">Clear History</button>
                    <button class="btn btn-secondary" onclick="exportHistory()">Export Log</button>
                </div>
                <div class="history-list" id="syncHistory">
                    <!-- History items will be populated by JavaScript -->
                </div>
            </section>

            <!-- Sync Statistics -->
            <section class="sync-stats">
                <h2>Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Total Syncs Today</h3>
                        <p class="stat-value">247</p>
                    </div>
                    <div class="stat-card">
                        <h3>Data Transferred</h3>
                        <p class="stat-value">1.2 GB</p>
                    </div>
                    <div class="stat-card">
                        <h3>Last Full Sync</h3>
                        <p class="stat-value">2 hours ago</p>
                    </div>
                    <div class="stat-card">
                        <h3>Sync Errors</h3>
                        <p class="stat-value">0</p>
                    </div>
                </div>
            </section>
        </div>

        <!-- Sync Progress Modal -->
        <div id="syncModal" class="modal">
            <div class="modal-content">
                <h3>Syncing Data</h3>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="syncProgress"></div>
                    </div>
                    <p class="progress-text" id="syncProgressText">Preparing...</p>
                </div>
                <div class="modal-actions">
                    <button class="btn btn-secondary" onclick="cancelSync()">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sync Manager JavaScript
        class SyncManager {
            constructor() {
                this.syncStatus = {
                    messages: { enabled: true, lastSync: Date.now() - 120000, status: 'synced' },
                    contacts: { enabled: true, lastSync: Date.now() - 300000, status: 'synced' },
                    calls: { enabled: true, lastSync: Date.now() - 60000, status: 'synced' },
                    photos: { enabled: false, lastSync: Date.now() - 3600000, status: 'paused' },
                    notifications: { enabled: true, lastSync: Date.now() - 30000, status: 'synced' },
                    files: { enabled: true, lastSync: Date.now() - 180000, status: 'syncing', progress: 45 }
                };

                this.syncHistory = [];
                this.init();
            }

            async init() {
                await this.loadSyncStatus();
                this.updateUI();
                this.loadSyncHistory();
                this.startStatusUpdates();
            }

            async loadSyncStatus() {
                try {
                    const result = await window.electronAPI.invoke('get-sync-status');
                    if (result.success) {
                        this.syncStatus = { ...this.syncStatus, ...result.data };
                    }
                } catch (error) {
                    console.error('Failed to load sync status:', error);
                }
            }

            updateUI() {
                // Update status cards
                Object.keys(this.syncStatus).forEach(type => {
                    const card = document.querySelector(`[data-type="${type}"]`);
                    if (card) {
                        const statusText = card.querySelector('.status-text');
                        const lastSync = card.querySelector('.last-sync');
                        const toggleBtn = card.querySelector('.btn-icon:last-child');

                        const status = this.syncStatus[type];
                        statusText.textContent = this.getStatusText(status);
                        statusText.className = `status-text status-${status.status}`;

                        if (status.progress !== undefined) {
                            lastSync.textContent = `Progress: ${status.progress}%`;
                        } else {
                            lastSync.textContent = `Last: ${this.formatTimeAgo(status.lastSync)}`;
                        }

                        toggleBtn.textContent = status.enabled ? '⏸️' : '▶️';
                        toggleBtn.title = status.enabled ? 'Pause Auto-sync' : 'Resume Auto-sync';
                    }
                });
            }

            getStatusText(status) {
                switch (status.status) {
                    case 'synced': return 'Synced';
                    case 'syncing': return 'Syncing...';
                    case 'paused': return 'Paused';
                    case 'error': return 'Error';
                    default: return 'Unknown';
                }
            }

            formatTimeAgo(timestamp) {
                const now = Date.now();
                const diff = now - timestamp;

                if (diff < 60000) return `${Math.floor(diff / 1000)} seconds ago`;
                if (diff < 3600000) return `${Math.floor(diff / 60000)} minutes ago`;
                if (diff < 86400000) return `${Math.floor(diff / 3600000)} hours ago`;
                return `${Math.floor(diff / 86400000)} days ago`;
            }

            async syncData(type) {
                try {
                    this.showSyncModal(type);

                    const result = await window.electronAPI.invoke('force-sync', type);
                    if (result.success) {
                        this.syncStatus[type].lastSync = Date.now();
                        this.syncStatus[type].status = 'synced';
                        this.addToHistory(`${type} sync completed successfully`);
                    } else {
                        this.syncStatus[type].status = 'error';
                        this.addToHistory(`${type} sync failed: ${result.error}`);
                    }

                    this.updateUI();
                    this.hideSyncModal();
                } catch (error) {
                    console.error('Sync failed:', error);
                    this.hideSyncModal();
                }
            }

            async toggleSync(type) {
                this.syncStatus[type].enabled = !this.syncStatus[type].enabled;

                if (this.syncStatus[type].enabled) {
                    this.syncStatus[type].status = 'synced';
                } else {
                    this.syncStatus[type].status = 'paused';
                }

                this.updateUI();
                this.addToHistory(`${type} auto-sync ${this.syncStatus[type].enabled ? 'enabled' : 'disabled'}`);
            }

            async forceSyncAll() {
                const enabledTypes = Object.keys(this.syncStatus).filter(type => this.syncStatus[type].enabled);

                for (const type of enabledTypes) {
                    await this.syncData(type);
                }
            }

            showSyncModal(type) {
                const modal = document.getElementById('syncModal');
                const progressText = document.getElementById('syncProgressText');
                const progressFill = document.getElementById('syncProgress');

                progressText.textContent = `Syncing ${type}...`;
                progressFill.style.width = '0%';
                modal.style.display = 'flex';

                // Simulate progress
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 20;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                    }
                    progressFill.style.width = `${progress}%`;
                }, 200);

                this.currentSyncInterval = interval;
            }

            hideSyncModal() {
                const modal = document.getElementById('syncModal');
                modal.style.display = 'none';

                if (this.currentSyncInterval) {
                    clearInterval(this.currentSyncInterval);
                }
            }

            cancelSync() {
                this.hideSyncModal();
                this.addToHistory('Sync cancelled by user');
            }

            addToHistory(message) {
                const historyItem = {
                    timestamp: Date.now(),
                    message: message
                };

                this.syncHistory.unshift(historyItem);
                if (this.syncHistory.length > 100) {
                    this.syncHistory = this.syncHistory.slice(0, 100);
                }

                this.updateHistoryUI();
            }

            loadSyncHistory() {
                // Add some sample history
                const sampleHistory = [
                    { timestamp: Date.now() - 60000, message: 'Messages sync completed successfully' },
                    { timestamp: Date.now() - 120000, message: 'Contacts sync completed successfully' },
                    { timestamp: Date.now() - 180000, message: 'Call history sync completed successfully' },
                    { timestamp: Date.now() - 300000, message: 'Full sync completed successfully' }
                ];

                this.syncHistory = sampleHistory;
                this.updateHistoryUI();
            }

            updateHistoryUI() {
                const historyContainer = document.getElementById('syncHistory');
                historyContainer.innerHTML = '';

                this.syncHistory.slice(0, 20).forEach(item => {
                    const historyItem = document.createElement('div');
                    historyItem.className = 'history-item';
                    historyItem.innerHTML = `
                        <div class="history-time">${new Date(item.timestamp).toLocaleTimeString()}</div>
                        <div class="history-message">${item.message}</div>
                    `;
                    historyContainer.appendChild(historyItem);
                });
            }

            clearHistory() {
                if (confirm('Are you sure you want to clear the sync history?')) {
                    this.syncHistory = [];
                    this.updateHistoryUI();
                }
            }

            exportHistory() {
                const data = this.syncHistory.map(item => ({
                    timestamp: new Date(item.timestamp).toISOString(),
                    message: item.message
                }));

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `sync-history-${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);
            }

            startStatusUpdates() {
                setInterval(() => {
                    // Update last sync times
                    Object.keys(this.syncStatus).forEach(type => {
                        if (this.syncStatus[type].enabled && this.syncStatus[type].status === 'synced') {
                            // Simulate periodic syncs
                            if (Math.random() < 0.1) {
                                this.syncStatus[type].lastSync = Date.now();
                            }
                        }
                    });

                    this.updateUI();
                }, 30000); // Update every 30 seconds
            }
        }

        // Global functions
        let syncManager;

        function syncData(type) {
            if (syncManager) {
                syncManager.syncData(type);
            }
        }

        function toggleSync(type) {
            if (syncManager) {
                syncManager.toggleSync(type);
            }
        }

        function forceSyncAll() {
            if (syncManager) {
                syncManager.forceSyncAll();
            }
        }

        function cancelSync() {
            if (syncManager) {
                syncManager.cancelSync();
            }
        }

        function clearHistory() {
            if (syncManager) {
                syncManager.clearHistory();
            }
        }

        function exportHistory() {
            if (syncManager) {
                syncManager.exportHistory();
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            syncManager = new SyncManager();
        });
    </script>
</body>
</html>
