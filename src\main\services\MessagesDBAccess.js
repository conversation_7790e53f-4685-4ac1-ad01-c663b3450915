const { EventEmitter } = require('events');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class MessagesDBAccess extends EventEmitter {
  constructor(vmBridge) {
    super();
    this.vmBridge = vmBridge;
    this.messagesDb = null;
    this.contactsDb = null;
    this.isConnected = false;
    this.lastMessageId = 0;
    this.messageWatcher = null;
    this.dbPaths = {
      messages: '/Users/<USER>/Library/Messages/chat.db',
      contacts: '/Users/<USER>/Library/Application Support/AddressBook/AddressBook-v22.abcddb'
    };
  }

  // Initialize database connections through VM bridge
  async initialize() {
    try {
      this.emit('status', 'Initializing Messages database access...');
      
      // Connect to databases through VM
      await this.connectToDatabases();
      
      // Start message monitoring
      await this.startMessageMonitoring();
      
      this.isConnected = true;
      this.emit('initialized');
      
    } catch (error) {
      this.emit('error', `Failed to initialize database access: ${error.message}`);
      throw error;
    }
  }

  async connectToDatabases() {
    this.emit('status', 'Connecting to macOS databases...');
    
    // Check if databases exist in VM
    const dbExists = await this.vmBridge.executeInVM(`test -f "${this.dbPaths.messages}" && echo "exists"`);
    
    if (!dbExists.includes('exists')) {
      throw new Error('Messages database not found in macOS VM');
    }
    
    // Test database access
    await this.testDatabaseAccess();
    
    this.emit('status', 'Database connections established');
  }

  async testDatabaseAccess() {
    const testQuery = 'SELECT COUNT(*) as count FROM message LIMIT 1';
    const result = await this.executeQuery(testQuery);
    
    if (!result || result.length === 0) {
      throw new Error('Unable to access Messages database');
    }
    
    console.log(`Messages database accessible with ${result[0].count} messages`);
  }

  // Execute SQL query through VM bridge
  async executeQuery(query, params = []) {
    const command = this.buildSQLiteCommand(query, params);
    const result = await this.vmBridge.executeInVM(command);
    
    try {
      return JSON.parse(result);
    } catch (error) {
      console.error('Failed to parse query result:', error);
      return [];
    }
  }

  buildSQLiteCommand(query, params = []) {
    // Escape parameters for shell safety
    const escapedParams = params.map(param => 
      typeof param === 'string' ? `"${param.replace(/"/g, '\\"')}"` : param
    );
    
    // Build sqlite3 command with JSON output
    const sqliteCmd = `sqlite3 -json "${this.dbPaths.messages}" "${query.replace(/"/g, '\\"')}"`;
    
    return sqliteCmd;
  }

  // Get recent messages from database
  async getRecentMessages(limit = 100, offset = 0) {
    const query = `
      SELECT 
        m.ROWID as id,
        m.text,
        m.date,
        m.date_read,
        m.date_delivered,
        m.is_from_me,
        m.is_read,
        m.service,
        m.account,
        m.subject,
        m.balloon_bundle_id,
        h.id as phone_number,
        h.service as handle_service,
        c.chat_identifier,
        c.display_name as chat_name,
        c.room_name
      FROM message m
      LEFT JOIN handle h ON m.handle_id = h.ROWID
      LEFT JOIN chat_message_join cmj ON m.ROWID = cmj.message_id
      LEFT JOIN chat c ON cmj.chat_id = c.ROWID
      ORDER BY m.date DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    const messages = await this.executeQuery(query);
    
    // Process and format messages
    return messages.map(message => this.formatMessage(message));
  }

  // Get messages for specific conversation
  async getConversationMessages(phoneNumber, limit = 50) {
    const query = `
      SELECT 
        m.ROWID as id,
        m.text,
        m.date,
        m.date_read,
        m.date_delivered,
        m.is_from_me,
        m.is_read,
        m.service,
        h.id as phone_number
      FROM message m
      JOIN handle h ON m.handle_id = h.ROWID
      JOIN chat_message_join cmj ON m.ROWID = cmj.message_id
      JOIN chat c ON cmj.chat_id = c.ROWID
      WHERE h.id LIKE '%${phoneNumber}%'
      ORDER BY m.date DESC
      LIMIT ${limit}
    `;

    const messages = await this.executeQuery(query);
    return messages.map(message => this.formatMessage(message));
  }

  // Get all conversations
  async getConversations() {
    const query = `
      SELECT 
        c.ROWID as chat_id,
        c.chat_identifier,
        c.display_name,
        c.room_name,
        h.id as phone_number,
        MAX(m.date) as last_message_date,
        COUNT(m.ROWID) as message_count,
        SUM(CASE WHEN m.is_read = 0 AND m.is_from_me = 0 THEN 1 ELSE 0 END) as unread_count
      FROM chat c
      LEFT JOIN chat_message_join cmj ON c.ROWID = cmj.chat_id
      LEFT JOIN message m ON cmj.message_id = m.ROWID
      LEFT JOIN chat_handle_join chj ON c.ROWID = chj.chat_id
      LEFT JOIN handle h ON chj.handle_id = h.ROWID
      GROUP BY c.ROWID
      ORDER BY last_message_date DESC
    `;

    const conversations = await this.executeQuery(query);
    return conversations.map(conv => this.formatConversation(conv));
  }

  // Search messages
  async searchMessages(searchTerm, limit = 50) {
    const query = `
      SELECT 
        m.ROWID as id,
        m.text,
        m.date,
        m.is_from_me,
        h.id as phone_number,
        c.display_name as chat_name
      FROM message m
      LEFT JOIN handle h ON m.handle_id = h.ROWID
      LEFT JOIN chat_message_join cmj ON m.ROWID = cmj.message_id
      LEFT JOIN chat c ON cmj.chat_id = c.ROWID
      WHERE m.text LIKE '%${searchTerm}%'
      ORDER BY m.date DESC
      LIMIT ${limit}
    `;

    const messages = await this.executeQuery(query);
    return messages.map(message => this.formatMessage(message));
  }

  // Get message attachments
  async getMessageAttachments(messageId) {
    const query = `
      SELECT 
        a.ROWID as attachment_id,
        a.filename,
        a.mime_type,
        a.total_bytes,
        a.created_date,
        a.start_date,
        a.user_info
      FROM attachment a
      JOIN message_attachment_join maj ON a.ROWID = maj.attachment_id
      WHERE maj.message_id = ${messageId}
    `;

    const attachments = await this.executeQuery(query);
    return attachments.map(attachment => this.formatAttachment(attachment));
  }

  // Send message through AppleScript
  async sendMessage(phoneNumber, text) {
    const script = `
      tell application "Messages"
        set targetService to 1st service whose service type = iMessage
        set targetBuddy to buddy "${phoneNumber}" of targetService
        send "${text.replace(/"/g, '\\"')}" to targetBuddy
      end tell
    `;
    
    try {
      await this.vmBridge.executeInVM(`osascript -e '${script}'`);
      this.emit('message-sent', { phoneNumber, text, timestamp: Date.now() });
      return true;
    } catch (error) {
      this.emit('error', `Failed to send message: ${error.message}`);
      throw error;
    }
  }

  // Start monitoring for new messages
  async startMessageMonitoring() {
    this.emit('status', 'Starting message monitoring...');
    
    // Get current latest message ID
    const latestQuery = 'SELECT MAX(ROWID) as max_id FROM message';
    const result = await this.executeQuery(latestQuery);
    
    if (result && result.length > 0) {
      this.lastMessageId = result[0].max_id || 0;
      console.log(`Starting message monitoring from ID: ${this.lastMessageId}`);
      
      // Start polling for new messages
      this.messageWatcher = setInterval(() => {
        this.checkForNewMessages();
      }, 3000); // Check every 3 seconds
    }
  }

  async checkForNewMessages() {
    try {
      const query = `
        SELECT 
          m.ROWID as id,
          m.text,
          m.date,
          m.is_from_me,
          h.id as phone_number,
          c.display_name as chat_name
        FROM message m
        LEFT JOIN handle h ON m.handle_id = h.ROWID
        LEFT JOIN chat_message_join cmj ON m.ROWID = cmj.message_id
        LEFT JOIN chat c ON cmj.chat_id = c.ROWID
        WHERE m.ROWID > ${this.lastMessageId}
        ORDER BY m.date ASC
      `;
      
      const newMessages = await this.executeQuery(query);
      
      if (newMessages && newMessages.length > 0) {
        console.log(`Found ${newMessages.length} new messages`);
        
        for (const message of newMessages) {
          const formattedMessage = this.formatMessage(message);
          this.emit('new-message', formattedMessage);
          this.lastMessageId = Math.max(this.lastMessageId, message.id);
        }
      }
    } catch (error) {
      console.error('Error checking for new messages:', error);
    }
  }

  // Format message data
  formatMessage(message) {
    return {
      id: message.id,
      text: message.text || '',
      date: this.convertAppleTimestamp(message.date),
      dateRead: message.date_read ? this.convertAppleTimestamp(message.date_read) : null,
      dateDelivered: message.date_delivered ? this.convertAppleTimestamp(message.date_delivered) : null,
      isFromMe: Boolean(message.is_from_me),
      isRead: Boolean(message.is_read),
      service: message.service || 'iMessage',
      phoneNumber: message.phone_number || '',
      chatName: message.chat_name || '',
      subject: message.subject || null,
      balloonBundleId: message.balloon_bundle_id || null
    };
  }

  // Format conversation data
  formatConversation(conversation) {
    return {
      chatId: conversation.chat_id,
      chatIdentifier: conversation.chat_identifier,
      displayName: conversation.display_name || conversation.phone_number || 'Unknown',
      roomName: conversation.room_name,
      phoneNumber: conversation.phone_number,
      lastMessageDate: this.convertAppleTimestamp(conversation.last_message_date),
      messageCount: conversation.message_count || 0,
      unreadCount: conversation.unread_count || 0
    };
  }

  // Format attachment data
  formatAttachment(attachment) {
    return {
      id: attachment.attachment_id,
      filename: attachment.filename,
      mimeType: attachment.mime_type,
      size: attachment.total_bytes,
      createdDate: this.convertAppleTimestamp(attachment.created_date),
      startDate: this.convertAppleTimestamp(attachment.start_date),
      userInfo: attachment.user_info
    };
  }

  // Convert Apple timestamp to Unix timestamp
  convertAppleTimestamp(appleTimestamp) {
    if (!appleTimestamp) return null;
    // Apple timestamp is nanoseconds since 2001-01-01 00:00:00 UTC
    const appleEpoch = 978307200; // Seconds between 1970-01-01 and 2001-01-01
    return Math.floor(appleTimestamp / 1000000000) + appleEpoch;
  }

  // Get database statistics
  async getDatabaseStats() {
    const queries = {
      totalMessages: 'SELECT COUNT(*) as count FROM message',
      totalChats: 'SELECT COUNT(*) as count FROM chat',
      totalContacts: 'SELECT COUNT(DISTINCT handle_id) as count FROM message',
      unreadMessages: 'SELECT COUNT(*) as count FROM message WHERE is_read = 0 AND is_from_me = 0',
      todayMessages: `SELECT COUNT(*) as count FROM message WHERE date > ${(Date.now() - 86400000) * 1000000}`
    };

    const stats = {};
    
    for (const [key, query] of Object.entries(queries)) {
      try {
        const result = await this.executeQuery(query);
        stats[key] = result && result.length > 0 ? result[0].count : 0;
      } catch (error) {
        stats[key] = 0;
      }
    }

    return stats;
  }

  // Stop message monitoring
  stopMessageMonitoring() {
    if (this.messageWatcher) {
      clearInterval(this.messageWatcher);
      this.messageWatcher = null;
    }
  }

  // Cleanup and disconnect
  async disconnect() {
    this.emit('status', 'Disconnecting from Messages database...');
    
    this.stopMessageMonitoring();
    this.isConnected = false;
    
    this.emit('disconnected');
  }

  // Get connection status
  getStatus() {
    return {
      connected: this.isConnected,
      lastMessageId: this.lastMessageId,
      monitoring: !!this.messageWatcher
    };
  }
}

module.exports = MessagesDBAccess;
