const { EventEmitter } = require('events');

/**
 * Intel Unison Message Handler - CRITICAL PRIORITY
 * Handles SMS/iMessage sending and receiving exactly like Intel Unison
 */
class MessageHandler extends EventEmitter {
  constructor(connectionManager, database) {
    super();
    
    this.conn = connectionManager;
    this.db = database;
    this.pendingMessages = new Map();
    this.messageCache = new Map();
    
    // Message state tracking
    this.isInitialized = false;
    this.messageSequence = 0;
    
    // Intel Unison message constants
    this.MESSAGE_TIMEOUT = 30000; // 30 seconds
    this.MAX_RETRIES = 3;
    
    this.setupConnectionEvents();
  }

  async initialize() {
    console.log('📱 Initializing Message Handler...');
    
    try {
      // Set up message event handlers
      this.setupMessageHandlers();
      
      // Initialize message cache
      await this.loadRecentMessages();
      
      this.isInitialized = true;
      console.log('✅ Message Handler initialized');
      
    } catch (error) {
      console.error('❌ Message Handler initialization failed:', error);
      throw error;
    }
  }

  setupConnectionEvents() {
    this.conn.on('message-received', (message, device) => {
      this.handleIncomingMessage(message, device);
    });
    
    this.conn.on('device-connected', (device) => {
      console.log(`📱 Device connected for messaging: ${device.name}`);
      this.requestMessageSync(device);
    });
    
    this.conn.on('connection-lost', (device) => {
      console.log(`💔 Lost messaging connection to ${device.name}`);
      this.handleConnectionLoss(device);
    });
  }

  setupMessageHandlers() {
    // Handle different message types from iPhone
    this.messageHandlers = {
      'message-received': this.processIncomingMessage.bind(this),
      'message-status': this.processMessageStatus.bind(this),
      'message-sync': this.processMessageSync.bind(this),
      'message-delivery': this.processDeliveryReceipt.bind(this),
      'typing-indicator': this.processTypingIndicator.bind(this)
    };
  }

  /**
   * CRITICAL FUNCTION: Send SMS/iMessage to iPhone
   * This MUST work for the primary objective
   */
  async sendMessage(phoneNumber, messageText, options = {}) {
    console.log('🚨 SENDING MESSAGE - CRITICAL PATH');
    console.log(`📤 To: ${phoneNumber}`);
    console.log(`💬 Text: ${messageText}`);
    
    // Step 1: Validate inputs
    if (!phoneNumber || !messageText) {
      throw new Error('Phone number and message text are required');
    }
    
    // Step 2: Validate connection
    if (!this.conn.activeConnection) {
      throw new Error('No active connection to iPhone - cannot send message');
    }
    
    try {
      // Step 3: Format phone number (critical for delivery)
      const formattedNumber = this.formatPhoneNumber(phoneNumber);
      console.log(`📞 Formatted number: ${formattedNumber}`);
      
      // Step 4: Create message packet (Intel Unison format)
      const messageId = this.generateMessageId();
      const message = {
        type: 'send-message',
        action: 'send',
        payload: {
          id: messageId,
          threadId: this.generateThreadId(formattedNumber),
          participants: [formattedNumber],
          body: messageText,
          timestamp: Date.now(),
          messageType: options.isImessage ? 'imessage' : 'sms',
          priority: options.priority || 'normal',
          requireDeliveryReceipt: true
        }
      };
      
      console.log(`📦 Message packet created: ${messageId}`);
      
      // Step 5: Store message locally BEFORE sending
      await this.storeMessage(message.payload, true);
      console.log(`💾 Message stored locally: ${messageId}`);
      
      // Step 6: Send via active connection
      console.log('📡 Sending to iPhone...');
      const result = await this.conn.sendPacket(message);
      
      // Step 7: Track pending message
      this.trackPendingMessage(messageId, message.payload);
      
      console.log('✅ MESSAGE SENT SUCCESSFULLY');
      
      // Step 8: Emit success event
      this.emit('message-sent', {
        messageId,
        phoneNumber: formattedNumber,
        text: messageText,
        timestamp: Date.now()
      });
      
      return {
        success: true,
        messageId,
        timestamp: Date.now(),
        recipient: formattedNumber
      };
      
    } catch (error) {
      console.error('❌ CRITICAL: Message send failed:', error);
      
      // Store failed message for retry
      await this.storeFailed
      essage(phoneNumber, messageText, error);
      
      this.emit('message-failed', {
        phoneNumber,
        text: messageText,
        error: error.message
      });
      
      throw error;
    }
  }

  formatPhoneNumber(number) {
    console.log(`🔧 Formatting phone number: ${number}`);
    
    // Remove all non-digits
    let cleaned = number.replace(/\D/g, '');
    
    // Handle different formats
    if (cleaned.length === 10) {
      // US number without country code
      cleaned = '1' + cleaned;
    } else if (cleaned.length === 11 && cleaned.startsWith('1')) {
      // US number with country code
      // Keep as is
    } else if (cleaned.length > 11) {
      // International number
      // Keep as is
    }
    
    // Add + prefix for international format
    const formatted = '+' + cleaned;
    console.log(`✅ Formatted: ${number} -> ${formatted}`);
    
    return formatted;
  }

  generateMessageId() {
    this.messageSequence++;
    return `msg_${Date.now()}_${this.messageSequence}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateThreadId(phoneNumber) {
    // Create consistent thread ID for conversation
    return `thread_${Buffer.from(phoneNumber).toString('base64').replace(/[^a-zA-Z0-9]/g, '')}`;
  }

  async storeMessage(messageData, isSent = false) {
    if (!this.db) {
      console.warn('⚠️ No database - message not stored');
      return;
    }
    
    try {
      const message = {
        id: messageData.id,
        thread_id: messageData.threadId,
        sender: isSent ? 'self' : messageData.participants[0],
        recipients: JSON.stringify(messageData.participants),
        body: messageData.body,
        timestamp: messageData.timestamp,
        is_read: isSent ? 1 : 0,
        is_delivered: 0,
        is_sent: isSent ? 1 : 0,
        message_type: messageData.messageType || 'sms',
        metadata: JSON.stringify({
          priority: messageData.priority,
          originalNumber: messageData.originalNumber
        })
      };
      
      await this.db.storeMessage(message);
      console.log(`💾 Message stored: ${messageData.id}`);
      
      // Update cache
      this.messageCache.set(messageData.id, message);
      
    } catch (error) {
      console.error('❌ Message storage failed:', error);
    }
  }

  async storeFailedMessage(phoneNumber, messageText, error) {
    try {
      const failedMessage = {
        id: this.generateMessageId(),
        phoneNumber,
        messageText,
        error: error.message,
        timestamp: Date.now(),
        retryCount: 0,
        status: 'failed'
      };
      
      // Store in failed messages table (will implement with database)
      console.log(`💾 Failed message stored for retry: ${failedMessage.id}`);
      
    } catch (error) {
      console.error('❌ Failed message storage error:', error);
    }
  }

  trackPendingMessage(messageId, messageData) {
    console.log(`⏳ Tracking pending message: ${messageId}`);
    
    this.pendingMessages.set(messageId, {
      messageData,
      sentAt: Date.now(),
      retryCount: 0,
      status: 'sent'
    });
    
    // Set timeout for delivery confirmation
    setTimeout(() => {
      this.checkMessageDelivery(messageId);
    }, this.MESSAGE_TIMEOUT);
  }

  checkMessageDelivery(messageId) {
    const pending = this.pendingMessages.get(messageId);
    if (!pending) return;
    
    if (pending.status === 'sent') {
      console.log(`⚠️ Message delivery timeout: ${messageId}`);
      
      // Mark as delivery timeout
      pending.status = 'timeout';
      this.emit('message-timeout', {
        messageId,
        messageData: pending.messageData
      });
      
      // Attempt retry if under limit
      if (pending.retryCount < this.MAX_RETRIES) {
        this.retryMessage(messageId);
      } else {
        console.log(`❌ Message failed after ${this.MAX_RETRIES} retries: ${messageId}`);
        this.pendingMessages.delete(messageId);
      }
    }
  }

  async retryMessage(messageId) {
    const pending = this.pendingMessages.get(messageId);
    if (!pending) return;
    
    pending.retryCount++;
    console.log(`🔄 Retrying message ${messageId} (attempt ${pending.retryCount})`);
    
    try {
      const messageData = pending.messageData;
      const retryPacket = {
        type: 'send-message',
        action: 'retry',
        payload: {
          ...messageData,
          retryAttempt: pending.retryCount,
          originalId: messageId
        }
      };
      
      await this.conn.sendPacket(retryPacket);
      pending.status = 'retrying';
      
    } catch (error) {
      console.error(`❌ Message retry failed: ${error.message}`);
    }
  }

  handleIncomingMessage(message, device) {
    const handler = this.messageHandlers[message.type];
    if (handler) {
      handler(message, device);
    } else {
      console.log(`❓ Unknown message type: ${message.type}`);
    }
  }

  processIncomingMessage(message, device) {
    console.log(`📥 Incoming message from ${device.name}`);
    
    const messageData = message.payload;
    
    // Store incoming message
    this.storeMessage(messageData, false);
    
    // Emit for UI update
    this.emit('message-received', {
      id: messageData.id,
      sender: messageData.sender,
      body: messageData.body,
      timestamp: messageData.timestamp,
      threadId: messageData.threadId
    });
    
    console.log(`✅ Processed incoming message: ${messageData.id}`);
  }

  processMessageStatus(message, device) {
    console.log(`📊 Message status update: ${message.payload.messageId}`);
    
    const { messageId, status, timestamp } = message.payload;
    const pending = this.pendingMessages.get(messageId);
    
    if (pending) {
      pending.status = status;
      pending.statusTimestamp = timestamp;
      
      switch (status) {
        case 'delivered':
          console.log(`✅ Message delivered: ${messageId}`);
          this.emit('message-delivered', { messageId, timestamp });
          this.pendingMessages.delete(messageId);
          break;
          
        case 'read':
          console.log(`👁️ Message read: ${messageId}`);
          this.emit('message-read', { messageId, timestamp });
          break;
          
        case 'failed':
          console.log(`❌ Message failed: ${messageId}`);
          this.emit('message-failed', { messageId, timestamp });
          this.retryMessage(messageId);
          break;
      }
    }
  }

  processMessageSync(message, device) {
    console.log(`🔄 Message sync from ${device.name}`);
    
    const { messages, conversations } = message.payload;
    
    // Process sync data
    if (messages && messages.length > 0) {
      messages.forEach(msg => {
        this.storeMessage(msg, false);
      });
      
      console.log(`✅ Synced ${messages.length} messages`);
    }
    
    this.emit('sync-complete', {
      messageCount: messages?.length || 0,
      conversationCount: conversations?.length || 0
    });
  }

  processDeliveryReceipt(message, device) {
    const { messageId } = message.payload;
    console.log(`📋 Delivery receipt: ${messageId}`);
    
    this.processMessageStatus({
      payload: {
        messageId,
        status: 'delivered',
        timestamp: Date.now()
      }
    }, device);
  }

  processTypingIndicator(message, device) {
    const { phoneNumber, isTyping } = message.payload;
    
    this.emit('typing-indicator', {
      phoneNumber,
      isTyping,
      timestamp: Date.now()
    });
  }

  async requestMessageSync(device) {
    console.log(`🔄 Requesting message sync from ${device.name}`);
    
    try {
      const syncRequest = {
        type: 'sync-request',
        payload: {
          requestType: 'messages',
          limit: 100,
          since: Date.now() - (24 * 60 * 60 * 1000) // Last 24 hours
        }
      };
      
      await this.conn.sendPacket(syncRequest);
      
    } catch (error) {
      console.error('❌ Message sync request failed:', error);
    }
  }

  async loadRecentMessages() {
    if (!this.db) return;
    
    try {
      const messages = await this.db.getRecentMessages(100);
      messages.forEach(msg => {
        this.messageCache.set(msg.id, msg);
      });
      
      console.log(`📚 Loaded ${messages.length} recent messages`);
      
    } catch (error) {
      console.error('❌ Failed to load recent messages:', error);
    }
  }

  handleConnectionLoss(device) {
    console.log(`💔 Handling messaging connection loss for ${device.name}`);
    
    // Mark pending messages as connection lost
    for (const [messageId, pending] of this.pendingMessages) {
      if (pending.status === 'sent') {
        pending.status = 'connection_lost';
        console.log(`⚠️ Message ${messageId} connection lost`);
      }
    }
  }

  getMessageStatus() {
    return {
      pending: this.pendingMessages.size,
      cached: this.messageCache.size,
      isInitialized: this.isInitialized,
      hasConnection: !!this.conn.activeConnection
    };
  }

  async cleanup() {
    console.log('🧹 Cleaning up Message Handler...');
    
    this.pendingMessages.clear();
    this.messageCache.clear();
    
    console.log('✅ Message Handler cleanup complete');
  }
}

module.exports = { MessageHandler };