const { EventEmitter } = require('events');
const { exec } = require('child_process');
const os = require('os');
const fs = require('fs');
const path = require('path');

class VMHealthMonitor extends EventEmitter {
  constructor(vmBridge, vmManager) {
    super();
    this.vmBridge = vmBridge;
    this.vmManager = vmManager;
    this.isMonitoring = false;
    this.healthCheckInterval = null;
    this.performanceCheckInterval = null;
    this.recoveryAttempts = 0;
    this.maxRecoveryAttempts = 3;
    this.healthMetrics = {
      vmStatus: 'unknown',
      bridgeConnected: false,
      cpuUsage: 0,
      memoryUsage: 0,
      diskUsage: 0,
      networkLatency: 0,
      lastHealthCheck: null,
      uptime: 0,
      errorCount: 0,
      warningCount: 0
    };
    this.thresholds = {
      cpuUsage: 80,
      memoryUsage: 85,
      diskUsage: 90,
      networkLatency: 1000,
      maxErrors: 10,
      maxWarnings: 20
    };
  }

  // Start health monitoring
  start() {
    if (this.isMonitoring) {
      console.log('VM Health Monitor already running');
      return;
    }

    console.log('Starting VM Health Monitor...');
    this.isMonitoring = true;

    // Start health checks every 30 seconds
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, 30000);

    // Start performance monitoring every 10 seconds
    this.performanceCheckInterval = setInterval(() => {
      this.checkPerformanceMetrics();
    }, 10000);

    // Initial health check
    this.performHealthCheck();

    this.emit('monitoring-started');
  }

  // Stop health monitoring
  stop() {
    if (!this.isMonitoring) {
      return;
    }

    console.log('Stopping VM Health Monitor...');
    this.isMonitoring = false;

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    if (this.performanceCheckInterval) {
      clearInterval(this.performanceCheckInterval);
      this.performanceCheckInterval = null;
    }

    this.emit('monitoring-stopped');
  }

  // Perform comprehensive health check
  async performHealthCheck() {
    try {
      this.healthMetrics.lastHealthCheck = new Date();

      // Check VM status
      await this.checkVMStatus();

      // Check bridge connectivity
      await this.checkBridgeConnectivity();

      // Check system resources
      await this.checkSystemResources();

      // Check network connectivity
      await this.checkNetworkConnectivity();

      // Evaluate overall health
      const healthStatus = this.evaluateOverallHealth();

      this.emit('health-check-completed', {
        status: healthStatus,
        metrics: this.healthMetrics,
        timestamp: new Date()
      });

      // Take action based on health status
      await this.handleHealthStatus(healthStatus);

    } catch (error) {
      console.error('Health check failed:', error);
      this.healthMetrics.errorCount++;
      this.emit('health-check-error', error);
    }
  }

  // Check VM process status
  async checkVMStatus() {
    try {
      if (!this.vmManager) {
        this.healthMetrics.vmStatus = 'not-initialized';
        return;
      }

      const isRunning = this.vmManager.isVMRunning();
      this.healthMetrics.vmStatus = isRunning ? 'running' : 'stopped';

      if (!isRunning) {
        this.emit('vm-not-running');
      }

    } catch (error) {
      this.healthMetrics.vmStatus = 'error';
      this.healthMetrics.errorCount++;
      throw error;
    }
  }

  // Check bridge WebSocket connectivity
  async checkBridgeConnectivity() {
    try {
      if (!this.vmBridge) {
        this.healthMetrics.bridgeConnected = false;
        return;
      }

      const status = this.vmBridge.getStatus();
      this.healthMetrics.bridgeConnected = status.bridgeConnected;

      if (!status.bridgeConnected) {
        this.emit('bridge-disconnected');
      }

    } catch (error) {
      this.healthMetrics.bridgeConnected = false;
      this.healthMetrics.errorCount++;
      throw error;
    }
  }

  // Check system resource usage
  async checkSystemResources() {
    try {
      // Check CPU usage
      this.healthMetrics.cpuUsage = await this.getCPUUsage();

      // Check memory usage
      this.healthMetrics.memoryUsage = await this.getMemoryUsage();

      // Check disk usage
      this.healthMetrics.diskUsage = await this.getDiskUsage();

      // Check for resource warnings
      this.checkResourceThresholds();

    } catch (error) {
      this.healthMetrics.errorCount++;
      throw error;
    }
  }

  // Check network connectivity to VM
  async checkNetworkConnectivity() {
    try {
      const startTime = Date.now();
      
      // Ping VM bridge port
      await this.pingVMBridge();
      
      this.healthMetrics.networkLatency = Date.now() - startTime;

      if (this.healthMetrics.networkLatency > this.thresholds.networkLatency) {
        this.emit('high-network-latency', this.healthMetrics.networkLatency);
      }

    } catch (error) {
      this.healthMetrics.networkLatency = -1;
      this.healthMetrics.errorCount++;
      this.emit('network-connectivity-failed');
    }
  }

  // Ping VM bridge to check connectivity
  async pingVMBridge() {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Ping timeout'));
      }, 5000);

      // Try to connect to VM bridge port
      const net = require('net');
      const socket = new net.Socket();

      socket.connect(8080, 'localhost', () => {
        clearTimeout(timeout);
        socket.destroy();
        resolve();
      });

      socket.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  // Get CPU usage percentage
  async getCPUUsage() {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage();
      
      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage);
        const totalUsage = endUsage.user + endUsage.system;
        const percentage = (totalUsage / 1000000) * 100; // Convert to percentage
        resolve(Math.min(percentage, 100));
      }, 1000);
    });
  }

  // Get memory usage percentage
  async getMemoryUsage() {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    return (usedMemory / totalMemory) * 100;
  }

  // Get disk usage percentage
  async getDiskUsage() {
    return new Promise((resolve) => {
      if (os.platform() === 'win32') {
        exec('wmic logicaldisk get size,freespace,caption', (error, stdout) => {
          if (error) {
            resolve(0);
            return;
          }

          const lines = stdout.trim().split('\n').slice(1);
          let totalSize = 0;
          let totalFree = 0;

          lines.forEach(line => {
            const parts = line.trim().split(/\s+/);
            if (parts.length >= 3) {
              const free = parseInt(parts[1]) || 0;
              const size = parseInt(parts[2]) || 0;
              totalSize += size;
              totalFree += free;
            }
          });

          const usagePercentage = totalSize > 0 ? ((totalSize - totalFree) / totalSize) * 100 : 0;
          resolve(usagePercentage);
        });
      } else {
        resolve(0); // Fallback for non-Windows systems
      }
    });
  }

  // Check if resource usage exceeds thresholds
  checkResourceThresholds() {
    if (this.healthMetrics.cpuUsage > this.thresholds.cpuUsage) {
      this.healthMetrics.warningCount++;
      this.emit('high-cpu-usage', this.healthMetrics.cpuUsage);
    }

    if (this.healthMetrics.memoryUsage > this.thresholds.memoryUsage) {
      this.healthMetrics.warningCount++;
      this.emit('high-memory-usage', this.healthMetrics.memoryUsage);
    }

    if (this.healthMetrics.diskUsage > this.thresholds.diskUsage) {
      this.healthMetrics.warningCount++;
      this.emit('high-disk-usage', this.healthMetrics.diskUsage);
    }
  }

  // Evaluate overall health status
  evaluateOverallHealth() {
    const issues = [];

    if (this.healthMetrics.vmStatus !== 'running') {
      issues.push('VM not running');
    }

    if (!this.healthMetrics.bridgeConnected) {
      issues.push('Bridge disconnected');
    }

    if (this.healthMetrics.cpuUsage > this.thresholds.cpuUsage) {
      issues.push('High CPU usage');
    }

    if (this.healthMetrics.memoryUsage > this.thresholds.memoryUsage) {
      issues.push('High memory usage');
    }

    if (this.healthMetrics.diskUsage > this.thresholds.diskUsage) {
      issues.push('High disk usage');
    }

    if (this.healthMetrics.networkLatency > this.thresholds.networkLatency) {
      issues.push('High network latency');
    }

    if (this.healthMetrics.errorCount > this.thresholds.maxErrors) {
      issues.push('Too many errors');
    }

    if (issues.length === 0) {
      return 'healthy';
    } else if (issues.length <= 2) {
      return 'warning';
    } else {
      return 'critical';
    }
  }

  // Handle health status and take appropriate actions
  async handleHealthStatus(status) {
    switch (status) {
      case 'healthy':
        this.recoveryAttempts = 0;
        break;

      case 'warning':
        this.emit('health-warning', {
          status,
          metrics: this.healthMetrics
        });
        break;

      case 'critical':
        this.emit('health-critical', {
          status,
          metrics: this.healthMetrics
        });
        await this.attemptRecovery();
        break;
    }
  }

  // Attempt automatic recovery
  async attemptRecovery() {
    if (this.recoveryAttempts >= this.maxRecoveryAttempts) {
      this.emit('recovery-failed', 'Max recovery attempts reached');
      return;
    }

    this.recoveryAttempts++;
    this.emit('recovery-attempt', this.recoveryAttempts);

    try {
      // Try to restart VM if not running
      if (this.healthMetrics.vmStatus !== 'running') {
        await this.recoverVM();
      }

      // Try to reconnect bridge if disconnected
      if (!this.healthMetrics.bridgeConnected) {
        await this.recoverBridge();
      }

      this.emit('recovery-success', this.recoveryAttempts);

    } catch (error) {
      this.emit('recovery-error', error);
    }
  }

  // Recover VM
  async recoverVM() {
    if (this.vmManager) {
      console.log('Attempting to recover VM...');
      await this.vmManager.createOptimizedMacOSVM();
    }
  }

  // Recover bridge connection
  async recoverBridge() {
    if (this.vmBridge) {
      console.log('Attempting to recover bridge connection...');
      await this.vmBridge.createWebSocketTunnel();
    }
  }

  // Check performance metrics
  async checkPerformanceMetrics() {
    try {
      // Update uptime
      this.healthMetrics.uptime = process.uptime();

      // Get VM performance metrics if available
      if (this.vmManager) {
        const vmMetrics = await this.vmManager.getVMMetrics();
        if (vmMetrics) {
          this.healthMetrics.vmMemory = vmMetrics.memory;
          this.healthMetrics.vmCpuTime = vmMetrics.cpuTime;
        }
      }

      this.emit('performance-metrics-updated', this.healthMetrics);

    } catch (error) {
      console.error('Performance metrics check failed:', error);
    }
  }

  // Get current health metrics
  getHealthMetrics() {
    return { ...this.healthMetrics };
  }

  // Update monitoring thresholds
  updateThresholds(newThresholds) {
    this.thresholds = { ...this.thresholds, ...newThresholds };
    this.emit('thresholds-updated', this.thresholds);
  }

  // Reset error and warning counters
  resetCounters() {
    this.healthMetrics.errorCount = 0;
    this.healthMetrics.warningCount = 0;
    this.recoveryAttempts = 0;
    this.emit('counters-reset');
  }

  // Generate health report
  generateHealthReport() {
    return {
      timestamp: new Date(),
      status: this.evaluateOverallHealth(),
      metrics: this.getHealthMetrics(),
      thresholds: this.thresholds,
      recoveryAttempts: this.recoveryAttempts,
      isMonitoring: this.isMonitoring
    };
  }
}

module.exports = VMHealthMonitor;
