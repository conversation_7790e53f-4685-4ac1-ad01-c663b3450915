/* Common Styles and Variables */
:root {
  /* Light Theme Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f1f3f4;
  --bg-hover: #e9ecef;
  --bg-active: #dee2e6;
  
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-tertiary: #9ca3af;
  --text-disabled: #adb5bd;
  
  --border-color: #dee2e6;
  --border-hover: #ced4da;
  --border-active: #adb5bd;
  
  --accent-color: #007bff;
  --accent-hover: #0056b3;
  --accent-light: #b3d9ff;
  
  --success-color: #28a745;
  --success-hover: #218838;
  --success-light: #d4edda;
  
  --warning-color: #ffc107;
  --warning-hover: #e0a800;
  --warning-light: #fff3cd;
  
  --error-color: #dc3545;
  --error-hover: #c82333;
  --error-light: #f8d7da;
  
  --info-color: #17a2b8;
  --info-hover: #138496;
  --info-light: #d1ecf1;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Border Radius */
  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  
  /* Font Weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1010;
  --z-fixed: 1020;
  --z-modal-backdrop: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
  --z-toast: 1070;
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3a3a3a;
  --bg-hover: #404040;
  --bg-active: #4a4a4a;
  
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-tertiary: #999999;
  --text-disabled: #666666;
  
  --border-color: #404040;
  --border-hover: #4a4a4a;
  --border-active: #555555;
  
  --accent-color: #0d6efd;
  --accent-hover: #0b5ed7;
  --accent-light: #1e3a8a;
  
  --success-color: #198754;
  --success-hover: #157347;
  --success-light: #0f5132;
  
  --warning-color: #ffc107;
  --warning-hover: #e0a800;
  --warning-light: #664d03;
  
  --error-color: #dc3545;
  --error-hover: #c82333;
  --error-light: #721c24;
  
  --info-color: #0dcaf0;
  --info-hover: #0bb3d0;
  --info-light: #055160;
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

/* Focus Styles */
:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* Button Base Styles */
button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  outline: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Input Base Styles */
input, select, textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  outline: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

input:focus, select:focus, textarea:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px var(--accent-light);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: var(--font-weight-bold); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-normal { font-weight: var(--font-weight-normal); }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-disabled { color: var(--text-disabled); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

.border { border: 1px solid var(--border-color); }
.border-none { border: none; }

.rounded { border-radius: var(--border-radius-md); }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.transition-fast { transition: all var(--transition-fast); }
.transition-normal { transition: all var(--transition-normal); }
.transition-slow { transition: all var(--transition-slow); }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.px-xs { padding-left: var(--spacing-xs); padding-right: var(--spacing-xs); }
.px-sm { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px-md { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-lg { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
.px-xl { padding-left: var(--spacing-xl); padding-right: var(--spacing-xl); }

.py-xs { padding-top: var(--spacing-xs); padding-bottom: var(--spacing-xs); }
.py-sm { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py-md { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-lg { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
.py-xl { padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl); }

.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

.mx-xs { margin-left: var(--spacing-xs); margin-right: var(--spacing-xs); }
.mx-sm { margin-left: var(--spacing-sm); margin-right: var(--spacing-sm); }
.mx-md { margin-left: var(--spacing-md); margin-right: var(--spacing-md); }
.mx-lg { margin-left: var(--spacing-lg); margin-right: var(--spacing-lg); }
.mx-xl { margin-left: var(--spacing-xl); margin-right: var(--spacing-xl); }

.my-xs { margin-top: var(--spacing-xs); margin-bottom: var(--spacing-xs); }
.my-sm { margin-top: var(--spacing-sm); margin-bottom: var(--spacing-sm); }
.my-md { margin-top: var(--spacing-md); margin-bottom: var(--spacing-md); }
.my-lg { margin-top: var(--spacing-lg); margin-bottom: var(--spacing-lg); }
.my-xl { margin-top: var(--spacing-xl); margin-bottom: var(--spacing-xl); }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

.hidden { display: none; }
.visible { visibility: visible; }
.invisible { visibility: hidden; }

.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

.cursor-pointer { cursor: pointer; }
.cursor-default { cursor: default; }
.cursor-not-allowed { cursor: not-allowed; }

.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }

.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

/* Animation */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

.animate-spin { animation: spin 1s linear infinite; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-bounce { animation: bounce 1s infinite; }

/* Responsive Breakpoints */
@media (max-width: 640px) {
  .sm\:hidden { display: none; }
  .sm\:flex { display: flex; }
  .sm\:text-sm { font-size: var(--font-size-sm); }
  .sm\:p-sm { padding: var(--spacing-sm); }
}

@media (max-width: 768px) {
  .md\:hidden { display: none; }
  .md\:flex { display: flex; }
  .md\:text-sm { font-size: var(--font-size-sm); }
  .md\:p-sm { padding: var(--spacing-sm); }
}

@media (max-width: 1024px) {
  .lg\:hidden { display: none; }
  .lg\:flex { display: flex; }
  .lg\:text-sm { font-size: var(--font-size-sm); }
  .lg\:p-sm { padding: var(--spacing-sm); }
}

@media (max-width: 1280px) {
  .xl\:hidden { display: none; }
  .xl\:flex { display: flex; }
  .xl\:text-sm { font-size: var(--font-size-sm); }
  .xl\:p-sm { padding: var(--spacing-sm); }
}