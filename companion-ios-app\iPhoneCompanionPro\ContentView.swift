import SwiftUI
import Network

struct ContentView: View {
    @StateObject private var connectionManager = ConnectionManager()
    @StateObject private var messageBridge = MessageBridge()
    @State private var showingQRScanner = false
    @State private var isConnected = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Header
                VStack(spacing: 10) {
                    Image(systemName: "iphone.and.laptop")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)
                    
                    Text("iPhone Companion Pro")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Connect your iPhone to Windows PC")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                // Connection Status
                VStack(spacing: 15) {
                    HStack {
                        Circle()
                            .fill(isConnected ? Color.green : Color.red)
                            .frame(width: 12, height: 12)
                        
                        Text(isConnected ? "Connected to PC" : "Not Connected")
                            .font(.headline)
                    }
                    
                    if isConnected {
                        VStack(alignment: .leading, spacing: 5) {
                            Text("PC: \(connectionManager.connectedPC)")
                            Text("Messages: \(messageBridge.isEnabled ? "Enabled" : "Disabled")")
                            Text("Calls: \(connectionManager.callsEnabled ? "Enabled" : "Disabled")")
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                // Action Buttons
                VStack(spacing: 15) {
                    if !isConnected {
                        Button(action: {
                            showingQRScanner = true
                        }) {
                            HStack {
                                Image(systemName: "qrcode.viewfinder")
                                Text("Scan QR Code to Connect")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                        }
                        
                        Button(action: {
                            connectionManager.searchForPC()
                        }) {
                            HStack {
                                Image(systemName: "wifi")
                                Text("Search for PC on Network")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                        }
                    } else {
                        Button(action: {
                            connectionManager.disconnect()
                        }) {
                            HStack {
                                Image(systemName: "xmark.circle")
                                Text("Disconnect")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.red)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                        }
                    }
                }
                
                // Features List
                VStack(alignment: .leading, spacing: 10) {
                    Text("Features")
                        .font(.headline)
                    
                    FeatureRow(icon: "message", title: "Messages", description: "Send and receive messages from PC")
                    FeatureRow(icon: "phone", title: "Calls", description: "Make and receive calls via PC")
                    FeatureRow(icon: "bell", title: "Notifications", description: "Mirror notifications to PC")
                    FeatureRow(icon: "doc", title: "Files", description: "Transfer files between devices")
                    FeatureRow(icon: "doc.on.clipboard", title: "Clipboard", description: "Sync clipboard content")
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("iPhone Companion")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showingQRScanner) {
            QRScannerView(connectionManager: connectionManager)
        }
        .onReceive(connectionManager.$isConnected) { connected in
            isConnected = connected
        }
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .frame(width: 30)
                .foregroundColor(.blue)
            
            VStack(alignment: .leading) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 2)
    }
}

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
    }
}
