<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Monitor - iPhone Companion Pro</title>
    <link rel="stylesheet" href="styles/performance-monitor.css">
    <link rel="stylesheet" href="styles/common.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="performance-container">
        <header class="performance-header">
            <h1>Performance Monitor</h1>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="exportReport()">
                    <span class="icon">📊</span>
                    Export Report
                </button>
                <button class="btn btn-primary" onclick="runOptimization()">
                    <span class="icon">⚡</span>
                    Optimize
                </button>
                <button class="close-btn" onclick="window.close()">×</button>
            </div>
        </header>

        <div class="performance-content">
            <!-- Real-time Metrics -->
            <section class="metrics-overview">
                <h2>Real-time Metrics</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-icon">🖥️</div>
                        <div class="metric-info">
                            <h3>CPU Usage</h3>
                            <p class="metric-value" id="cpuUsage">45%</p>
                            <div class="metric-bar">
                                <div class="metric-fill" id="cpuBar" style="width: 45%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">💾</div>
                        <div class="metric-info">
                            <h3>Memory Usage</h3>
                            <p class="metric-value" id="memoryUsage">1.2 GB</p>
                            <div class="metric-bar">
                                <div class="metric-fill" id="memoryBar" style="width: 60%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">🌐</div>
                        <div class="metric-info">
                            <h3>Network</h3>
                            <p class="metric-value" id="networkUsage">2.4 MB/s</p>
                            <div class="metric-bar">
                                <div class="metric-fill" id="networkBar" style="width: 30%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">📱</div>
                        <div class="metric-info">
                            <h3>Connection</h3>
                            <p class="metric-value" id="connectionStatus">Stable</p>
                            <div class="metric-bar">
                                <div class="metric-fill good" id="connectionBar" style="width: 90%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">🎥</div>
                        <div class="metric-info">
                            <h3>Frame Rate</h3>
                            <p class="metric-value" id="frameRate">30 FPS</p>
                            <div class="metric-bar">
                                <div class="metric-fill good" id="frameBar" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">⏱️</div>
                        <div class="metric-info">
                            <h3>Latency</h3>
                            <p class="metric-value" id="latency">12ms</p>
                            <div class="metric-bar">
                                <div class="metric-fill good" id="latencyBar" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Performance Charts -->
            <section class="charts-section">
                <h2>Performance Trends</h2>
                <div class="charts-grid">
                    <div class="chart-container">
                        <h3>CPU & Memory Usage</h3>
                        <canvas id="systemChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <h3>Network Activity</h3>
                        <canvas id="networkChart"></canvas>
                    </div>
                </div>
            </section>

            <!-- Performance Issues -->
            <section class="issues-section">
                <h2>Performance Issues</h2>
                <div class="issues-list" id="issuesList">
                    <!-- Issues will be populated by JavaScript -->
                </div>
            </section>

            <!-- Optimization Recommendations -->
            <section class="recommendations-section">
                <h2>Optimization Recommendations</h2>
                <div class="recommendations-list" id="recommendationsList">
                    <!-- Recommendations will be populated by JavaScript -->
                </div>
            </section>

            <!-- System Information -->
            <section class="system-info">
                <h2>System Information</h2>
                <div class="info-grid">
                    <div class="info-group">
                        <h3>Application</h3>
                        <div class="info-item">
                            <span class="info-label">Version:</span>
                            <span class="info-value">1.0.0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Uptime:</span>
                            <span class="info-value" id="uptime">2h 34m</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Process ID:</span>
                            <span class="info-value" id="processId">12345</span>
                        </div>
                    </div>

                    <div class="info-group">
                        <h3>Device Connection</h3>
                        <div class="info-item">
                            <span class="info-label">Device:</span>
                            <span class="info-value" id="deviceName">iPhone 15 Pro</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Connection Type:</span>
                            <span class="info-value" id="connectionType">Wi-Fi</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Signal Strength:</span>
                            <span class="info-value" id="signalStrength">Excellent</span>
                        </div>
                    </div>

                    <div class="info-group">
                        <h3>Performance Profile</h3>
                        <div class="info-item">
                            <span class="info-label">Current Profile:</span>
                            <span class="info-value" id="currentProfile">Balanced</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Quality Setting:</span>
                            <span class="info-value" id="qualitySetting">High</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Hardware Acceleration:</span>
                            <span class="info-value" id="hardwareAccel">Enabled</span>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <!-- Optimization Modal -->
        <div id="optimizationModal" class="modal">
            <div class="modal-content">
                <h3>Performance Optimization</h3>
                <div class="optimization-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="optimizationProgress"></div>
                    </div>
                    <p class="progress-text" id="optimizationText">Analyzing system performance...</p>
                </div>
                <div class="optimization-results" id="optimizationResults" style="display: none;">
                    <h4>Optimization Complete</h4>
                    <ul id="optimizationList"></ul>
                </div>
                <div class="modal-actions">
                    <button class="btn btn-secondary" onclick="closeOptimizationModal()" id="optimizationCloseBtn" style="display: none;">Close</button>
                    <button class="btn btn-danger" onclick="cancelOptimization()" id="optimizationCancelBtn">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Performance Monitor JavaScript
        class PerformanceMonitor {
            constructor() {
                this.metrics = {
                    cpu: 0,
                    memory: 0,
                    network: 0,
                    connection: 100,
                    frameRate: 30,
                    latency: 12
                };

                this.charts = {};
                this.issues = [];
                this.recommendations = [];
                this.startTime = Date.now();

                this.init();
            }

            async init() {
                await this.loadPerformanceData();
                this.initializeCharts();
                this.updateMetrics();
                this.loadIssues();
                this.loadRecommendations();
                this.startRealTimeUpdates();
            }

            async loadPerformanceData() {
                try {
                    const result = await window.electronAPI.invoke('get-performance-metrics');
                    if (result.success) {
                        this.metrics = { ...this.metrics, ...result.data };
                    }
                } catch (error) {
                    console.error('Failed to load performance data:', error);
                }
            }

            initializeCharts() {
                // System Chart (CPU & Memory)
                const systemCtx = document.getElementById('systemChart').getContext('2d');
                this.charts.system = new Chart(systemCtx, {
                    type: 'line',
                    data: {
                        labels: this.generateTimeLabels(),
                        datasets: [{
                            label: 'CPU Usage (%)',
                            data: this.generateRandomData(20, 80),
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4
                        }, {
                            label: 'Memory Usage (%)',
                            data: this.generateRandomData(40, 70),
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        },
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        }
                    }
                });

                // Network Chart
                const networkCtx = document.getElementById('networkChart').getContext('2d');
                this.charts.network = new Chart(networkCtx, {
                    type: 'line',
                    data: {
                        labels: this.generateTimeLabels(),
                        datasets: [{
                            label: 'Download (MB/s)',
                            data: this.generateRandomData(0, 5),
                            borderColor: '#8b5cf6',
                            backgroundColor: 'rgba(139, 92, 246, 0.1)',
                            tension: 0.4
                        }, {
                            label: 'Upload (MB/s)',
                            data: this.generateRandomData(0, 2),
                            borderColor: '#f59e0b',
                            backgroundColor: 'rgba(245, 158, 11, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        },
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        }
                    }
                });
            }

            generateTimeLabels() {
                const labels = [];
                const now = new Date();
                for (let i = 19; i >= 0; i--) {
                    const time = new Date(now.getTime() - i * 30000);
                    labels.push(time.toLocaleTimeString());
                }
                return labels;
            }

            generateRandomData(min, max) {
                const data = [];
                for (let i = 0; i < 20; i++) {
                    data.push(Math.random() * (max - min) + min);
                }
                return data;
            }

            updateMetrics() {
                // Simulate real-time metrics
                this.metrics.cpu = Math.random() * 60 + 20;
                this.metrics.memory = Math.random() * 40 + 40;
                this.metrics.network = Math.random() * 5;
                this.metrics.latency = Math.random() * 20 + 5;

                // Update UI
                document.getElementById('cpuUsage').textContent = `${Math.round(this.metrics.cpu)}%`;
                document.getElementById('cpuBar').style.width = `${this.metrics.cpu}%`;

                document.getElementById('memoryUsage').textContent = `${(this.metrics.memory / 10).toFixed(1)} GB`;
                document.getElementById('memoryBar').style.width = `${this.metrics.memory}%`;

                document.getElementById('networkUsage').textContent = `${this.metrics.network.toFixed(1)} MB/s`;
                document.getElementById('networkBar').style.width = `${(this.metrics.network / 5) * 100}%`;

                document.getElementById('latency').textContent = `${Math.round(this.metrics.latency)}ms`;
                document.getElementById('latencyBar').style.width = `${100 - (this.metrics.latency / 50) * 100}%`;

                // Update uptime
                const uptime = Date.now() - this.startTime;
                const hours = Math.floor(uptime / 3600000);
                const minutes = Math.floor((uptime % 3600000) / 60000);
                document.getElementById('uptime').textContent = `${hours}h ${minutes}m`;

                // Update metric bar colors
                this.updateMetricBarColors();
            }

            updateMetricBarColors() {
                const cpuBar = document.getElementById('cpuBar');
                const memoryBar = document.getElementById('memoryBar');

                // CPU color based on usage
                if (this.metrics.cpu > 80) {
                    cpuBar.className = 'metric-fill critical';
                } else if (this.metrics.cpu > 60) {
                    cpuBar.className = 'metric-fill warning';
                } else {
                    cpuBar.className = 'metric-fill good';
                }

                // Memory color based on usage
                if (this.metrics.memory > 80) {
                    memoryBar.className = 'metric-fill critical';
                } else if (this.metrics.memory > 60) {
                    memoryBar.className = 'metric-fill warning';
                } else {
                    memoryBar.className = 'metric-fill good';
                }
            }

            loadIssues() {
                const sampleIssues = [
                    {
                        severity: 'warning',
                        title: 'High CPU Usage',
                        description: 'CPU usage has been consistently above 70% for the last 10 minutes.',
                        recommendation: 'Consider closing other applications or reducing video quality.'
                    },
                    {
                        severity: 'info',
                        title: 'Network Fluctuation',
                        description: 'Network speed has been varying. This may affect streaming quality.',
                        recommendation: 'Check your Wi-Fi connection or switch to a wired connection.'
                    }
                ];

                this.issues = sampleIssues;
                this.updateIssuesUI();
            }

            updateIssuesUI() {
                const issuesList = document.getElementById('issuesList');
                issuesList.innerHTML = '';

                if (this.issues.length === 0) {
                    issuesList.innerHTML = '<p class="no-issues">No performance issues detected.</p>';
                    return;
                }

                this.issues.forEach(issue => {
                    const issueElement = document.createElement('div');
                    issueElement.className = `issue-item issue-${issue.severity}`;
                    issueElement.innerHTML = `
                        <div class="issue-icon">${this.getIssueIcon(issue.severity)}</div>
                        <div class="issue-content">
                            <h4>${issue.title}</h4>
                            <p>${issue.description}</p>
                            <p class="issue-recommendation">${issue.recommendation}</p>
                        </div>
                    `;
                    issuesList.appendChild(issueElement);
                });
            }

            getIssueIcon(severity) {
                switch (severity) {
                    case 'critical': return '🔴';
                    case 'warning': return '🟡';
                    case 'info': return '🔵';
                    default: return '⚪';
                }
            }

            loadRecommendations() {
                const sampleRecommendations = [
                    {
                        title: 'Enable Hardware Acceleration',
                        description: 'Use GPU acceleration for better video performance.',
                        impact: 'High',
                        implemented: false
                    },
                    {
                        title: 'Optimize Sync Frequency',
                        description: 'Reduce sync frequency for non-critical data to save resources.',
                        impact: 'Medium',
                        implemented: true
                    },
                    {
                        title: 'Update Network Drivers',
                        description: 'Newer drivers may improve connection stability.',
                        impact: 'Medium',
                        implemented: false
                    }
                ];

                this.recommendations = sampleRecommendations;
                this.updateRecommendationsUI();
            }

            updateRecommendationsUI() {
                const recommendationsList = document.getElementById('recommendationsList');
                recommendationsList.innerHTML = '';

                this.recommendations.forEach((rec, index) => {
                    const recElement = document.createElement('div');
                    recElement.className = `recommendation-item ${rec.implemented ? 'implemented' : ''}`;
                    recElement.innerHTML = `
                        <div class="recommendation-content">
                            <h4>${rec.title}</h4>
                            <p>${rec.description}</p>
                            <span class="impact-badge impact-${rec.impact.toLowerCase()}">${rec.impact} Impact</span>
                        </div>
                        <div class="recommendation-actions">
                            ${rec.implemented ?
                                '<span class="implemented-badge">✓ Implemented</span>' :
                                `<button class="btn btn-primary btn-sm" onclick="implementRecommendation(${index})">Apply</button>`
                            }
                        </div>
                    `;
                    recommendationsList.appendChild(recElement);
                });
            }

            startRealTimeUpdates() {
                setInterval(() => {
                    this.updateMetrics();
                    this.updateCharts();
                }, 2000);
            }

            updateCharts() {
                // Update system chart
                if (this.charts.system) {
                    const systemChart = this.charts.system;
                    systemChart.data.datasets[0].data.shift();
                    systemChart.data.datasets[0].data.push(this.metrics.cpu);
                    systemChart.data.datasets[1].data.shift();
                    systemChart.data.datasets[1].data.push(this.metrics.memory);

                    systemChart.data.labels.shift();
                    systemChart.data.labels.push(new Date().toLocaleTimeString());
                    systemChart.update('none');
                }

                // Update network chart
                if (this.charts.network) {
                    const networkChart = this.charts.network;
                    networkChart.data.datasets[0].data.shift();
                    networkChart.data.datasets[0].data.push(this.metrics.network);
                    networkChart.data.datasets[1].data.shift();
                    networkChart.data.datasets[1].data.push(this.metrics.network * 0.3);

                    networkChart.data.labels.shift();
                    networkChart.data.labels.push(new Date().toLocaleTimeString());
                    networkChart.update('none');
                }
            }

            async runOptimization() {
                const modal = document.getElementById('optimizationModal');
                const progressFill = document.getElementById('optimizationProgress');
                const progressText = document.getElementById('optimizationText');
                const results = document.getElementById('optimizationResults');
                const resultsList = document.getElementById('optimizationList');
                const closeBtn = document.getElementById('optimizationCloseBtn');
                const cancelBtn = document.getElementById('optimizationCancelBtn');

                modal.style.display = 'flex';
                results.style.display = 'none';
                closeBtn.style.display = 'none';
                cancelBtn.style.display = 'block';

                const optimizations = [
                    'Analyzing system performance...',
                    'Optimizing memory usage...',
                    'Cleaning temporary files...',
                    'Updating performance settings...',
                    'Applying network optimizations...',
                    'Finalizing optimizations...'
                ];

                let progress = 0;
                for (let i = 0; i < optimizations.length; i++) {
                    progressText.textContent = optimizations[i];
                    progress = ((i + 1) / optimizations.length) * 100;
                    progressFill.style.width = `${progress}%`;

                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                // Show results
                results.style.display = 'block';
                closeBtn.style.display = 'block';
                cancelBtn.style.display = 'none';

                const appliedOptimizations = [
                    'Freed 245 MB of memory',
                    'Reduced CPU usage by 15%',
                    'Optimized network buffers',
                    'Updated performance profile'
                ];

                resultsList.innerHTML = '';
                appliedOptimizations.forEach(opt => {
                    const li = document.createElement('li');
                    li.textContent = opt;
                    resultsList.appendChild(li);
                });
            }

            async exportReport() {
                try {
                    const reportData = {
                        timestamp: new Date().toISOString(),
                        metrics: this.metrics,
                        issues: this.issues,
                        recommendations: this.recommendations,
                        uptime: Date.now() - this.startTime
                    };

                    const result = await window.electronAPI.invoke('export-performance-report', reportData);
                    if (result.success) {
                        alert('Performance report exported successfully!');
                    } else {
                        alert('Failed to export report: ' + result.error);
                    }
                } catch (error) {
                    console.error('Failed to export report:', error);
                    alert('Failed to export report');
                }
            }
        }

        // Global functions
        let performanceMonitor;

        function runOptimization() {
            if (performanceMonitor) {
                performanceMonitor.runOptimization();
            }
        }

        function exportReport() {
            if (performanceMonitor) {
                performanceMonitor.exportReport();
            }
        }

        function closeOptimizationModal() {
            document.getElementById('optimizationModal').style.display = 'none';
        }

        function cancelOptimization() {
            closeOptimizationModal();
        }

        function implementRecommendation(index) {
            if (performanceMonitor) {
                performanceMonitor.recommendations[index].implemented = true;
                performanceMonitor.updateRecommendationsUI();
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            performanceMonitor = new PerformanceMonitor();
        });
    </script>
</body>
</html>
