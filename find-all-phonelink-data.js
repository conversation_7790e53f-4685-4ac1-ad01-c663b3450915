const fs = require('fs');
const path = require('path');

console.log('🔍 Searching for ALL Phone Link data files...\n');

const phoneDir = 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\Microsoft.YourPhone_8wekyb3d8bbwe';
const extensions = ['.json', '.xml', '.txt', '.dat', '.cache', '.log'];
const messageKeywords = ['message', 'sms', 'text', 'conversation', 'chat'];

function searchFiles(dir, results = { messages: [], other: [] }) {
    try {
        const files = fs.readdirSync(dir);
        files.forEach(file => {
            const filePath = path.join(dir, file);
            try {
                const stat = fs.statSync(filePath);
                
                if (stat.isDirectory() && !file.startsWith('.') && !file.includes('$')) {
                    searchFiles(filePath, results);
                } else if (stat.isFile()) {
                    const ext = path.extname(file).toLowerCase();
                    const nameLC = file.toLowerCase();
                    
                    // Check if it might contain messages
                    if (messageKeywords.some(keyword => nameLC.includes(keyword))) {
                        results.messages.push({
                            name: file,
                            path: filePath,
                            size: (stat.size / 1024).toFixed(2) + ' KB'
                        });
                    } else if (extensions.includes(ext) && stat.size > 1024) {
                        results.other.push({
                            name: file,
                            path: filePath,
                            size: (stat.size / 1024).toFixed(2) + ' KB'
                        });
                    }
                }
            } catch (e) {
                // Skip permission errors
            }
        });
    } catch (e) {
        // Skip permission errors
    }
    return results;
}

const results = searchFiles(phoneDir);

console.log('📱 Message-related files:');
if (results.messages.length > 0) {
    results.messages.forEach(f => {
        console.log(`\n   ${f.name} (${f.size})`);
        console.log(`   ${f.path}`);
    });
} else {
    console.log('   None found');
}

console.log('\n📄 Other data files (showing first 10):');
results.other.slice(0, 10).forEach(f => {
    console.log(`   ${f.name} (${f.size})`);
});

// Check LocalState specifically
const localState = path.join(phoneDir, 'LocalState');
if (fs.existsSync(localState)) {
    console.log('\n📁 LocalState contents:');
    fs.readdirSync(localState).forEach(file => {
        const stat = fs.statSync(path.join(localState, file));
        if (stat.isFile()) {
            console.log(`   ${file} (${(stat.size / 1024).toFixed(2)} KB)`);
        }
    });
}