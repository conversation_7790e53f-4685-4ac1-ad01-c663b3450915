const fs = require('fs');
const path = require('path');
const { app } = require('electron');

class ErrorLogger {
  constructor() {
    this.logDir = path.join(app.getPath('userData'), 'error-logs');
    this.currentLog = path.join(this.logDir, `error-${new Date().toISOString().split('T')[0]}.log`);
    
    // Create error log directory
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  logError(component, error, context = {}) {
    const errorEntry = {
      timestamp: new Date().toISOString(),
      component,
      error: {
        message: error.message,
        stack: error.stack,
        code: error.code
      },
      context,
      system: {
        platform: process.platform,
        version: process.version,
        memory: process.memoryUsage()
      }
    };

    const logLine = JSON.stringify(errorEntry) + '\n';
    
    // Write to file
    fs.appendFileSync(this.currentLog, logLine);
    
    // Also log to console with red color
    console.error('\x1b[31m%s\x1b[0m', `[ERROR] ${component}: ${error.message}`);
    
    return errorEntry;
  }

  getRecentErrors(count = 50) {
    try {
      const logs = fs.readFileSync(this.currentLog, 'utf8');
      const lines = logs.trim().split('\n');
      return lines.slice(-count).map(line => JSON.parse(line));
    } catch (error) {
      return [];
    }
  }
}

module.exports = new ErrorLogger();