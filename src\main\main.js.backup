const { app, BrowserWindow, <PERSON><PERSON>, <PERSON>ray, ipcMain } = require('electron');
const path = require('path');
// Essential services for real iPhone connection
const { MessageService } = require('./services/MessageService');
const { WebBridgeServer } = require('./services/WebBridgeServer');
const Store = require('electron-store');

// Initialize store
const store = new Store();

// Essential services for real iPhone connection
let messageService;
let webBridge;
// Global references
let mainWindow;

let tray;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1000,
    minHeight: 600,
    frame: false,
    backgroundColor: '#0a0a0a',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    }
  });

  // Load the app
  mainWindow.loadFile('src/renderer/views/index.html');

  // Open DevTools in development
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// IPC handlers for window management
ipcMain.on('minimize-window', () => {
  mainWindow.minimize();
});


// Window handling with logging
ipcMain.handle('open-messages', async () => {
  console.log('📱 Messages window requested');
  
  const { BrowserWindow } = require('electron');
  
  if (!global.messagesWindow) {
    global.messagesWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      center: true,
      show: true,
      title: 'iPhone Messages',
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    });
    
    const messagesPath = path.join(__dirname, '../renderer/views/messages.html');
    console.log('Loading messages from:', messagesPath);
    
    global.messagesWindow.loadFile(messagesPath);
    
    global.messagesWindow.on('closed', () => {
      });
    
    // Send Phone Link data to messages window
    global.messagesWindow.webContents.on('did-finish-load', async () => {
      if (focusedConnectionManager && focusedConnectionManager.phoneLinkBridge) {
        const messages = await focusedConnectionManager.phoneLinkBridge.getMessages();
        global.messagesWindow.webContents.send('messages-data', messages);
      }
    });
  } else {
    global.messagesWindow.show();
    global.messagesWindow.focus();
  }
  
  return { success: true };
});

ipcMain.handle('open-calls', async () => {
  console.log('📞 Calls window requested');
  
  const { BrowserWindow } = require('electron');
  
  if (!global.callsWindow) {
    global.callsWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      center: true,
      show: true,
      title: 'iPhone Calls',
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    });
    
    const callsPath = path.join(__dirname, '../renderer/views/calls.html');
    console.log('Loading calls from:', callsPath);
    
    global.callsWindow.loadFile(callsPath);
    
    global.callsWindow.on('closed', () => {
      });
    
    // Send Phone Link data to calls window
    global.callsWindow.webContents.on('did-finish-load', async () => {
      if (focusedConnectionManager && focusedConnectionManager.phoneLinkBridge) {
        const calls = await focusedConnectionManager.phoneLinkBridge.getCallHistory();
        global.callsWindow.webContents.send('calls-data', calls);
      }
    });
  } else {
    global.callsWindow.show();
    global.callsWindow.focus();
  }
  
  return { success: true };
});


ipcMain.on('maximize-window', () => {
  if (mainWindow.isMaximized()) {
    mainWindow.unmaximize();
  } else {
    mainWindow.maximize();
  }
});

ipcMain.on('close-window', () => {
  mainWindow.hide();
});

async function initializeServices() {
  console.log('🚀 Initializing iPhone Companion Pro services...');

  // Initialize focused connection manager for REAL iPhone data only
  console.log('📱 Setting up Real iPhone Connection Manager - NO MOCK DATA');

  // We'll initialize the FocusedConnectionManager when needed in the IPC handler
  // This avoids startup issues and allows for better error handling

  // Initialize essential services only
  console.log('📨 Initializing Message Service...');
  messageService = new MessageService();

  console.log('🌐 Initializing Web Bridge Server...');
  webBridge = new WebBridgeServer();

  // Set up web bridge event handlers
  webBridge.on('device-connected', (deviceInfo) => {
    console.log('📱 iPhone connected via web bridge:', deviceInfo.name);
    if (mainWindow) {
      mainWindow.webContents.send('device-connected', deviceInfo);
    }
  });

  webBridge.on('device-disconnected', () => {
    console.log('📱 iPhone disconnected from web bridge');
    if (mainWindow) {
      mainWindow.webContents.send('device-disconnected');
    }
  });

  console.log('✅ Essential services initialized successfully');
}

// App events
app.whenReady().then(() => {

// ===== WINDOW HANDLERS - SINGLE INSTANCE =====
ipcMain.handle('open-messages', async () => {
  console.log('📱 Opening Messages window');
  
  if (messagesWindow) {
    messagesWindow.show();
    messagesWindow.focus();
    return;
  }
  
  messagesWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'iPhone Messages',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  messagesWindow.loadFile(path.join(__dirname, '../renderer/views/messages.html'));
  
  messagesWindow.on('closed', () => {
    messagesWindow = null;
  });
  
  messagesWindow.webContents.on('did-finish-load', () => {
    console.log('Messages window loaded');
    // Send any available data
    messagesWindow.webContents.send('messages-ready');
  });
});

ipcMain.handle('open-calls', async () => {
  console.log('📞 Opening Calls window');
  
  if (callsWindow) {
    callsWindow.show();
    callsWindow.focus();
    return;
  }
  
  callsWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'iPhone Calls',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  callsWindow.loadFile(path.join(__dirname, '../renderer/views/calls.html'));
  
  callsWindow.on('closed', () => {
    callsWindow = null;
  });
  
  callsWindow.webContents.on('did-finish-load', () => {
    console.log('Calls window loaded');
    // Send any available data
    callsWindow.webContents.send('calls-ready');
  });
});
// ===== END WINDOW HANDLERS =====

  createWindow();
  
  // Initialize services
  initializeServices();
  
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC handlers for connection - Updated to use FocusedConnectionManager
ipcMain.handle('connect-device', async () => {
  try {
    console.log('🔌 Connection request received - using REAL iPhone data only');
    
    // Use the new focused connection manager
    const FocusedConnectionManager = require('./services/FocusedConnectionManager');
    const focusedManager = new FocusedConnectionManager();
    
    // Set up event listeners for real-time feedback
    focusedManager.on('status', (status) => {
      if (mainWindow) {
        mainWindow.webContents.send('connection-status', status);
      }
    });
    
    focusedManager.on('method-failed', (data) => {
      console.log(`❌ ${data.method} failed: ${data.error}`);
      if (mainWindow) {
        mainWindow.webContents.send('connection-method-failed', data);
      }
    });
    
    const result = await focusedManager.connect();
    
    if (result.success) {
      console.log(`✅ Real iPhone connected via ${result.method}`);
      
      // Store the connection manager for later use
      global.activeConnectionManager = focusedManager;
      
      return { 
        success: true, 
        device: result.deviceInfo,
        method: result.method,
        capabilities: result.deviceInfo.capabilities
      };
    } else {
      return { success: false, error: result.error };
    }
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('disconnect-device', async () => {
  try {
    if (global.activeConnectionManager) {
      global.activeConnectionManager.disconnect();
      global.activeConnectionManager = null;
    }
    
    console.log('📱 iPhone disconnected');
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Wireless connection handler
ipcMain.handle('show-wireless-connect', async () => {
  try {
    // Start web bridge server
    await webBridge.start();
    
    return new Promise((resolve) => {
      webBridge.once('qr-code', (data) => {
        resolve({ success: true, qr: data.qr, url: data.url });
      });
    });
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Message IPC handlers
ipcMain.handle('get-conversations', async () => {
  const conversations = messageService.getConversations();
  return { success: true, data: conversations };
});

ipcMain.handle('send-message', async (event, data) => {
  const message = messageService.sendMessage(data.phoneNumber, data.text);
  return { success: true, data: message };
});

// Basic device info handler (will be replaced with real data when connected)
ipcMain.handle('get-device-info', async () => {
  if (global.activeConnectionManager) {
    const status = global.activeConnectionManager.getConnectionStatus();
    return {
      success: true,
      device: status.deviceInfo
    };
  } else {
    return {
      success: false,
      error: 'No iPhone connected'
    };
  }
});

// Add missing IPC handlers that the UI is calling
ipcMain.handle('start-mirror', async () => {
  console.log('🎥 Mirror request received');
  if (global.activeConnectionManager) {
    const status = global.activeConnectionManager.getConnectionStatus();
    if (status.connected && status.connectionType === 'airplay') {
      return { success: true, message: 'AirPlay mirror active' };
    }
  }
  return { success: false, error: 'No AirPlay connection available for mirroring' };
});

ipcMain.handle('open-messages', async () => {
  console.log('💬 Messages request received');

  // Create messages window
  const messagesWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    show: false,
    title: 'iPhone Messages',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  messagesWindow.loadFile('src/renderer/views/messages.html');

  return { success: true };
});

ipcMain.handle('open-calls', async () => {
  console.log('📞 Calls request received');

  // Create calls window
  const callsWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    show: false,
    title: 'iPhone Calls',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  callsWindow.loadFile('src/renderer/views/calls.html');

  return { success: true };
});

ipcMain.handle('open-notifications', async () => {
  console.log('🔔 Notifications request received');
  return { success: true };
});

ipcMain.handle('open-files', async () => {
  console.log('📁 Files request received');
  return { success: true };
});

ipcMain.handle('open-settings', async () => {
  console.log('⚙️ Settings request received');
  return { success: true };
});

console.log('🚀 iPhone Companion Pro main process initialized');


// ===== WINDOW HANDLERS - CLEAN VERSION =====
const windowManager = {
  messages: null,
  calls: null,
  mirror: null,
  photos: null,
  files: null,
  settings: null
};

// Messages Window
ipcMain.handle('open-messages', async () => {
  console.log('📱 Opening Messages window');
  
  if (windowManager.messages && !windowManager.messages.isDestroyed()) {
    windowManager.messages.show();
    windowManager.messages.focus();
    return;
  }
  
  windowManager.messages = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'iPhone Messages',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  windowManager.messages.loadFile(path.join(__dirname, '../renderer/views/messages.html'));
  windowManager.messages.on('closed', () => { windowManager.messages = null; });
});

// Calls Window
ipcMain.handle('open-calls', async () => {
  console.log('📞 Opening Calls window');
  
  if (windowManager.calls && !windowManager.calls.isDestroyed()) {
    windowManager.calls.show();
    windowManager.calls.focus();
    return;
  }
  
  windowManager.calls = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'iPhone Calls',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  windowManager.calls.loadFile(path.join(__dirname, '../renderer/views/calls.html'));
  windowManager.calls.on('closed', () => { windowManager.calls = null; });
});

// Photos Window
ipcMain.handle('open-photos', async () => {
  console.log('📸 Opening Photos window');
  
  if (windowManager.photos && !windowManager.photos.isDestroyed()) {
    windowManager.photos.show();
    windowManager.photos.focus();
    return;
  }
  
  windowManager.photos = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'iPhone Photos',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  windowManager.photos.loadFile(path.join(__dirname, '../renderer/views/photos.html'));
  windowManager.photos.on('closed', () => { windowManager.photos = null; });
});

// Files Window
ipcMain.handle('open-files', async () => {
  console.log('📁 Opening Files window');
  
  if (windowManager.files && !windowManager.files.isDestroyed()) {
    windowManager.files.show();
    windowManager.files.focus();
    return;
  }
  
  windowManager.files = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'iPhone Files',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  windowManager.files.loadFile(path.join(__dirname, '../renderer/views/files.html'));
  windowManager.files.on('closed', () => { windowManager.files = null; });
});

// Settings Window
ipcMain.handle('open-settings', async () => {
  console.log('⚙️ Opening Settings window');
  
  if (windowManager.settings && !windowManager.settings.isDestroyed()) {
    windowManager.settings.show();
    windowManager.settings.focus();
    return;
  }
  
  windowManager.settings = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'Settings',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  windowManager.settings.loadFile(path.join(__dirname, '../renderer/views/settings.html'));
  windowManager.settings.on('closed', () => { windowManager.settings = null; });
});

// Mirror Window
ipcMain.handle('open-mirror', async () => {
  console.log('🖥️ Opening Screen Mirror window');
  
  if (windowManager.mirror && !windowManager.mirror.isDestroyed()) {
    windowManager.mirror.show();
    windowManager.mirror.focus();
    return;
  }
  
  windowManager.mirror = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'Screen Mirror',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  windowManager.mirror.loadFile(path.join(__dirname, '../renderer/views/mirror.html'));
  windowManager.mirror.on('closed', () => { windowManager.mirror = null; });
});
// ===== END WINDOW HANDLERS =====
