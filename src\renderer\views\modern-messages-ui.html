<!DOCTYPE html>
<html>
<head>
    <title>Messages - iPhone Companion Pro</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #000;
            color: #fff;
            height: 100vh;
            display: flex;
        }

        /* Conversations List */
        .conversations {
            width: 350px;
            background: #111;
            border-right: 1px solid #333;
            overflow-y: auto;
        }

        .conversation-item {
            padding: 16px;
            border-bottom: 1px solid #222;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }

        .conversation-item:hover {
            background: #1a1a1a;
        }

        .conversation-item.active {
            background: #0084ff;
        }

        .contact-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            float: left;
            margin-right: 12px;
        }

        .conversation-info {
            overflow: hidden;
        }

        .contact-name {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 4px;
        }

        .last-message {
            color: #888;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .timestamp {
            position: absolute;
            top: 16px;
            right: 16px;
            font-size: 12px;
            color: #666;
        }

        .unread-badge {
            position: absolute;
            bottom: 16px;
            right: 16px;
            background: #0084ff;
            color: white;
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 600;
        }

        /* Messages Area */
        .messages-container {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .messages-header {
            padding: 20px;
            border-bottom: 1px solid #333;
            background: #0a0a0a;
        }

        .messages-list {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .message {
            max-width: 70%;
            word-wrap: break-word;
        }

        .message.sent {
            align-self: flex-end;
        }

        .message-bubble {
            padding: 12px 16px;
            border-radius: 18px;
            position: relative;
        }

        .message.received .message-bubble {
            background: #333;
            color: #fff;
        }

        .message.sent .message-bubble {
            background: #0084ff;
            color: #fff;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 4px;
        }

        /* Input Area */
        .message-input-container {
            padding: 20px;
            border-top: 1px solid #333;
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            background: #222;
            border: 1px solid #444;
            border-radius: 24px;
            padding: 12px 20px;
            color: white;
            font-size: 14px;
            outline: none;
        }

        .message-input:focus {
            border-color: #0084ff;
        }

        .send-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #0084ff;
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: scale(1.1);
        }

        .send-button:active {
            transform: scale(0.95);
        }


        /* Loading States */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #666;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #333;
            border-top: 2px solid #0084ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Status Indicators */
        .connection-status {
            position: fixed;
            bottom: 20px;
            left: 20px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .connection-status.connected {
            background: #28a745;
            color: white;
        }

        .connection-status.disconnected {
            background: #dc3545;
            color: white;
        }

        .connection-status.connecting {
            background: #ffc107;
            color: black;
        }
    </style>
</head>
<body>
    <div class="conversations" id="conversationsList">
        <div style="padding: 16px; border-bottom: 1px solid #333; display: flex; align-items: center; justify-content: space-between;">
            <h2 style="font-size: 18px; font-weight: 600;">Messages</h2>
            <button class="new-message-btn" onclick="newMessage()" style="background: #0084ff; border: none; border-radius: 50%; width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M12 5v14m-7-7h14"/>
                </svg>
            </button>
        </div>
        <div class="loading">
            <div class="spinner"></div>
            Loading conversations...
        </div>
    </div>
    
    <div class="messages-container">
        <div class="messages-header">
            <h2 id="currentContact">Select a conversation</h2>
        </div>
        
        <div class="messages-list" id="messagesList">
            <div class="loading">
                <div class="spinner"></div>
                Select a conversation to view messages
            </div>
        </div>
        
        <div class="message-input-container">
            <input type="text" class="message-input" placeholder="Type a message..." id="messageInput" disabled>
            <button class="send-button" onclick="sendMessage()" disabled>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M22 2L11 13" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        </div>
    </div>


    <div class="connection-status disconnected" id="connectionStatus">
        Disconnected from iPhone
    </div>

    <script src="../js/modern-messages.js"></script>
</body>
</html>
