const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

console.log('🔍 Deep search for Phone Link messages...\n');

const phoneDir = 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\Microsoft.YourPhone_8wekyb3d8bbwe';

// Search for all .db files
function findAllDatabases(dir, fileList = []) {
    try {
        const files = fs.readdirSync(dir);
        files.forEach(file => {
            const filePath = path.join(dir, file);
            const stat = fs.statSync(filePath);
            
            if (stat.isDirectory() && !file.startsWith('.') && !file.includes('$')) {
                findAllDatabases(filePath, fileList);
            } else if (file.endsWith('.db') || file.endsWith('.sqlite')) {
                fileList.push({
                    path: filePath,
                    size: (stat.size / 1024 / 1024).toFixed(2),
                    name: file
                });
            }
        });
    } catch (e) {
        // Skip permission errors
    }
    return fileList;
}

// Find all databases
console.log('Searching entire Phone Link directory...');
const allDbs = findAllDatabases(phoneDir);

console.log(`\nFound ${allDbs.length} databases:\n`);
allDbs.forEach(db => {
    console.log(`📄 ${db.name}`);
    console.log(`   Path: ${db.path}`);
    console.log(`   Size: ${db.size} MB\n`);
});

// Check contacts.db for message-related tables
console.log('\n🔍 Checking contacts.db for message tables...');
const contactsDb = path.join(phoneDir, 'LocalCache\\Indexed\\e1e8a106-3e02-48fa-b8d7-a055e20bd025\\System\\Database\\contacts.db');

const db = new sqlite3.Database(contactsDb, sqlite3.OPEN_READONLY, (err) => {
    if (err) {
        console.error('Error opening contacts.db:', err);
        return;
    }
    
    // List all tables
    db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {
        if (err) {
            console.error('Error reading tables:', err);
            return;
        }
        
        console.log('\n📊 Tables in contacts.db:');
        tables.forEach(table => {
            console.log(`   - ${table.name}`);
        });
        
        // Check if any table might contain messages
        const messageTables = tables.filter(t => 
            t.name.toLowerCase().includes('message') || 
            t.name.toLowerCase().includes('sms') ||
            t.name.toLowerCase().includes('conversation')
        );
        
        if (messageTables.length > 0) {
            console.log('\n✅ Found message-related tables:', messageTables.map(t => t.name));
        }
        
        db.close();
    });
});

// Also check LocalState folder
const localStatePath = path.join(phoneDir, 'LocalState');
if (fs.existsSync(localStatePath)) {
    console.log('\n📁 Checking LocalState folder...');
    const localStateFiles = fs.readdirSync(localStatePath);
    localStateFiles.forEach(file => {
        if (file.endsWith('.db') || file.endsWith('.sqlite')) {
            const stat = fs.statSync(path.join(localStatePath, file));
            console.log(`   Found: ${file} (${(stat.size / 1024 / 1024).toFixed(2)} MB)`);
        }
    });
}