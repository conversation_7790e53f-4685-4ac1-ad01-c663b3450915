const dgram = require('dgram');
const { EventEmitter } = require('events');

// Import Windows Bluetooth Bridge for WSL environments
const { WindowsBluetoothBridge } = require('./WindowsBluetoothBridge');

// Try to import noble, fallback to Windows bridge if not available
let noble = null;
try {
  noble = require('@abandonware/noble');
} catch (error) {
  console.log('⚠️ Native Bluetooth not available - using Windows Bluetooth Bridge');
}

/**
 * Intel Unison Discovery Protocol Implementation
 * Handles Bluetooth LE and WiFi Direct discovery exactly like Intel Unison
 */
class UnisonDiscovery extends EventEmitter {
  constructor() {
    super();
    
    // Intel Unison exact protocol specifications
    this.BLUETOOTH_SERVICE_UUID = '0000FE2C-0000-1000-8000-00805F9B34FB';
    this.MANUFACTURER_DATA_ID = 0x0075; // Intel Corp
    this.WIFI_DIRECT_PREFIX = 'DIRECT-XX-Intel_Unison';
    
    // Network ports (exact match)
    this.UDP_DISCOVERY_PORT = 26817;
    this.TCP_DATA_PORT = 26818;
    this.WEBSOCKET_PORT = 26819;
    
    // Discovery state
    this.isScanning = false;
    this.discoveredDevices = new Map();
    this.bluetoothState = 'unknown';
    
    // Sockets
    this.udpSocket = null;
    this.broadcastInterval = null;
    
    // Windows Bluetooth Bridge
    this.windowsBluetooth = new WindowsBluetoothBridge();
    this.usingWindowsBridge = false;
  }

  async initialize() {
    console.log('🔍 Initializing Intel Unison Discovery Protocol...');
    
    try {
      // Initialize Bluetooth LE discovery
      await this.initializeBluetoothDiscovery();
      
      // Initialize WiFi Direct/UDP discovery
      await this.initializeUDPDiscovery();
      
      console.log('✅ Discovery protocol initialized');
      
    } catch (error) {
      console.error('❌ Discovery initialization failed:', error);
      throw error;
    }
  }

  async initializeBluetoothDiscovery() {
    console.log('📡 Initializing Bluetooth LE discovery...');
    
    // Try Windows Bluetooth Bridge first (for WSL environments)
    try {
      const bridgeSuccess = await this.windowsBluetooth.initialize();
      if (bridgeSuccess) {
        console.log('✅ Using Windows Bluetooth Bridge');
        this.usingWindowsBridge = true;
        this.bluetoothState = 'poweredOn';
        
        // Set up Windows bridge event handlers
        this.windowsBluetooth.on('devices-discovered', (devices) => {
          devices.forEach(device => this.handleWindowsBluetoothDevice(device));
        });
        
        this.windowsBluetooth.on('device-connected', (device) => {
          this.emit('device-connected', device);
        });
        
        this.windowsBluetooth.on('status-update', (status) => {
          this.bluetoothState = status.available ? 'poweredOn' : 'poweredOff';
        });
        
        return;
      }
    } catch (error) {
      console.log('⚠️ Windows Bluetooth Bridge failed, trying native...');
    }
    
    // Fallback to native noble
    if (!noble) {
      console.log('⚠️ Bluetooth not available - skipping Bluetooth discovery');
      this.bluetoothState = 'unavailable';
      return;
    }
    
    // Set up noble for Bluetooth LE scanning
    noble.on('stateChange', (state) => {
      console.log(`📡 Bluetooth state: ${state}`);
      this.bluetoothState = state;
      
      if (state === 'poweredOn') {
        console.log('✅ Bluetooth ready for scanning');
        this.emit('bluetooth-ready');
      } else {
        console.log('⚠️ Bluetooth not available');
        this.stopBluetoothScanning();
      }
    });
    
    noble.on('discover', (peripheral) => {
      this.handleBluetoothDevice(peripheral);
    });
    
    noble.on('scanStart', () => {
      console.log('🔍 Bluetooth scanning started');
    });
    
    noble.on('scanStop', () => {
      console.log('⏹️ Bluetooth scanning stopped');
    });
  }

  async initializeUDPDiscovery() {
    console.log('🌐 Initializing UDP discovery...');
    
    this.udpSocket = dgram.createSocket('udp4');
    
    this.udpSocket.on('message', (msg, rinfo) => {
      this.handleUDPMessage(msg, rinfo);
    });
    
    this.udpSocket.on('error', (error) => {
      console.error('❌ UDP discovery error:', error);
      
      // If port is in use, try alternative ports
      if (error.code === 'EADDRINUSE') {
        console.log('🔄 Port in use, trying alternative ports...');
        this.tryAlternativePort();
      }
    });
    
    this.bindUDPSocket();
  }

  bindUDPSocket() {
    try {
      this.udpSocket.bind(this.UDP_DISCOVERY_PORT, () => {
        console.log(`✅ UDP discovery listening on port ${this.UDP_DISCOVERY_PORT}`);
      });
    } catch (error) {
      console.error('❌ Failed to bind UDP socket:', error);
      this.tryAlternativePort();
    }
  }

  tryAlternativePort() {
    const alternativePorts = [26827, 26837, 26847, 26857];
    
    for (const port of alternativePorts) {
      try {
        console.log(`🔄 Trying port ${port}...`);
        this.UDP_DISCOVERY_PORT = port;
        this.udpSocket.bind(port, () => {
          console.log(`✅ UDP discovery listening on alternative port ${port}`);
          return;
        });
        break;
      } catch (error) {
        console.log(`❌ Port ${port} also in use`);
        continue;
      }
    }
  }

  startDiscovery() {
    console.log('🔍 Starting Intel Unison device discovery...');
    
    if (this.isScanning) {
      console.log('⚠️ Discovery already running');
      return;
    }
    
    this.isScanning = true;
    
    // Start Bluetooth LE scanning
    this.startBluetoothScanning();
    
    // Start UDP broadcasting
    this.startUDPBroadcasting();
    
    console.log('✅ Discovery started');
    this.emit('discovery-started');
  }

  stopDiscovery() {
    console.log('⏹️ Stopping discovery...');
    
    this.isScanning = false;
    
    // Stop Bluetooth scanning
    this.stopBluetoothScanning();
    
    // Stop UDP broadcasting
    this.stopUDPBroadcasting();
    
    console.log('✅ Discovery stopped');
    this.emit('discovery-stopped');
  }

  startBluetoothScanning() {
    if (this.usingWindowsBridge) {
      console.log('🔍 Starting iPhone scan via Windows Bluetooth Bridge...');
      this.windowsBluetooth.scanForDevices()
        .then(() => {
          console.log('✅ Windows Bluetooth scan initiated');
        })
        .catch(error => {
          console.error('❌ Windows Bluetooth scan failed:', error);
        });
      return;
    }
    
    if (!noble || this.bluetoothState !== 'poweredOn') {
      console.log('⚠️ Bluetooth not ready for scanning');
      return;
    }
    
    console.log('🔍 Starting Bluetooth LE scan for Intel Unison devices...');
    
    // Scan for Intel Unison service UUID
    noble.startScanning([this.BLUETOOTH_SERVICE_UUID], false);
  }

  stopBluetoothScanning() {
    if (noble && noble.state === 'poweredOn') {
      noble.stopScanning();
    }
  }

  startUDPBroadcasting() {
    console.log('📡 Starting UDP broadcasting...');
    
    // Broadcast every 5 seconds (Intel Unison interval)
    this.broadcastInterval = setInterval(() => {
      this.broadcastUDPPresence();
    }, 5000);
    
    // Initial broadcast
    setTimeout(() => this.broadcastUDPPresence(), 1000);
  }

  stopUDPBroadcasting() {
    if (this.broadcastInterval) {
      clearInterval(this.broadcastInterval);
      this.broadcastInterval = null;
    }
  }

  broadcastUDPPresence() {
    const hostname = require('os').hostname();
    const message = `UNISON_DISCOVER_REQUEST:PC:${hostname}:${this.TCP_DATA_PORT}`;
    const buffer = Buffer.from(message);
    
    // Try multiple broadcast methods for Windows compatibility
    const broadcastAddresses = ['***************'];
    
    // On Windows, also try local network broadcast
    if (process.platform === 'win32') {
      const networkInterfaces = require('os').networkInterfaces();
      for (const interfaceName in networkInterfaces) {
        const interfaces = networkInterfaces[interfaceName];
        for (const iface of interfaces) {
          if (iface.family === 'IPv4' && !iface.internal) {
            // Calculate broadcast address
            const ip = iface.address.split('.');
            const subnet = iface.netmask.split('.');
            const broadcast = ip.map((octet, i) => {
              return parseInt(octet) | (255 - parseInt(subnet[i]));
            }).join('.');
            broadcastAddresses.push(broadcast);
          }
        }
      }
    }
    
    for (const broadcastAddr of broadcastAddresses) {
      this.udpSocket.send(buffer, 0, buffer.length, this.UDP_DISCOVERY_PORT, broadcastAddr, (error) => {
        if (error) {
          console.error(`❌ Broadcast error to ${broadcastAddr}:`, error);
          
          // If broadcast fails due to permissions, try without broadcast
          if (error.code === 'EACCES' && broadcastAddr === '***************') {
            console.log('🔄 Broadcast permission denied, trying local network only...');
          }
        } else {
          console.log(`📡 Broadcasted UDP discovery to ${broadcastAddr}`);
        }
      });
    }
  }

  handleBluetoothDevice(peripheral) {
    const { advertisement, rssi } = peripheral;
    
    console.log(`📱 Bluetooth device discovered: ${peripheral.id}`);
    console.log(`   Name: ${advertisement.localName || 'Unknown'}`);
    console.log(`   RSSI: ${rssi}dBm`);
    
    // Check if this is an Intel Unison compatible device
    if (this.isIntelUnisonDevice(peripheral)) {
      const deviceInfo = {
        id: peripheral.id,
        name: advertisement.localName || 'Unknown iPhone',
        type: 'bluetooth',
        protocol: 'ble',
        rssi: rssi,
        services: advertisement.serviceUuids || [],
        manufacturerData: advertisement.manufacturerData,
        discoveredAt: Date.now(),
        peripheral: peripheral
      };
      
      this.addDiscoveredDevice(deviceInfo);
      this.emit('device-discovered', deviceInfo);
      
      console.log('✅ Intel Unison compatible device found via Bluetooth');
    }
  }

  handleWindowsBluetoothDevice(device) {
    console.log(`📱 iPhone discovered via Windows Bluetooth: ${device.name}`);
    console.log(`   Address: ${device.address}`);
    console.log(`   RSSI: ${device.rssi}dBm`);
    
    const deviceInfo = {
      id: device.address,
      name: device.name || 'iPhone',
      type: 'bluetooth',
      protocol: 'ble-windows',
      rssi: device.rssi,
      address: device.address,
      services: device.serviceUuids || [],
      txPower: device.txPower,
      discoveredAt: Date.now(),
      windowsDevice: device
    };
    
    this.addDiscoveredDevice(deviceInfo);
    this.emit('device-discovered', deviceInfo);
    
    console.log('✅ iPhone found via Windows Bluetooth Bridge');
  }

  handleUDPMessage(msg, rinfo) {
    const message = msg.toString();
    console.log(`📡 UDP message from ${rinfo.address}:${rinfo.port}: ${message}`);
    
    // Parse Intel Unison UDP messages
    if (message.startsWith('UNISON_DEVICE:')) {
      this.handleUnisonDeviceResponse(message, rinfo);
    } else if (message.startsWith('UNISON_DISCOVER_REQUEST:')) {
      this.handleDiscoveryRequest(message, rinfo);
    }
  }

  handleUnisonDeviceResponse(message, rinfo) {
    // Format: UNISON_DEVICE:iPhone:UUID:capabilities
    const parts = message.split(':');
    
    if (parts.length >= 3) {
      const deviceInfo = {
        id: parts[2], // UUID
        name: `${parts[1]} (${rinfo.address})`,
        type: 'network',
        protocol: 'udp',
        deviceType: parts[1], // iPhone, iPad, etc.
        address: rinfo.address,
        port: rinfo.port,
        capabilities: parts[3] ? parts[3].split(',') : [],
        discoveredAt: Date.now()
      };
      
      this.addDiscoveredDevice(deviceInfo);
      this.emit('device-discovered', deviceInfo);
      
      console.log(`✅ Intel Unison device found: ${deviceInfo.name}`);
    }
  }

  handleDiscoveryRequest(message, rinfo) {
    console.log(`🔍 Discovery request from ${rinfo.address}`);
    
    // Respond with our presence (act like Intel Unison PC)
    const hostname = require('os').hostname();
    const response = `UNISON_DEVICE:PC:${hostname}:messaging,calls,notifications,files`;
    const buffer = Buffer.from(response);
    
    this.udpSocket.send(buffer, 0, buffer.length, rinfo.port, rinfo.address, (error) => {
      if (error) {
        console.error('❌ Discovery response error:', error);
      } else {
        console.log('📡 Sent discovery response');
      }
    });
  }

  isIntelUnisonDevice(peripheral) {
    const { advertisement } = peripheral;
    
    // Check for Intel Unison service UUID
    if (advertisement.serviceUuids && 
        advertisement.serviceUuids.includes(this.BLUETOOTH_SERVICE_UUID)) {
      return true;
    }
    
    // Check manufacturer data for Intel
    if (advertisement.manufacturerData) {
      const manufacturerData = advertisement.manufacturerData;
      if (manufacturerData.length >= 2) {
        const manufacturerId = manufacturerData.readUInt16LE(0);
        if (manufacturerId === this.MANUFACTURER_DATA_ID) {
          return true;
        }
      }
    }
    
    // Check device name patterns
    if (advertisement.localName) {
      const name = advertisement.localName.toLowerCase();
      if (name.includes('intel') && name.includes('unison') ||
          name.includes('iphone') && advertisement.serviceUuids) {
        return true;
      }
    }
    
    return false;
  }

  addDiscoveredDevice(deviceInfo) {
    const existingDevice = this.discoveredDevices.get(deviceInfo.id);
    
    if (existingDevice) {
      // Update existing device info
      Object.assign(existingDevice, deviceInfo);
      existingDevice.lastSeen = Date.now();
    } else {
      // Add new device
      deviceInfo.lastSeen = Date.now();
      this.discoveredDevices.set(deviceInfo.id, deviceInfo);
      console.log(`📱 New device added: ${deviceInfo.name} (${deviceInfo.protocol})`);
    }
  }

  getDiscoveredDevices() {
    return Array.from(this.discoveredDevices.values());
  }

  getDevice(deviceId) {
    return this.discoveredDevices.get(deviceId);
  }

  async connectToDevice(deviceId) {
    const device = this.getDevice(deviceId);
    
    if (!device) {
      throw new Error(`Device ${deviceId} not found`);
    }
    
    console.log(`🔗 Connecting to device: ${device.name}`);
    
    // Emit connection attempt
    this.emit('connection-attempt', device);
    
    return device;
  }

  removeStaleDevices(maxAge = 60000) {
    const now = Date.now();
    const staleDevices = [];
    
    for (const [id, device] of this.discoveredDevices) {
      if (now - device.lastSeen > maxAge) {
        staleDevices.push(id);
      }
    }
    
    staleDevices.forEach(id => {
      const device = this.discoveredDevices.get(id);
      console.log(`🗑️ Removing stale device: ${device.name}`);
      this.discoveredDevices.delete(id);
      this.emit('device-removed', device);
    });
    
    return staleDevices.length;
  }

  getDiscoveryStatus() {
    return {
      isScanning: this.isScanning,
      bluetoothState: this.bluetoothState,
      devicesFound: this.discoveredDevices.size,
      protocols: {
        bluetooth: this.bluetoothState === 'poweredOn',
        udp: this.udpSocket !== null
      }
    };
  }

  async cleanup() {
    console.log('🧹 Cleaning up discovery service...');
    
    this.stopDiscovery();
    
    if (this.udpSocket) {
      this.udpSocket.close();
      this.udpSocket = null;
    }
    
    this.discoveredDevices.clear();
    
    console.log('✅ Discovery cleanup complete');
  }
}

module.exports = { UnisonDiscovery };