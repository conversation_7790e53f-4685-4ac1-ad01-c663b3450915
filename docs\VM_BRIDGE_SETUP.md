# macOS VM Bridge Setup Guide

## Overview

The macOS VM Bridge is an advanced feature of iPhone Companion Pro that enables direct access to iPhone data through a macOS virtual machine. This provides the most comprehensive iPhone integration possible on Windows, including real-time message synchronization, contacts access, and native iOS app functionality.

## Prerequisites

### System Requirements

- **Operating System**: Windows 10/11 (64-bit)
- **RAM**: Minimum 12GB (16GB+ recommended)
- **CPU**: Intel VT-x or AMD-V virtualization support
- **Storage**: 100GB+ free space
- **Network**: Stable internet connection

### Required Software

1. **QEMU for Windows**
   - Download from: https://qemu.weilnetz.de/w64/
   - Install with default settings
   - Ensure QEMU is added to system PATH

2. **macOS Installation Media**
   - Legal macOS installer (Big Sur 11.0+ recommended)
   - Convert to QEMU-compatible format

3. **Virtualization Support**
   - Enable VT-x/AMD-V in BIOS/UEFI
   - Disable Hyper-V if enabled

## Installation Steps

### Step 1: Enable Virtualization

1. **Check Virtualization Support**
   ```cmd
   wmic cpu get VirtualizationFirmwareEnabled
   ```
   Should return `TRUE`

2. **Enable in BIOS/UEFI**
   - <PERSON>art computer and enter BIOS/UEFI
   - Find "Virtualization Technology" or "VT-x"/"AMD-V"
   - Enable the setting
   - Save and exit

3. **Disable Hyper-V (if enabled)**
   ```cmd
   dism.exe /Online /Disable-Feature:Microsoft-Hyper-V
   ```

### Step 2: Install QEMU

1. **Download QEMU**
   - Visit https://qemu.weilnetz.de/w64/
   - Download latest stable version
   - Run installer as Administrator

2. **Verify Installation**
   ```cmd
   qemu-system-x86_64 --version
   ```

3. **Install Additional Components**
   - Download OVMF BIOS files
   - Place in QEMU installation directory

### Step 3: Prepare macOS Installation

1. **Download macOS Installer**
   - Use legitimate macOS installer
   - Convert to QCOW2 format:
   ```cmd
   qemu-img convert -f raw -O qcow2 macos-installer.dmg macos-installer.qcow2
   ```

2. **Create VM Disk**
   ```cmd
   qemu-img create -f qcow2 macos.qcow2 80G
   ```

### Step 4: Configure iPhone Companion Pro

1. **Open Settings**
   - Launch iPhone Companion Pro
   - Click Settings (⚙️) button
   - Select "macOS VM Bridge"

2. **Enable VM Bridge**
   - Click "Enable VM Bridge"
   - Configure VM settings:
     - Memory: 8GB (adjust based on system)
     - CPU Cores: 4 (adjust based on system)
     - Disk Size: 80GB

3. **Network Configuration**
   - Bridge Port: 8080 (default)
   - SSH Port: 2222 (default)
   - VNC Port: 5900 (default)

### Step 5: Install macOS in VM

1. **Start VM Installation**
   - VM Bridge will automatically start QEMU
   - Connect via VNC to localhost:5900
   - Follow macOS installation process

2. **Complete macOS Setup**
   - Create user account
   - Enable necessary permissions
   - Install Xcode Command Line Tools

3. **Install Bridge Service**
   - VM Bridge automatically installs Node.js service
   - Service connects to Messages.app database
   - WebSocket tunnel established

## Configuration

### VM Performance Tuning

1. **Memory Allocation**
   - Minimum: 6GB for basic functionality
   - Recommended: 8GB for optimal performance
   - Maximum: 50% of host system RAM

2. **CPU Configuration**
   - Minimum: 2 cores
   - Recommended: 4 cores
   - Maximum: 75% of host CPU cores

3. **Storage Optimization**
   - Use SSD for VM disk if possible
   - Enable write caching for better performance
   - Regular disk cleanup and optimization

### Network Configuration

1. **Port Forwarding**
   - Bridge Service: 8080
   - SSH Access: 2222
   - VNC Console: 5900
   - iTunes Sync: 62078, 62087

2. **Firewall Settings**
   - Allow QEMU through Windows Firewall
   - Open required ports for VM communication

### Security Settings

1. **Enable Encryption**
   - WebSocket communication encryption
   - Secure authentication tokens
   - Regular security updates

2. **Access Control**
   - Restrict VM network access
   - Monitor connection attempts
   - Regular security audits

## Troubleshooting

### Common Issues

1. **VM Won't Start**
   - Check virtualization is enabled
   - Verify QEMU installation
   - Check available system resources
   - Review error logs

2. **Bridge Connection Failed**
   - Verify VM is running
   - Check network configuration
   - Restart bridge service
   - Check firewall settings

3. **Poor Performance**
   - Increase VM memory allocation
   - Add more CPU cores
   - Use SSD storage
   - Close unnecessary applications

4. **Messages Not Syncing**
   - Check macOS permissions
   - Verify Messages.app is signed in
   - Restart bridge service
   - Check database access

### Error Codes

- **VM_001**: Virtualization not supported
- **VM_002**: Insufficient memory
- **VM_003**: QEMU not found
- **VM_004**: VM creation failed
- **VM_005**: Bridge connection timeout

### Log Files

- VM Logs: `%USERPROFILE%/iPhone-Companion-Pro/macos-vm/logs/`
- Bridge Logs: Available in VM Bridge UI
- System Logs: Windows Event Viewer

## Advanced Configuration

### Custom VM Settings

1. **Edit VM Configuration**
   ```javascript
   // In VM Bridge settings
   {
     "memory": "8G",
     "cores": 4,
     "diskSize": "80G",
     "enableAppleFeatures": true,
     "enableEncryption": true
   }
   ```

2. **Performance Optimization**
   - Enable KVM acceleration
   - Use virtio drivers
   - Optimize disk I/O
   - Configure CPU affinity

### Backup and Recovery

1. **VM Snapshots**
   - Create snapshots before major changes
   - Regular backup schedule
   - Test restore procedures

2. **Configuration Backup**
   - Export VM Bridge settings
   - Backup VM disk images
   - Document custom configurations

## Legal Considerations

### macOS Licensing

- Only use legally obtained macOS installers
- Comply with Apple's Software License Agreement
- VM Bridge is for personal use only
- Respect intellectual property rights

### Privacy and Security

- Messages data remains on local system
- No data transmitted to external servers
- User responsible for data security
- Regular security updates recommended

## Support

### Getting Help

1. **Documentation**
   - Check this guide first
   - Review FAQ section
   - Search known issues

2. **Community Support**
   - GitHub Issues
   - Community Forums
   - User Discord

3. **Professional Support**
   - Enterprise support available
   - Custom configuration assistance
   - Training and consultation

### Reporting Issues

1. **Bug Reports**
   - Include system information
   - Provide error logs
   - Describe reproduction steps
   - Include screenshots if helpful

2. **Feature Requests**
   - Describe use case
   - Explain expected behavior
   - Consider implementation complexity

## Updates and Maintenance

### Regular Maintenance

1. **VM Updates**
   - Keep macOS updated
   - Update bridge service
   - Monitor performance metrics

2. **System Maintenance**
   - Regular disk cleanup
   - Monitor resource usage
   - Update host system

3. **Security Updates**
   - Apply security patches
   - Update encryption keys
   - Review access logs

### Version Compatibility

- iPhone Companion Pro: v1.0.0+
- macOS: Big Sur 11.0+
- QEMU: 6.0.0+
- Windows: 10/11 64-bit

---

**Note**: This is an advanced feature requiring technical knowledge. Ensure you understand the implications and have adequate system resources before proceeding.
