<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Device Manager - iPhone Companion Pro</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/device-manager.css">
</head>
<body>
    <div class="titlebar">
        <div class="titlebar-title">Device Manager</div>
        <div class="titlebar-controls">
            <button class="titlebar-button" onclick="window.close()">✕</button>
        </div>
    </div>

    <div class="device-manager-container">
        <!-- Device Overview -->
        <div class="device-overview">
            <div class="device-card">
                <div class="device-header">
                    <div class="device-icon">📱</div>
                    <div class="device-info">
                        <h2 id="device-name">iPhone 14 Pro</h2>
                        <p id="device-model">iPhone15,2</p>
                        <div class="connection-status" id="connection-status">
                            <div class="status-dot connected"></div>
                            <span>Connected via USB</span>
                        </div>
                    </div>
                    <div class="device-actions">
                        <button class="action-btn primary" onclick="refreshDeviceInfo()">🔄 Refresh</button>
                        <button class="action-btn secondary" onclick="disconnectDevice()">🔌 Disconnect</button>
                    </div>
                </div>
                
                <div class="device-stats">
                    <div class="stat-item">
                        <div class="stat-icon">💾</div>
                        <div class="stat-info">
                            <div class="stat-label">Storage</div>
                            <div class="stat-value" id="storage-info">45.2 GB / 128 GB</div>
                            <div class="stat-bar">
                                <div class="stat-progress" id="storage-progress" style="width: 35%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-icon">🔋</div>
                        <div class="stat-info">
                            <div class="stat-label">Battery</div>
                            <div class="stat-value" id="battery-info">78% - Charging</div>
                            <div class="stat-bar">
                                <div class="stat-progress battery" id="battery-progress" style="width: 78%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-icon">🌡️</div>
                        <div class="stat-info">
                            <div class="stat-label">Temperature</div>
                            <div class="stat-value" id="temperature-info">32°C - Normal</div>
                        </div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-icon">📶</div>
                        <div class="stat-info">
                            <div class="stat-label">Signal</div>
                            <div class="stat-value" id="signal-info">4 bars - Verizon</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Device Details Tabs -->
        <div class="device-details">
            <div class="tab-navigation">
                <button class="tab-btn active" data-tab="system" onclick="switchTab('system')">System Info</button>
                <button class="tab-btn" data-tab="apps" onclick="switchTab('apps')">Apps</button>
                <button class="tab-btn" data-tab="storage" onclick="switchTab('storage')">Storage</button>
                <button class="tab-btn" data-tab="network" onclick="switchTab('network')">Network</button>
                <button class="tab-btn" data-tab="security" onclick="switchTab('security')">Security</button>
                <button class="tab-btn" data-tab="diagnostics" onclick="switchTab('diagnostics')">Diagnostics</button>
            </div>

            <!-- System Info Tab -->
            <div class="tab-content active" id="system-tab">
                <div class="info-section">
                    <h3>Device Information</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">Device Name:</span>
                            <span class="info-value" id="sys-device-name">iPhone 14 Pro</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Model:</span>
                            <span class="info-value" id="sys-model">iPhone15,2</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">iOS Version:</span>
                            <span class="info-value" id="sys-ios-version">17.1.1</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Serial Number:</span>
                            <span class="info-value" id="sys-serial">F2LW8J9N14</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">UDID:</span>
                            <span class="info-value" id="sys-udid">00008030-001E24E02E38802E</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Capacity:</span>
                            <span class="info-value" id="sys-capacity">128 GB</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Color:</span>
                            <span class="info-value" id="sys-color">Deep Purple</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Carrier:</span>
                            <span class="info-value" id="sys-carrier">Verizon</span>
                        </div>
                    </div>
                </div>

                <div class="info-section">
                    <h3>Hardware Information</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">Processor:</span>
                            <span class="info-value">A16 Bionic</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">RAM:</span>
                            <span class="info-value">6 GB</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Display:</span>
                            <span class="info-value">6.1" Super Retina XDR</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Resolution:</span>
                            <span class="info-value">1179 × 2556</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Camera:</span>
                            <span class="info-value">48MP Main, 12MP Ultra Wide</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Face ID:</span>
                            <span class="info-value">Enabled</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Apps Tab -->
            <div class="tab-content" id="apps-tab">
                <div class="apps-header">
                    <h3>Installed Applications</h3>
                    <div class="apps-controls">
                        <input type="text" id="apps-search" placeholder="Search apps..." onkeyup="searchApps()">
                        <select id="apps-filter" onchange="filterApps()">
                            <option value="all">All Apps</option>
                            <option value="user">User Apps</option>
                            <option value="system">System Apps</option>
                        </select>
                    </div>
                </div>
                <div class="apps-list" id="apps-list">
                    <!-- Apps will be loaded here -->
                </div>
            </div>

            <!-- Storage Tab -->
            <div class="tab-content" id="storage-tab">
                <div class="storage-overview">
                    <h3>Storage Usage</h3>
                    <div class="storage-chart">
                        <canvas id="storage-chart-canvas" width="300" height="300"></canvas>
                    </div>
                    <div class="storage-breakdown" id="storage-breakdown">
                        <!-- Storage breakdown will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Network Tab -->
            <div class="tab-content" id="network-tab">
                <div class="network-info">
                    <h3>Network Information</h3>
                    <div class="network-sections">
                        <div class="network-section">
                            <h4>Cellular</h4>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-label">Carrier:</span>
                                    <span class="info-value">Verizon</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Signal Strength:</span>
                                    <span class="info-value">-65 dBm</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Network Type:</span>
                                    <span class="info-value">5G</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Data Usage:</span>
                                    <span class="info-value">2.3 GB this month</span>
                                </div>
                            </div>
                        </div>

                        <div class="network-section">
                            <h4>Wi-Fi</h4>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-label">Network:</span>
                                    <span class="info-value">Home_WiFi_5G</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">IP Address:</span>
                                    <span class="info-value">*************</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">MAC Address:</span>
                                    <span class="info-value">A4:83:E7:2B:5F:1C</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Signal Strength:</span>
                                    <span class="info-value">-45 dBm (Excellent)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Tab -->
            <div class="tab-content" id="security-tab">
                <div class="security-info">
                    <h3>Security Status</h3>
                    <div class="security-items">
                        <div class="security-item">
                            <div class="security-icon">🔒</div>
                            <div class="security-details">
                                <h4>Device Lock</h4>
                                <p>Passcode enabled with Face ID</p>
                                <span class="status-badge secure">Secure</span>
                            </div>
                        </div>
                        
                        <div class="security-item">
                            <div class="security-icon">🔐</div>
                            <div class="security-details">
                                <h4>Data Protection</h4>
                                <p>FileVault encryption enabled</p>
                                <span class="status-badge secure">Secure</span>
                            </div>
                        </div>
                        
                        <div class="security-item">
                            <div class="security-icon">📍</div>
                            <div class="security-details">
                                <h4>Find My iPhone</h4>
                                <p>Location services enabled</p>
                                <span class="status-badge secure">Active</span>
                            </div>
                        </div>
                        
                        <div class="security-item">
                            <div class="security-icon">🛡️</div>
                            <div class="security-details">
                                <h4>App Store</h4>
                                <p>Only trusted apps allowed</p>
                                <span class="status-badge secure">Protected</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Diagnostics Tab -->
            <div class="tab-content" id="diagnostics-tab">
                <div class="diagnostics-info">
                    <h3>Device Diagnostics</h3>
                    <div class="diagnostics-actions">
                        <button class="action-btn primary" onclick="runDiagnostics()">🔍 Run Full Diagnostics</button>
                        <button class="action-btn secondary" onclick="exportLogs()">📄 Export Logs</button>
                        <button class="action-btn secondary" onclick="clearCache()">🗑️ Clear Cache</button>
                    </div>
                    
                    <div class="diagnostics-results" id="diagnostics-results">
                        <div class="diagnostic-item">
                            <div class="diagnostic-icon">✅</div>
                            <div class="diagnostic-info">
                                <h4>Battery Health</h4>
                                <p>Maximum capacity: 89%</p>
                                <p>Peak performance capability: Normal</p>
                            </div>
                        </div>
                        
                        <div class="diagnostic-item">
                            <div class="diagnostic-icon">✅</div>
                            <div class="diagnostic-info">
                                <h4>Storage Health</h4>
                                <p>No issues detected</p>
                                <p>Available space: 82.8 GB</p>
                            </div>
                        </div>
                        
                        <div class="diagnostic-item">
                            <div class="diagnostic-icon">⚠️</div>
                            <div class="diagnostic-info">
                                <h4>Network Performance</h4>
                                <p>Occasional slow Wi-Fi detected</p>
                                <p>Recommendation: Reset network settings</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        let deviceInfo = {};
        let apps = [];
        let currentTab = 'system';

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            loadDeviceInfo();
            loadApps();
            setupEventListeners();

            // Listen for device updates
            ipcRenderer.on('device-info-updated', (event, data) => {
                deviceInfo = data;
                updateDeviceDisplay();
            });

            ipcRenderer.on('device-disconnected', () => {
                updateConnectionStatus(false);
            });

            ipcRenderer.on('device-connected', () => {
                updateConnectionStatus(true);
                loadDeviceInfo();
            });
        });

        function setupEventListeners() {
            // Auto-refresh device info every 30 seconds
            setInterval(() => {
                if (document.getElementById('connection-status').classList.contains('connected')) {
                    refreshDeviceInfo();
                }
            }, 30000);
        }

        async function loadDeviceInfo() {
            try {
                const result = await ipcRenderer.invoke('get-device-info');
                if (result.success) {
                    deviceInfo = result.data;
                    updateDeviceDisplay();
                } else {
                    // Use sample data for demo
                    deviceInfo = generateSampleDeviceInfo();
                    updateDeviceDisplay();
                }
            } catch (error) {
                console.error('Failed to load device info:', error);
                deviceInfo = generateSampleDeviceInfo();
                updateDeviceDisplay();
            }
        }

        function generateSampleDeviceInfo() {
            return {
                name: 'iPhone 14 Pro',
                model: 'iPhone15,2',
                iosVersion: '17.1.1',
                serialNumber: 'F2LW8J9N14',
                udid: '00008030-001E24E02E38802E',
                capacity: 128 * 1024 * 1024 * 1024,
                usedStorage: 45.2 * 1024 * 1024 * 1024,
                batteryLevel: 78,
                batteryState: 'charging',
                temperature: 32,
                carrier: 'Verizon',
                signalStrength: -65,
                networkType: '5G',
                color: 'Deep Purple',
                isConnected: true
            };
        }

        function updateDeviceDisplay() {
            // Update header info
            document.getElementById('device-name').textContent = deviceInfo.name || 'Unknown Device';
            document.getElementById('device-model').textContent = deviceInfo.model || 'Unknown Model';

            // Update stats
            const usedGB = (deviceInfo.usedStorage / (1024 * 1024 * 1024)).toFixed(1);
            const totalGB = (deviceInfo.capacity / (1024 * 1024 * 1024)).toFixed(1);
            const storagePercent = (deviceInfo.usedStorage / deviceInfo.capacity) * 100;

            document.getElementById('storage-info').textContent = `${usedGB} GB / ${totalGB} GB`;
            document.getElementById('storage-progress').style.width = `${storagePercent}%`;

            const batteryStatus = deviceInfo.batteryState === 'charging' ? 'Charging' : 'Not Charging';
            document.getElementById('battery-info').textContent = `${deviceInfo.batteryLevel}% - ${batteryStatus}`;
            document.getElementById('battery-progress').style.width = `${deviceInfo.batteryLevel}%`;

            document.getElementById('temperature-info').textContent = `${deviceInfo.temperature}°C - Normal`;
            document.getElementById('signal-info').textContent = `${Math.abs(deviceInfo.signalStrength)} dBm - ${deviceInfo.carrier}`;

            // Update system tab
            document.getElementById('sys-device-name').textContent = deviceInfo.name;
            document.getElementById('sys-model').textContent = deviceInfo.model;
            document.getElementById('sys-ios-version').textContent = deviceInfo.iosVersion;
            document.getElementById('sys-serial').textContent = deviceInfo.serialNumber;
            document.getElementById('sys-udid').textContent = deviceInfo.udid;
            document.getElementById('sys-capacity').textContent = `${totalGB} GB`;
            document.getElementById('sys-color').textContent = deviceInfo.color;
            document.getElementById('sys-carrier').textContent = deviceInfo.carrier;

            updateConnectionStatus(deviceInfo.isConnected);
        }

        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connection-status');
            const dot = statusElement.querySelector('.status-dot');
            const text = statusElement.querySelector('span');

            if (connected) {
                dot.className = 'status-dot connected';
                text.textContent = 'Connected via USB';
            } else {
                dot.className = 'status-dot disconnected';
                text.textContent = 'Disconnected';
            }
        }

        async function refreshDeviceInfo() {
            const refreshBtn = document.querySelector('[onclick="refreshDeviceInfo()"]');
            if (refreshBtn) {
                refreshBtn.innerHTML = '🔄 Refreshing...';
                refreshBtn.disabled = true;
            }

            await loadDeviceInfo();

            if (refreshBtn) {
                refreshBtn.innerHTML = '🔄 Refresh';
                refreshBtn.disabled = false;
            }
        }

        async function disconnectDevice() {
            try {
                const result = await ipcRenderer.invoke('disconnect-device');
                if (result.success) {
                    updateConnectionStatus(false);
                }
            } catch (error) {
                console.error('Failed to disconnect device:', error);
            }
        }

        function switchTab(tabName) {
            currentTab = tabName;

            // Update tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // Update tab content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`${tabName}-tab`).classList.add('active');

            // Load tab-specific data
            switch (tabName) {
                case 'apps':
                    loadApps();
                    break;
                case 'storage':
                    loadStorageInfo();
                    break;
                case 'diagnostics':
                    loadDiagnostics();
                    break;
            }
        }

        async function loadApps() {
            try {
                const result = await ipcRenderer.invoke('get-device-apps');
                if (result.success) {
                    apps = result.data;
                } else {
                    apps = generateSampleApps();
                }
                renderApps();
            } catch (error) {
                console.error('Failed to load apps:', error);
                apps = generateSampleApps();
                renderApps();
            }
        }

        function generateSampleApps() {
            return [
                { name: 'Messages', bundleId: 'com.apple.MobileSMS', version: '17.1', size: 45.2, type: 'system' },
                { name: 'Safari', bundleId: 'com.apple.mobilesafari', version: '17.1', size: 123.5, type: 'system' },
                { name: 'Instagram', bundleId: 'com.burbn.instagram', version: '302.0', size: 287.3, type: 'user' },
                { name: 'WhatsApp', bundleId: 'net.whatsapp.WhatsApp', version: '23.21.78', size: 156.8, type: 'user' },
                { name: 'Spotify', bundleId: 'com.spotify.client', version: '8.8.78', size: 234.1, type: 'user' },
                { name: 'YouTube', bundleId: 'com.google.ios.youtube', version: '18.45.2', size: 198.7, type: 'user' },
                { name: 'Settings', bundleId: 'com.apple.Preferences', version: '17.1', size: 23.4, type: 'system' },
                { name: 'Camera', bundleId: 'com.apple.camera', version: '17.1', size: 67.8, type: 'system' }
            ];
        }

        function renderApps() {
            const container = document.getElementById('apps-list');
            if (!container) return;

            container.innerHTML = '';

            apps.forEach(app => {
                const appElement = document.createElement('div');
                appElement.className = 'app-item';

                const appIcon = app.type === 'system' ? '⚙️' : '📱';
                const sizeText = `${app.size.toFixed(1)} MB`;

                appElement.innerHTML = `
                    <div class="app-icon">${appIcon}</div>
                    <div class="app-info">
                        <div class="app-name">${app.name}</div>
                        <div class="app-details">
                            <span class="app-version">v${app.version}</span>
                            <span class="app-size">${sizeText}</span>
                            <span class="app-type">${app.type}</span>
                        </div>
                    </div>
                    <div class="app-actions">
                        <button class="action-btn small" onclick="openApp('${app.bundleId}')">Open</button>
                        ${app.type === 'user' ? `<button class="action-btn small danger" onclick="uninstallApp('${app.bundleId}')">Uninstall</button>` : ''}
                    </div>
                `;

                container.appendChild(appElement);
            });
        }

        function searchApps() {
            const searchTerm = document.getElementById('apps-search').value.toLowerCase();
            const filteredApps = apps.filter(app =>
                app.name.toLowerCase().includes(searchTerm) ||
                app.bundleId.toLowerCase().includes(searchTerm)
            );

            renderFilteredApps(filteredApps);
        }

        function filterApps() {
            const filter = document.getElementById('apps-filter').value;
            let filteredApps = apps;

            if (filter !== 'all') {
                filteredApps = apps.filter(app => app.type === filter);
            }

            renderFilteredApps(filteredApps);
        }

        function renderFilteredApps(filteredApps) {
            const container = document.getElementById('apps-list');
            container.innerHTML = '';

            filteredApps.forEach(app => {
                const appElement = document.createElement('div');
                appElement.className = 'app-item';

                const appIcon = app.type === 'system' ? '⚙️' : '📱';
                const sizeText = `${app.size.toFixed(1)} MB`;

                appElement.innerHTML = `
                    <div class="app-icon">${appIcon}</div>
                    <div class="app-info">
                        <div class="app-name">${app.name}</div>
                        <div class="app-details">
                            <span class="app-version">v${app.version}</span>
                            <span class="app-size">${sizeText}</span>
                            <span class="app-type">${app.type}</span>
                        </div>
                    </div>
                    <div class="app-actions">
                        <button class="action-btn small" onclick="openApp('${app.bundleId}')">Open</button>
                        ${app.type === 'user' ? `<button class="action-btn small danger" onclick="uninstallApp('${app.bundleId}')">Uninstall</button>` : ''}
                    </div>
                `;

                container.appendChild(appElement);
            });
        }

        async function openApp(bundleId) {
            try {
                const result = await ipcRenderer.invoke('open-app', { bundleId });
                if (result.success) {
                    console.log(`Opened app: ${bundleId}`);
                } else {
                    alert('Failed to open app: ' + result.error);
                }
            } catch (error) {
                alert('Failed to open app: ' + error.message);
            }
        }

        async function uninstallApp(bundleId) {
            if (confirm('Are you sure you want to uninstall this app?')) {
                try {
                    const result = await ipcRenderer.invoke('uninstall-app', { bundleId });
                    if (result.success) {
                        loadApps(); // Refresh app list
                    } else {
                        alert('Failed to uninstall app: ' + result.error);
                    }
                } catch (error) {
                    alert('Failed to uninstall app: ' + error.message);
                }
            }
        }

        function loadStorageInfo() {
            // Create storage chart
            const canvas = document.getElementById('storage-chart-canvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const radius = 100;

            // Sample storage data
            const storageData = [
                { label: 'Photos', value: 15.2, color: '#FF6B6B' },
                { label: 'Apps', value: 12.8, color: '#4ECDC4' },
                { label: 'System', value: 8.5, color: '#45B7D1' },
                { label: 'Videos', value: 6.3, color: '#96CEB4' },
                { label: 'Music', value: 2.4, color: '#FFEAA7' },
                { label: 'Other', value: 0.8, color: '#DDA0DD' }
            ];

            let currentAngle = 0;
            const total = storageData.reduce((sum, item) => sum + item.value, 0);

            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw pie chart
            storageData.forEach(item => {
                const sliceAngle = (item.value / total) * 2 * Math.PI;

                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
                ctx.closePath();
                ctx.fillStyle = item.color;
                ctx.fill();

                currentAngle += sliceAngle;
            });

            // Update storage breakdown
            const breakdown = document.getElementById('storage-breakdown');
            breakdown.innerHTML = '';

            storageData.forEach(item => {
                const itemElement = document.createElement('div');
                itemElement.className = 'storage-item';
                itemElement.innerHTML = `
                    <div class="storage-color" style="background: ${item.color}"></div>
                    <div class="storage-label">${item.label}</div>
                    <div class="storage-value">${item.value} GB</div>
                `;
                breakdown.appendChild(itemElement);
            });
        }

        async function runDiagnostics() {
            const btn = document.querySelector('[onclick="runDiagnostics()"]');
            btn.innerHTML = '🔍 Running Diagnostics...';
            btn.disabled = true;

            try {
                const result = await ipcRenderer.invoke('run-device-diagnostics');
                if (result.success) {
                    updateDiagnosticsResults(result.data);
                }
            } catch (error) {
                console.error('Failed to run diagnostics:', error);
            }

            btn.innerHTML = '🔍 Run Full Diagnostics';
            btn.disabled = false;
        }

        function updateDiagnosticsResults(results) {
            // Update diagnostics display with results
            console.log('Diagnostics results:', results);
        }

        async function exportLogs() {
            try {
                const result = await ipcRenderer.invoke('export-device-logs');
                if (result.success) {
                    alert('Logs exported successfully!');
                }
            } catch (error) {
                alert('Failed to export logs: ' + error.message);
            }
        }

        async function clearCache() {
            if (confirm('Are you sure you want to clear device cache?')) {
                try {
                    const result = await ipcRenderer.invoke('clear-device-cache');
                    if (result.success) {
                        alert('Cache cleared successfully!');
                    }
                } catch (error) {
                    alert('Failed to clear cache: ' + error.message);
                }
            }
        }

        function loadDiagnostics() {
            // Load diagnostic information
            console.log('Loading diagnostics...');
        }
    </script>
</body>
</html>
