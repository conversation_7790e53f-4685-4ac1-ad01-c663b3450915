// Quick test to verify real iPhone data connections
const { PhoneLinkBridge } = require('./src/main/services/PhoneLinkBridge');

async function testRealData() {
    console.log('🔍 Testing Real iPhone Data Connections...\n');

    // Test Phone Link Bridge
    console.log('📱 Testing Phone Link Bridge...');
    const phoneLinkBridge = new PhoneLinkBridge();

    let messages = [];
    try {
        messages = await phoneLinkBridge.getPhoneLinkMessages();

        if (messages && messages.length > 0) {
            console.log(`✅ SUCCESS: Found ${messages.length} conversations from Phone Link!`);

            // Show details of first few conversations
            messages.slice(0, 3).forEach((conv, index) => {
                console.log(`\n📞 Conversation ${index + 1}:`);
                console.log(`   Contact: ${conv.contactName}`);
                console.log(`   Phone: ${conv.phoneNumber}`);
                console.log(`   Messages: ${conv.messages?.length || 0}`);
                console.log(`   Real Data: ${conv.isReal ? '✅ YES' : '❌ NO'}`);
                console.log(`   Source: ${conv.source}`);

                if (conv.messages && conv.messages.length > 0) {
                    console.log(`   Last Message: "${conv.lastMessage}"`);
                    console.log(`   Timestamp: ${conv.lastMessageTime}`);
                }
            });
        } else {
            console.log('❌ No messages found in Phone Link');
        }
    } catch (error) {
        console.error('❌ Phone Link Bridge Error:', error.message);
        console.error('Full error:', error);
    }

    console.log('\n' + '='.repeat(50));
    console.log('📊 REAL DATA VERIFICATION SUMMARY');
    console.log('='.repeat(50));

    // Check if we have any real data sources
    const hasPhoneLink = messages && messages.length > 0;
    const hasRealMessages = messages && messages.some(conv => conv.isReal);

    console.log(`Phone Link Integration: ${hasPhoneLink ? '✅ WORKING' : '❌ NOT WORKING'}`);
    console.log(`Real Message Data: ${hasRealMessages ? '✅ FOUND' : '❌ NOT FOUND'}`);

    if (hasRealMessages) {
        console.log('\n🎉 BREAKTHROUGH: You have REAL iPhone data flowing!');
        console.log('Next steps:');
        console.log('1. Test AirPlay screen mirroring');
        console.log('2. Install iOS companion app');
        console.log('3. Set up iOS shortcuts for live sync');
    } else {
        console.log('\n⚠️  Only demo/contact data found. To get real messages:');
        console.log('1. Ensure iPhone is connected to Phone Link');
        console.log('2. Grant message permissions in Phone Link');
        console.log('3. Try the iOS companion app method');
    }
}

// Run the test
testRealData().catch(console.error);
