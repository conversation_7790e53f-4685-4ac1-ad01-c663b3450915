// Connection Status Component - Updated with all methods
import React, { useState, useEffect } from 'react';
import './ConnectionStatus.css';

const ConnectionStatus = () => {
  const [connections, setConnections] = useState({
    usb: { connected: false, available: false },
    phoneLink: { connected: false, available: false },
    airplay: { connected: false, available: false },
    macosVM: { connected: false, available: false },
    bluetooth: { connected: false, available: false }
  });

  useEffect(() => {
    // Listen for connection status updates
    window.electronAPI.onConnectionChange((event, data) => {
      setConnections(prev => ({
        ...prev,
        [data.type]: {
          ...prev[data.type],
          connected: data.status === 'connected'
        }
      }));
    });

    // Request initial status
    window.electronAPI.getConnectionStatus().then(status => {
      setConnections(prev => {
        const updated = { ...prev };
        Object.keys(status).forEach(type => {
          if (updated[type]) {
            updated[type] = { ...updated[type], ...status[type] };
          }
        });
        return updated;
      });
    });
  }, []);

  const getStatusIcon = (connection) => {
    if (!connection.available) return '⚫';
    return connection.connected ? '🟢' : '🔴';
  };

  const getStatusText = (connection) => {
    if (!connection.available) return 'Not Available';
    return connection.connected ? 'Connected' : 'Disconnected';
  };

  return (
    <div className="connection-status">
      <h3>📱 iPhone Connections</h3>
      <div className="connection-grid">
        <div className="connection-item">
          <span className="icon">{getStatusIcon(connections.usb)}</span>
          <span className="label">USB (iTunes)</span>
          <span className="status">{getStatusText(connections.usb)}</span>
        </div>
        
        <div className="connection-item">
          <span className="icon">{getStatusIcon(connections.phoneLink)}</span>
          <span className="label">Phone Link</span>
          <span className="status">{getStatusText(connections.phoneLink)}</span>
        </div>
        
        <div className="connection-item">
          <span className="icon">{getStatusIcon(connections.airplay)}</span>
          <span className="label">AirPlay</span>
          <span className="status">{getStatusText(connections.airplay)}</span>
        </div>
        
        <div className="connection-item">
          <span className="icon">{getStatusIcon(connections.macosVM)}</span>
          <span className="label">macOS VM</span>
          <span className="status">{getStatusText(connections.macosVM)}</span>
        </div>
        
        <div className="connection-item">
          <span className="icon">{getStatusIcon(connections.bluetooth)}</span>
          <span className="label">Bluetooth</span>
          <span className="status">{getStatusText(connections.bluetooth)}</span>
        </div>
      </div>
    </div>
  );
};

export default ConnectionStatus;