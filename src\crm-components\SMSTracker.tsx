import React, { useState, useEffect, useRef } from 'react';
import { useUser } from '@clerk/nextjs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { AlertCircle, Phone, MessageCircle, Send, Search, Filter, MoreVertical, Bot } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface Message {
  id: string;
  phoneNumber: string;
  contactName: string;
  content: string;
  direction: 'inbound' | 'outbound';
  timestamp: Date;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  sentiment?: {
    score: number;
    label: 'positive' | 'negative' | 'neutral';
    confidence: number;
  };
  intent?: {
    category: string;
    confidence: number;
  };
  leadScore?: number;
}

interface Contact {
  id: string;
  phoneNumber: string;
  name: string;
  type: 'client' | 'lead';
  lastActivity: Date;
  unreadCount: number;
  leadScore: number;
  avatar?: string;
}

interface SMSTrackerProps {
  clientId?: string;
  leadId?: string;
  height?: string;
  showAI?: boolean;
}

export const SMSTracker: React.FC<SMSTrackerProps> = ({ 
  clientId, 
  leadId, 
  height = '600px',
  showAI = true 
}) => {
  const { user } = useUser();
  const [messages, setMessages] = useState<Message[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'syncing'>('disconnected');
  const [aiInsights, setAIInsights] = useState<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const wsRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    initializeConnection();
    loadInitialData();
    
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [user]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (selectedContact && showAI) {
      loadAIInsights(selectedContact.phoneNumber);
    }
  }, [selectedContact, showAI]);

  const initializeConnection = () => {
    if (!user) return;

    // Connect to Intel Unison++ WebSocket
    const wsUrl = 'ws://localhost:8765';
    wsRef.current = new WebSocket(wsUrl);

    wsRef.current.onopen = () => {
      setIsConnected(true);
      setConnectionStatus('connected');
      console.log('📱 Connected to Intel Unison++');
    };

    wsRef.current.onmessage = (event) => {
      const data = JSON.parse(event.data);
      handleRealtimeMessage(data);
    };

    wsRef.current.onclose = () => {
      setIsConnected(false);
      setConnectionStatus('disconnected');
      console.log('📱 Disconnected from Intel Unison++');
      
      // Attempt to reconnect after 5 seconds
      setTimeout(initializeConnection, 5000);
    };

    wsRef.current.onerror = (error) => {
      console.error('📱 WebSocket error:', error);
      setConnectionStatus('disconnected');
    };
  };

  const handleRealtimeMessage = (data: any) => {
    switch (data.type) {
      case 'sync-update':
        if (data.eventType === 'message-received' || data.eventType === 'message-sent') {
          addRealtimeMessage(data.data);
        }
        break;
      
      case 'initial-sync':
        setMessages(data.messages.map(formatMessage));
        break;
      
      case 'intelligence-processed':
        updateMessageIntelligence(data);
        break;
    }
  };

  const addRealtimeMessage = (messageData: any) => {
    const formattedMessage = formatMessage(messageData);
    
    setMessages(prev => {
      const exists = prev.some(m => m.id === formattedMessage.id);
      if (exists) return prev;
      
      return [...prev, formattedMessage].sort((a, b) => 
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );
    });

    // Update contact's unread count and last activity
    setContacts(prev => prev.map(contact => {
      if (contact.phoneNumber === formattedMessage.phoneNumber) {
        return {
          ...contact,
          lastActivity: formattedMessage.timestamp,
          unreadCount: formattedMessage.direction === 'inbound' ? contact.unreadCount + 1 : contact.unreadCount
        };
      }
      return contact;
    }));
  };

  const updateMessageIntelligence = (data: any) => {
    setMessages(prev => prev.map(msg => {
      if (msg.id === data.messageId) {
        return {
          ...msg,
          sentiment: data.sentiment,
          intent: data.intent
        };
      }
      return msg;
    }));

    // Update contact's lead score
    setContacts(prev => prev.map(contact => {
      if (contact.phoneNumber === data.phoneNumber) {
        return {
          ...contact,
          leadScore: data.leadScore?.score || contact.leadScore
        };
      }
      return contact;
    }));
  };

  const formatMessage = (data: any): Message => ({
    id: data.id || Date.now().toString(),
    phoneNumber: data.phoneNumber || data.threadId,
    contactName: data.contactName || data.phoneNumber,
    content: data.messageText || data.text || data.content,
    direction: data.isOutgoing ? 'outbound' : 'inbound',
    timestamp: new Date(data.timestamp),
    status: data.isDelivered ? 'delivered' : 'sent',
    sentiment: data.sentiment,
    intent: data.intent,
    leadScore: data.leadScore
  });

  const loadInitialData = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Load contacts
      const contactsResponse = await fetch('/api/contacts', {
        headers: {
          'Authorization': `Bearer ${user.id}`,
          'Content-Type': 'application/json'
        }
      });
      const contactsData = await contactsResponse.json();
      
      if (contactsData.success) {
        setContacts(contactsData.contacts.map(formatContact));
      }

      // Load messages for specific client/lead if provided
      if (clientId || leadId) {
        const specificContact = contactsData.contacts.find((c: any) => 
          c.id === clientId || c.id === leadId
        );
        if (specificContact) {
          await loadMessagesForContact(specificContact.phone);
        }
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatContact = (data: any): Contact => ({
    id: data.id,
    phoneNumber: data.phone || data.phoneNumber,
    name: data.name || data.displayName,
    type: data.type || 'lead',
    lastActivity: new Date(data.lastActivity || Date.now()),
    unreadCount: data.unreadCount || 0,
    leadScore: data.leadScore || 50,
    avatar: data.avatar || data.photoPath
  });

  const loadMessagesForContact = async (phoneNumber: string) => {
    if (!user) return;

    try {
      const response = await fetch(`/api/messages/${phoneNumber}`, {
        headers: {
          'Authorization': `Bearer ${user.id}`,
          'Content-Type': 'application/json'
        }
      });
      const data = await response.json();
      
      if (data.success) {
        setMessages(data.messages.map(formatMessage));
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  const loadAIInsights = async (phoneNumber: string) => {
    if (!user || !showAI) return;

    try {
      const response = await fetch(`/api/ai/insights/${phoneNumber}`, {
        headers: {
          'Authorization': `Bearer ${user.id}`,
          'Content-Type': 'application/json'
        }
      });
      const data = await response.json();
      
      if (data.success) {
        setAIInsights(data.insights);
      }
    } catch (error) {
      console.error('Error loading AI insights:', error);
    }
  };

  const sendMessage = async () => {
    if (!selectedContact || !newMessage.trim() || !user) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.id}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          to: selectedContact.phoneNumber,
          message: newMessage,
          clientId: selectedContact.type === 'client' ? selectedContact.id : undefined,
          leadId: selectedContact.type === 'lead' ? selectedContact.id : undefined
        })
      });

      const data = await response.json();
      
      if (data.success) {
        setNewMessage('');
        
        // Add optimistic message
        const optimisticMessage: Message = {
          id: Date.now().toString(),
          phoneNumber: selectedContact.phoneNumber,
          contactName: selectedContact.name,
          content: newMessage,
          direction: 'outbound',
          timestamp: new Date(),
          status: 'sent'
        };
        
        setMessages(prev => [...prev, optimisticMessage]);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const makeCall = async (phoneNumber: string) => {
    if (!user) return;

    try {
      const response = await fetch('/api/calls', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.id}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          phoneNumber,
          direction: 'outbound',
          type: 'voice'
        })
      });

      const data = await response.json();
      
      if (data.success) {
        // This would integrate with your phone system
        console.log('Call initiated:', phoneNumber);
      }
    } catch (error) {
      console.error('Error making call:', error);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const getSentimentColor = (sentiment?: Message['sentiment']) => {
    if (!sentiment) return 'bg-gray-500';
    
    switch (sentiment.label) {
      case 'positive': return 'bg-green-500';
      case 'negative': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getLeadScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-gray-500';
  };

  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contact.phoneNumber.includes(searchQuery)
  );

  return (
    <div className="flex h-full max-h-screen">
      {/* Contacts Sidebar */}
      <div className="w-80 border-r bg-gray-50 flex flex-col">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Messages</h2>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                connectionStatus === 'connected' ? 'bg-green-500' : 
                connectionStatus === 'syncing' ? 'bg-yellow-500' : 'bg-red-500'
              }`} />
              <span className="text-xs text-gray-500 capitalize">{connectionStatus}</span>
            </div>
          </div>
          
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search contacts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <ScrollArea className="flex-1">
          <div className="p-2">
            {filteredContacts.map((contact) => (
              <div
                key={contact.id}
                className={`p-3 rounded-lg cursor-pointer transition-colors ${
                  selectedContact?.id === contact.id 
                    ? 'bg-blue-100 border-blue-300' 
                    : 'hover:bg-gray-100'
                }`}
                onClick={() => {
                  setSelectedContact(contact);
                  loadMessagesForContact(contact.phoneNumber);
                }}
              >
                <div className="flex items-center space-x-3">
                  <Avatar>
                    <AvatarImage src={contact.avatar} />
                    <AvatarFallback>
                      {contact.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium truncate">{contact.name}</p>
                      {contact.unreadCount > 0 && (
                        <Badge variant="secondary" className="text-xs">
                          {contact.unreadCount}
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge variant="outline" className="text-xs">
                        {contact.type}
                      </Badge>
                      
                      {showAI && (
                        <div className="flex items-center space-x-1">
                          <div className={`w-2 h-2 rounded-full ${getLeadScoreColor(contact.leadScore)}`} />
                          <span className="text-xs text-gray-500">{contact.leadScore}</span>
                        </div>
                      )}
                    </div>
                    
                    <p className="text-xs text-gray-500 mt-1">
                      {formatDistanceToNow(contact.lastActivity, { addSuffix: true })}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {selectedContact ? (
          <>
            {/* Chat Header */}
            <div className="border-b p-4 bg-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar>
                    <AvatarImage src={selectedContact.avatar} />
                    <AvatarFallback>
                      {selectedContact.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div>
                    <h3 className="font-semibold">{selectedContact.name}</h3>
                    <p className="text-sm text-gray-500">{selectedContact.phoneNumber}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {showAI && aiInsights && (
                    <div className="flex items-center space-x-2 mr-4">
                      <Bot className="h-4 w-4 text-blue-500" />
                      <Badge variant="outline" className="text-xs">
                        Lead Score: {aiInsights.leadScore?.score || 'N/A'}
                      </Badge>
                    </div>
                  )}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => makeCall(selectedContact.phoneNumber)}
                  >
                    <Phone className="h-4 w-4 mr-2" />
                    Call
                  </Button>
                  
                  <Button variant="outline" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <ScrollArea className="flex-1 p-4" style={{ maxHeight: height }}>
              <div className="space-y-4">
                {messages
                  .filter(msg => msg.phoneNumber === selectedContact.phoneNumber)
                  .map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${
                        message.direction === 'outbound' ? 'justify-end' : 'justify-start'
                      }`}
                    >
                      <div
                        className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                          message.direction === 'outbound'
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-100 text-gray-900'
                        }`}
                      >
                        <p className="text-sm">{message.content}</p>
                        
                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs opacity-70">
                            {formatDistanceToNow(message.timestamp, { addSuffix: true })}
                          </span>
                          
                          <div className="flex items-center space-x-1">
                            {showAI && message.sentiment && (
                              <div className={`w-2 h-2 rounded-full ${getSentimentColor(message.sentiment)}`} />
                            )}
                            
                            {message.direction === 'outbound' && (
                              <span className="text-xs opacity-70">
                                {message.status === 'delivered' ? '✓✓' : '✓'}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* AI Insights Panel */}
            {showAI && aiInsights && (
              <div className="border-t p-4 bg-gray-50">
                <div className="flex items-center space-x-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <Bot className="h-4 w-4 text-blue-500" />
                    <span className="font-medium">AI Insights:</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span>Lead Score:</span>
                    <Badge variant="outline" className={getLeadScoreColor(aiInsights.leadScore?.score || 0)}>
                      {aiInsights.leadScore?.score || 'N/A'}
                    </Badge>
                  </div>
                  
                  {aiInsights.conversationInsights && (
                    <div className="flex items-center space-x-2">
                      <span>Sentiment:</span>
                      <Badge variant="outline">
                        {aiInsights.conversationInsights.sentimentTrend?.slice(-1)[0]?.label || 'N/A'}
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Message Input */}
            <div className="border-t p-4 bg-white">
              <div className="flex items-center space-x-2">
                <Input
                  placeholder="Type a message..."
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                  className="flex-1"
                />
                <Button
                  onClick={sendMessage}
                  disabled={!newMessage.trim() || isLoading}
                  className="px-4"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Select a conversation
              </h3>
              <p className="text-gray-500">
                Choose a contact to start messaging
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SMSTracker;