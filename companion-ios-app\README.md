# iPhone Companion Pro - iOS Companion App

This is the companion iOS app for iPhone Companion Pro on Windows. It enables real message integration, call management, and enhanced iPhone control features.

## Features

- **Real Message Integration**: Send and receive actual iPhone messages through the Windows app
- **Call Management**: Handle incoming/outgoing calls via Windows interface
- **Notification Mirroring**: Receive iPhone notifications on Windows
- **File Transfer**: Seamless file sharing between iPhone and Windows
- **Clipboard Sync**: Universal clipboard functionality
- **Photo Access**: View and manage iPhone photos from Windows

## Installation

1. Download the app from the App Store (coming soon)
2. Or build from source using Xcode
3. Connect to your Windows PC running iPhone Companion Pro
4. Grant necessary permissions for messages, calls, and notifications

## Setup Instructions

1. **Install the App**: Download and install on your iPhone
2. **Connect to PC**: Make sure iPhone and PC are on the same WiFi network
3. **Open iPhone Companion Pro** on Windows
4. **Scan QR Code**: Use the companion app to scan the QR code shown in the Windows app
5. **Grant Permissions**: Allow access to Messages, Contacts, and Notifications when prompted
6. **Enjoy**: Your iPhone is now fully integrated with your Windows PC!

## Technical Details

- Uses WebSocket connection for real-time communication
- End-to-end encryption for all data transfer
- Complies with iOS security and privacy requirements
- No data stored on external servers - direct PC to iPhone connection only

## Privacy & Security

- All communication is direct between your iPhone and PC
- No data is sent to external servers
- Messages and calls remain private and secure
- You can disconnect at any time

## Requirements

- iOS 14.0 or later
- iPhone Companion Pro for Windows
- Same WiFi network for initial setup
- Internet connection for some features

## Support

For support and troubleshooting, visit: [Support Page URL]

## Building from Source

### Prerequisites
- Xcode 12.0 or later
- iOS 14.0 SDK
- Apple Developer Account (for device testing)

### Build Steps
1. Clone this repository
2. Open `iPhoneCompanionPro.xcodeproj` in Xcode
3. Configure your development team and bundle identifier
4. Build and run on device or simulator

### Key Components
- `MessageBridge.swift` - Handles message integration
- `CallManager.swift` - Manages call functionality  
- `NotificationService.swift` - Notification mirroring
- `WebSocketClient.swift` - PC communication
- `SecurityManager.swift` - Encryption and security

## License

MIT License - See LICENSE file for details
