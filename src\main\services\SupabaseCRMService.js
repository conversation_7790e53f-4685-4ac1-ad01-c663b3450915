const { createClient } = require('@supabase/supabase-js');
const express = require('express');
const cors = require('cors');
const { EventEmitter } = require('events');

class SupabaseCRMService extends EventEmitter {
  constructor(phoneService, messageService) {
    super();
    this.app = express();
    this.app.use(cors());
    this.app.use(express.json());
    
    this.phoneService = phoneService;
    this.messageService = messageService;
    this.server = null;
    this.supabase = null;
    
    // Initialize Supabase client
    this.initializeSupabase();
    this.setupEndpoints();
    this.setupRealtimeSubscriptions();
  }

  initializeSupabase() {
    // These would normally come from environment variables
    const supabaseUrl = process.env.SUPABASE_URL || 'YOUR_SUPABASE_URL';
    const supabaseKey = process.env.SUPABASE_ANON_KEY || 'YOUR_SUPABASE_ANON_KEY';
    
    if (supabaseUrl !== 'YOUR_SUPABASE_URL' && supabaseKey !== 'YOUR_SUPABASE_ANON_KEY') {
      this.supabase = createClient(supabaseUrl, supabaseKey);
      console.log('✅ Supabase client initialized');
    } else {
      console.log('⚠️ Supabase credentials not configured. Using local storage only.');
    }
  }

  setupEndpoints() {
    // Health check endpoint
    this.app.get('/api/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: Date.now(),
        supabaseConnected: !!this.supabase,
        integrations: this.getIntegrationStatus()
      });
    });

    // Intel Unison-style message sync endpoints
    this.app.get('/api/messages/:phoneNumber', async (req, res) => {
      try {
        const { phoneNumber } = req.params;
        const { userId } = this.extractAuth(req);
        
        if (!userId) {
          return res.status(401).json({ error: 'Unauthorized' });
        }

        // Get messages from both local and Supabase
        const localMessages = await this.getLocalMessages(phoneNumber);
        const supabaseMessages = await this.getSupabaseMessages(userId, phoneNumber);
        
        // Merge and deduplicate messages
        const allMessages = this.mergeMessages(localMessages, supabaseMessages);
        
        res.json({
          success: true,
          phoneNumber,
          messages: allMessages,
          count: allMessages.length,
          sources: {
            local: localMessages.length,
            supabase: supabaseMessages.length
          }
        });
      } catch (error) {
        console.error('Error getting messages:', error);
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Send message endpoint (Intel Unison-style)
    this.app.post('/api/send', async (req, res) => {
      try {
        const { to, message, clientId, leadId } = req.body;
        const { userId } = this.extractAuth(req);
        
        if (!userId) {
          return res.status(401).json({ error: 'Unauthorized' });
        }

        if (!to || !message) {
          return res.status(400).json({ 
            error: 'Phone number (to) and message are required' 
          });
        }

        // Send via iPhone (using existing message service)
        const sentMessage = await this.messageService.sendMessage(to, message);
        
        // Save to Supabase CRM
        const communication = await this.saveCommunication(userId, {
          phone_number: to,
          direction: 'outbound',
          type: 'sms',
          content: message,
          client_id: clientId || null,
          lead_id: leadId || null,
          status: 'completed',
          external_id: sentMessage.id,
          metadata: {
            source: 'iphone_companion',
            message_id: sentMessage.id,
            timestamp: sentMessage.timestamp
          }
        });

        res.json({
          success: true,
          messageId: sentMessage.id,
          communicationId: communication?.id,
          phoneNumber: to,
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Error sending message:', error);
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Get contacts endpoint (from CRM)
    this.app.get('/api/contacts', async (req, res) => {
      try {
        const { userId } = this.extractAuth(req);
        
        if (!userId) {
          return res.status(401).json({ error: 'Unauthorized' });
        }

        const contacts = await this.getCRMContacts(userId);
        
        res.json({
          success: true,
          contacts,
          count: contacts.length
        });
      } catch (error) {
        console.error('Error getting contacts:', error);
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Get call history endpoint
    this.app.get('/api/calls', async (req, res) => {
      try {
        const { userId } = this.extractAuth(req);
        const { limit = 100 } = req.query;
        
        if (!userId) {
          return res.status(401).json({ error: 'Unauthorized' });
        }

        const calls = await this.getCallHistory(userId, parseInt(limit));
        
        res.json({
          success: true,
          calls,
          count: calls.length
        });
      } catch (error) {
        console.error('Error getting calls:', error);
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Log call endpoint
    this.app.post('/api/calls', async (req, res) => {
      try {
        const { phoneNumber, duration, direction, type = 'voice' } = req.body;
        const { userId } = this.extractAuth(req);
        
        if (!userId) {
          return res.status(401).json({ error: 'Unauthorized' });
        }

        // Save call to Supabase CRM
        const communication = await this.saveCommunication(userId, {
          phone_number: phoneNumber,
          direction,
          type: 'call',
          duration: duration || 0,
          status: 'completed',
          metadata: {
            call_type: type,
            source: 'iphone_companion'
          }
        });

        res.json({
          success: true,
          communicationId: communication?.id,
          phoneNumber,
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Error logging call:', error);
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Webhook endpoint for Phone Link events
    this.app.post('/api/webhooks/phone-link', async (req, res) => {
      try {
        const { userId } = this.extractAuth(req);
        const event = req.body;
        
        if (!userId) {
          return res.status(401).json({ error: 'Unauthorized' });
        }

        await this.processPhoneLinkEvent(userId, event);
        
        res.json({ 
          received: true, 
          eventType: event.type,
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Error processing webhook:', error);
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Get conversation threads
    this.app.get('/api/conversations', async (req, res) => {
      try {
        const { userId } = this.extractAuth(req);
        
        if (!userId) {
          return res.status(401).json({ error: 'Unauthorized' });
        }

        const conversations = await this.getConversationThreads(userId);
        
        res.json({
          success: true,
          conversations,
          count: conversations.length
        });
      } catch (error) {
        console.error('Error getting conversations:', error);
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Sync status endpoint
    this.app.get('/api/sync/status', async (req, res) => {
      try {
        const { userId } = this.extractAuth(req);
        
        if (!userId) {
          return res.status(401).json({ error: 'Unauthorized' });
        }

        const status = await this.getSyncStatus(userId);
        
        res.json({
          success: true,
          syncStatus: status,
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Error getting sync status:', error);
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Trigger manual sync
    this.app.post('/api/sync/trigger', async (req, res) => {
      try {
        const { userId } = this.extractAuth(req);
        
        if (!userId) {
          return res.status(401).json({ error: 'Unauthorized' });
        }

        const result = await this.triggerManualSync(userId);
        
        res.json({
          success: true,
          syncResult: result,
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Error triggering sync:', error);
        res.status(500).json({ success: false, error: error.message });
      }
    });
  }

  setupRealtimeSubscriptions() {
    if (!this.supabase) return;

    // Subscribe to communications table changes
    this.supabase
      .channel('communications')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'communications'
      }, (payload) => {
        console.log('📬 New communication received:', payload.new);
        this.emit('communication-received', payload.new);
      })
      .subscribe();

    console.log('✅ Realtime subscriptions established');
  }

  async getLocalMessages(phoneNumber) {
    try {
      // Get messages from local MessageService
      const conversation = this.messageService.getConversation(phoneNumber);
      return conversation ? conversation.messages : [];
    } catch (error) {
      console.error('Error getting local messages:', error);
      return [];
    }
  }

  async getSupabaseMessages(userId, phoneNumber) {
    if (!this.supabase) return [];

    try {
      const { data, error } = await this.supabase
        .from('communications')
        .select('*')
        .eq('clerk_user_id', userId)
        .eq('phone_number', phoneNumber)
        .eq('type', 'sms')
        .order('created_at', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting Supabase messages:', error);
      return [];
    }
  }

  mergeMessages(localMessages, supabaseMessages) {
    const allMessages = [...localMessages];
    
    // Add Supabase messages that don't exist locally
    supabaseMessages.forEach(supabaseMsg => {
      const exists = allMessages.some(localMsg => 
        localMsg.text === supabaseMsg.content &&
        Math.abs(new Date(localMsg.timestamp) - new Date(supabaseMsg.created_at)) < 5000
      );
      
      if (!exists) {
        allMessages.push({
          id: supabaseMsg.id,
          text: supabaseMsg.content,
          timestamp: new Date(supabaseMsg.created_at),
          isIncoming: supabaseMsg.direction === 'inbound',
          source: 'supabase'
        });
      }
    });
    
    // Sort by timestamp
    return allMessages.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
  }

  async saveCommunication(userId, communication) {
    if (!this.supabase) {
      console.log('⚠️ Supabase not available, saving locally only');
      return null;
    }

    try {
      const { data, error } = await this.supabase
        .from('communications')
        .insert({
          clerk_user_id: userId,
          ...communication
        })
        .select()
        .single();

      if (error) throw error;
      
      console.log('✅ Communication saved to Supabase:', data.id);
      return data;
    } catch (error) {
      console.error('Error saving communication:', error);
      return null;
    }
  }

  async getCRMContacts(userId) {
    if (!this.supabase) return [];

    try {
      // Get clients
      const { data: clients, error: clientsError } = await this.supabase
        .from('active_clients')
        .select('id, name, phone, email')
        .eq('clerk_user_id', userId);

      if (clientsError) throw clientsError;

      // Get leads
      const { data: leads, error: leadsError } = await this.supabase
        .from('leads')
        .select('id, name, phone, email')
        .eq('clerk_user_id', userId);

      if (leadsError) throw leadsError;

      // Combine and format
      const contacts = [
        ...(clients || []).map(c => ({ ...c, type: 'client' })),
        ...(leads || []).map(l => ({ ...l, type: 'lead' }))
      ];

      return contacts;
    } catch (error) {
      console.error('Error getting CRM contacts:', error);
      return [];
    }
  }

  async getCallHistory(userId, limit) {
    if (!this.supabase) return [];

    try {
      const { data, error } = await this.supabase
        .from('communications')
        .select('*')
        .eq('clerk_user_id', userId)
        .eq('type', 'call')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting call history:', error);
      return [];
    }
  }

  async getConversationThreads(userId) {
    if (!this.supabase) return [];

    try {
      const { data, error } = await this.supabase
        .from('conversation_threads')
        .select('*')
        .eq('clerk_user_id', userId)
        .order('last_activity', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting conversation threads:', error);
      return [];
    }
  }

  async processPhoneLinkEvent(userId, event) {
    console.log('📱 Processing Phone Link event:', event.type);

    switch (event.type) {
      case 'message_received':
        await this.saveCommunication(userId, {
          phone_number: event.from,
          direction: 'inbound',
          type: 'sms',
          content: event.content,
          external_id: event.messageId,
          metadata: event.metadata || {}
        });
        break;

      case 'message_sent':
        await this.saveCommunication(userId, {
          phone_number: event.to,
          direction: 'outbound',
          type: 'sms',
          content: event.content,
          external_id: event.messageId,
          metadata: event.metadata || {}
        });
        break;

      case 'call_received':
      case 'call_made':
        await this.saveCommunication(userId, {
          phone_number: event.phoneNumber,
          direction: event.type === 'call_received' ? 'inbound' : 'outbound',
          type: 'call',
          duration: event.duration || 0,
          metadata: event.metadata || {}
        });
        break;
    }
  }

  async getSyncStatus(userId) {
    // Get status from both local and Supabase
    const localStatus = this.messageService.getIntegrationStatus();
    
    let supabaseStatus = { connected: false, lastSync: null };
    if (this.supabase) {
      try {
        const { data } = await this.supabase
          .from('communications')
          .select('created_at')
          .eq('clerk_user_id', userId)
          .order('created_at', { ascending: false })
          .limit(1);
        
        supabaseStatus = {
          connected: true,
          lastSync: data?.[0]?.created_at || null
        };
      } catch (error) {
        console.error('Error getting Supabase status:', error);
      }
    }

    return {
      local: localStatus,
      supabase: supabaseStatus,
      timestamp: new Date().toISOString()
    };
  }

  async triggerManualSync(userId) {
    console.log('🔄 Triggering manual sync for user:', userId);
    
    // Get recent local messages and sync to Supabase
    const conversations = this.messageService.getConversations();
    let syncedCount = 0;

    for (const conversation of conversations) {
      for (const message of conversation.messages.slice(-10)) { // Last 10 messages per conversation
        if (message.isReal && message.source !== 'supabase') {
          await this.saveCommunication(userId, {
            phone_number: conversation.phoneNumber,
            direction: message.isIncoming ? 'inbound' : 'outbound',
            type: 'sms',
            content: message.text,
            external_id: message.id,
            metadata: {
              source: message.source || 'iphone_companion',
              original_timestamp: message.timestamp
            }
          });
          syncedCount++;
        }
      }
    }

    return {
      messagesSynced: syncedCount,
      timestamp: new Date().toISOString()
    };
  }

  extractAuth(req) {
    // In a real implementation, this would extract the user ID from JWT token
    // For now, we'll use a header or default user
    const userId = req.headers['x-user-id'] || req.headers['authorization']?.replace('Bearer ', '') || 'default-user';
    return { userId };
  }

  getIntegrationStatus() {
    return {
      messageService: !!this.messageService,
      phoneService: !!this.phoneService,
      supabase: !!this.supabase,
      apiRunning: !!this.server
    };
  }

  async start(port = 7777) {
    return new Promise((resolve, reject) => {
      const tryPort = (currentPort) => {
        this.server = this.app.listen(currentPort, (err) => {
          if (err) {
            if (err.code === 'EADDRINUSE' && currentPort < port + 10) {
              console.log(`⚠️ Port ${currentPort} in use, trying ${currentPort + 1}...`);
              tryPort(currentPort + 1);
            } else {
              reject(err);
            }
          } else {
            console.log('🚀 Intel Unison++ CRM API running on http://localhost:' + currentPort);
            console.log('📊 Enhanced CRM Endpoints:');
            console.log('   GET  /api/messages/:phoneNumber - Get conversation history');
            console.log('   POST /api/send - Send message via iPhone');
            console.log('   GET  /api/contacts - Get CRM contacts (clients + leads)');
            console.log('   GET  /api/calls - Get call history');
            console.log('   POST /api/calls - Log call activity');
            console.log('   GET  /api/conversations - Get conversation threads');
            console.log('   POST /api/webhooks/phone-link - Receive Phone Link events');
            console.log('   GET  /api/sync/status - Get sync status');
            console.log('   POST /api/sync/trigger - Trigger manual sync');
            console.log('   GET  /api/health - Health check');
            resolve();
          }
        });

        this.server.on('error', (error) => {
          if (error.code === 'EADDRINUSE' && currentPort < port + 10) {
            console.log(`⚠️ Port ${currentPort} in use, trying ${currentPort + 1}...`);
            tryPort(currentPort + 1);
          } else {
            reject(error);
          }
        });
      };

      tryPort(port);
    });
  }

  stop() {
    if (this.server) {
      this.server.close();
      console.log('🛑 Intel Unison++ CRM API server stopped');
    }
  }
}

module.exports = SupabaseCRMService;