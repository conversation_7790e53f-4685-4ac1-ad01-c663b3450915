<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Notifications - iPhone Companion Pro</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/notifications.css">
</head>
<body>
    <div class="titlebar">
        <div class="titlebar-title">Notifications</div>
        <div class="titlebar-controls">
            <button class="titlebar-button" onclick="window.close()">✕</button>
        </div>
    </div>

    <div class="notifications-container">
        <!-- Header with controls -->
        <div class="notifications-header">
            <div class="header-left">
                <h1>🔔 iPhone Notifications</h1>
                <p class="subtitle">Manage and view your iPhone notifications on Windows</p>
            </div>
            <div class="header-controls">
                <button class="control-btn" onclick="refreshNotifications()" title="Refresh">
                    <span>🔄</span> Refresh
                </button>
                <button class="control-btn" onclick="markAllAsRead()" title="Mark all as read">
                    <span>✓</span> Mark All Read
                </button>
                <button class="control-btn" onclick="clearAllNotifications()" title="Clear all">
                    <span>🗑️</span> Clear All
                </button>
                <button class="control-btn" onclick="openNotificationSettings()" title="Settings">
                    <span>⚙️</span> Settings
                </button>
            </div>
        </div>

        <!-- Notification filters -->
        <div class="notification-filters">
            <button class="filter-btn active" data-filter="all" onclick="filterNotifications('all')">
                All <span class="count" id="count-all">0</span>
            </button>
            <button class="filter-btn" data-filter="unread" onclick="filterNotifications('unread')">
                Unread <span class="count" id="count-unread">0</span>
            </button>
            <button class="filter-btn" data-filter="messages" onclick="filterNotifications('messages')">
                Messages <span class="count" id="count-messages">0</span>
            </button>
            <button class="filter-btn" data-filter="calls" onclick="filterNotifications('calls')">
                Calls <span class="count" id="count-calls">0</span>
            </button>
            <button class="filter-btn" data-filter="apps" onclick="filterNotifications('apps')">
                Apps <span class="count" id="count-apps">0</span>
            </button>
        </div>

        <!-- Search bar -->
        <div class="search-container">
            <input type="text" id="notification-search" placeholder="Search notifications..." onkeyup="searchNotifications()">
            <button class="search-btn" onclick="searchNotifications()">🔍</button>
        </div>

        <!-- Notifications list -->
        <div class="notifications-content">
            <div class="notifications-list" id="notifications-list">
                <!-- Notifications will be loaded here -->
                <div class="loading-notifications">
                    <div class="spinner"></div>
                    <p>Loading notifications...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Details Modal -->
    <div class="modal-overlay" id="notification-details-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Notification Details</h2>
                <button class="modal-close" onclick="closeNotificationDetailsModal()">✕</button>
            </div>
            <div class="modal-body">
                <div class="notification-details-content" id="notification-details-content">
                    <!-- Notification details will be loaded here -->
                </div>
                <div class="notification-actions">
                    <button class="action-btn primary" onclick="replyToNotification()">Reply</button>
                    <button class="action-btn secondary" onclick="markAsRead()">Mark as Read</button>
                    <button class="action-btn secondary" onclick="deleteNotification()">Delete</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Settings Modal -->
    <div class="modal-overlay" id="notification-settings-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Notification Settings</h2>
                <button class="modal-close" onclick="closeNotificationSettingsModal()">✕</button>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h3>General Settings</h3>
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="enable-notifications" checked>
                            <span class="checkmark"></span>
                            Enable iPhone notifications on Windows
                        </label>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="show-previews" checked>
                            <span class="checkmark"></span>
                            Show notification previews
                        </label>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="play-sounds" checked>
                            <span class="checkmark"></span>
                            Play notification sounds
                        </label>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="auto-dismiss">
                            <span class="checkmark"></span>
                            Auto-dismiss after 10 seconds
                        </label>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>App Notifications</h3>
                    <div class="app-notifications-list" id="app-notifications-list">
                        <!-- App notification settings will be loaded here -->
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Do Not Disturb</h3>
                    <div class="setting-item">
                        <label class="setting-label">
                            <input type="checkbox" id="dnd-enabled">
                            <span class="checkmark"></span>
                            Enable Do Not Disturb
                        </label>
                    </div>
                    <div class="setting-item">
                        <label for="dnd-start">Start time:</label>
                        <input type="time" id="dnd-start" value="22:00">
                    </div>
                    <div class="setting-item">
                        <label for="dnd-end">End time:</label>
                        <input type="time" id="dnd-end" value="07:00">
                    </div>
                </div>

                <div class="settings-actions">
                    <button class="action-btn primary" onclick="saveNotificationSettings()">Save Settings</button>
                    <button class="action-btn secondary" onclick="resetNotificationSettings()">Reset to Default</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Reply Modal -->
    <div class="modal-overlay" id="quick-reply-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Quick Reply</h2>
                <button class="modal-close" onclick="closeQuickReplyModal()">✕</button>
            </div>
            <div class="modal-body">
                <div class="reply-context" id="reply-context">
                    <!-- Original notification context will be shown here -->
                </div>
                <div class="reply-input-container">
                    <textarea id="reply-text" placeholder="Type your reply..." rows="3"></textarea>
                    <div class="reply-actions">
                        <button class="action-btn secondary" onclick="closeQuickReplyModal()">Cancel</button>
                        <button class="action-btn primary" onclick="sendQuickReply()">Send</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="scripts/notifications.js"></script>
</body>
</html>
