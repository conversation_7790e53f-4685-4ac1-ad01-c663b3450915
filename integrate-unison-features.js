const fs = require('fs');
const path = require('path');

console.log('🔧 Integrating Intel Unison features into iPhone Companion Pro\n');

// Check if required dependencies are installed
const requiredDeps = ['chokidar', 'express', 'cors'];
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

console.log('📦 Checking dependencies...');
const missingDeps = [];

requiredDeps.forEach(dep => {
  if (!packageJson.dependencies[dep] && !packageJson.devDependencies[dep]) {
    missingDeps.push(dep);
  }
});

if (missingDeps.length > 0) {
  console.log('❌ Missing dependencies:', missingDeps.join(', '));
  console.log('📥 Please install missing dependencies manually:');
  console.log(`   npm install ${missingDeps.join(' ')}`);
  console.log('⚠️  Continuing with setup...');
} else {
  console.log('✅ All required dependencies are installed');
}

// Verify files were created
const filesToCheck = [
  'src/main/services/CRMBridge.js',
  'src/main/services/PhoneLinkBridge.js'
];

console.log('\n📁 Verifying integration files...');
let allFilesExist = true;

filesToCheck.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - Ready`);
  } else {
    console.log(`❌ ${file} - Missing`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ Some integration files are missing. Please run the integration setup again.');
  process.exit(1);
}

// Create data directory for Unison-style storage
const dataDir = path.join(process.env.APPDATA || process.env.HOME, 'iPhone-Companion-Pro');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
  console.log(`✅ Created data directory: ${dataDir}`);
} else {
  console.log(`✅ Data directory exists: ${dataDir}`);
}

// Create a test CRM client script
const testCRMClient = `
const axios = require('axios');

class TestCRMClient {
  constructor(baseURL = 'http://localhost:7777') {
    this.baseURL = baseURL;
  }

  async getContacts() {
    try {
      const response = await axios.get(\`\${this.baseURL}/api/crm/contacts\`);
      return response.data;
    } catch (error) {
      console.error('Error getting contacts:', error.message);
      return null;
    }
  }

  async getMessages(phone) {
    try {
      const response = await axios.get(\`\${this.baseURL}/api/crm/messages/\${phone}\`);
      return response.data;
    } catch (error) {
      console.error('Error getting messages:', error.message);
      return null;
    }
  }

  async sendMessage(phone, text, campaignId = 'test') {
    try {
      const response = await axios.post(\`\${this.baseURL}/api/crm/send\`, {
        phone,
        text,
        campaignId
      });
      return response.data;
    } catch (error) {
      console.error('Error sending message:', error.message);
      return null;
    }
  }

  async getStatus() {
    try {
      const response = await axios.get(\`\${this.baseURL}/api/crm/status\`);
      return response.data;
    } catch (error) {
      console.error('Error getting status:', error.message);
      return null;
    }
  }
}

// Example usage
async function testCRM() {
  const crm = new TestCRMClient();
  
  console.log('🧪 Testing CRM API...');
  
  // Test status
  const status = await crm.getStatus();
  console.log('Status:', status);
  
  // Test contacts
  const contacts = await crm.getContacts();
  console.log('Contacts:', contacts?.count || 0);
  
  // Test sending a message (uncomment to test)
  // const result = await crm.sendMessage('+1234567890', 'Test message from CRM', 'test-campaign');
  // console.log('Send result:', result);
}

if (require.main === module) {
  testCRM();
}

module.exports = TestCRMClient;
`;

fs.writeFileSync('test-crm-client.js', testCRMClient);
console.log('✅ Created test CRM client: test-crm-client.js');

console.log('\n🎉 Integration complete!');
console.log('\n🚀 Your app now has:');
console.log('   ✅ Unison-style local database');
console.log('   ✅ Persistent message storage');
console.log('   ✅ CRM API endpoints');
console.log('   ✅ Real-time Phone Link monitoring');
console.log('   ✅ Messages that never disappear');

console.log('\n📱 Next steps:');
console.log('   1. Restart your app: npm start');
console.log('   2. Your CRM can now call http://localhost:7777/api/crm/messages/:phone');
console.log('   3. Test the API: node test-crm-client.js');
console.log('   4. Messages persist even after restart!');

console.log('\n📊 CRM API Endpoints:');
console.log('   GET  /api/crm/contacts - Get all contacts');
console.log('   GET  /api/crm/messages/:phone - Get messages for specific contact');
console.log('   POST /api/crm/send - Send message via iPhone');
console.log('   GET  /api/crm/conversations - Get conversation threads');
console.log('   GET  /api/crm/status - Integration status');
console.log('   GET  /api/crm/health - Health check');

console.log('\n🔥 Ready to rock! Your iPhone Companion Pro now works like Intel Unison!');
