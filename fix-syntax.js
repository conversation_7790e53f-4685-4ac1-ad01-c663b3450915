#!/usr/bin/env node

/**
 * Fix JavaScript syntax errors in app-clean.js
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'src/renderer/scripts/app-clean.js');

try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    console.log('🔧 Fixing JavaScript syntax errors...');
    
    // Fix function declarations that are missing the 'function' keyword
    content = content.replace(/^\s{4}([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/gm, 'function $1(');
    
    // Fix 'this' references to use 'app' instead
    content = content.replace(/this\.currentConversation/g, 'app.currentConversation');
    content = content.replace(/this\.conversations/g, 'app.conversations');
    content = content.replace(/this\./g, 'app.');
    
    // Fix indentation issues
    content = content.replace(/^\s{8}/gm, '    ');
    
    // Ensure proper function declarations for standalone functions
    const functionsToFix = [
        'updateConversationsList',
        'searchMessages', 
        'newMessage',
        'searchCalls',
        'showDialer',
        'callContact',
        'videoCall',
        'newMessage'
    ];
    
    functionsToFix.forEach(funcName => {
        const regex = new RegExp(`^\\s*${funcName}\\s*\\(`, 'gm');
        content = content.replace(regex, `function ${funcName}(`);
    });
    
    fs.writeFileSync(filePath, content);
    
    console.log('✅ Syntax errors fixed successfully');
    
    // Test the syntax
    require('child_process').exec(`node -c "${filePath}"`, (error, stdout, stderr) => {
        if (error) {
            console.error('❌ Syntax errors still exist:', stderr);
        } else {
            console.log('✅ JavaScript syntax is now valid');
        }
    });
    
} catch (error) {
    console.error('❌ Failed to fix syntax:', error);
}