/* VM Bridge Specific Styles */
.vm-bridge-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Header Section */
.vm-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
    border-radius: 12px;
    border: 1px solid #333;
}

.vm-icon {
    font-size: 48px;
    opacity: 0.8;
}

.vm-title h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #fff;
}

.vm-title p {
    margin: 5px 0 0 0;
    color: #999;
    font-size: 14px;
}

.vm-status {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #1a1a1a;
    border-radius: 20px;
    border: 1px solid #333;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #666;
    animation: pulse 2s infinite;
}

.status-indicator.healthy { background: #4CAF50; }
.status-indicator.warning { background: #FF9800; }
.status-indicator.critical { background: #F44336; }
.status-indicator.disabled { background: #666; }

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 30px;
}

.action-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn.primary {
    background: #007AFF;
    color: white;
}

.action-btn.primary:hover {
    background: #0056CC;
}

.action-btn.secondary {
    background: #2a2a2a;
    color: #fff;
    border: 1px solid #444;
}

.action-btn.secondary:hover {
    background: #3a3a3a;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Configuration Tabs */
.config-tabs {
    background: #1a1a1a;
    border-radius: 12px;
    border: 1px solid #333;
    overflow: hidden;
}

.tab-buttons {
    display: flex;
    background: #141414;
    border-bottom: 1px solid #333;
}

.tab-btn {
    flex: 1;
    padding: 16px 20px;
    border: none;
    background: transparent;
    color: #999;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn.active {
    color: #007AFF;
    background: #1a1a1a;
    border-bottom: 2px solid #007AFF;
}

.tab-btn:hover:not(.active) {
    color: #fff;
    background: #2a2a2a;
}

.tab-content {
    display: none;
    padding: 30px;
}

.tab-content.active {
    display: block;
}

/* Overview Tab */
.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.overview-card {
    background: #2a2a2a;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #444;
    text-align: center;
}

.overview-card h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #999;
    font-weight: 500;
}

.metric-value {
    font-size: 24px;
    font-weight: 600;
    color: #fff;
    margin-bottom: 5px;
}

.metric-label {
    font-size: 12px;
    color: #666;
}

.feature-status {
    background: #2a2a2a;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #444;
}

.feature-status h3 {
    margin: 0 0 15px 0;
    color: #fff;
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.feature-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #333;
}

.feature-item:last-child {
    border-bottom: none;
}

.feature-name {
    color: #fff;
    font-size: 14px;
}

.feature-status-indicator {
    font-size: 16px;
}

/* Configuration Tab */
.config-section {
    margin-bottom: 30px;
}

.config-section h3 {
    margin: 0 0 15px 0;
    color: #fff;
    font-size: 16px;
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.config-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.config-item label {
    color: #999;
    font-size: 14px;
    font-weight: 500;
}

.config-item select,
.config-item input {
    padding: 10px 12px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 6px;
    color: #fff;
    font-size: 14px;
}

.config-item select:focus,
.config-item input:focus {
    outline: none;
    border-color: #007AFF;
}

.config-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 3px;
    position: relative;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #007AFF;
    border-color: #007AFF;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Monitoring Tab */
.monitoring-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.monitor-card {
    background: #2a2a2a;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #444;
}

.monitor-card h3 {
    margin: 0 0 15px 0;
    color: #fff;
    font-size: 16px;
}

.metrics-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.metric-row {
    display: flex;
    align-items: center;
    gap: 10px;
}

.metric-row span:first-child {
    min-width: 100px;
    color: #999;
    font-size: 14px;
}

.metric-bar {
    flex: 1;
    height: 6px;
    background: #444;
    border-radius: 3px;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #FF9800, #F44336);
    transition: width 0.3s ease;
}

.health-indicators {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.health-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #333;
}

.health-item:last-child {
    border-bottom: none;
}

.health-label {
    color: #999;
    font-size: 14px;
}

.health-status,
.health-value {
    color: #fff;
    font-size: 14px;
    font-weight: 500;
}

.monitoring-actions {
    display: flex;
    gap: 12px;
}

/* Logs Tab */
.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.logs-header h3 {
    margin: 0;
    color: #fff;
}

.log-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.log-controls select {
    padding: 6px 10px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 4px;
    color: #fff;
    font-size: 12px;
}

.logs-container {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 6px;
    height: 400px;
    overflow-y: auto;
    padding: 10px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

.log-entry {
    display: flex;
    gap: 10px;
    padding: 4px 0;
    border-bottom: 1px solid #2a2a2a;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-time {
    color: #666;
    min-width: 140px;
}

.log-level {
    min-width: 60px;
    font-weight: bold;
}

.log-level.error { color: #F44336; }
.log-level.warning { color: #FF9800; }
.log-level.info { color: #4CAF50; }

.log-message {
    color: #fff;
    flex: 1;
}

/* Scrollbar Styling */
.logs-container::-webkit-scrollbar {
    width: 8px;
}

.logs-container::-webkit-scrollbar-track {
    background: #1a1a1a;
}

.logs-container::-webkit-scrollbar-thumb {
    background: #444;
    border-radius: 4px;
}

.logs-container::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Responsive Design */
@media (max-width: 768px) {
    .vm-header {
        flex-direction: column;
        text-align: center;
    }

    .vm-status {
        margin-left: 0;
    }

    .quick-actions {
        flex-direction: column;
    }

    .monitoring-grid {
        grid-template-columns: 1fr;
    }

    .config-grid {
        grid-template-columns: 1fr;
    }
}
