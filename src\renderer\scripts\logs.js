/**
 * Intel Unison++ Logs and Developer Console
 * Real-time debugging and system monitoring
 */

class LogManager {
    constructor() {
        this.logs = [];
        this.filteredLogs = [];
        this.currentFilter = 'all';
        this.isPaused = false;
        this.autoScroll = true;
        this.maxLogs = 1000; // Maximum logs to keep in memory
        
        this.logDisplay = document.getElementById('log-display');
        this.searchInput = document.getElementById('log-search-input');
        this.searchStats = document.getElementById('search-stats');
        
        this.initializeWebSocket();
        this.setupEventListeners();
        this.startStatusUpdates();
    }

    initializeWebSocket() {
        console.log('🔗 Connecting to Intel Unison WebSocket...');
        
        try {
            this.ws = new WebSocket('ws://localhost:26819');
            
            this.ws.onopen = () => {
                this.addLog('info', 'WebSocket', '✅ Connected to Intel Unison Core');
                this.requestSystemStatus();
            };
            
            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                } catch (error) {
                    this.addLog('error', 'WebSocket', `❌ Parse error: ${error.message}`);
                }
            };
            
            this.ws.onclose = () => {
                this.addLog('warn', 'WebSocket', '⚠️ Connection lost - attempting reconnect...');
                setTimeout(() => this.initializeWebSocket(), 5000);
            };
            
            this.ws.onerror = (error) => {
                this.addLog('error', 'WebSocket', `❌ Connection error: ${error.message || 'Unknown error'}`);
            };
            
        } catch (error) {
            this.addLog('error', 'System', `❌ Failed to connect WebSocket: ${error.message}`);
        }
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'log':
                this.addLog(data.level, data.source, data.message);
                break;
            case 'status':
                this.updateSystemStatus(data.status);
                break;
            case 'device-discovered':
                this.addLog('info', 'Discovery', `📱 Device discovered: ${data.device.name}`);
                break;
            case 'device-connected':
                this.addLog('success', 'Connection', `✅ Device connected: ${data.device.name}`);
                break;
            case 'message-sent':
                this.addLog('success', 'Messages', `📤 Message sent to ${data.recipient}`);
                break;
            case 'message-received':
                this.addLog('info', 'Messages', `📥 Message received from ${data.sender}`);
                break;
            case 'error':
                this.addLog('error', data.source || 'System', `❌ ${data.message}`);
                break;
            default:
                this.addLog('debug', 'WebSocket', `📦 Received: ${data.type}`);
        }
    }

    setupEventListeners() {
        // Search functionality
        this.searchInput.addEventListener('input', () => this.searchLogs());
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey) {
                switch (event.key) {
                    case 'l':
                        event.preventDefault();
                        this.clearLogs();
                        break;
                    case 'f':
                        event.preventDefault();
                        this.searchInput.focus();
                        break;
                    case 's':
                        event.preventDefault();
                        this.exportLogs();
                        break;
                }
            }
        });
    }

    addLog(level, source, message) {
        if (this.isPaused) return;
        
        const timestamp = Date.now();
        const timeString = new Date(timestamp).toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            fractionalSecondDigits: 3
        });
        
        const logEntry = {
            timestamp,
            level,
            source,
            message,
            timeString
        };
        
        // Add to logs array
        this.logs.push(logEntry);
        
        // Limit log history
        if (this.logs.length > this.maxLogs) {
            this.logs.shift();
        }
        
        // Update display if not filtered out
        if (this.shouldShowLog(logEntry)) {
            this.renderLogEntry(logEntry);
        }
        
        // Update search stats
        this.updateSearchStats();
        
        // Auto scroll to bottom
        if (this.autoScroll) {
            this.scrollToBottom();
        }
        
        // Store in localStorage for persistence
        this.saveLogsToStorage();
    }

    shouldShowLog(logEntry) {
        // Filter by level
        if (this.currentFilter !== 'all' && logEntry.level !== this.currentFilter) {
            return false;
        }
        
        // Filter by search query
        const query = this.searchInput.value.toLowerCase();
        if (query) {
            const searchText = `${logEntry.source} ${logEntry.message}`.toLowerCase();
            return searchText.includes(query);
        }
        
        return true;
    }

    renderLogEntry(logEntry) {
        const logElement = document.createElement('div');
        logElement.className = `log-entry ${logEntry.level} new`;
        logElement.setAttribute('data-timestamp', logEntry.timestamp);
        
        logElement.innerHTML = `
            <div class="log-time">${logEntry.timeString}</div>
            <div class="log-level ${logEntry.level}">${logEntry.level.toUpperCase()}</div>
            <div class="log-source">${logEntry.source}</div>
            <div class="log-message">${this.escapeHtml(logEntry.message)}</div>
        `;
        
        this.logDisplay.appendChild(logElement);
        
        // Remove 'new' class after animation
        setTimeout(() => {
            logElement.classList.remove('new');
        }, 300);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    filterLogs(level) {
        this.currentFilter = level;
        
        // Update filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.level === level);
        });
        
        // Re-render logs
        this.refreshLogDisplay();
    }

    refreshLogDisplay() {
        this.logDisplay.innerHTML = '';
        
        const filteredLogs = this.logs.filter(log => this.shouldShowLog(log));
        filteredLogs.forEach(log => this.renderLogEntry(log));
        
        this.updateSearchStats();
        
        if (this.autoScroll) {
            this.scrollToBottom();
        }
    }

    searchLogs() {
        this.refreshLogDisplay();
    }

    updateSearchStats() {
        const query = this.searchInput.value;
        const visibleLogs = this.logDisplay.children.length;
        const totalLogs = this.logs.length;
        
        if (query) {
            this.searchStats.textContent = `${visibleLogs} of ${totalLogs} logs match "${query}"`;
        } else {
            this.searchStats.textContent = `${visibleLogs} logs displayed`;
        }
    }

    clearLogs() {
        this.logs = [];
        this.logDisplay.innerHTML = '';
        this.updateSearchStats();
        this.addLog('info', 'System', '🧹 Logs cleared');
        localStorage.removeItem('intel-unison-logs');
    }

    exportLogs() {
        const exportData = {
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            logs: this.logs
        };
        
        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `intel-unison-logs-${Date.now()}.json`;
        link.click();
        
        this.addLog('info', 'System', '📥 Logs exported successfully');
    }

    toggleAutoScroll() {
        this.autoScroll = !this.autoScroll;
        
        const btn = document.getElementById('autoscroll-btn');
        btn.classList.toggle('active', this.autoScroll);
        
        this.addLog('info', 'System', `Auto-scroll ${this.autoScroll ? 'enabled' : 'disabled'}`);
    }

    pauseLogs() {
        this.isPaused = !this.isPaused;
        
        const btn = document.getElementById('pause-btn');
        const icon = btn.querySelector('svg');
        
        btn.classList.toggle('active', this.isPaused);
        
        if (this.isPaused) {
            icon.innerHTML = `
                <polygon points="5 3 19 12 5 21 5 3"/>
            `;
            btn.title = 'Resume';
        } else {
            icon.innerHTML = `
                <rect x="6" y="4" width="4" height="16"/>
                <rect x="14" y="4" width="4" height="16"/>
            `;
            btn.title = 'Pause';
        }
        
        this.addLog('info', 'System', `Logging ${this.isPaused ? 'paused' : 'resumed'}`);
    }

    scrollToBottom() {
        this.logDisplay.scrollTop = this.logDisplay.scrollHeight;
    }

    saveLogsToStorage() {
        try {
            // Keep only last 500 logs in storage
            const recentLogs = this.logs.slice(-500);
            localStorage.setItem('intel-unison-logs', JSON.stringify(recentLogs));
        } catch (error) {
            console.error('Failed to save logs to storage:', error);
        }
    }

    loadLogsFromStorage() {
        try {
            const stored = localStorage.getItem('intel-unison-logs');
            if (stored) {
                this.logs = JSON.parse(stored);
                this.refreshLogDisplay();
                this.addLog('info', 'System', `📚 Loaded ${this.logs.length} logs from storage`);
            }
        } catch (error) {
            console.error('Failed to load logs from storage:', error);
        }
    }

    requestSystemStatus() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({ type: 'get-status' }));
        }
    }

    updateSystemStatus(status) {
        this.addLog('info', 'System', `📊 System Status: ${status.connections} connections, ${status.activeDevice ? 'device connected' : 'no active device'}`);
        
        // Update UI elements
        this.updateConnectionStatus(status);
        this.updateDeviceInfo(status);
    }

    updateConnectionStatus(status) {
        const connectionText = document.getElementById('connection-text');
        const statusDot = document.querySelector('.status-dot');
        
        if (connectionText && statusDot) {
            if (status.activeDevice) {
                connectionText.textContent = 'Connected';
                statusDot.className = 'status-dot connected';
            } else {
                connectionText.textContent = 'Not Connected';
                statusDot.className = 'status-dot';
            }
        }
    }

    updateDeviceInfo(status) {
        if (status.deviceInfo) {
            const deviceName = document.getElementById('device-name');
            const deviceModel = document.getElementById('device-model');
            const deviceiOS = document.getElementById('device-ios');
            const deviceBattery = document.getElementById('device-battery');
            
            if (deviceName) deviceName.textContent = status.deviceInfo.name || 'Unknown Device';
            if (deviceModel) deviceModel.textContent = status.deviceInfo.model || 'Unknown Model';
            if (deviceiOS) deviceiOS.textContent = status.deviceInfo.version || 'Unknown Version';
            if (deviceBattery) deviceBattery.textContent = status.deviceInfo.battery || 'Unknown';
        }
    }

    startStatusUpdates() {
        // Request status every 30 seconds
        setInterval(() => {
            this.requestSystemStatus();
        }, 30000);
        
        // Update current time
        setInterval(() => {
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = new Date().toLocaleTimeString();
            }
        }, 1000);
    }
}

// Developer Console functionality
class DeveloperConsole {
    constructor(logManager) {
        this.logManager = logManager;
        this.consoleOutput = document.getElementById('console-output');
        this.consoleInput = document.getElementById('console-input');
        this.commandHistory = [];
        this.historyIndex = -1;
        
        this.commands = {
            help: this.showHelp.bind(this),
            clear: this.clearConsole.bind(this),
            status: this.showStatus.bind(this),
            connect: this.connectDevice.bind(this),
            disconnect: this.disconnectDevice.bind(this),
            send: this.sendMessage.bind(this),
            scan: this.scanDevices.bind(this),
            export: this.exportData.bind(this),
            debug: this.toggleDebug.bind(this),
            test: this.runTest.bind(this)
        };
        
        this.setupEventListeners();
    }

    setupEventListeners() {
        this.consoleInput.addEventListener('keydown', (event) => {
            switch (event.key) {
                case 'Enter':
                    this.executeCommand();
                    break;
                case 'ArrowUp':
                    event.preventDefault();
                    this.navigateHistory(-1);
                    break;
                case 'ArrowDown':
                    event.preventDefault();
                    this.navigateHistory(1);
                    break;
                case 'Tab':
                    event.preventDefault();
                    this.autoComplete();
                    break;
            }
        });
    }

    executeCommand() {
        const input = this.consoleInput.value.trim();
        if (!input) return;
        
        // Add to history
        this.commandHistory.push(input);
        this.historyIndex = this.commandHistory.length;
        
        // Display command
        this.addConsoleLine(`> ${input}`, 'command');
        
        // Parse and execute
        const [cmd, ...args] = input.split(' ');
        const command = this.commands[cmd.toLowerCase()];
        
        if (command) {
            try {
                command(args);
            } catch (error) {
                this.addConsoleLine(`Error: ${error.message}`, 'error');
            }
        } else {
            this.addConsoleLine(`Unknown command: ${cmd}. Type 'help' for available commands.`, 'error');
        }
        
        // Clear input
        this.consoleInput.value = '';
        this.scrollToBottom();
    }

    addConsoleLine(text, type = 'normal') {
        const line = document.createElement('div');
        line.className = 'console-line';
        
        const prompt = document.createElement('span');
        prompt.className = 'console-prompt';
        prompt.textContent = '>';
        
        const textSpan = document.createElement('span');
        textSpan.className = `console-text ${type}`;
        textSpan.textContent = text;
        
        line.appendChild(prompt);
        line.appendChild(textSpan);
        
        this.consoleOutput.appendChild(line);
    }

    clearConsole() {
        this.consoleOutput.innerHTML = `
            <div class="console-line">
                <span class="console-prompt">></span>
                <span class="console-text">Intel Unison++ Developer Console</span>
            </div>
            <div class="console-line">
                <span class="console-prompt">></span>
                <span class="console-text">Type 'help' for available commands</span>
            </div>
        `;
    }

    showHelp() {
        const helpText = [
            'Available Commands:',
            '  help        - Show this help message',
            '  clear       - Clear console output',
            '  status      - Show system status',
            '  connect     - Connect to device',
            '  disconnect  - Disconnect from device',
            '  send <msg>  - Send test message',
            '  scan        - Scan for devices',
            '  export      - Export system data',
            '  debug       - Toggle debug mode',
            '  test        - Run system tests'
        ];
        
        helpText.forEach(line => this.addConsoleLine(line));
    }

    showStatus() {
        this.addConsoleLine('Requesting system status...');
        this.logManager.requestSystemStatus();
    }

    connectDevice(args) {
        const deviceId = args[0];
        if (!deviceId) {
            this.addConsoleLine('Usage: connect <device_id>', 'error');
            return;
        }
        
        this.addConsoleLine(`Attempting to connect to device: ${deviceId}`);
        // Implementation would call actual connection logic
    }

    disconnectDevice() {
        this.addConsoleLine('Disconnecting from all devices...');
        // Implementation would call actual disconnection logic
    }

    sendMessage(args) {
        const message = args.join(' ');
        if (!message) {
            this.addConsoleLine('Usage: send <message>', 'error');
            return;
        }
        
        this.addConsoleLine(`Sending test message: "${message}"`);
        // Implementation would call actual message sending
    }

    scanDevices() {
        this.addConsoleLine('Scanning for devices...');
        // Implementation would trigger device scan
    }

    exportData() {
        this.addConsoleLine('Exporting system data...');
        this.logManager.exportLogs();
    }

    toggleDebug() {
        this.addConsoleLine('Debug mode toggled');
        // Implementation would toggle debug logging
    }

    runTest() {
        this.addConsoleLine('Running system tests...');
        // Implementation would run test suite
    }

    navigateHistory(direction) {
        if (this.commandHistory.length === 0) return;
        
        this.historyIndex += direction;
        
        if (this.historyIndex < 0) {
            this.historyIndex = 0;
        } else if (this.historyIndex >= this.commandHistory.length) {
            this.historyIndex = this.commandHistory.length;
            this.consoleInput.value = '';
            return;
        }
        
        this.consoleInput.value = this.commandHistory[this.historyIndex] || '';
    }

    autoComplete() {
        const input = this.consoleInput.value;
        const commands = Object.keys(this.commands);
        const matches = commands.filter(cmd => cmd.startsWith(input));
        
        if (matches.length === 1) {
            this.consoleInput.value = matches[0] + ' ';
        } else if (matches.length > 1) {
            this.addConsoleLine(`Available: ${matches.join(', ')}`);
        }
    }

    scrollToBottom() {
        this.consoleOutput.scrollTop = this.consoleOutput.scrollHeight;
    }
}

// Global functions for HTML onclick handlers
let logManager;
let developerConsole;

function initializeLogs() {
    logManager = new LogManager();
    developerConsole = new DeveloperConsole(logManager);
    
    // Load previous logs
    logManager.loadLogsFromStorage();
    
    console.log('📋 Logs system initialized');
}

function filterLogs(level) {
    if (logManager) {
        logManager.filterLogs(level);
    }
}

function clearLogs() {
    if (logManager) {
        logManager.clearLogs();
    }
}

function exportLogs() {
    if (logManager) {
        logManager.exportLogs();
    }
}

function toggleAutoScroll() {
    if (logManager) {
        logManager.toggleAutoScroll();
    }
}

function pauseLogs() {
    if (logManager) {
        logManager.pauseLogs();
    }
}

function searchLogs() {
    if (logManager) {
        logManager.searchLogs();
    }
}

function toggleConsole() {
    const consoleElement = document.querySelector('.developer-console');
    const toggleBtn = document.getElementById('console-toggle');
    
    consoleElement.style.display = consoleElement.style.display === 'none' ? 'block' : 'none';
    toggleBtn.classList.toggle('active');
}

function clearConsole() {
    if (developerConsole) {
        developerConsole.clearConsole();
    }
}

function handleConsoleInput(event) {
    // This is handled by the DeveloperConsole class
    // Just here for the HTML onclick handler
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeLogs);
} else {
    initializeLogs();
}