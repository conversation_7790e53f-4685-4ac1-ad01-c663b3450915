const { BrowserWindow, ipcMain } = require('electron');
const path = require('path');

class MirrorWindow {
  constructor(airPlayServer = null) {
    this.window = null;
    this.isActive = false;
    this.airPlayServer = airPlayServer;
    this.setupIPC();
  }

  setupIPC() {
    // Handle touch events from the mirror window
    ipcMain.on('mirror-touch', (event, data) => {
      if (this.airPlayServer && this.airPlayServer.touchInputEnabled) {
        this.airPlayServer.sendTouchEvent(data.x, data.y, data.type);
      }
    });

    // Handle keyboard events
    ipcMain.on('mirror-keyboard', (event, data) => {
      if (this.airPlayServer) {
        this.sendKeyboardEvent(data);
      }
    });
  }

  create() {
    if (this.window) {
      this.window.focus();
      return this.window;
    }

    this.window = new BrowserWindow({
      width: 450,
      height: 900,
      minWidth: 400,
      minHeight: 800,
      title: 'iPhone Mirror - iPhone Companion Pro',
      backgroundColor: '#000',
      resizable: true,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        webSecurity: false
      }
    });

    this.window.loadFile(path.join(__dirname, '../../renderer/mirror.html'));

    this.window.on('closed', () => {
      this.window = null;
      this.isActive = false;
      if (this.airPlayServer) {
        this.airPlayServer.disableTouchInput();
      }
    });

    // Enable touch input when window is ready
    this.window.webContents.once('did-finish-load', () => {
      if (this.airPlayServer) {
        this.airPlayServer.enableTouchInput(this.window);
      }
    });

    this.isActive = true;
    return this.window;
  }

  sendFrame(frameData) {
    if (this.window && !this.window.isDestroyed()) {
      // Convert buffer to base64 data URL
      const base64 = frameData.toString('base64');
      const dataUrl = `data:image/jpeg;base64,${base64}`;

      this.window.webContents.send('screen-frame', dataUrl);
    }
  }

  updateFrame(frameData) {
    if (this.window && !this.window.isDestroyed()) {
      // Convert frame data to base64 for display
      const base64Frame = frameData.toString('base64');
      this.window.webContents.send('mirror-frame', {
        frame: base64Frame,
        timestamp: Date.now()
      });
    }
  }

  updateVideoFrame(h264Data) {
    if (this.window && !this.window.isDestroyed()) {
      // Send H.264 data for processing in renderer
      this.window.webContents.send('mirror-video-frame', {
        data: h264Data.toString('base64'),
        timestamp: Date.now()
      });
    }
  }

  sendKeyboardEvent(data) {
    // Send keyboard events to iPhone through AirPlay
    if (this.airPlayServer && this.airPlayServer.reverseConnection) {
      const keyEvent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>category</key>
  <string>keyboard</string>
  <key>key</key>
  <string>${data.key}</string>
  <key>type</key>
  <string>${data.type}</string>
</dict>
</plist>`;

      try {
        this.airPlayServer.reverseConnection.write(`POST /keyboard HTTP/1.1\r\n`);
        this.airPlayServer.reverseConnection.write(`Content-Type: text/x-apple-plist+xml\r\n`);
        this.airPlayServer.reverseConnection.write(`Content-Length: ${Buffer.byteLength(keyEvent)}\r\n`);
        this.airPlayServer.reverseConnection.write(`\r\n`);
        this.airPlayServer.reverseConnection.write(keyEvent);
      } catch (error) {
        console.error('Error sending keyboard event:', error);
      }
    }
  }

  sendStatus(status) {
    if (this.window && !this.window.isDestroyed()) {
      this.window.webContents.send('mirror-status', status);
    }
  }

  showInstructions() {
    if (this.window && !this.window.isDestroyed()) {
      this.window.webContents.send('show-instructions', {
        title: '📱 AirPlay Screen Mirroring',
        instructions: [
          'On your iPhone, swipe down from the top-right corner to open Control Center',
          'Tap "Screen Mirroring"',
          'Select "iPhone Companion Pro" from the list',
          'Enter the code if prompted (usually not required)',
          'Your iPhone screen will appear in this window',
          'Click and drag on the screen to interact with your iPhone'
        ]
      });
    }
  }

  close() {
    if (this.window && !this.window.isDestroyed()) {
      this.window.close();
    }
    this.window = null;
  }

  isOpen() {
    return this.window && !this.window.isDestroyed();
  }

  getBounds() {
    if (this.window && !this.window.isDestroyed()) {
      return this.window.getBounds();
    }
    return { width: 450, height: 900 };
  }
}

module.exports = { MirrorWindow };