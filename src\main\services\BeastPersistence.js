const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const { app } = require('electron');
const { EventEmitter } = require('events');
const { DatabaseMigration } = require('./DatabaseMigration');

class BeastPersistence extends EventEmitter {
  constructor() {
    super();
    this.dbPath = path.join(app.getPath('userData'), 'iphone-beast.db');
    this.db = null;
    this.syncInterval = null;
    this.isInitialized = false;
  }

  async initialize() {
    console.log('🔥 INITIALIZING INTEL UNISON++ PERSISTENCE 🔥');
    console.log('Database path:', this.dbPath);
    
    // Create database directory if it doesn't exist
    const dbDir = path.dirname(this.dbPath);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }

    // Run database migration first
    const migration = new DatabaseMigration();
    await migration.initialize();
    migration.close();
    console.log('✅ Database migration completed');

    // Open database
    this.db = new sqlite3.Database(this.dbPath);
    
    // Verify schema (tables should already exist from migration)
    await this.verifySchema();
    
    // Start auto-backup
    this.startAutoBackup();
    
    this.isInitialized = true;
    console.log('✅ Intel Unison++ Persistence Ready!');
  }

  async verifySchema() {
    // Verify that all required tables exist
    const requiredTables = ['messages', 'message_threads', 'calls', 'contacts', 'attachments', 'sync_status', 'preferences'];
    
    for (const table of requiredTables) {
      const result = await this.get("SELECT name FROM sqlite_master WHERE type='table' AND name=?", [table]);
      if (!result) {
        throw new Error(`Required table '${table}' not found. Database migration may have failed.`);
      }
    }
    
    console.log('✅ Database schema verified');
  }

  async createTables() {
    console.log('⚠️ Warning: createTables() called but schema should be handled by migration');
    const tables = [
      // Intel Unison-style Messages table with enhanced schema
      `CREATE TABLE IF NOT EXISTS messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        thread_id TEXT NOT NULL,
        phone_number TEXT NOT NULL,
        contact_name TEXT,
        message_text TEXT,
        timestamp INTEGER NOT NULL,
        is_outgoing BOOLEAN DEFAULT 0,
        is_delivered BOOLEAN DEFAULT 0,
        is_read BOOLEAN DEFAULT 1,
        has_attachment BOOLEAN DEFAULT 0,
        attachment_path TEXT,
        source TEXT DEFAULT 'phonelink',
        raw_data TEXT,
        created_at INTEGER DEFAULT (strftime('%s', 'now')),
        UNIQUE(phone_number, message_text, timestamp)
      )`,
      
      // Message threads table for conversation organization
      `CREATE TABLE IF NOT EXISTS message_threads (
        thread_id TEXT PRIMARY KEY,
        phone_number TEXT NOT NULL,
        contact_name TEXT,
        last_message TEXT,
        last_timestamp INTEGER,
        unread_count INTEGER DEFAULT 0,
        avatar TEXT,
        is_group BOOLEAN DEFAULT 0,
        raw_data TEXT
      )`,
      
      // Enhanced calls table
      `CREATE TABLE IF NOT EXISTS calls (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        phone_number TEXT NOT NULL,
        contact_name TEXT,
        duration INTEGER DEFAULT 0,
        timestamp INTEGER NOT NULL,
        type TEXT DEFAULT 'voice',
        is_incoming BOOLEAN DEFAULT 1,
        is_missed BOOLEAN DEFAULT 0,
        is_voicemail BOOLEAN DEFAULT 0,
        raw_data TEXT,
        created_at INTEGER DEFAULT (strftime('%s', 'now'))
      )`,
      
      // Contacts table with phone number indexing
      `CREATE TABLE IF NOT EXISTS contacts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        phone_number TEXT UNIQUE NOT NULL,
        display_name TEXT,
        first_name TEXT,
        last_name TEXT,
        email TEXT,
        avatar TEXT,
        photo_path TEXT,
        last_contacted INTEGER,
        raw_data TEXT,
        created_at INTEGER DEFAULT (strftime('%s', 'now'))
      )`,
      
      // Attachments table for media files
      `CREATE TABLE IF NOT EXISTS attachments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        message_id INTEGER,
        filename TEXT,
        filepath TEXT,
        mime_type TEXT,
        file_size INTEGER,
        timestamp INTEGER,
        FOREIGN KEY (message_id) REFERENCES messages (id)
      )`,
      
      // Sync status table for monitoring
      `CREATE TABLE IF NOT EXISTS sync_status (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        last_sync INTEGER NOT NULL,
        sync_type TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        device_id TEXT,
        records_synced INTEGER DEFAULT 0,
        error_message TEXT,
        created_at INTEGER DEFAULT (strftime('%s', 'now'))
      )`,
      
      // User preferences
      `CREATE TABLE IF NOT EXISTS preferences (
        key TEXT PRIMARY KEY,
        value TEXT,
        updated_at INTEGER DEFAULT (strftime('%s', 'now'))
      )`
    ];

    for (const query of tables) {
      await this.run(query);
    }
    
    // Ensure records_synced column exists (migration fix)
    try {
      await this.run(`ALTER TABLE sync_status ADD COLUMN records_synced INTEGER DEFAULT 0`);
      console.log('✅ Added missing records_synced column to sync_status table');
    } catch (error) {
      // Column might already exist, which is fine
      if (!error.message.includes('duplicate column name')) {
        console.log('ℹ️ Records_synced column already exists or other migration issue:', error.message);
      }
    }
    
    // Create performance indexes
    await this.run('CREATE INDEX IF NOT EXISTS idx_messages_thread ON messages(thread_id)');
    await this.run('CREATE INDEX IF NOT EXISTS idx_messages_phone_timestamp ON messages(phone_number, timestamp DESC)');
    await this.run('CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp DESC)');
    await this.run('CREATE INDEX IF NOT EXISTS idx_calls_timestamp ON calls(timestamp DESC)');
    await this.run('CREATE INDEX IF NOT EXISTS idx_calls_phone ON calls(phone_number)');
    await this.run('CREATE INDEX IF NOT EXISTS idx_contacts_phone ON contacts(phone_number)');
    await this.run('CREATE INDEX IF NOT EXISTS idx_threads_phone ON message_threads(phone_number)');
    await this.run('CREATE INDEX IF NOT EXISTS idx_threads_timestamp ON message_threads(last_timestamp DESC)');
    
    // Enable FTS5 for fast message search
    await this.run(`CREATE VIRTUAL TABLE IF NOT EXISTS messages_fts USING fts5(
      message_text,
      contact_name,
      phone_number,
      content='messages',
      content_rowid='id'
    )`);
    
    // Create FTS5 triggers
    await this.run(`CREATE TRIGGER IF NOT EXISTS messages_ai AFTER INSERT ON messages BEGIN
      INSERT INTO messages_fts(rowid, message_text, contact_name, phone_number) 
      VALUES (new.id, new.message_text, new.contact_name, new.phone_number);
    END`);
    
    await this.run(`CREATE TRIGGER IF NOT EXISTS messages_ad AFTER DELETE ON messages BEGIN
      INSERT INTO messages_fts(messages_fts, rowid, message_text, contact_name, phone_number) 
      VALUES('delete', old.id, old.message_text, old.contact_name, old.phone_number);
    END`);
  }

  // Promisified database methods
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) reject(err);
        else resolve(this);
      });
    });
  }

  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  }

  // Enhanced save methods for Intel Unison-style storage
  async saveMessage(message) {
    if (!this.isInitialized) return;
    
    const threadId = message.threadId || message.phoneNumber;
    const timestamp = message.timestamp ? new Date(message.timestamp).getTime() : Date.now();
    
    const sql = `
      INSERT OR IGNORE INTO messages 
      (thread_id, phone_number, contact_name, message_text, timestamp, 
       is_outgoing, is_delivered, is_read, has_attachment, attachment_path, source, raw_data)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const result = await this.run(sql, [
      threadId,
      message.phoneNumber,
      message.contactName || 'Unknown',
      message.messageText || message.text,
      timestamp,
      message.isOutgoing ? 1 : 0,
      message.isDelivered ? 1 : 0,
      message.isRead ? 1 : 0,
      message.hasAttachment ? 1 : 0,
      message.attachmentPath || null,
      message.source || 'phonelink',
      JSON.stringify(message)
    ]);
    
    // Update message thread
    await this.updateMessageThread(threadId, message.phoneNumber, message.contactName, message.messageText || message.text, timestamp);
    
    this.emit('data-saved', 'message');
    return result;
  }
  
  async updateMessageThread(threadId, phoneNumber, contactName, lastMessage, timestamp) {
    const sql = `
      INSERT OR REPLACE INTO message_threads 
      (thread_id, phone_number, contact_name, last_message, last_timestamp)
      VALUES (?, ?, ?, ?, ?)
    `;
    
    await this.run(sql, [threadId, phoneNumber, contactName, lastMessage, timestamp]);
  }

  async saveContact(contact) {
    if (!this.isInitialized) return;
    
    const sql = `
      INSERT OR REPLACE INTO contacts 
      (phone_number, display_name, first_name, last_name, email, avatar, photo_path, last_contacted, raw_data)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await this.run(sql, [
      contact.phoneNumber,
      contact.displayName || contact.name,
      contact.firstName,
      contact.lastName,
      contact.email,
      contact.avatar,
      contact.photoPath,
      contact.lastContacted ? new Date(contact.lastContacted).getTime() : null,
      JSON.stringify(contact)
    ]);
    
    this.emit('data-saved', 'contact');
  }

  async saveCall(call) {
    if (!this.isInitialized) return;
    
    const sql = `
      INSERT OR IGNORE INTO calls 
      (phone_number, contact_name, duration, timestamp, type, is_incoming, is_missed, is_voicemail, raw_data)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await this.run(sql, [
      call.phoneNumber,
      call.contactName || 'Unknown',
      call.duration || 0,
      call.timestamp ? new Date(call.timestamp).getTime() : Date.now(),
      call.type || 'voice',
      call.isIncoming ? 1 : 0,
      call.isMissed ? 1 : 0,
      call.isVoicemail ? 1 : 0,
      JSON.stringify(call)
    ]);
    
    this.emit('data-saved', 'call');
  }

  // Enhanced load methods for Intel Unison-style data
  async loadAllMessages(limit = 1000) {
    if (!this.isInitialized) return [];
    
    try {
      const sql = `
        SELECT m.*, 
               COALESCE(c.display_name, m.contact_name) as resolved_contact_name
        FROM messages m
        LEFT JOIN contacts c ON m.phone_number = c.phone_number
        ORDER BY m.timestamp DESC
        LIMIT ?
      `;
      
      const rows = await this.all(sql, [limit]);
      return rows.map(row => ({
        id: row.id,
        threadId: row.thread_id,
        phoneNumber: row.phone_number,
        contactName: row.resolved_contact_name || 'Unknown',
        messageText: row.message_text,
        timestamp: new Date(row.timestamp),
        isOutgoing: row.is_outgoing === 1,
        isDelivered: row.is_delivered === 1,
        isRead: row.is_read === 1,
        hasAttachment: row.has_attachment === 1,
        attachmentPath: row.attachment_path,
        source: row.source
      }));
    } catch (error) {
      console.error('❌ Error loading messages:', error);
      return [];
    }
  }
  
  async loadMessagesForThread(threadId, limit = 100) {
    if (!this.isInitialized) return [];
    
    try {
      const sql = `
        SELECT m.*, 
               COALESCE(c.display_name, m.contact_name) as resolved_contact_name
        FROM messages m
        LEFT JOIN contacts c ON m.phone_number = c.phone_number
        WHERE m.thread_id = ?
        ORDER BY m.timestamp ASC
        LIMIT ?
      `;
      
      const rows = await this.all(sql, [threadId, limit]);
      return rows.map(row => ({
        id: row.id,
        threadId: row.thread_id,
        phoneNumber: row.phone_number,
        contactName: row.resolved_contact_name || 'Unknown',
        messageText: row.message_text,
        timestamp: new Date(row.timestamp),
        isOutgoing: row.is_outgoing === 1,
        isDelivered: row.is_delivered === 1,
        isRead: row.is_read === 1,
        hasAttachment: row.has_attachment === 1,
        attachmentPath: row.attachment_path,
        source: row.source
      }));
    } catch (error) {
      console.error('❌ Error loading messages for thread:', error);
      return [];
    }
  }
  
  async searchMessages(query, limit = 50) {
    if (!this.isInitialized) return [];
    
    try {
      const sql = `
        SELECT m.*, 
               COALESCE(c.display_name, m.contact_name) as resolved_contact_name
        FROM messages_fts
        JOIN messages m ON messages_fts.rowid = m.id
        LEFT JOIN contacts c ON m.phone_number = c.phone_number
        WHERE messages_fts MATCH ?
        ORDER BY rank
        LIMIT ?
      `;
      
      const rows = await this.all(sql, [query, limit]);
      return rows.map(row => ({
        id: row.id,
        threadId: row.thread_id,
        phoneNumber: row.phone_number,
        contactName: row.resolved_contact_name || 'Unknown',
        messageText: row.message_text,
        timestamp: new Date(row.timestamp),
        isOutgoing: row.is_outgoing === 1,
        isDelivered: row.is_delivered === 1,
        isRead: row.is_read === 1,
        hasAttachment: row.has_attachment === 1,
        attachmentPath: row.attachment_path,
        source: row.source
      }));
    } catch (error) {
      console.error('❌ Error searching messages:', error);
      return [];
    }
  }

  async loadMessageThreads() {
    if (!this.isInitialized) return [];
    
    try {
      const sql = `
        SELECT mt.*, 
          COALESCE(c.display_name, mt.contact_name) as resolved_contact_name,
          c.photo_path,
          (SELECT COUNT(*) FROM messages m WHERE m.thread_id = mt.thread_id) as message_count,
          (SELECT COUNT(*) FROM messages m WHERE m.thread_id = mt.thread_id AND m.is_read = 0) as unread_count
        FROM message_threads mt
        LEFT JOIN contacts c ON mt.phone_number = c.phone_number
        ORDER BY mt.last_timestamp DESC
      `;
      
      const rows = await this.all(sql);
      return rows.map(row => ({
        threadId: row.thread_id,
        phoneNumber: row.phone_number,
        contactName: row.resolved_contact_name || 'Unknown',
        lastMessage: row.last_message,
        lastTimestamp: row.last_timestamp ? new Date(row.last_timestamp) : null,
        messageCount: row.message_count || 0,
        unreadCount: row.unread_count || 0,
        photoPath: row.photo_path,
        isGroup: row.is_group === 1
      }));
    } catch (error) {
      console.error('❌ Error loading message threads:', error);
      return [];
    }
  }

  async loadCalls(limit = 100) {
    if (!this.isInitialized) return [];
    
    try {
      const sql = `
        SELECT c.*, 
               COALESCE(contacts.display_name, c.contact_name) as resolved_contact_name
        FROM calls c
        LEFT JOIN contacts ON c.phone_number = contacts.phone_number
        ORDER BY c.timestamp DESC 
        LIMIT ?
      `;
      
      const rows = await this.all(sql, [limit]);
      return rows.map(row => ({
        id: row.id,
        phoneNumber: row.phone_number,
        contactName: row.resolved_contact_name || 'Unknown',
        duration: row.duration,
        timestamp: new Date(row.timestamp),
        type: row.type,
        isIncoming: row.is_incoming === 1,
        isMissed: row.is_missed === 1,
        isVoicemail: row.is_voicemail === 1
      }));
    } catch (error) {
      console.error('❌ Error loading calls:', error);
      return [];
    }
  }
  
  async loadContacts() {
    if (!this.isInitialized) return [];
    
    try {
      const sql = `
        SELECT * FROM contacts
        ORDER BY display_name ASC
      `;
      
      const rows = await this.all(sql);
      return rows.map(row => ({
        id: row.id,
        phoneNumber: row.phone_number,
        displayName: row.display_name,
        firstName: row.first_name,
        lastName: row.last_name,
        email: row.email,
        avatar: row.avatar,
        photoPath: row.photo_path,
        lastContacted: row.last_contacted ? new Date(row.last_contacted) : null
      }));
    } catch (error) {
      console.error('❌ Error loading contacts:', error);
      return [];
    }
  }

  // Auto-backup functionality - enhanced for mission-critical data
  startAutoBackup() {
    // Backup every 5 minutes
    this.syncInterval = setInterval(() => {
      this.createBackup();
    }, 5 * 60 * 1000);
    
    // Also backup on every major save (but debounced)
    let backupTimeout;
    this.on('data-saved', () => {
      clearTimeout(backupTimeout);
      backupTimeout = setTimeout(() => {
        this.createBackup();
      }, 10000); // Wait 10 seconds after last save
    });
  }

  async createBackup() {
    if (!this.isInitialized) return;
    
    const backupDir = path.join(app.getPath('userData'), 'backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(backupDir, `backup-${timestamp}.db`);
    
    try {
      // Copy database file
      fs.copyFileSync(this.dbPath, backupPath);
      
      // Keep only last 10 backups
      const backups = fs.readdirSync(backupDir)
        .filter(f => f.startsWith('backup-'))
        .sort()
        .reverse();
      
      if (backups.length > 10) {
        backups.slice(10).forEach(backup => {
          fs.unlinkSync(path.join(backupDir, backup));
        });
      }
      
      console.log(`✅ Backup created: ${backupPath}`);
    } catch (error) {
      console.error('Backup failed:', error);
    }
  }

  // Enhanced sync status methods
  async updateSyncStatus(syncType, status, deviceId = null, recordsSynced = 0, errorMessage = null) {
    if (!this.isInitialized) return;
    
    try {
      // First ensure the column exists
      await this.run(`ALTER TABLE sync_status ADD COLUMN records_synced INTEGER DEFAULT 0`);
    } catch (error) {
      // Column already exists, which is fine
    }
    
    const sql = `
      INSERT INTO sync_status (last_sync, sync_type, status, device_id, records_synced, error_message)
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    await this.run(sql, [
      Date.now(),
      syncType,
      status,
      deviceId,
      recordsSynced,
      errorMessage
    ]);
  }
  
  async getMessageStats() {
    if (!this.isInitialized) return {};
    
    const stats = await this.get(`
      SELECT 
        COUNT(*) as total_messages,
        COUNT(DISTINCT thread_id) as total_threads,
        COUNT(CASE WHEN is_outgoing = 1 THEN 1 END) as outgoing_messages,
        COUNT(CASE WHEN is_outgoing = 0 THEN 1 END) as incoming_messages,
        COUNT(CASE WHEN has_attachment = 1 THEN 1 END) as messages_with_attachments
      FROM messages
    `);
    
    return stats || {};
  }
  
  async exportForCRM(format = 'json') {
    if (!this.isInitialized) return null;
    
    const messages = await this.loadAllMessages(10000);
    const contacts = await this.loadContacts();
    const calls = await this.loadCalls(1000);
    
    const exportData = {
      messages,
      contacts,
      calls,
      exportedAt: new Date().toISOString(),
      totalRecords: messages.length + contacts.length + calls.length
    };
    
    if (format === 'json') {
      return JSON.stringify(exportData, null, 2);
    }
    
    return exportData;
  }

  async getLastSync(syncType) {
    if (!this.isInitialized) return null;
    
    const sql = `
      SELECT * FROM sync_status 
      WHERE sync_type = ? 
      ORDER BY last_sync DESC 
      LIMIT 1
    `;
    
    return await this.get(sql, [syncType]);
  }
  
  async bulkSaveMessages(messages) {
    if (!this.isInitialized || !messages.length) return;
    
    const sql = `
      INSERT OR IGNORE INTO messages 
      (thread_id, phone_number, contact_name, message_text, timestamp, 
       is_outgoing, is_delivered, is_read, has_attachment, attachment_path, source, raw_data)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    this.db.serialize(() => {
      this.db.run('BEGIN TRANSACTION');
      
      for (const message of messages) {
        const threadId = message.threadId || message.phoneNumber;
        const timestamp = message.timestamp ? new Date(message.timestamp).getTime() : Date.now();
        
        this.db.run(sql, [
          threadId,
          message.phoneNumber,
          message.contactName || 'Unknown',
          message.messageText || message.text,
          timestamp,
          message.isOutgoing ? 1 : 0,
          message.isDelivered ? 1 : 0,
          message.isRead ? 1 : 0,
          message.hasAttachment ? 1 : 0,
          message.attachmentPath || null,
          message.source || 'phonelink',
          JSON.stringify(message)
        ]);
      }
      
      this.db.run('COMMIT');
    });
    
    console.log(`✅ Bulk saved ${messages.length} messages`);
    this.emit('data-saved', 'bulk-messages');
  }

  // Integration testing methods
  async deleteMessage(messageId) {
    try {
      await this.run('DELETE FROM messages WHERE id = ?', [messageId]);
      return { success: true };
    } catch (error) {
      console.error('❌ Error deleting message:', error);
      return { success: false, error: error.message };
    }
  }

  async deleteContact(contactId) {
    try {
      await this.run('DELETE FROM contacts WHERE id = ?', [contactId]);
      return { success: true };
    } catch (error) {
      console.error('❌ Error deleting contact:', error);
      return { success: false, error: error.message };
    }
  }

  async deleteCall(callId) {
    try {
      await this.run('DELETE FROM calls WHERE id = ?', [callId]);
      return { success: true };
    } catch (error) {
      console.error('❌ Error deleting call:', error);
      return { success: false, error: error.message };
    }
  }

  async checkIntegrity() {
    try {
      // Run PRAGMA integrity_check
      const result = await this.get('PRAGMA integrity_check');
      return result && result.integrity_check === 'ok';
    } catch (error) {
      console.error('❌ Database integrity check failed:', error);
      return false;
    }
  }

  close() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    
    if (this.db) {
      this.db.close();
    }
    
    console.log('🔥 Intel Unison++ Persistence closed');
  }
}

module.exports = { BeastPersistence };
