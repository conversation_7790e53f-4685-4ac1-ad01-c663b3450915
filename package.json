{"name": "iphone-companion-pro", "version": "1.0.0", "productName": "iPhone Companion Pro", "description": "Professional iPhone companion for Windows - Better than Phone Link", "main": "src/main/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "test": "node tests/integration-tests.js", "build": "node scripts/build.js", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "dist": "electron-builder --publish=never", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@types/mdns": "^0.0.38", "@types/node": "^20.0.0", "@types/ws": "^8.18.1", "archiver": "^7.0.1", "electron": "^37.2.0", "electron-builder": "^24.6.4", "typescript": "^5.0.0"}, "dependencies": {"@abandonware/noble": "^1.9.2-15", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@types/fluent-ffmpeg": "^2.1.27", "axios": "^1.6.0", "better-sqlite3": "^12.2.0", "bonjour-service": "^1.0.14", "chokidar": "^4.0.3", "cors": "^2.8.5", "electron-store": "^8.2.0", "express": "^4.18.2", "ffmpeg-static": "^5.2.0", "mdns-js": "^0.2.9", "node-datachannel": "^0.28.0", "node-machine-id": "^1.1.12", "node-persist": "^4.0.4", "peer": "^1.0.2", "qrcode": "^1.5.4", "sharp": "^0.34.2", "simple-peer": "^9.11.1", "socket.io": "^4.6.1", "socket.io-client": "^4.6.1", "sqlite3": "^5.1.7", "usb": "^2.11.0", "winston": "^3.11.0", "ws": "^8.14.2"}, "build": {"appId": "com.iphone.companion.pro", "productName": "iPhone Companion Pro", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "uninstallDisplayName": "iPhone Companion Pro", "allowToChangeInstallationDirectory": true, "perMachine": true}}}