#!/usr/bin/env node

console.clear();
console.log('═'.repeat(60));
console.log('🔥 INTEL UNISON++ STANDALONE MODE');
console.log('═'.repeat(60));

// Set up environment
process.env.INTEL_UNISON_MODE = 'standalone';

// Fix paths for standalone mode
const path = require('path');
const os = require('os');

global.app = {
  getPath: (name) => {
    const baseDir = path.join(os.homedir(), '.intel-unison-companion');
    switch(name) {
      case 'userData': return baseDir;
      case 'logs': return path.join(baseDir, 'logs');
      case 'temp': return os.tmpdir();
      default: return baseDir;
    }
  },
  getName: () => 'Intel Unison Companion',
  getVersion: () => '1.0.0'
};

// Create directories
const fs = require('fs');
const userDataPath = global.app.getPath('userData');
if (!fs.existsSync(userDataPath)) {
  fs.mkdirSync(userDataPath, { recursive: true });
}

// Start Intel Unison
async function startIntelUnison() {
  try {
    const { IntelUnisonCore } = require('./intel-unison/IntelUnisonCore');
    const core = new IntelUnisonCore();
    
    await core.initialize();
    await core.start();
    
    console.log('\n✅ Intel Unison++ is running!');
    console.log('📱 Waiting for iPhone connection...');
    console.log('🌐 Open http://localhost:3000 in your browser\n');
    
    // Keep process alive
    process.stdin.resume();
    
  } catch (error) {
    console.error('❌ Failed to start:', error);
    process.exit(1);
  }
}

// Handle shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down Intel Unison++...');
  process.exit(0);
});

// Start the app
startIntelUnison();