const { ipc<PERSON><PERSON><PERSON> } = require('electron');

let callHistory = [];
let currentCall = null;
let callTimer = null;
let callStartTime = null;

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    loadCallHistory();
    
    // Initialize microphone with system default
    initializeMicrophone();
    
    // Listen for call events
    ipcRenderer.on('incoming-call', (event, call) => {
        handleIncomingCall(call);
    });
    
    ipcRenderer.on('call-started', (event, call) => {
        handleCallStarted(call);
    });
    
    ipcRenderer.on('call-connected', (event, call) => {
        handleCallConnected(call);
    });
    
    ipcRenderer.on('call-ended', (event, call) => {
        handleCallEnded(call);
    });
    
    ipcRenderer.on('call-history-updated', (event, history) => {
        callHistory = history;
        renderCallHistory();
    });

    ipcRenderer.on('show-call-instructions', (event, data) => {
        showCallInstructions(data);
    });
    
    // Handle menu actions
    ipcRenderer.on('export-call-history', () => {
        console.log('📤 Export call history action triggered from menu');
        exportCallHistory();
    });
});

async function loadCallHistory() {
    try {
        console.log('📞 Loading call history from iPhone...');
        
        const result = await ipcRenderer.invoke('get-call-history');
        if (result.success && result.data && result.data.length > 0) {
            console.log(`📞 Loaded ${result.data.length} calls from database`);
            callHistory = result.data;
            renderCallHistory();
        } else {
            // Show loading state for call history
            console.log('📞 No call history available yet - waiting for iPhone sync...');
            showCallHistoryLoadingState();
        }
    } catch (error) {
        console.error('❌ Failed to load call history:', error);
        showCallHistoryLoadingState();
    }
}

function renderCallHistory() {
    const container = document.getElementById('call-history');
    container.innerHTML = '';
    
    if (callHistory.length === 0) {
        showCallHistoryLoadingState();
        return;
    }
    
    // Sort calls by timestamp (most recent first)
    const sortedCalls = callHistory.sort((a, b) => {
        const timeA = a.startTime || a.timestamp || 0;
        const timeB = b.startTime || b.timestamp || 0;
        return timeB - timeA;
    });
    
    console.log(`📞 Rendering ${sortedCalls.length} calls in call history`);
    
    sortedCalls.forEach(call => {
        const div = document.createElement('div');
        div.className = 'call-item';
        
        // Enhanced call direction and status icons
        let directionIcon = '📞';
        let statusClass = '';
        
        if (call.direction === 'incoming' || call.type === 'incoming') {
            if (call.status === 'declined' || call.status === 'missed') {
                directionIcon = '📵'; // Missed call
                statusClass = 'missed';
            } else {
                directionIcon = '📞⬇️'; // Incoming accepted
                statusClass = 'incoming';
            }
        } else if (call.direction === 'outgoing' || call.type === 'outgoing') {
            directionIcon = '📞⬆️'; // Outgoing call
            statusClass = 'outgoing';
        }
        
        // Handle different possible field names from database
        const contactName = call.contactName || call.contact_name || call.name || 'Unknown';
        const phoneNumber = call.phoneNumber || call.phone_number || call.number || 'Unknown';
        const duration = call.duration || 0;
        const callTime = call.startTime || call.timestamp || call.call_time || Date.now();
        
        // Format duration properly
        const durationText = duration > 0 ? formatDuration(duration) : 
                            (call.status === 'missed' || call.status === 'declined') ? 'Missed' : 'Connected';
        
        // Format time with better relative timing
        const timeText = formatCallTime(callTime);
        
        div.innerHTML = `
            <div class="call-contact">
                <span class="contact-name">${contactName}</span>
                <span class="call-status ${statusClass}">${directionIcon}</span>
            </div>
            <div class="call-details">
                <div class="call-direction">
                    <span class="phone-number">${phoneNumber}</span>
                </div>
                <div class="call-meta">
                    <div class="call-time">${timeText}</div>
                    <div class="call-duration">${durationText}</div>
                </div>
            </div>
        `;
        
        // Add click handler to call back this number
        div.onclick = () => callBack(phoneNumber, contactName);
        div.style.cursor = 'pointer';
        
        container.appendChild(div);
    });
    
    console.log('✅ Call history rendered successfully');
}

function addDigit(digit) {
    const input = document.getElementById('phone-input');
    input.value += digit;
}

function clearInput() {
    document.getElementById('phone-input').value = '';
}

async function makeCall() {
    const phoneNumber = document.getElementById('phone-input').value.trim();
    
    if (!phoneNumber) {
        alert('Please enter a phone number');
        return;
    }
    
    const result = await ipcRenderer.invoke('make-call', { phoneNumber });
    
    if (result.success) {
        console.log('Call initiated:', result.callId);
    } else {
        alert('Failed to make call: ' + result.error);
    }
}

async function callBack(phoneNumber, contactName) {
    const result = await ipcRenderer.invoke('make-call', { phoneNumber, contactName });
    
    if (result.success) {
        console.log('Call back initiated:', result.callId);
    } else {
        alert('Failed to call back: ' + result.error);
    }
}

function handleIncomingCall(call) {
    currentCall = call;
    showActiveCall(call);
    
    // Show incoming call controls
    const controls = document.getElementById('call-controls');
    controls.innerHTML = `
        <button class="control-btn answer" onclick="answerCall()" title="Answer">📞</button>
        <button class="control-btn decline" onclick="declineCall()" title="Decline">📵</button>
    `;
    
    document.getElementById('call-status').textContent = 'Incoming call...';
    
    // Play ringtone (if available)
    playRingtone();
}

function handleCallStarted(call) {
    currentCall = call;
    showActiveCall(call);
    
    const controls = document.getElementById('call-controls');
    controls.innerHTML = `
        <button class="control-btn decline" onclick="endCall()" title="End Call">📵</button>
    `;
    
    document.getElementById('call-status').textContent = 'Calling...';
}

function handleCallConnected(call) {
    currentCall = call;
    callStartTime = Date.now();
    
    const controls = document.getElementById('call-controls');
    controls.innerHTML = `
        <button class="control-btn mute" onclick="toggleMute()" title="Mute">🔇</button>
        <button class="control-btn speaker" onclick="toggleSpeaker()" title="Speaker">🔊</button>
        <button class="control-btn decline" onclick="endCall()" title="End Call">📵</button>
    `;
    
    document.getElementById('call-status').textContent = 'Connected';
    
    // Start call timer
    startCallTimer();
    
    // Stop ringtone
    stopRingtone();
}

function handleCallEnded(call) {
    hideActiveCall();
    stopCallTimer();
    stopRingtone();
    
    currentCall = null;
    callStartTime = null;
    
    // Refresh call history
    loadCallHistory();
}

function showActiveCall(call) {
    const overlay = document.getElementById('active-call');
    const avatar = document.getElementById('call-avatar');
    const contact = document.getElementById('call-contact');
    
    // Set contact info
    contact.textContent = call.contactName;
    
    // Set avatar (first letter of contact name or phone icon)
    if (call.contactName && call.contactName !== call.phoneNumber) {
        avatar.textContent = call.contactName.charAt(0).toUpperCase();
    } else {
        avatar.textContent = '📞';
    }
    
    overlay.classList.remove('hidden');
}

function hideActiveCall() {
    document.getElementById('active-call').classList.add('hidden');
}

async function answerCall() {
    const result = await ipcRenderer.invoke('answer-call');
    if (!result.success) {
        alert('Failed to answer call: ' + result.error);
    }
}

async function declineCall() {
    const result = await ipcRenderer.invoke('decline-call');
    if (!result.success) {
        alert('Failed to decline call: ' + result.error);
    }
}

async function endCall() {
    const result = await ipcRenderer.invoke('end-call');
    if (!result.success) {
        alert('Failed to end call: ' + result.error);
    }
}

async function toggleMute() {
    const result = await ipcRenderer.invoke('toggle-mute');
    // Update UI based on mute state
    const muteBtn = document.querySelector('.control-btn.mute');
    if (result.muted) {
        muteBtn.style.background = '#f44336';
        muteBtn.title = 'Unmute';
    } else {
        muteBtn.style.background = '#666';
        muteBtn.title = 'Mute';
    }
}

// Initialize microphone with system default device
async function initializeMicrophone() {
    try {
        console.log('🎤 Initializing system default microphone...');
        
        // Use system default microphone instead of searching Windows Store
        const stream = await navigator.mediaDevices.getUserMedia({ 
            audio: { 
                deviceId: 'default',
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
            } 
        });
        
        console.log('✅ Microphone initialized successfully');
        
        // Store the stream for later use
        window.microphoneStream = stream;
        
        return stream;
    } catch (error) {
        console.error('❌ Failed to initialize microphone:', error);
        
        // Show user-friendly error message
        alert('Could not access microphone. Please check your microphone permissions in system settings.');
        
        return null;
    }
}

async function toggleSpeaker() {
    const result = await ipcRenderer.invoke('toggle-speaker');
    // Update UI based on speaker state
    const speakerBtn = document.querySelector('.control-btn.speaker');
    if (result.speakerOn) {
        speakerBtn.style.background = '#007AFF';
        speakerBtn.title = 'Speaker Off';
    } else {
        speakerBtn.style.background = '#666';
        speakerBtn.title = 'Speaker On';
    }
}

function startCallTimer() {
    callTimer = setInterval(() => {
        if (callStartTime) {
            const elapsed = Math.floor((Date.now() - callStartTime) / 1000);
            document.getElementById('call-duration').textContent = formatDuration(elapsed);
        }
    }, 1000);
}

function stopCallTimer() {
    if (callTimer) {
        clearInterval(callTimer);
        callTimer = null;
    }
}

function playRingtone() {
    // In a real implementation, you would play an audio file
    console.log('Playing ringtone...');
}

function stopRingtone() {
    // Stop ringtone audio
    console.log('Stopping ringtone...');
}

function showDialer() {
    // Focus on the phone input
    document.getElementById('phone-input').focus();
}

function showCallInstructions(data) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
    `;

    modal.innerHTML = `
        <div style="background: #1a1a1a; padding: 40px; border-radius: 20px; text-align: center; max-width: 500px;">
            <h2 style="margin-bottom: 30px;">📞 Make Call</h2>
            
            <div style="margin-bottom: 20px;">
                <h3>${data.contactName || data.phoneNumber}</h3>
                <p style="color: #999;">${data.phoneNumber}</p>
            </div>

            <div style="text-align: left; background: #2a2a2a; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <h3>Instructions:</h3>
                <ol style="margin: 10px 0; padding-left: 20px;">
                    ${data.instructions.map(step => `<li style="margin: 10px 0;">${step}</li>`).join('')}
                </ol>
            </div>

            <p style="color: #007AFF; margin-bottom: 20px;">The call will be managed through your iPhone</p>

            <button onclick="this.parentElement.parentElement.remove()" style="
                padding: 10px 30px;
                background: #007AFF;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
            ">Got it!</button>
        </div>
    `;

    document.body.appendChild(modal);
}

// Show loading state for call history
function showCallHistoryLoadingState() {
    const container = document.getElementById('call-history');
    container.innerHTML = `
        <div class="loading-state" style="padding: 20px; text-align: center; color: #999;">
            <div class="spinner" style="width: 20px; height: 20px; border: 2px solid #333; border-top: 2px solid #007AFF; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 10px;"></div>
            <div>📞 Loading call history...</div>
            <div style="font-size: 12px; margin-top: 5px;">Waiting for iPhone sync</div>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;
}

// Export call history functionality (moved from UI to menu actions)
function exportCallHistory() {
    console.log('📤 Exporting call history...');
    
    try {
        if (callHistory.length === 0) {
            alert('No call history to export');
            return;
        }
        
        // Create export data
        const exportData = {
            callHistory: callHistory,
            exportDate: new Date().toISOString(),
            exportedBy: 'iPhone Companion Pro',
            totalCalls: callHistory.length
        };
        
        // Convert to JSON
        const jsonData = JSON.stringify(exportData, null, 2);
        
        // Create download link
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `call-history-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        console.log('✅ Call history exported successfully');
        alert('Call history exported successfully');
    } catch (error) {
        console.error('❌ Export failed:', error);
        alert('Export failed: ' + error.message);
    }
}

// Utility functions
function formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

function formatCallTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    // Less than 1 minute
    if (diff < 60000) return 'Just now';
    
    // Less than 1 hour
    if (diff < 3600000) {
        const minutes = Math.floor(diff / 60000);
        return `${minutes}m ago`;
    }
    
    // Less than 1 day
    if (diff < 86400000) {
        const hours = Math.floor(diff / 3600000);
        return `${hours}h ago`;
    }
    
    // Less than 1 week
    if (diff < 604800000) {
        const days = Math.floor(diff / 86400000);
        return `${days}d ago`;
    }
    
    // More than 1 week - show actual date
    return date.toLocaleDateString([], { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function formatDuration(seconds) {
    if (seconds < 60) {
        return `${seconds}s`;
    }
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.target.id === 'phone-input') {
        if (e.key === 'Enter') {
            makeCall();
        }
    }
});
