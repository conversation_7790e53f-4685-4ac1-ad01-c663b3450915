const { EventEmitter } = require('events');
const os = require('os');
const { exec } = require('child_process');
const Store = require('electron-store');

class VMPerformanceOptimizer extends EventEmitter {
  constructor(vmBridge, vmManager, healthMonitor) {
    super();
    this.vmBridge = vmBridge;
    this.vmManager = vmManager;
    this.healthMonitor = healthMonitor;
    this.store = new Store();
    
    this.performanceProfiles = {
      'power-saver': {
        name: 'Power Saver',
        memory: '4G',
        cores: 2,
        cpuLimit: 50,
        ioThrottle: true,
        networkQoS: 'low',
        frameRate: 15,
        quality: 'low'
      },
      'balanced': {
        name: 'Balanced',
        memory: '6G',
        cores: 4,
        cpuLimit: 75,
        ioThrottle: false,
        networkQoS: 'medium',
        frameRate: 30,
        quality: 'medium'
      },
      'performance': {
        name: 'Performance',
        memory: '8G',
        cores: 6,
        cpuLimit: 90,
        ioThrottle: false,
        networkQoS: 'high',
        frameRate: 60,
        quality: 'high'
      },
      'maximum': {
        name: 'Maximum Performance',
        memory: '12G',
        cores: 8,
        cpuLimit: 95,
        ioThrottle: false,
        networkQoS: 'highest',
        frameRate: 60,
        quality: 'ultra'
      }
    };
    
    this.currentProfile = 'balanced';
    this.adaptiveOptimization = true;
    this.optimizationInterval = null;
    this.performanceMetrics = {
      cpu: [],
      memory: [],
      network: [],
      disk: []
    };
    
    this.loadSettings();
    this.startPerformanceMonitoring();
  }

  // Load saved settings
  loadSettings() {
    this.currentProfile = this.store.get('vmPerformanceProfile', 'balanced');
    this.adaptiveOptimization = this.store.get('vmAdaptiveOptimization', true);
  }

  // Save settings
  saveSettings() {
    this.store.set('vmPerformanceProfile', this.currentProfile);
    this.store.set('vmAdaptiveOptimization', this.adaptiveOptimization);
  }

  // Start performance monitoring
  startPerformanceMonitoring() {
    this.optimizationInterval = setInterval(() => {
      this.collectPerformanceMetrics();
      
      if (this.adaptiveOptimization) {
        this.performAdaptiveOptimization();
      }
    }, 10000); // Every 10 seconds
  }

  // Stop performance monitoring
  stopPerformanceMonitoring() {
    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval);
      this.optimizationInterval = null;
    }
  }

  // Collect performance metrics
  async collectPerformanceMetrics() {
    try {
      const metrics = {
        timestamp: Date.now(),
        cpu: await this.getCPUUsage(),
        memory: await this.getMemoryUsage(),
        network: await this.getNetworkUsage(),
        disk: await this.getDiskUsage(),
        vmMetrics: await this.getVMSpecificMetrics()
      };

      // Store metrics (keep last 100 entries)
      Object.keys(this.performanceMetrics).forEach(key => {
        if (metrics[key] !== undefined) {
          this.performanceMetrics[key].push(metrics[key]);
          if (this.performanceMetrics[key].length > 100) {
            this.performanceMetrics[key].shift();
          }
        }
      });

      this.emit('metrics-collected', metrics);
      
    } catch (error) {
      console.error('Failed to collect performance metrics:', error);
    }
  }

  // Get CPU usage
  async getCPUUsage() {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage();
      
      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage);
        const totalUsage = endUsage.user + endUsage.system;
        const percentage = (totalUsage / 1000000) * 100;
        resolve(Math.min(percentage, 100));
      }, 1000);
    });
  }

  // Get memory usage
  async getMemoryUsage() {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    return (usedMemory / totalMemory) * 100;
  }

  // Get network usage
  async getNetworkUsage() {
    // Simplified network usage calculation
    return new Promise((resolve) => {
      exec('netstat -e', (error, stdout) => {
        if (error) {
          resolve(0);
          return;
        }
        
        // Parse network statistics (simplified)
        const lines = stdout.split('\n');
        const bytesLine = lines.find(line => line.includes('Bytes'));
        if (bytesLine) {
          const bytes = parseInt(bytesLine.split(/\s+/)[1]) || 0;
          resolve(bytes);
        } else {
          resolve(0);
        }
      });
    });
  }

  // Get disk usage
  async getDiskUsage() {
    return new Promise((resolve) => {
      exec('wmic logicaldisk get size,freespace,caption', (error, stdout) => {
        if (error) {
          resolve(0);
          return;
        }

        const lines = stdout.trim().split('\n').slice(1);
        let totalSize = 0;
        let totalFree = 0;

        lines.forEach(line => {
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 3) {
            const free = parseInt(parts[1]) || 0;
            const size = parseInt(parts[2]) || 0;
            totalSize += size;
            totalFree += free;
          }
        });

        const usagePercentage = totalSize > 0 ? ((totalSize - totalFree) / totalSize) * 100 : 0;
        resolve(usagePercentage);
      });
    });
  }

  // Get VM-specific metrics
  async getVMSpecificMetrics() {
    if (!this.vmManager) {
      return {};
    }

    try {
      const vmMetrics = await this.vmManager.getVMMetrics();
      return vmMetrics || {};
    } catch (error) {
      return {};
    }
  }

  // Perform adaptive optimization
  async performAdaptiveOptimization() {
    const currentMetrics = this.getCurrentAverageMetrics();
    const recommendedProfile = this.analyzeAndRecommendProfile(currentMetrics);
    
    if (recommendedProfile !== this.currentProfile) {
      console.log(`Adaptive optimization recommends switching to: ${recommendedProfile}`);
      this.emit('profile-recommendation', {
        current: this.currentProfile,
        recommended: recommendedProfile,
        reason: this.getRecommendationReason(currentMetrics)
      });
      
      // Auto-apply if enabled
      const autoApply = this.store.get('vmAutoApplyOptimizations', false);
      if (autoApply) {
        await this.applyPerformanceProfile(recommendedProfile);
      }
    }
  }

  // Get current average metrics
  getCurrentAverageMetrics() {
    const recentCount = Math.min(10, this.performanceMetrics.cpu.length);
    
    if (recentCount === 0) {
      return { cpu: 0, memory: 0, network: 0, disk: 0 };
    }

    const recent = {
      cpu: this.performanceMetrics.cpu.slice(-recentCount),
      memory: this.performanceMetrics.memory.slice(-recentCount),
      network: this.performanceMetrics.network.slice(-recentCount),
      disk: this.performanceMetrics.disk.slice(-recentCount)
    };

    return {
      cpu: recent.cpu.reduce((sum, val) => sum + val, 0) / recent.cpu.length,
      memory: recent.memory.reduce((sum, val) => sum + val, 0) / recent.memory.length,
      network: recent.network.reduce((sum, val) => sum + val, 0) / recent.network.length,
      disk: recent.disk.reduce((sum, val) => sum + val, 0) / recent.disk.length
    };
  }

  // Analyze metrics and recommend profile
  analyzeAndRecommendProfile(metrics) {
    const { cpu, memory, disk } = metrics;
    
    // High resource usage - recommend power saver
    if (cpu > 85 || memory > 90 || disk > 95) {
      return 'power-saver';
    }
    
    // Low resource usage - can use higher performance
    if (cpu < 30 && memory < 50 && disk < 70) {
      const systemSpecs = this.getSystemSpecs();
      if (systemSpecs.totalMemory > 16 && systemSpecs.cpuCores > 6) {
        return 'performance';
      } else if (systemSpecs.totalMemory > 12 && systemSpecs.cpuCores > 4) {
        return 'balanced';
      }
    }
    
    // Medium usage - balanced profile
    if (cpu < 70 && memory < 75) {
      return 'balanced';
    }
    
    // Default to current profile if no clear recommendation
    return this.currentProfile;
  }

  // Get recommendation reason
  getRecommendationReason(metrics) {
    const { cpu, memory, disk } = metrics;
    
    if (cpu > 85) return 'High CPU usage detected';
    if (memory > 90) return 'High memory usage detected';
    if (disk > 95) return 'High disk usage detected';
    if (cpu < 30 && memory < 50) return 'Low resource usage - can increase performance';
    
    return 'Optimizing for current usage patterns';
  }

  // Get system specifications
  getSystemSpecs() {
    return {
      totalMemory: Math.round(os.totalmem() / (1024 * 1024 * 1024)), // GB
      cpuCores: os.cpus().length,
      platform: os.platform(),
      arch: os.arch()
    };
  }

  // Apply performance profile
  async applyPerformanceProfile(profileName) {
    if (!this.performanceProfiles[profileName]) {
      throw new Error(`Unknown performance profile: ${profileName}`);
    }

    const profile = this.performanceProfiles[profileName];
    const previousProfile = this.currentProfile;
    
    try {
      this.emit('profile-applying', { profile: profileName, config: profile });
      
      // Update VM configuration
      if (this.vmBridge) {
        const vmConfig = {
          memory: profile.memory,
          cores: profile.cores,
          enableAppleFeatures: profile.quality !== 'low',
          enableEncryption: true
        };
        
        this.vmBridge.updateConfig(vmConfig);
      }
      
      // Apply system-level optimizations
      await this.applySystemOptimizations(profile);
      
      // Apply network optimizations
      await this.applyNetworkOptimizations(profile);
      
      // Apply disk optimizations
      await this.applyDiskOptimizations(profile);
      
      this.currentProfile = profileName;
      this.saveSettings();
      
      this.emit('profile-applied', {
        previous: previousProfile,
        current: profileName,
        config: profile
      });
      
      console.log(`Applied performance profile: ${profile.name}`);
      
    } catch (error) {
      this.emit('profile-apply-failed', { profile: profileName, error });
      throw error;
    }
  }

  // Apply system-level optimizations
  async applySystemOptimizations(profile) {
    try {
      // Set CPU priority for QEMU process
      if (this.vmManager && this.vmManager.vmProcess) {
        const priority = profile.cpuLimit > 80 ? 'high' : 'normal';
        await this.setCPUPriority(this.vmManager.vmProcess.pid, priority);
      }
      
      // Configure power settings
      await this.configurePowerSettings(profile);
      
    } catch (error) {
      console.error('Failed to apply system optimizations:', error);
    }
  }

  // Apply network optimizations
  async applyNetworkOptimizations(profile) {
    try {
      // Configure network QoS
      const qosSettings = {
        'low': { bandwidth: '10M', latency: 'high' },
        'medium': { bandwidth: '50M', latency: 'medium' },
        'high': { bandwidth: '100M', latency: 'low' },
        'highest': { bandwidth: 'unlimited', latency: 'lowest' }
      };
      
      const settings = qosSettings[profile.networkQoS] || qosSettings['medium'];
      
      // Apply network settings (implementation would depend on specific requirements)
      console.log(`Applying network QoS: ${profile.networkQoS}`, settings);
      
    } catch (error) {
      console.error('Failed to apply network optimizations:', error);
    }
  }

  // Apply disk optimizations
  async applyDiskOptimizations(profile) {
    try {
      if (profile.ioThrottle) {
        // Enable I/O throttling for better system responsiveness
        console.log('Enabling I/O throttling');
      } else {
        // Disable I/O throttling for maximum performance
        console.log('Disabling I/O throttling');
      }
      
    } catch (error) {
      console.error('Failed to apply disk optimizations:', error);
    }
  }

  // Set CPU priority for process
  async setCPUPriority(pid, priority) {
    return new Promise((resolve) => {
      const priorityMap = {
        'low': 'belownormal',
        'normal': 'normal',
        'high': 'abovenormal',
        'realtime': 'realtime'
      };
      
      const windowsPriority = priorityMap[priority] || 'normal';
      exec(`wmic process where processid=${pid} CALL setpriority ${windowsPriority}`, (error) => {
        if (error) {
          console.error('Failed to set CPU priority:', error);
        }
        resolve();
      });
    });
  }

  // Configure power settings
  async configurePowerSettings(profile) {
    return new Promise((resolve) => {
      const powerPlan = profile.cpuLimit > 80 ? 'high performance' : 'balanced';
      exec(`powercfg /setactive SCHEME_${powerPlan.toUpperCase().replace(' ', '_')}`, (error) => {
        if (error) {
          console.error('Failed to set power plan:', error);
        }
        resolve();
      });
    });
  }

  // Get available performance profiles
  getAvailableProfiles() {
    const systemSpecs = this.getSystemSpecs();
    const availableProfiles = {};
    
    Object.entries(this.performanceProfiles).forEach(([key, profile]) => {
      const memoryRequired = parseInt(profile.memory);
      const coresRequired = profile.cores;
      
      const isAvailable = systemSpecs.totalMemory >= memoryRequired + 4 && // +4GB for host system
                         systemSpecs.cpuCores >= coresRequired;
      
      availableProfiles[key] = {
        ...profile,
        available: isAvailable,
        reason: !isAvailable ? 'Insufficient system resources' : null
      };
    });
    
    return availableProfiles;
  }

  // Get current performance profile
  getCurrentProfile() {
    return {
      name: this.currentProfile,
      config: this.performanceProfiles[this.currentProfile],
      metrics: this.getCurrentAverageMetrics(),
      systemSpecs: this.getSystemSpecs()
    };
  }

  // Enable/disable adaptive optimization
  setAdaptiveOptimization(enabled) {
    this.adaptiveOptimization = enabled;
    this.saveSettings();
    
    if (enabled) {
      console.log('Adaptive optimization enabled');
    } else {
      console.log('Adaptive optimization disabled');
    }
    
    this.emit('adaptive-optimization-changed', enabled);
  }

  // Create custom performance profile
  createCustomProfile(name, config) {
    this.performanceProfiles[name] = {
      name: config.displayName || name,
      ...config
    };
    
    this.emit('custom-profile-created', { name, config });
  }

  // Delete custom performance profile
  deleteCustomProfile(name) {
    if (this.performanceProfiles[name] && !['power-saver', 'balanced', 'performance', 'maximum'].includes(name)) {
      delete this.performanceProfiles[name];
      
      if (this.currentProfile === name) {
        this.currentProfile = 'balanced';
        this.saveSettings();
      }
      
      this.emit('custom-profile-deleted', name);
    }
  }

  // Get performance recommendations
  getPerformanceRecommendations() {
    const metrics = this.getCurrentAverageMetrics();
    const systemSpecs = this.getSystemSpecs();
    const recommendations = [];
    
    // CPU recommendations
    if (metrics.cpu > 80) {
      recommendations.push({
        type: 'cpu',
        severity: 'high',
        message: 'High CPU usage detected. Consider reducing VM cores or switching to power-saver mode.',
        action: 'reduce_cpu_allocation'
      });
    }
    
    // Memory recommendations
    if (metrics.memory > 85) {
      recommendations.push({
        type: 'memory',
        severity: 'high',
        message: 'High memory usage detected. Consider reducing VM memory allocation.',
        action: 'reduce_memory_allocation'
      });
    }
    
    // System upgrade recommendations
    if (systemSpecs.totalMemory < 16) {
      recommendations.push({
        type: 'hardware',
        severity: 'medium',
        message: 'Consider upgrading to 16GB+ RAM for better VM performance.',
        action: 'upgrade_memory'
      });
    }
    
    if (systemSpecs.cpuCores < 6) {
      recommendations.push({
        type: 'hardware',
        severity: 'medium',
        message: 'Consider upgrading to a CPU with 6+ cores for better VM performance.',
        action: 'upgrade_cpu'
      });
    }
    
    return recommendations;
  }

  // Get performance statistics
  getPerformanceStats() {
    const metrics = this.performanceMetrics;
    const stats = {};
    
    Object.keys(metrics).forEach(key => {
      if (metrics[key].length > 0) {
        const values = metrics[key];
        stats[key] = {
          current: values[values.length - 1],
          average: values.reduce((sum, val) => sum + val, 0) / values.length,
          min: Math.min(...values),
          max: Math.max(...values),
          trend: this.calculateTrend(values)
        };
      }
    });
    
    return stats;
  }

  // Calculate trend for metrics
  calculateTrend(values) {
    if (values.length < 2) return 'stable';
    
    const recent = values.slice(-10);
    const older = values.slice(-20, -10);
    
    if (older.length === 0) return 'stable';
    
    const recentAvg = recent.reduce((sum, val) => sum + val, 0) / recent.length;
    const olderAvg = older.reduce((sum, val) => sum + val, 0) / older.length;
    
    const change = ((recentAvg - olderAvg) / olderAvg) * 100;
    
    if (change > 10) return 'increasing';
    if (change < -10) return 'decreasing';
    return 'stable';
  }

  // Cleanup and stop
  stop() {
    this.stopPerformanceMonitoring();
    this.emit('optimizer-stopped');
  }
}

module.exports = VMPerformanceOptimizer;
