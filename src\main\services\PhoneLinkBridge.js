
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const { EventEmitter } = require('events');
const chokidar = require('chokidar');

class PhoneLinkBridge extends EventEmitter {
  constructor() {
    super();
    this.basePath = 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\Microsoft.YourPhone_8wekyb3d8bbwe\\LocalCache\\Indexed\\e1e8a106-3e02-48fa-b8d7-a055e20bd025\\System\\Database';
    this.isConnected = false;

    // Unison-style local storage
    this.localDB = null;
    this.localDBPath = path.join(process.env.APPDATA, 'iPhone-Companion-Pro', 'unison-data.db');
    this.watcher = null;
  }

  async connect() {
    console.log('🔌 Connecting to Phone Link with Unison-style persistence...');

    // Initialize local database (like Unison)
    await this.initLocalDatabase();

    // Start monitoring Phone Link
    this.startPhoneLinkMonitoring();

    this.isConnected = true;
    this.emit('connected');
    return true;
  }

  async initLocalDatabase() {
    try {
      // Create Unison-style local database
      const dir = path.dirname(this.localDBPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      this.localDB = new sqlite3.Database(this.localDBPath);

      // Create tables exactly like Intel Unison
      await new Promise((resolve, reject) => {
        this.localDB.serialize(() => {
          this.localDB.run(`
            CREATE TABLE IF NOT EXISTS messages (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              thread_id TEXT,
              phone_number TEXT,
              contact_name TEXT,
              message_text TEXT,
              timestamp INTEGER,
              is_outgoing INTEGER DEFAULT 0,
              is_read INTEGER DEFAULT 1,
              is_mms INTEGER DEFAULT 0,
              UNIQUE(phone_number, message_text, timestamp)
            )
          `, (err) => {
            if (err) console.error('Error creating messages table:', err);
          });

          this.localDB.run(`
            CREATE TABLE IF NOT EXISTS message_threads (
              thread_id TEXT PRIMARY KEY,
              phone_number TEXT,
              contact_name TEXT,
              last_message TEXT,
              last_timestamp INTEGER,
              unread_count INTEGER DEFAULT 0
            )
          `, (err) => {
            if (err) console.error('Error creating message_threads table:', err);
          });

          this.localDB.run(`
            CREATE TABLE IF NOT EXISTS contacts (
              phone_number TEXT PRIMARY KEY,
              display_name TEXT,
              photo_path TEXT,
              last_contacted INTEGER
            )
          `, (err) => {
            if (err) console.error('Error creating contacts table:', err);
            resolve();
          });
        });
      });

      console.log('✅ Local Unison-style database initialized');
    } catch (error) {
      console.error('❌ Failed to initialize local database:', error);
      throw error;
    }
  }

  startPhoneLinkMonitoring() {
    // Monitor Phone Link databases for changes
    try {
      if (fs.existsSync(this.basePath)) {
        this.watcher = chokidar.watch(this.basePath, {
          persistent: true,
          ignoreInitial: true,
          usePolling: true,
          interval: 5000
        });

        this.watcher.on('change', async (path) => {
          console.log('📱 Phone Link data changed, syncing...');
          await this.syncFromPhoneLink();
        });

        console.log('✅ Phone Link monitoring started');
      } else {
        console.log('📱 Phone Link path not found, will sync manually');
      }
    } catch (error) {
      console.log('📱 Phone Link monitoring not available, using manual sync:', error.message);
    }
  }

  async syncFromPhoneLink() {
    try {
      // Get contacts from Phone Link
      const contacts = await this.getContacts();

      // Save to local DB (Unison-style)
      for (const contact of contacts) {
        await this.saveContactLocal(contact);
      }

      // Get calls
      const calls = await this.getCallHistory();

      // Emit updates
      this.emit('data-synced', { contacts: contacts.length, calls: calls.length });
      console.log(`📊 Synced ${contacts.length} contacts, ${calls.length} calls`);
    } catch (error) {
      console.log('📱 Sync failed:', error.message);
    }
  }

  async saveContactLocal(contact) {
    return new Promise((resolve, reject) => {
      this.localDB.run(
        'INSERT OR REPLACE INTO contacts (phone_number, display_name) VALUES (?, ?)',
        [contact.phoneNumber || '', contact.name || ''],
        resolve
      );
    });
  }

  async getContacts() {
    const dbPath = path.join(this.basePath, 'contacts.db');

    return new Promise((resolve, reject) => {
      // Check if Phone Link database exists
      if (!fs.existsSync(dbPath)) {
        console.log('📱 Phone Link contacts.db not found, checking alternative locations...');
        resolve([]);
        return;
      }

      const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
        if (err) {
          console.log('📱 Could not open Phone Link contacts.db:', err.message);
          resolve([]);
          return;
        }

        // First, check what tables exist in the database
        db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {
          if (err) {
            console.log('📱 Could not read Phone Link database schema:', err.message);
            db.close();
            resolve([]);
            return;
          }

          console.log('📱 Phone Link database tables:', tables.map(t => t.name));

          // Try different table names that Phone Link might use
          const possibleTables = ['contacts', 'contact', 'phonebook', 'addressbook'];
          const availableTable = possibleTables.find(tableName =>
            tables.some(t => t.name.toLowerCase() === tableName.toLowerCase())
          );

          if (!availableTable) {
            console.log('📱 No recognized contact tables found in Phone Link database');
            db.close();
            resolve([]);
            return;
          }

          // Try to read from the found table
          db.all(`SELECT * FROM ${availableTable} LIMIT 10`, (err, rows) => {
            if (err) {
              console.log(`📱 Could not read from ${availableTable} table:`, err.message);
              resolve([]);
            } else {
              console.log(`📱 Found ${rows.length} rows in ${availableTable} table`);
              if (rows.length > 0) {
                console.log('📱 Sample row structure:', Object.keys(rows[0]));
              }

              const contacts = rows.map(row => ({
                id: row.contact_id || row.id || row.rowid,
                name: row.display_name || row.name || row.full_name || row.first_name || 'Unknown',
                phoneNumber: row.phone_number || row.number || row.phone || '',
                thumbnail: row.thumbnail || row.photo
              })).filter(contact => contact.name !== 'My Number' && contact.phoneNumber);

              console.log(`📱 Processed ${contacts.length} valid contacts from Phone Link`);
              resolve(contacts);
            }
            db.close();
          });
        });
      });
    });
  }

  async getCallHistory() {
    const dbPath = path.join(this.basePath, 'calling.db');
    
    return new Promise((resolve, reject) => {
      const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
        if (err) {
          console.error('Error opening calling.db:', err);
          resolve([]);
          return;
        }
        
        db.all("SELECT * FROM call_history ORDER BY start_time DESC LIMIT 50", (err, rows) => {
          if (err) {
            console.error('Error reading call history:', err);
            resolve([]);
          } else {
            const calls = rows.map(row => ({
              id: row.id || Date.now(),
              phoneNumber: row.phone_number || '',
              contactName: row.display_name || row.phone_number || 'Unknown',
              timestamp: row.start_time || Date.now(),
              duration: row.duration || 0,
              type: row.call_type || 'unknown',
              isIncoming: row.is_incoming || false,
              isMissed: row.is_missed || false
            }));
            resolve(calls);
          }
          db.close();
        });
      });
    });
  }

  async getMessages() {
    // For now, return empty until we find message database
    console.log('📱 Messages database not found in standard location');
    return [];
  }

  async getMessagesForCRM() {
    // Return messages in CRM-friendly format
    return new Promise((resolve, reject) => {
      this.localDB.all(
        'SELECT * FROM messages ORDER BY timestamp DESC',
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
  }

  async sendMessage(phoneNumber, text) {
    // Save to local DB immediately (like Unison)
    return new Promise((resolve, reject) => {
      this.localDB.run(
        'INSERT INTO messages (phone_number, message_text, timestamp, is_outgoing) VALUES (?, ?, ?, 1)',
        [phoneNumber, text, Date.now()],
        (err) => {
          if (err) reject(err);
          else {
            this.emit('message-sent', { phoneNumber, text });
            resolve({ success: true });
          }
        }
      );
    });
  }

  // Get all messages for a specific phone number
  async getMessagesForPhone(phoneNumber) {
    return new Promise((resolve, reject) => {
      this.localDB.all(
        'SELECT * FROM messages WHERE phone_number = ? ORDER BY timestamp ASC',
        [phoneNumber],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
  }

  // Get all contacts from local database
  async getLocalContacts() {
    return new Promise((resolve, reject) => {
      this.localDB.all(
        'SELECT * FROM contacts ORDER BY display_name',
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
  }

  // Close database connection
  close() {
    if (this.watcher) {
      this.watcher.close();
    }
    if (this.localDB) {
      this.localDB.close();
    }
  }
}

module.exports = { PhoneLinkBridge };
