const assert = require('assert');
const { EventEmitter } = require('events');
const path = require('path');
const fs = require('fs');

// Import VM Bridge components
const MacOSVMBridge = require('../src/main/services/MacOSVMBridge');
const VMManager = require('../src/main/services/VMManager');
const WebSocketTunnel = require('../src/main/services/WebSocketTunnel');
const MessagesDBAccess = require('../src/main/services/MessagesDBAccess');
const VMHealthMonitor = require('../src/main/services/VMHealthMonitor');
const VMErrorRecovery = require('../src/main/services/VMErrorRecovery');
const VMPerformanceOptimizer = require('../src/main/services/VMPerformanceOptimizer');
const VMSecurity = require('../src/main/services/VMSecurity');

class VMBridgeTestSuite {
  constructor() {
    this.testResults = [];
    this.mockServices = {};
    this.setupMocks();
  }

  // Setup mock services for testing
  setupMocks() {
    // Mock VM Bridge
    this.mockServices.vmBridge = new EventEmitter();
    this.mockServices.vmBridge.getStatus = () => ({
      vmStatus: 'running',
      bridgeConnected: true,
      config: { memory: '8G', cores: 4 }
    });
    this.mockServices.vmBridge.updateConfig = () => {};
    this.mockServices.vmBridge.executeInVM = () => Promise.resolve('success');
    this.mockServices.vmBridge.createWebSocketTunnel = () => Promise.resolve();

    // Mock VM Manager
    this.mockServices.vmManager = new EventEmitter();
    this.mockServices.vmManager.isVMRunning = () => true;
    this.mockServices.vmManager.createOptimizedMacOSVM = () => Promise.resolve({});
    this.mockServices.vmManager.stopVM = () => Promise.resolve();
    this.mockServices.vmManager.getVMMetrics = () => Promise.resolve({
      memory: 4096,
      cpuTime: '00:10:30'
    });

    // Mock Health Monitor
    this.mockServices.healthMonitor = new EventEmitter();
    this.mockServices.healthMonitor.updateThresholds = () => {};
  }

  // Run all tests
  async runAllTests() {
    console.log('Starting VM Bridge Test Suite...\n');

    const testSuites = [
      { name: 'VM Bridge Core Tests', tests: this.runVMBridgeTests.bind(this) },
      { name: 'VM Manager Tests', tests: this.runVMManagerTests.bind(this) },
      { name: 'WebSocket Tunnel Tests', tests: this.runWebSocketTests.bind(this) },
      { name: 'Messages DB Tests', tests: this.runMessagesDBTests.bind(this) },
      { name: 'Health Monitor Tests', tests: this.runHealthMonitorTests.bind(this) },
      { name: 'Error Recovery Tests', tests: this.runErrorRecoveryTests.bind(this) },
      { name: 'Performance Optimizer Tests', tests: this.runPerformanceTests.bind(this) },
      { name: 'Security Tests', tests: this.runSecurityTests.bind(this) },
      { name: 'Integration Tests', tests: this.runIntegrationTests.bind(this) }
    ];

    for (const suite of testSuites) {
      console.log(`\n=== ${suite.name} ===`);
      try {
        await suite.tests();
      } catch (error) {
        console.error(`Test suite failed: ${error.message}`);
      }
    }

    this.printTestSummary();
    return this.testResults;
  }

  // VM Bridge core functionality tests
  async runVMBridgeTests() {
    await this.test('VM Bridge Initialization', async () => {
      const vmBridge = new MacOSVMBridge();
      assert(vmBridge instanceof EventEmitter, 'VM Bridge should be an EventEmitter');
      assert(typeof vmBridge.setupVMBridge === 'function', 'Should have setupVMBridge method');
    });

    await this.test('VM Configuration Update', async () => {
      const vmBridge = new MacOSVMBridge();
      const newConfig = { memory: '6G', cores: 2 };
      vmBridge.updateConfig(newConfig);
      assert(vmBridge.vmConfig.memory === '6G', 'Memory should be updated');
      assert(vmBridge.vmConfig.cores === 2, 'Cores should be updated');
    });

    await this.test('VM Status Retrieval', async () => {
      const vmBridge = new MacOSVMBridge();
      const status = vmBridge.getStatus();
      assert(typeof status === 'object', 'Status should be an object');
      assert('vmStatus' in status, 'Status should include vmStatus');
      assert('bridgeConnected' in status, 'Status should include bridgeConnected');
    });
  }

  // VM Manager tests
  async runVMManagerTests() {
    await this.test('VM Manager Initialization', async () => {
      const vmConfig = {
        vmDir: '/tmp/test-vm',
        diskPath: '/tmp/test-vm/disk.qcow2'
      };
      const vmManager = new VMManager(vmConfig);
      assert(vmManager instanceof EventEmitter, 'VM Manager should be an EventEmitter');
    });

    await this.test('QEMU Arguments Building', async () => {
      const vmConfig = {
        memory: '8G',
        cores: 4,
        bridgePort: 8080,
        vmDir: '/tmp/test-vm',
        diskPath: '/tmp/test-vm/disk.qcow2'
      };
      const vmManager = new VMManager(vmConfig);
      const args = vmManager.buildQemuArguments();
      assert(Array.isArray(args), 'QEMU arguments should be an array');
      assert(args.includes('-m'), 'Should include memory argument');
      assert(args.includes('8G'), 'Should include memory value');
    });

    await this.test('VM Running Check', async () => {
      const vmManager = new VMManager({ vmDir: '/tmp/test-vm' });
      const isRunning = vmManager.isVMRunning();
      assert(typeof isRunning === 'boolean', 'isVMRunning should return boolean');
    });
  }

  // WebSocket tunnel tests
  async runWebSocketTests() {
    await this.test('WebSocket Tunnel Initialization', async () => {
      const config = { bridgePort: 8080 };
      const tunnel = new WebSocketTunnel(config);
      assert(tunnel instanceof EventEmitter, 'WebSocket Tunnel should be an EventEmitter');
      assert(tunnel.config.bridgePort === 8080, 'Should store configuration');
    });

    await this.test('Message Encryption/Decryption', async () => {
      const tunnel = new WebSocketTunnel({ bridgePort: 8080 });
      tunnel.generateEncryptionKey();
      
      const testMessage = { type: 'test', data: 'hello world' };
      const encrypted = tunnel.encryptMessage(testMessage);
      const decrypted = tunnel.decryptMessage(encrypted);
      
      assert.deepEqual(decrypted, testMessage, 'Decrypted message should match original');
    });

    await this.test('Request/Response Handling', async () => {
      const tunnel = new WebSocketTunnel({ bridgePort: 8080 });
      
      // Simulate pending request
      const requestId = 'test-request-123';
      tunnel.pendingRequests.set(requestId, {
        resolve: (data) => data,
        reject: (error) => error
      });
      
      tunnel.resolveRequest(requestId, { success: true });
      assert(!tunnel.pendingRequests.has(requestId), 'Request should be removed after resolution');
    });
  }

  // Messages database tests
  async runMessagesDBTests() {
    await this.test('Messages DB Initialization', async () => {
      const messagesDB = new MessagesDBAccess(this.mockServices.vmBridge);
      assert(messagesDB instanceof EventEmitter, 'Messages DB should be an EventEmitter');
    });

    await this.test('SQL Query Building', async () => {
      const messagesDB = new MessagesDBAccess(this.mockServices.vmBridge);
      const query = 'SELECT * FROM message WHERE id = ?';
      const params = [123];
      const command = messagesDB.buildSQLiteCommand(query, params);
      
      assert(typeof command === 'string', 'Command should be a string');
      assert(command.includes('sqlite3'), 'Command should include sqlite3');
    });

    await this.test('Message Formatting', async () => {
      const messagesDB = new MessagesDBAccess(this.mockServices.vmBridge);
      const rawMessage = {
        id: 1,
        text: 'Hello',
        date: 694224000000000000, // Apple timestamp
        is_from_me: 1,
        phone_number: '+**********'
      };
      
      const formatted = messagesDB.formatMessage(rawMessage);
      assert(formatted.id === 1, 'ID should be preserved');
      assert(formatted.isFromMe === true, 'Boolean conversion should work');
      assert(typeof formatted.date === 'number', 'Date should be converted to Unix timestamp');
    });
  }

  // Health monitor tests
  async runHealthMonitorTests() {
    await this.test('Health Monitor Initialization', async () => {
      const healthMonitor = new VMHealthMonitor(
        this.mockServices.vmBridge,
        this.mockServices.vmManager
      );
      assert(healthMonitor instanceof EventEmitter, 'Health Monitor should be an EventEmitter');
    });

    await this.test('Health Evaluation', async () => {
      const healthMonitor = new VMHealthMonitor(
        this.mockServices.vmBridge,
        this.mockServices.vmManager
      );
      
      // Test healthy state
      healthMonitor.healthMetrics = {
        vmStatus: 'running',
        bridgeConnected: true,
        cpuUsage: 50,
        memoryUsage: 60,
        networkLatency: 100
      };
      
      const status = healthMonitor.evaluateOverallHealth();
      assert(status === 'healthy', 'Should evaluate as healthy');
    });

    await this.test('Threshold Checking', async () => {
      const healthMonitor = new VMHealthMonitor(
        this.mockServices.vmBridge,
        this.mockServices.vmManager
      );
      
      healthMonitor.healthMetrics = { cpuUsage: 95, memoryUsage: 95 };
      
      let warningEmitted = false;
      healthMonitor.on('high-cpu-usage', () => { warningEmitted = true; });
      
      healthMonitor.checkResourceThresholds();
      assert(warningEmitted, 'Should emit warning for high CPU usage');
    });
  }

  // Error recovery tests
  async runErrorRecoveryTests() {
    await this.test('Error Recovery Initialization', async () => {
      const errorRecovery = new VMErrorRecovery(
        this.mockServices.vmBridge,
        this.mockServices.vmManager,
        this.mockServices.healthMonitor
      );
      assert(errorRecovery instanceof EventEmitter, 'Error Recovery should be an EventEmitter');
    });

    await this.test('Recovery Strategy Selection', async () => {
      const errorRecovery = new VMErrorRecovery(
        this.mockServices.vmBridge,
        this.mockServices.vmManager,
        this.mockServices.healthMonitor
      );
      
      const strategies = errorRecovery.recoveryStrategies.get('VM_STARTUP_FAILED');
      assert(Array.isArray(strategies), 'Should return array of strategies');
      assert(strategies.length > 0, 'Should have recovery strategies');
      assert(strategies[0].priority === 1, 'First strategy should have priority 1');
    });

    await this.test('Backoff Delay Calculation', async () => {
      const errorRecovery = new VMErrorRecovery(
        this.mockServices.vmBridge,
        this.mockServices.vmManager,
        this.mockServices.healthMonitor
      );
      
      const delay1 = errorRecovery.calculateBackoffDelay(0);
      const delay2 = errorRecovery.calculateBackoffDelay(1);
      const delay3 = errorRecovery.calculateBackoffDelay(2);
      
      assert(delay2 > delay1, 'Delay should increase with attempt count');
      assert(delay3 > delay2, 'Delay should continue increasing');
    });
  }

  // Performance optimizer tests
  async runPerformanceTests() {
    await this.test('Performance Optimizer Initialization', async () => {
      const optimizer = new VMPerformanceOptimizer(
        this.mockServices.vmBridge,
        this.mockServices.vmManager,
        this.mockServices.healthMonitor
      );
      assert(optimizer instanceof EventEmitter, 'Performance Optimizer should be an EventEmitter');
    });

    await this.test('Performance Profile Selection', async () => {
      const optimizer = new VMPerformanceOptimizer(
        this.mockServices.vmBridge,
        this.mockServices.vmManager,
        this.mockServices.healthMonitor
      );
      
      const profiles = optimizer.getAvailableProfiles();
      assert(typeof profiles === 'object', 'Should return profiles object');
      assert('balanced' in profiles, 'Should include balanced profile');
      assert('performance' in profiles, 'Should include performance profile');
    });

    await this.test('Metrics Analysis', async () => {
      const optimizer = new VMPerformanceOptimizer(
        this.mockServices.vmBridge,
        this.mockServices.vmManager,
        this.mockServices.healthMonitor
      );
      
      const highUsageMetrics = { cpu: 90, memory: 95, disk: 80 };
      const recommendation = optimizer.analyzeAndRecommendProfile(highUsageMetrics);
      assert(recommendation === 'power-saver', 'Should recommend power-saver for high usage');
      
      const lowUsageMetrics = { cpu: 20, memory: 30, disk: 40 };
      const recommendation2 = optimizer.analyzeAndRecommendProfile(lowUsageMetrics);
      assert(['balanced', 'performance'].includes(recommendation2), 'Should recommend higher performance for low usage');
    });
  }

  // Security tests
  async runSecurityTests() {
    await this.test('Security Initialization', async () => {
      const security = new VMSecurity(this.mockServices.vmBridge);
      assert(security instanceof EventEmitter, 'Security should be an EventEmitter');
    });

    await this.test('Encryption/Decryption', async () => {
      const security = new VMSecurity(this.mockServices.vmBridge);
      await security.initializeMasterKey();
      
      const testData = { message: 'secret data', timestamp: Date.now() };
      const encrypted = security.encryptData(testData);
      const decrypted = security.decryptData(encrypted);
      
      assert.deepEqual(decrypted, testData, 'Decrypted data should match original');
    });

    await this.test('Session Management', async () => {
      const security = new VMSecurity(this.mockServices.vmBridge);
      
      const clientInfo = { username: 'test', ip: '127.0.0.1' };
      const session = security.generateSessionToken(clientInfo);
      
      assert(typeof session.sessionId === 'string', 'Should generate session ID');
      assert(typeof session.token === 'string', 'Should generate session token');
      
      const isValid = security.validateSessionToken(session.sessionId, session.token);
      assert(isValid === true, 'Should validate correct session token');
      
      const isInvalid = security.validateSessionToken(session.sessionId, 'wrong-token');
      assert(isInvalid === false, 'Should reject invalid session token');
    });
  }

  // Integration tests
  async runIntegrationTests() {
    await this.test('VM Bridge Full Workflow', async () => {
      // This would test the complete workflow from VM startup to message sync
      // For now, we'll test component integration
      
      const vmBridge = new MacOSVMBridge();
      const healthMonitor = new VMHealthMonitor(vmBridge, this.mockServices.vmManager);
      const errorRecovery = new VMErrorRecovery(vmBridge, this.mockServices.vmManager, healthMonitor);
      
      // Test event propagation
      let errorHandled = false;
      errorRecovery.on('error-detected', () => { errorHandled = true; });
      
      vmBridge.emit('error', new Error('Test error'));
      
      // Give time for event propagation
      await new Promise(resolve => setTimeout(resolve, 100));
      
      assert(errorHandled, 'Error should be handled by recovery system');
    });

    await this.test('Performance and Security Integration', async () => {
      const optimizer = new VMPerformanceOptimizer(
        this.mockServices.vmBridge,
        this.mockServices.vmManager,
        this.mockServices.healthMonitor
      );
      
      const security = new VMSecurity(this.mockServices.vmBridge);
      
      // Test that performance optimization doesn't break security
      await optimizer.applyPerformanceProfile('balanced');
      
      const testData = { test: 'data' };
      const encrypted = security.encryptData(testData);
      const decrypted = security.decryptData(encrypted);
      
      assert.deepEqual(decrypted, testData, 'Security should work after performance optimization');
    });
  }

  // Test helper method
  async test(name, testFunction) {
    try {
      await testFunction();
      console.log(`✓ ${name}`);
      this.testResults.push({ name, status: 'PASS', error: null });
    } catch (error) {
      console.log(`✗ ${name}: ${error.message}`);
      this.testResults.push({ name, status: 'FAIL', error: error.message });
    }
  }

  // Print test summary
  printTestSummary() {
    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    const total = this.testResults.length;
    
    console.log('\n=== Test Summary ===');
    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (failed > 0) {
      console.log('\nFailed Tests:');
      this.testResults
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(`  - ${r.name}: ${r.error}`));
    }
  }

  // Generate test report
  generateTestReport() {
    return {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.testResults.length,
        passed: this.testResults.filter(r => r.status === 'PASS').length,
        failed: this.testResults.filter(r => r.status === 'FAIL').length
      },
      results: this.testResults,
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
      }
    };
  }
}

// Export for use in other test files
module.exports = VMBridgeTestSuite;

// Run tests if this file is executed directly
if (require.main === module) {
  const testSuite = new VMBridgeTestSuite();
  testSuite.runAllTests().then(() => {
    const report = testSuite.generateTestReport();
    
    // Save test report
    const reportPath = path.join(__dirname, 'vm-bridge-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\nTest report saved to: ${reportPath}`);
    
    // Exit with appropriate code
    process.exit(report.summary.failed > 0 ? 1 : 0);
  }).catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });
}
