const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Global state
let currentConversation = null;
let conversations = [];
let messages = [];
let connectionStatus = 'disconnected';

// DOM elements
const conversationsList = document.getElementById('conversationsList');
const messagesList = document.getElementById('messagesList');
const messageInput = document.getElementById('messageInput');
const currentContact = document.getElementById('currentContact');
const connectionStatusEl = document.getElementById('connectionStatus');
const crmContact = document.getElementById('crmContact');
const crmMessageCount = document.getElementById('crmMessageCount');
const crmLastContact = document.getElementById('crmLastContact');

// Initialize the messages interface
document.addEventListener('DOMContentLoaded', () => {
    console.log('🎨 Modern Messages UI initialized');
    
    // Load initial data
    loadConversations();
    checkConnectionStatus();
    
    // Set up event listeners
    setupEventListeners();
    
    // Enable input when Enter is pressed
    messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
});

// Set up IPC event listeners
function setupEventListeners() {
    // Listen for real iPhone data
    ipcRenderer.on('messages-data', (event, data) => {
        console.log('📱 Received real iPhone messages:', data);
        updateConversations(data.conversations || []);
        updateConnectionStatus('connected');
    });
    
    ipcRenderer.on('new-message', (event, message) => {
        console.log('📨 New message received:', message);
        addNewMessage(message);
    });
    
    ipcRenderer.on('connection-status', (event, status) => {
        console.log('🔌 Connection status:', status);
        updateConnectionStatus(status.connected ? 'connected' : 'disconnected');
    });
    
    ipcRenderer.on('device-connected', (event, deviceInfo) => {
        console.log('📱 iPhone connected:', deviceInfo);
        updateConnectionStatus('connected');
        loadConversations();
    });
    
    ipcRenderer.on('device-disconnected', () => {
        console.log('📱 iPhone disconnected');
        updateConnectionStatus('disconnected');
    });
}

// Load conversations from iPhone
async function loadConversations() {
    try {
        console.log('📱 Loading conversations from iPhone...');
        
        // Request conversations from main process
        const result = await ipcRenderer.invoke('get-conversations');
        
        if (result.success && result.data) {
            updateConversations(result.data);
        } else {
            // Show sample conversations for demo
            showSampleConversations();
        }
    } catch (error) {
        console.error('❌ Failed to load conversations:', error);
        showSampleConversations();
    }
}

// Update conversations list
function updateConversations(conversationsData) {
    conversations = conversationsData;
    renderConversations();
}

// Render conversations in the sidebar
function renderConversations() {
    if (conversations.length === 0) {
        conversationsList.innerHTML = `
            <div class="loading">
                <div class="spinner"></div>
                No conversations found
            </div>
        `;
        return;
    }
    
    conversationsList.innerHTML = conversations.map(conv => `
        <div class="conversation-item" onclick="selectConversation('${conv.id}')" data-id="${conv.id}">
            <div class="contact-avatar">${getInitials(conv.contact.name)}</div>
            <div class="conversation-info">
                <div class="contact-name">${conv.contact.name}</div>
                <div class="last-message">${conv.lastMessage.text}</div>
            </div>
            <div class="timestamp">${formatTime(conv.lastMessage.timestamp)}</div>
            ${conv.unreadCount > 0 ? `<div class="unread-badge">${conv.unreadCount}</div>` : ''}
        </div>
    `).join('');
}

// Select a conversation
function selectConversation(conversationId) {
    currentConversation = conversations.find(c => c.id === conversationId);
    
    if (!currentConversation) return;
    
    // Update UI
    document.querySelectorAll('.conversation-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-id="${conversationId}"]`).classList.add('active');
    
    // Update header
    currentContact.textContent = currentConversation.contact.name;
    
    // Load messages for this conversation
    loadMessages(conversationId);
    
    // Update CRM panel
    updateCRMPanel(currentConversation);
    
    // Enable input
    messageInput.disabled = false;
    document.querySelector('.send-button').disabled = false;
}

// Load messages for a conversation
async function loadMessages(conversationId) {
    try {
        messagesList.innerHTML = `
            <div class="loading">
                <div class="spinner"></div>
                Loading messages...
            </div>
        `;
        
        // Request messages from main process
        const result = await ipcRenderer.invoke('get-messages', { conversationId });
        
        if (result.success && result.data) {
            messages = result.data;
            renderMessages();
        } else {
            // Show sample messages
            showSampleMessages();
        }
    } catch (error) {
        console.error('❌ Failed to load messages:', error);
        showSampleMessages();
    }
}

// Render messages
function renderMessages() {
    if (messages.length === 0) {
        messagesList.innerHTML = `
            <div class="loading">
                No messages in this conversation
            </div>
        `;
        return;
    }
    
    messagesList.innerHTML = messages.map(msg => `
        <div class="message ${msg.sent ? 'sent' : 'received'}">
            <div class="message-bubble">
                ${msg.text}
                <div class="message-time">${formatTime(msg.timestamp)}</div>
            </div>
        </div>
    `).join('');
    
    // Scroll to bottom
    messagesList.scrollTop = messagesList.scrollHeight;
}

// Send a message
async function sendMessage() {
    const text = messageInput.value.trim();
    if (!text || !currentConversation) return;
    
    try {
        console.log('📤 Sending message:', text);
        
        // Add message to UI immediately
        const tempMessage = {
            id: Date.now(),
            text: text,
            sent: true,
            timestamp: new Date(),
            status: 'sending'
        };
        
        messages.push(tempMessage);
        renderMessages();
        messageInput.value = '';
        
        // Send via main process
        const result = await ipcRenderer.invoke('send-message', {
            conversationId: currentConversation.id,
            phoneNumber: currentConversation.contact.phoneNumber,
            text: text
        });
        
        if (result.success) {
            console.log('✅ Message sent successfully');
            // Update message status
            tempMessage.status = 'sent';
            renderMessages();
        } else {
            console.error('❌ Failed to send message:', result.error);
            tempMessage.status = 'failed';
            renderMessages();
        }
    } catch (error) {
        console.error('❌ Error sending message:', error);
    }
}

// Add new incoming message
function addNewMessage(message) {
    if (currentConversation && message.conversationId === currentConversation.id) {
        messages.push(message);
        renderMessages();
    }
    
    // Update conversation list
    loadConversations();
}

// Update connection status
function updateConnectionStatus(status) {
    connectionStatus = status;
    connectionStatusEl.className = `connection-status ${status}`;
    
    switch (status) {
        case 'connected':
            connectionStatusEl.textContent = 'Connected to iPhone';
            break;
        case 'connecting':
            connectionStatusEl.textContent = 'Connecting to iPhone...';
            break;
        default:
            connectionStatusEl.textContent = 'Disconnected from iPhone';
    }
}

// Check connection status
async function checkConnectionStatus() {
    try {
        const result = await ipcRenderer.invoke('get-device-info');
        updateConnectionStatus(result.success ? 'connected' : 'disconnected');
    } catch (error) {
        updateConnectionStatus('disconnected');
    }
}

// Update CRM panel
function updateCRMPanel(conversation) {
    crmContact.textContent = conversation.contact.name;
    crmMessageCount.textContent = conversation.messageCount || 0;
    crmLastContact.textContent = formatTime(conversation.lastMessage.timestamp);
}

// CRM Integration functions
function openInCRM() {
    if (!currentConversation) return;
    
    console.log('🔗 Opening contact in CRM:', currentConversation.contact);
    // Integrate with your CRM system here
    alert(`Opening ${currentConversation.contact.name} in CRM`);
}

function exportConversation() {
    if (!currentConversation) return;
    
    console.log('📄 Exporting conversation:', currentConversation.id);
    // Export conversation data
    const exportData = {
        contact: currentConversation.contact,
        messages: messages,
        exportDate: new Date()
    };
    
    // Save to file or send to CRM
    alert(`Exporting conversation with ${currentConversation.contact.name}`);
}

// Utility functions
function getInitials(name) {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
}

function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h`;
    return date.toLocaleDateString();
}

// NO MORE SAMPLE DATA - REAL IPHONE DATA ONLY
function showRealConversations() {
    console.log('📱 Loading real iPhone conversations only...');
    // Request real conversations from main process
    if (window.electronAPI) {
        window.electronAPI.getConversations();
    }
}

function showRealMessages(conversationId) {
    console.log('📱 Loading real iPhone messages only...');
    // Request real messages from main process
    if (window.electronAPI) {
        window.electronAPI.getMessages(conversationId);
    }
}

console.log('🎨 Modern Messages UI script loaded');
