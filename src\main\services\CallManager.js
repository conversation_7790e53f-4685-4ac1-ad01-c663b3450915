const { EventEmitter } = require('events');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class CallManager extends EventEmitter {
  constructor(airPlayServer = null, webBridge = null) {
    super();
    this.airPlayServer = airPlayServer;
    this.webBridge = webBridge;
    this.currentCall = null;
    this.callHistory = [];
    this.contacts = new Map();
    this.audioProcess = null;
    this.isCallActive = false;
    this.callStartTime = null;
    this.integrationMethods = {
      airPlay: false,
      companionApp: false,
      webBridge: false
    };
  }

  async initialize() {
    console.log('Initializing call manager...');

    // Set up AirPlay integration if available
    if (this.airPlayServer) {
      this.setupAirPlayIntegration();
    }

    // Set up web bridge integration if available
    if (this.webBridge) {
      this.setupWebBridgeIntegration();
    }

    // Load call history and contacts
    this.loadCallHistory();
    this.loadContacts();

    console.log('Call manager initialized with integrations:', this.integrationMethods);
  }

  setupAirPlayIntegration() {
    if (!this.airPlayServer) return;

    this.airPlayServer.on('mirror-start', () => {
      this.integrationMethods.airPlay = true;
      console.log('AirPlay call integration enabled');
    });

    this.airPlayServer.on('mirror-stop', () => {
      this.integrationMethods.airPlay = false;
      console.log('AirPlay call integration disabled');
    });

    // Listen for call-related events from iPhone
    this.airPlayServer.on('call-incoming', (data) => {
      this.handleIncomingCall(data);
    });

    this.airPlayServer.on('call-outgoing', (data) => {
      this.handleOutgoingCall(data);
    });

    this.airPlayServer.on('call-ended', (data) => {
      this.handleCallEnded(data);
    });
  }

  setupWebBridgeIntegration() {
    if (!this.webBridge) return;

    this.webBridge.on('device-connected', () => {
      this.integrationMethods.webBridge = true;
      console.log('Web bridge call integration enabled');
    });

    this.webBridge.on('call-data', (data) => {
      this.handleWebBridgeCallData(data);
    });
  }

  // Call handling methods
  async makeCall(phoneNumber, contactName = null) {
    try {
      console.log(`Initiating call to ${phoneNumber}`);

      const callData = {
        id: Date.now().toString(),
        phoneNumber: phoneNumber,
        contactName: contactName || this.getContactName(phoneNumber),
        direction: 'outgoing',
        startTime: new Date(),
        status: 'dialing'
      };

      this.currentCall = callData;
      this.isCallActive = true;
      this.callStartTime = Date.now();

      // Try to initiate call through available integrations
      if (this.integrationMethods.companionApp) {
        await this.makeCallViaCompanionApp(phoneNumber);
      } else if (this.integrationMethods.airPlay) {
        await this.makeCallViaAirPlay(phoneNumber);
      } else {
        // Fallback: open phone app or show instructions
        this.showCallInstructions(phoneNumber, contactName);
      }

      this.emit('call-started', callData);
      return { success: true, callId: callData.id };

    } catch (error) {
      console.error('Failed to make call:', error);
      return { success: false, error: error.message };
    }
  }

  async makeCallViaCompanionApp(phoneNumber) {
    // Send call request to companion app
    if (this.webBridge && this.webBridge.connectedClients.size > 0) {
      const callRequest = {
        type: 'make_call',
        phoneNumber: phoneNumber,
        timestamp: Date.now()
      };

      this.webBridge.broadcast(callRequest);
    }
  }

  async makeCallViaAirPlay(phoneNumber) {
    // Send call command through AirPlay reverse connection
    if (this.airPlayServer && this.airPlayServer.reverseConnection) {
      const callCommand = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>category</key>
  <string>call</string>
  <key>action</key>
  <string>make_call</string>
  <key>phoneNumber</key>
  <string>${phoneNumber}</string>
</dict>
</plist>`;

      try {
        this.airPlayServer.reverseConnection.write(`POST /call HTTP/1.1\r\n`);
        this.airPlayServer.reverseConnection.write(`Content-Type: text/x-apple-plist+xml\r\n`);
        this.airPlayServer.reverseConnection.write(`Content-Length: ${Buffer.byteLength(callCommand)}\r\n`);
        this.airPlayServer.reverseConnection.write(`\r\n`);
        this.airPlayServer.reverseConnection.write(callCommand);
      } catch (error) {
        console.error('Error sending call command via AirPlay:', error);
      }
    }
  }

  showCallInstructions(phoneNumber, contactName) {
    // Emit event to show call instructions in UI
    this.emit('show-call-instructions', {
      phoneNumber,
      contactName,
      instructions: [
        'Pick up your iPhone',
        'Open the Phone app',
        `Call ${contactName || phoneNumber}`,
        'The call will be managed through iPhone Companion Pro'
      ]
    });
  }

  handleIncomingCall(callData) {
    console.log('Incoming call from:', callData.phoneNumber);

    const call = {
      id: Date.now().toString(),
      phoneNumber: callData.phoneNumber,
      contactName: this.getContactName(callData.phoneNumber),
      direction: 'incoming',
      startTime: new Date(),
      status: 'ringing'
    };

    this.currentCall = call;
    this.emit('incoming-call', call);
  }

  handleOutgoingCall(callData) {
    console.log('Outgoing call to:', callData.phoneNumber);

    if (this.currentCall && this.currentCall.phoneNumber === callData.phoneNumber) {
      this.currentCall.status = 'connected';
      this.emit('call-connected', this.currentCall);
    }
  }

  handleCallEnded(callData) {
    console.log('Call ended:', callData);

    if (this.currentCall) {
      const endTime = new Date();
      const duration = this.callStartTime ? Math.floor((Date.now() - this.callStartTime) / 1000) : 0;

      this.currentCall.endTime = endTime;
      this.currentCall.duration = duration;
      this.currentCall.status = 'ended';

      // Add to call history
      this.addToCallHistory(this.currentCall);

      this.emit('call-ended', this.currentCall);

      // Clean up
      this.currentCall = null;
      this.isCallActive = false;
      this.callStartTime = null;
    }
  }

  answerCall() {
    if (this.currentCall && this.currentCall.status === 'ringing') {
      this.currentCall.status = 'connected';
      this.callStartTime = Date.now();
      this.isCallActive = true;

      // Send answer command to iPhone
      this.sendCallCommand('answer');

      this.emit('call-answered', this.currentCall);
    }
  }

  declineCall() {
    if (this.currentCall && this.currentCall.status === 'ringing') {
      this.currentCall.status = 'declined';

      // Send decline command to iPhone
      this.sendCallCommand('decline');

      this.emit('call-declined', this.currentCall);

      this.currentCall = null;
      this.isCallActive = false;
    }
  }

  endCall() {
    if (this.currentCall && this.isCallActive) {
      // Send end call command to iPhone
      this.sendCallCommand('end');

      this.handleCallEnded({ phoneNumber: this.currentCall.phoneNumber });
    }
  }

  sendCallCommand(action) {
    // Send call control commands through available integrations
    if (this.integrationMethods.companionApp && this.webBridge) {
      const command = {
        type: 'call_control',
        action: action,
        timestamp: Date.now()
      };
      this.webBridge.broadcast(command);
    }

    if (this.integrationMethods.airPlay && this.airPlayServer && this.airPlayServer.reverseConnection) {
      const callCommand = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>category</key>
  <string>call</string>
  <key>action</key>
  <string>${action}</string>
</dict>
</plist>`;

      try {
        this.airPlayServer.reverseConnection.write(`POST /call HTTP/1.1\r\n`);
        this.airPlayServer.reverseConnection.write(`Content-Type: text/x-apple-plist+xml\r\n`);
        this.airPlayServer.reverseConnection.write(`Content-Length: ${Buffer.byteLength(callCommand)}\r\n`);
        this.airPlayServer.reverseConnection.write(`\r\n`);
        this.airPlayServer.reverseConnection.write(callCommand);
      } catch (error) {
        console.error('Error sending call command via AirPlay:', error);
      }
    }
  }

  // Utility methods
  getContactName(phoneNumber) {
    return this.contacts.get(phoneNumber) || phoneNumber;
  }

  addToCallHistory(call) {
    this.callHistory.unshift(call);
    
    // Keep only last 100 calls
    if (this.callHistory.length > 100) {
      this.callHistory = this.callHistory.slice(0, 100);
    }

    this.saveCallHistory();
  }

  getCallHistory() {
    return this.callHistory;
  }

  getCurrentCall() {
    return this.currentCall;
  }

  isInCall() {
    return this.isCallActive;
  }

  loadCallHistory() {
    // Load call history from storage
    try {
      const historyPath = path.join(__dirname, '../../../data/call-history.json');
      if (fs.existsSync(historyPath)) {
        const data = fs.readFileSync(historyPath, 'utf8');
        this.callHistory = JSON.parse(data);
      }
    } catch (error) {
      console.error('Error loading call history:', error);
      this.callHistory = [];
    }
  }

  saveCallHistory() {
    try {
      const dataDir = path.join(__dirname, '../../../data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      const historyPath = path.join(dataDir, 'call-history.json');
      fs.writeFileSync(historyPath, JSON.stringify(this.callHistory, null, 2));
    } catch (error) {
      console.error('Error saving call history:', error);
    }
  }

  loadContacts() {
    // Load contacts from storage
    try {
      const contactsPath = path.join(__dirname, '../../../data/contacts.json');
      if (fs.existsSync(contactsPath)) {
        const data = fs.readFileSync(contactsPath, 'utf8');
        const contactsArray = JSON.parse(data);
        this.contacts = new Map(contactsArray);
      }
    } catch (error) {
      console.error('Error loading contacts:', error);
      this.contacts = new Map();
    }
  }

  handleWebBridgeCallData(data) {
    switch (data.type) {
      case 'incoming_call':
        this.handleIncomingCall(data);
        break;
      case 'call_connected':
        this.handleOutgoingCall(data);
        break;
      case 'call_ended':
        this.handleCallEnded(data);
        break;
      default:
        console.log('Unknown call data type:', data.type);
    }
  }
}

module.exports = { CallManager };
