/**
 * Intel Unison++ Integration Test Suite
 * Comprehensive testing of all systems working together
 */

const path = require('path');
const fs = require('fs');
const { IntelUnisonApp } = require('../main/IntelUnisonApp');
const axios = require('axios');

class IntelUnisonIntegrationTest {
  constructor() {
    this.app = null;
    this.testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      errors: []
    };
    this.testData = {
      phoneNumbers: ['+1234567890', '+0987654321', '************'],
      messages: [
        'Hello, this is a test message from Intel Unison++',
        'Testing bidirectional sync functionality',
        'iPhone integration test message',
        'CRM sync validation message'
      ],
      contacts: [
        { name: 'Test Contact 1', phone: '+1234567890' },
        { name: 'Test Contact 2', phone: '+0987654321' },
        { name: 'Test Contact 3', phone: '************' }
      ]
    };
  }

  async runAllTests() {
    console.log('🔥 STARTING INTEL UNISON++ INTEGRATION TESTS 🔥');
    console.log('='.repeat(60));

    try {
      // Initialize application
      await this.initializeApplication();

      // Run test suites
      await this.testApplicationInitialization();
      await this.testDatabasePersistence();
      await this.testMessageExtraction();
      await this.testAPIEndpoints();
      await this.testRealTimeSync();
      await this.testBidirectionalSync();
      await this.testSearchFunctionality();
      await this.testDataIntegrity();
      await this.testPerformance();
      await this.testErrorHandling();

      // Generate test report
      this.generateTestReport();

    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      this.testResults.errors.push(`Test suite failure: ${error.message}`);
    } finally {
      // Cleanup
      await this.cleanup();
    }
  }

  async initializeApplication() {
    console.log('\n📱 Initializing Intel Unison++ Application...');

    this.app = new IntelUnisonApp();
    
    // Set up error handling
    this.app.on('service-error', (error) => {
      console.warn(`⚠️ Service error during tests: ${error.service} - ${error.error.message}`);
    });

    await this.app.initialize();
    await this.app.start();

    console.log('✅ Application initialized and started');
  }

  async testApplicationInitialization() {
    console.log('\n🔧 Testing Application Initialization...');

    await this.runTest('Application Status Check', async () => {
      const status = this.app.getApplicationStatus();
      
      this.assert(status.isInitialized, 'Application should be initialized');
      this.assert(status.isRunning, 'Application should be running');
      this.assert(status.services, 'Services should be available');
      this.assert(Object.keys(status.services).length > 0, 'Should have active services');
      
      console.log(`   📊 Services: ${Object.keys(status.services).length}`);
      console.log(`   ⏱️  Uptime: ${status.uptime}ms`);
    });

    await this.runTest('Service Health Check', async () => {
      const services = this.app.getServiceStatuses();
      
      for (const [serviceName, serviceStatus] of Object.entries(services)) {
        this.assert(
          serviceStatus.status !== 'error', 
          `Service ${serviceName} should not be in error state: ${serviceStatus.error || 'Unknown error'}`
        );
        console.log(`   ✅ ${serviceName}: ${serviceStatus.status || 'running'}`);
      }
    });
  }

  async testDatabasePersistence() {
    console.log('\n💾 Testing Database Persistence...');

    const persistence = this.app.getService('persistence');

    await this.runTest('Database Connection', async () => {
      this.assert(persistence, 'Persistence service should be available');
      this.assert(persistence.isInitialized, 'Database should be initialized');
    });

    await this.runTest('Message Storage and Retrieval', async () => {
      const testMessage = {
        threadId: this.testData.phoneNumbers[0],
        phoneNumber: this.testData.phoneNumbers[0],
        contactName: 'Test Contact',
        messageText: 'Database persistence test message',
        timestamp: Date.now(),
        isOutgoing: false,
        isDelivered: true,
        isRead: false,
        source: 'integration_test'
      };

      // Save message
      await persistence.saveMessage(testMessage);

      // Retrieve message
      const messages = await persistence.loadAllMessages(10);
      const savedMessage = messages.find(m => m.messageText === testMessage.messageText);

      this.assert(savedMessage, 'Message should be saved and retrievable');
      this.assert(savedMessage.phoneNumber === testMessage.phoneNumber, 'Phone number should match');
      this.assert(savedMessage.messageText === testMessage.messageText, 'Message text should match');
    });

    await this.runTest('Contact Storage', async () => {
      const testContact = {
        phoneNumber: this.testData.phoneNumbers[0],
        displayName: 'Integration Test Contact',
        firstName: 'Integration',
        lastName: 'Test',
        email: '<EMAIL>'
      };

      await persistence.saveContact(testContact);

      const contacts = await persistence.loadContacts();
      const savedContact = contacts.find(c => c.phoneNumber === testContact.phoneNumber);

      this.assert(savedContact, 'Contact should be saved and retrievable');
      this.assert(savedContact.displayName === testContact.displayName, 'Contact name should match');
    });

    await this.runTest('Database Stats', async () => {
      const stats = await persistence.getMessageStats();
      
      this.assert(stats.total_messages >= 0, 'Should have message count');
      this.assert(stats.total_threads >= 0, 'Should have thread count');
      
      console.log(`   📊 Messages: ${stats.total_messages}, Threads: ${stats.total_threads}`);
    });
  }

  async testMessageExtraction() {
    console.log('\n🔍 Testing Message Extraction...');

    const messageExtraction = this.app.getService('messageExtraction');

    await this.runTest('Extraction Service Status', async () => {
      this.assert(messageExtraction, 'Message extraction service should be available');
      
      const status = messageExtraction.getStatus();
      this.assert(status.isRunning, 'Message extraction should be running');
      this.assert(status.activeMethods.length > 0, 'Should have active extraction methods');
      
      console.log(`   🔍 Active methods: ${status.activeMethods.join(', ')}`);
    });

    await this.runTest('Message Processing', async () => {
      const testRawMessage = {
        phoneNumber: this.testData.phoneNumbers[1],
        messageText: 'Extracted message test',
        timestamp: Date.now(),
        source: 'test_extraction'
      };

      // Simulate message extraction
      await messageExtraction.processExtractedMessage(testRawMessage, 'test');

      // Wait a moment for processing
      await this.sleep(1000);

      // Verify message was processed
      const persistence = this.app.getService('persistence');
      const messages = await persistence.loadAllMessages(10);
      const extractedMessage = messages.find(m => m.messageText === testRawMessage.messageText);

      this.assert(extractedMessage, 'Extracted message should be saved');
    });
  }

  async testAPIEndpoints() {
    console.log('\n🌐 Testing API Endpoints...');

    const baseURL = 'http://localhost:7777';

    await this.runTest('Health Check Endpoint', async () => {
      const response = await axios.get(`${baseURL}/api/health`);
      
      this.assert(response.status === 200, 'Health check should return 200');
      this.assert(response.data.status === 'healthy', 'API should be healthy');
      this.assert(response.data.integrations, 'Should have integration status');
    });

    await this.runTest('Send Message Endpoint', async () => {
      const messageData = {
        to: this.testData.phoneNumbers[2],
        message: 'API test message'
      };

      const response = await axios.post(`${baseURL}/api/send`, messageData);
      
      this.assert(response.status === 200, 'Send message should return 200');
      this.assert(response.data.success, 'Message send should be successful');
      this.assert(response.data.messageId, 'Should return message ID');
    });

    await this.runTest('Get Messages Endpoint', async () => {
      const phoneNumber = this.testData.phoneNumbers[0];
      const response = await axios.get(`${baseURL}/api/messages/${phoneNumber}`);
      
      this.assert(response.status === 200, 'Get messages should return 200');
      this.assert(Array.isArray(response.data.messages), 'Should return messages array');
      this.assert(response.data.phoneNumber === phoneNumber, 'Phone number should match');
    });

    await this.runTest('Get Contacts Endpoint', async () => {
      const response = await axios.get(`${baseURL}/api/contacts`);
      
      this.assert(response.status === 200, 'Get contacts should return 200');
      this.assert(Array.isArray(response.data.contacts), 'Should return contacts array');
    });

    await this.runTest('Sync Status Endpoint', async () => {
      const response = await axios.get(`${baseURL}/api/sync/status`);
      
      this.assert(response.status === 200, 'Sync status should return 200');
      this.assert(response.data.success, 'Sync status should be successful');
      this.assert(response.data.syncStatus, 'Should have sync status data');
    });
  }

  async testRealTimeSync() {
    console.log('\n🔄 Testing Real-Time Sync...');

    const syncService = this.app.getService('syncService');

    await this.runTest('Sync Service Status', async () => {
      this.assert(syncService, 'Sync service should be available');
      
      const status = syncService.getSyncStatus();
      this.assert(status.isRunning, 'Sync service should be running');
      this.assert(status.connectedDevices >= 0, 'Should have device count');
      
      console.log(`   📱 Connected devices: ${status.connectedDevices}`);
    });

    await this.runTest('Message Sync', async () => {
      const messageService = this.app.getService('messageService');
      
      // Send a test message
      const testMessage = await messageService.sendMessage(
        this.testData.phoneNumbers[0], 
        'Real-time sync test message'
      );

      this.assert(testMessage, 'Message should be sent');
      this.assert(testMessage.phoneNumber === this.testData.phoneNumbers[0], 'Phone number should match');

      // Wait for sync
      await this.sleep(2000);

      // Verify sync occurred (check if message is in database)
      const persistence = this.app.getService('persistence');
      const messages = await persistence.loadMessagesForThread(this.testData.phoneNumbers[0], 10);
      const syncedMessage = messages.find(m => m.messageText === 'Real-time sync test message');

      this.assert(syncedMessage, 'Message should be synced to database');
    });
  }

  async testBidirectionalSync() {
    console.log('\n↔️ Testing Bidirectional Sync...');

    const bidirectionalSync = this.app.getService('bidirectionalSync');

    await this.runTest('Bidirectional Sync Status', async () => {
      this.assert(bidirectionalSync, 'Bidirectional sync service should be available');
      
      const status = bidirectionalSync.getSyncStatus();
      this.assert(status.isRunning, 'Bidirectional sync should be running');
      this.assert(status.syncDirections, 'Should have sync directions');
      
      console.log(`   📊 Queue size: ${status.queueSize}`);
    });

    await this.runTest('Cross-Platform Data Consistency', async () => {
      const messageService = this.app.getService('messageService');
      const persistence = this.app.getService('persistence');

      // Add a message through message service
      const testMessage = {
        phoneNumber: this.testData.phoneNumbers[1],
        contactName: 'Bidirectional Test',
        text: 'Bidirectional sync test message',
        timestamp: new Date(),
        isIncoming: true
      };

      await messageService.addMessage(testMessage);

      // Wait for bidirectional sync
      await this.sleep(3000);

      // Verify message exists in database
      const dbMessages = await persistence.loadMessagesForThread(testMessage.phoneNumber, 10);
      const syncedMessage = dbMessages.find(m => m.messageText === testMessage.text);

      this.assert(syncedMessage, 'Message should be synced across platforms');
    });
  }

  async testSearchFunctionality() {
    console.log('\n🔍 Testing Search Functionality...');

    const searchService = this.app.getService('searchService');

    await this.runTest('Search Service Initialization', async () => {
      this.assert(searchService, 'Search service should be available');
    });

    await this.runTest('Message Search', async () => {
      // First, add some searchable messages
      const persistence = this.app.getService('persistence');
      
      const searchableMessage = {
        threadId: this.testData.phoneNumbers[0],
        phoneNumber: this.testData.phoneNumbers[0],
        contactName: 'Search Test Contact',
        messageText: 'This is a unique searchable message for testing',
        timestamp: Date.now(),
        isOutgoing: false,
        source: 'search_test'
      };

      await persistence.saveMessage(searchableMessage);

      // Wait for FTS5 index update
      await this.sleep(1000);

      // Perform search
      const searchResults = await searchService.search('unique searchable message');

      this.assert(searchResults.messages, 'Search should return messages');
      this.assert(searchResults.messages.length > 0, 'Should find the test message');
      
      const foundMessage = searchResults.messages.find(m => 
        m.messageText.includes('unique searchable message')
      );
      this.assert(foundMessage, 'Should find the specific test message');

      console.log(`   🔍 Found ${searchResults.messages.length} messages`);
    });

    await this.runTest('Search Performance', async () => {
      const startTime = Date.now();
      const results = await searchService.search('test');
      const searchTime = Date.now() - startTime;

      this.assert(searchTime < 1000, `Search should be fast (${searchTime}ms < 1000ms)`);
      console.log(`   ⚡ Search completed in ${searchTime}ms`);
    });
  }

  async testDataIntegrity() {
    console.log('\n🔒 Testing Data Integrity...');

    const persistence = this.app.getService('persistence');

    await this.runTest('Data Consistency Check', async () => {
      // Get all messages and verify they have required fields
      const messages = await persistence.loadAllMessages(100);
      
      for (const message of messages) {
        this.assert(message.phoneNumber, 'Message should have phone number');
        this.assert(message.messageText, 'Message should have text');
        this.assert(message.timestamp, 'Message should have timestamp');
        this.assert(typeof message.isOutgoing === 'boolean', 'Message should have isOutgoing flag');
      }

      console.log(`   ✅ Verified ${messages.length} messages for data integrity`);
    });

    await this.runTest('Database Backup Verification', async () => {
      // Trigger a backup
      await persistence.createBackup();

      // Verify backup was created
      const backupDir = path.join(process.cwd(), 'backups');
      if (fs.existsSync(backupDir)) {
        const backups = fs.readdirSync(backupDir).filter(f => f.startsWith('backup-'));
        this.assert(backups.length > 0, 'Should have created backup files');
        console.log(`   💾 Found ${backups.length} backup files`);
      } else {
        console.log('   ⚠️ Backup directory not found (may be expected in test environment)');
      }
    });
  }

  async testPerformance() {
    console.log('\n⚡ Testing Performance...');

    await this.runTest('Message Processing Performance', async () => {
      const messageService = this.app.getService('messageService');
      const messageCount = 10;
      const startTime = Date.now();

      // Send multiple messages rapidly
      const promises = [];
      for (let i = 0; i < messageCount; i++) {
        promises.push(
          messageService.sendMessage(
            this.testData.phoneNumbers[0], 
            `Performance test message ${i}`
          )
        );
      }

      await Promise.all(promises);
      const totalTime = Date.now() - startTime;
      const avgTime = totalTime / messageCount;

      this.assert(avgTime < 500, `Average message processing should be fast (${avgTime}ms < 500ms)`);
      console.log(`   📊 Processed ${messageCount} messages in ${totalTime}ms (avg: ${avgTime}ms)`);
    });

    await this.runTest('Database Query Performance', async () => {
      const persistence = this.app.getService('persistence');
      const startTime = Date.now();

      // Perform multiple database operations
      await Promise.all([
        persistence.loadAllMessages(50),
        persistence.loadMessageThreads(),
        persistence.loadContacts(),
        persistence.getMessageStats()
      ]);

      const queryTime = Date.now() - startTime;
      this.assert(queryTime < 2000, `Database queries should be fast (${queryTime}ms < 2000ms)`);
      console.log(`   📊 Database queries completed in ${queryTime}ms`);
    });
  }

  async testErrorHandling() {
    console.log('\n🛡️ Testing Error Handling...');

    await this.runTest('Invalid API Requests', async () => {
      const baseURL = 'http://localhost:7777';

      try {
        // Test invalid phone number
        await axios.post(`${baseURL}/api/send`, {
          to: 'invalid-phone',
          message: 'Test'
        });
        this.assert(false, 'Should have thrown error for invalid phone number');
      } catch (error) {
        this.assert(error.response.status >= 400, 'Should return error status for invalid request');
      }

      try {
        // Test missing message content
        await axios.post(`${baseURL}/api/send`, {
          to: '+1234567890'
          // missing message
        });
        this.assert(false, 'Should have thrown error for missing message');
      } catch (error) {
        this.assert(error.response.status >= 400, 'Should return error status for missing message');
      }
    });

    await this.runTest('Service Recovery', async () => {
      // Test that services can recover from errors
      const messageService = this.app.getService('messageService');
      
      // This should not crash the service
      try {
        await messageService.sendMessage(null, null);
      } catch (error) {
        // Expected error
      }

      // Service should still be functional
      const status = this.app.getApplicationStatus();
      this.assert(status.isRunning, 'Application should still be running after error');
    });
  }

  async runTest(testName, testFunction) {
    this.testResults.total++;
    console.log(`\n   🧪 Running: ${testName}`);

    try {
      await testFunction();
      this.testResults.passed++;
      console.log(`   ✅ PASSED: ${testName}`);
    } catch (error) {
      this.testResults.failed++;
      const errorMessage = `${testName}: ${error.message}`;
      this.testResults.errors.push(errorMessage);
      console.log(`   ❌ FAILED: ${errorMessage}`);
    }
  }

  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  generateTestReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 INTEL UNISON++ INTEGRATION TEST REPORT');
    console.log('='.repeat(60));
    console.log(`📈 Total Tests: ${this.testResults.total}`);
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`📊 Success Rate: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%`);

    if (this.testResults.errors.length > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

    if (this.testResults.failed === 0) {
      console.log('\n🎉 ALL TESTS PASSED! Intel Unison++ is working perfectly!');
    } else {
      console.log(`\n⚠️ ${this.testResults.failed} test(s) failed. Review and fix issues.`);
    }

    console.log('='.repeat(60));
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up test environment...');

    try {
      if (this.app) {
        await this.app.stop();
        console.log('✅ Application stopped');
      }
    } catch (error) {
      console.log(`⚠️ Cleanup error: ${error.message}`);
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const testSuite = new IntelUnisonIntegrationTest();
  testSuite.runAllTests().then(() => {
    process.exit(testSuite.testResults.failed > 0 ? 1 : 0);
  }).catch((error) => {
    console.error('💥 Test suite crashed:', error);
    process.exit(1);
  });
}

module.exports = { IntelUnisonIntegrationTest };