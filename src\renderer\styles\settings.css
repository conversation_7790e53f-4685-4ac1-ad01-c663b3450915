/* Settings Styles */
.settings-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.settings-header h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.settings-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.settings-nav {
    width: 250px;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    overflow-y: auto;
}

.settings-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.settings-nav li {
    border-bottom: 1px solid var(--border-color);
}

.nav-link {
    display: block;
    padding: 1rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.2s ease;
}

.nav-link:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.nav-link.active {
    background: var(--accent-color);
    color: white;
}

.settings-main {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.settings-section {
    display: none;
}

.settings-section.active {
    display: block;
}

.settings-section h2 {
    margin: 0 0 2rem 0;
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
}

.setting-group {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.setting-group h3 {
    margin: 0 0 1rem 0;
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.5rem 0;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    font-weight: 500;
    color: var(--text-primary);
    flex: 1;
}

.setting-item input,
.setting-item select {
    width: 200px;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.setting-item input[type="range"] {
    width: 150px;
}

.range-value {
    margin-left: 0.5rem;
    font-weight: 500;
    color: var(--accent-color);
    min-width: 60px;
    text-align: right;
}

/* Custom Checkbox */
.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
}

.checkbox-label input[type="checkbox"] {
    opacity: 0;
    position: absolute;
    width: 0;
    height: 0;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: 4px;
    margin-right: 0.5rem;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--accent-color);
    border-color: var(--accent-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Custom Radio */
.radio-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
}

.radio-label input[type="radio"] {
    opacity: 0;
    position: absolute;
    width: 0;
    height: 0;
}

.radio-mark {
    width: 20px;
    height: 20px;
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    margin-right: 0.5rem;
    position: relative;
    transition: all 0.2s ease;
}

.radio-label input[type="radio"]:checked + .radio-mark {
    border-color: var(--accent-color);
}

.radio-label input[type="radio"]:checked + .radio-mark::after {
    content: '';
    position: absolute;
    left: 4px;
    top: 4px;
    width: 8px;
    height: 8px;
    background: var(--accent-color);
    border-radius: 50%;
}

.settings-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1rem 2rem;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background: var(--accent-hover);
}

.btn-primary.modified {
    background: var(--warning-color);
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-hover);
}

.btn-danger {
    background: var(--error-color);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: var(--success-color);
}

.notification-error {
    background: var(--error-color);
}

.notification-info {
    background: var(--info-color);
}

/* Responsive */
@media (max-width: 768px) {
    .settings-content {
        flex-direction: column;
    }
    
    .settings-nav {
        width: 100%;
        height: auto;
    }
    
    .settings-nav ul {
        display: flex;
        overflow-x: auto;
    }
    
    .settings-nav li {
        border-bottom: none;
        border-right: 1px solid var(--border-color);
        white-space: nowrap;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .setting-item input,
    .setting-item select {
        width: 100%;
    }
}

/* CRM Integration Styles */
.crm-status-display {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #666;
    transition: all 0.3s ease;
}

.status-indicator.connected {
    background: var(--success-color);
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.4);
}

.status-indicator.error {
    background: var(--error-color);
    box-shadow: 0 0 8px rgba(244, 67, 54, 0.4);
}

.status-info {
    flex: 1;
}

.status-text {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.status-details {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.crm-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
    margin-top: 16px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 6px;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.stat-value {
    font-weight: 600;
    color: var(--primary-color);
}
