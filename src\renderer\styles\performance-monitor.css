/* Performance Monitor Styles */
.performance-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.performance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.performance-header h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.performance-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.performance-content section {
    margin-bottom: 2rem;
}

.performance-content h2 {
    margin: 0 0 1rem 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.metric-card {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.metric-card:hover {
    border-color: var(--accent-color);
}

.metric-icon {
    font-size: 2rem;
    margin-right: 1rem;
}

.metric-info {
    flex: 1;
}

.metric-info h3 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.metric-value {
    margin: 0 0 0.5rem 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.metric-bar {
    width: 100%;
    height: 4px;
    background: var(--bg-hover);
    border-radius: 2px;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: var(--accent-color);
    transition: width 0.3s ease;
}

.metric-fill.good {
    background: var(--success-color);
}

.metric-fill.warning {
    background: var(--warning-color);
}

.metric-fill.critical {
    background: var(--error-color);
}

/* Charts */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1rem;
}

.chart-container {
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    height: 300px;
}

.chart-container h3 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
}

.chart-container canvas {
    max-height: 250px;
}

/* Issues */
.issues-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.no-issues {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.issue-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    border-left: 4px solid var(--border-color);
}

.issue-item.issue-critical {
    border-left-color: var(--error-color);
}

.issue-item.issue-warning {
    border-left-color: var(--warning-color);
}

.issue-item.issue-info {
    border-left-color: var(--info-color);
}

.issue-icon {
    font-size: 1.2rem;
    margin-right: 1rem;
    margin-top: 0.2rem;
}

.issue-content h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 500;
}

.issue-content p {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.issue-recommendation {
    font-style: italic;
    color: var(--accent-color) !important;
}

/* Recommendations */
.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.recommendation-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.recommendation-item.implemented {
    opacity: 0.7;
    border-color: var(--success-color);
}

.recommendation-content h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 500;
}

.recommendation-content p {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.impact-badge {
    display: inline-block;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
}

.impact-high {
    background: var(--error-color);
    color: white;
}

.impact-medium {
    background: var(--warning-color);
    color: white;
}

.impact-low {
    background: var(--info-color);
    color: white;
}

.implemented-badge {
    color: var(--success-color);
    font-weight: 500;
}

/* System Info */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.info-group {
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.info-group h3 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.info-value {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-primary);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    min-width: 400px;
    max-width: 500px;
}

.modal-content h3 {
    margin: 0 0 1rem 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.optimization-progress {
    margin-bottom: 1.5rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: var(--accent-color);
    transition: width 0.3s ease;
}

.progress-text {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.optimization-results h4 {
    margin: 0 0 1rem 0;
    color: var(--success-color);
}

.optimization-results ul {
    margin: 0;
    padding-left: 1.5rem;
}

.optimization-results li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background: var(--accent-hover);
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-hover);
}

.btn-danger {
    background: var(--error-color);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
}

.icon {
    font-size: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .performance-content {
        padding: 1rem;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .recommendation-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}
