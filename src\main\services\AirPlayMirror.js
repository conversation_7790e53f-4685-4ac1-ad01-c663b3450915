const { EventEmitter } = require('events');
const mdns = require('mdns');
const { createServer } = require('http');
const { Server: WebSocketServer } = require('ws');

class AirPlayMirror extends EventEmitter {
  constructor() {
    super();
    this.server = null;
    this.wsServer = null;
    this.mirrorStream = null;
  }

  async startAirPlayServer() {
    console.log('Starting AirPlay receiver...');
    
    // Create HTTP server for AirPlay
    this.server = createServer((req, res) => {
      this.handleAirPlayRequest(req, res);
    });

    // Create WebSocket server for screen data
    this.wsServer = new WebSocketServer({ server: this.server });
    
    this.wsServer.on('connection', (ws) => {
      console.log('iPhone connected for mirroring!');
      this.emit('mirror-connected');
      
      ws.on('message', (data) => {
        // Handle incoming screen frames
        this.emit('screen-frame', data);
      });
      
      ws.on('close', () => {
        this.emit('mirror-disconnected');
      });
    });

    // Start server
    const port = 7000;
    this.server.listen(port, () => {
      console.log(`AirPlay server listening on port ${port}`);
      this.advertiseAirPlay(port);
    });
  }

  advertiseAirPlay(port) {
    try {
      // Advertise as AirPlay receiver using Bonjour/mDNS
      const name = 'iPhone Companion Pro';
      
      // Create AirPlay service advertisement
      const service = mdns.createAdvertisement(mdns.tcp('airplay'), port, {
        name: name,
        txtRecord: {
          'deviceid': '00:00:00:00:00:00',
          'features': '0x5A7FFFF7,0x1E',
          'model': 'AppleTV3,2',
          'pw': '0',
          'srcvers': '220.68',
          'vv': '2'
        }
      });
      
      service.start();
      console.log('AirPlay service advertised as:', name);
      
      this.emit('ready', { name, port });
    } catch (error) {
      console.error('mDNS error:', error);
      
      // Fallback: Use simple discovery
      this.simplifiedDiscovery(port);
    }
  }

  simplifiedDiscovery(port) {
    // Alternative discovery method without mdns
    const dgram = require('dgram');
    const socket = dgram.createSocket('udp4');
    
    socket.bind(() => {
      socket.setBroadcast(true);
      
      const message = JSON.stringify({
        name: 'iPhone Companion Pro',
        type: 'airplay',
        port: port,
        ip: this.getLocalIP()
      });
      
      setInterval(() => {
        socket.send(message, 5353, '***********');
      }, 5000);
    });
  }

  handleAirPlayRequest(req, res) {
    console.log('AirPlay request:', req.method, req.url);
    
    switch (req.url) {
      case '/info':
        res.writeHead(200, { 'Content-Type': 'text/x-apple-plist+xml' });
        res.end(this.getDeviceInfo());
        break;
        
      case '/play':
        // Handle play request
        this.handlePlayRequest(req, res);
        break;
        
      case '/reverse':
        // Reverse HTTP connection for events
        res.writeHead(101);
        res.end();
        break;
        
      default:
        res.writeHead(200);
        res.end();
    }
  }

  getDeviceInfo() {
    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>deviceid</key>
  <string>00:00:00:00:00:00</string>
  <key>features</key>
  <integer>1518338359</integer>
  <key>model</key>
  <string>AppleTV3,2</string>
  <key>protovers</key>
  <string>1.0</string>
  <key>srcvers</key>
  <string>220.68</string>
</dict>
</plist>`;
  }

  handlePlayRequest(req, res) {
    let body = '';
    
    req.on('data', chunk => {
      body += chunk;
    });
    
    req.on('end', () => {
      console.log('Play request body:', body);
      res.writeHead(200);
      res.end();
      
      this.emit('play-request', body);
    });
  }

  getLocalIP() {
    const { networkInterfaces } = require('os');
    const nets = networkInterfaces();
    
    for (const name of Object.keys(nets)) {
      for (const net of nets[name]) {
        if (net.family === 'IPv4' && !net.internal) {
          return net.address;
        }
      }
    }
    
    return '127.0.0.1';
  }

  stop() {
    if (this.server) {
      this.server.close();
    }
    if (this.wsServer) {
      this.wsServer.close();
    }
  }
}

module.exports = { AirPlayMirror };