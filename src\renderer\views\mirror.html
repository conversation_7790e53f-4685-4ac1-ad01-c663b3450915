<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>iPhone Screen Mirror</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .mirror-container {
            position: relative;
            background: #1a1a1a;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.5);
        }
        
        .status-bar {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .iphone-frame {
            position: relative;
            width: 390px;
            height: 844px;
            background: #000;
            border-radius: 40px;
            padding: 10px;
            box-shadow: 
                inset 0 0 2px 2px #333,
                0 0 0 10px #1a1a1a;
        }
        
        #mirror-screen {
            width: 100%;
            height: 100%;
            background: #000;
            border-radius: 30px;
            overflow: hidden;
            position: relative;
        }
        
        #mirror-canvas {
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .waiting {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #666;
        }
        
        .waiting h2 {
            font-size: 24px;
            margin-bottom: 20px;
        }
        
        .instructions {
            background: #1a1a1a;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .instructions ol {
            text-align: left;
            margin: 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 10px 0;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }
        
        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #2a2a2a;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .control-btn:hover {
            background: #3a3a3a;
            transform: scale(1.1);
        }
        
        .control-btn:active {
            transform: scale(0.95);
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .settings-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 300px;
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #333;
        }

        .panel-header h3 {
            margin: 0;
            color: #fff;
            font-size: 16px;
        }

        .close-btn {
            background: none;
            border: none;
            color: #999;
            font-size: 18px;
            cursor: pointer;
            padding: 2px;
        }

        .close-btn:hover {
            color: #fff;
        }

        .panel-content {
            padding: 20px;
        }

        .setting-group {
            margin-bottom: 15px;
        }

        .setting-group label {
            display: block;
            color: #fff;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .setting-group select {
            width: 100%;
            padding: 8px 12px;
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 6px;
            color: #fff;
            font-size: 14px;
        }

        .setting-group input[type="checkbox"] {
            margin-right: 8px;
        }

        .connection-status {
            position: absolute;
            top: 20px;
            left: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(26, 26, 26, 0.9);
            padding: 10px 15px;
            border-radius: 20px;
            border: 1px solid #333;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #FF3B30;
            transition: background 0.3s ease;
        }

        .status-indicator.connected {
            background: #34C759;
            animation: pulse 2s infinite;
        }

        .connection-status span {
            color: #fff;
            font-size: 13px;
            font-weight: 500;
        }

        .stats {
            display: flex;
            gap: 10px;
            margin-left: 10px;
            padding-left: 10px;
            border-left: 1px solid #444;
        }

        .stats span {
            color: #999;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="mirror-container">
        <div class="status-bar">
            <h1>iPhone Screen Mirror</h1>
            <p id="status-text" class="pulse">Waiting for connection...</p>
        </div>
        
        <div class="iphone-frame">
            <div id="mirror-screen">
                <canvas id="mirror-canvas" style="display: none;"></canvas>
                
                <div class="waiting" id="waiting-screen">
                    <h2>📱 Connect Your iPhone</h2>
                    
                    <div class="instructions">
                        <ol>
                            <li>Make sure your iPhone and PC are on the same WiFi</li>
                            <li>Open Control Center on your iPhone</li>
                            <li>Tap "Screen Mirroring" 🔲</li>
                            <li>Select "iPhone Companion Pro"</li>
                        </ol>
                    </div>
                    
                    <p style="color: #007AFF;">Your iPhone screen will appear here</p>
                </div>
            </div>
        </div>
        
        <div class="controls" id="controls" style="display: none;">
            <button class="control-btn" onclick="sendHome()" title="Home">🏠</button>
            <button class="control-btn" onclick="sendBack()" title="Back">⬅️</button>
            <button class="control-btn" onclick="toggleRotation()" title="Rotate">🔄</button>
            <button class="control-btn" onclick="takeScreenshot()" title="Screenshot">📸</button>
            <button class="control-btn" onclick="toggleFullscreen()" title="Fullscreen">⛶</button>
            <button class="control-btn" onclick="openSettings()" title="Settings">⚙️</button>
        </div>

        <!-- Quality and Settings Panel -->
        <div class="settings-panel" id="settings-panel" style="display: none;">
            <div class="panel-header">
                <h3>Mirror Settings</h3>
                <button class="close-btn" onclick="closeSettings()">✕</button>
            </div>
            <div class="panel-content">
                <div class="setting-group">
                    <label>Quality:</label>
                    <select id="quality-select" onchange="changeQuality()">
                        <option value="low">Low (480p)</option>
                        <option value="medium" selected>Medium (720p)</option>
                        <option value="high">High (1080p)</option>
                        <option value="ultra">Ultra (4K)</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label>Frame Rate:</label>
                    <select id="framerate-select" onchange="changeFrameRate()">
                        <option value="15">15 FPS</option>
                        <option value="30" selected>30 FPS</option>
                        <option value="60">60 FPS</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label>Latency Mode:</label>
                    <select id="latency-select" onchange="changeLatencyMode()">
                        <option value="low">Low Latency</option>
                        <option value="balanced" selected>Balanced</option>
                        <option value="quality">Quality Priority</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="audio-enabled" onchange="toggleAudio()" checked>
                        Enable Audio
                    </label>
                </div>
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="touch-feedback" onchange="toggleTouchFeedback()" checked>
                        Touch Feedback
                    </label>
                </div>
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="auto-rotate" onchange="toggleAutoRotate()">
                        Auto Rotate
                    </label>
                </div>
            </div>
        </div>

        <!-- Connection Status -->
        <div class="connection-status" id="connection-status">
            <div class="status-indicator" id="status-indicator"></div>
            <span id="connection-text">Disconnected</span>
            <div class="stats" id="stats" style="display: none;">
                <span id="fps-counter">0 FPS</span>
                <span id="latency-counter">0ms</span>
            </div>
        </div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');
        const canvas = document.getElementById('mirror-canvas');
        const ctx = canvas.getContext('2d');
        const waitingScreen = document.getElementById('waiting-screen');
        const statusText = document.getElementById('status-text');
        const controls = document.getElementById('controls');
        
        let isConnected = false;
        let scale = 1;
        let touchFeedbackEnabled = true;
        let audioEnabled = true;
        let autoRotateEnabled = false;
        let currentQuality = 'medium';
        let currentFrameRate = 30;
        let currentLatencyMode = 'balanced';

        // Performance tracking
        let frameCount = 0;
        let lastFrameTime = Date.now();
        let fpsCounter = 0;
        let latencySum = 0;
        let latencyCount = 0;
        
        // Listen for mirror connection
        ipcRenderer.on('mirror-connected', (event, data) => {
            isConnected = true;
            waitingScreen.style.display = 'none';
            canvas.style.display = 'block';
            controls.style.display = 'flex';
            statusText.textContent = 'Connected - You can now control your iPhone!';
            statusText.classList.remove('pulse');

            // Update connection status
            updateConnectionStatus(true, data);

            // Start performance monitoring
            startPerformanceMonitoring();
        });

        // Listen for mirror disconnection
        ipcRenderer.on('mirror-disconnected', () => {
            isConnected = false;
            waitingScreen.style.display = 'block';
            canvas.style.display = 'none';
            controls.style.display = 'none';
            statusText.textContent = 'Disconnected - Waiting for connection...';
            statusText.classList.add('pulse');

            // Update connection status
            updateConnectionStatus(false);

            // Stop performance monitoring
            stopPerformanceMonitoring();
        });
        
        // Handle screen frames
        ipcRenderer.on('screen-frame', (event, frameData, timestamp) => {
            handleFrame(frameData, timestamp);

            // Draw frame to canvas
            const img = new Image();
            img.onload = () => {
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);

                // Show connected state
                if (!isConnected) {
                    isConnected = true;
                    waitingScreen.style.display = 'none';
                    canvas.style.display = 'block';
                    controls.style.display = 'flex';
                    statusText.textContent = 'Connected - You can now control your iPhone!';
                    statusText.classList.remove('pulse');
                    updateConnectionStatus(true);
                    startPerformanceMonitoring();
                }
            };
            img.src = frameData;
        });

        // Handle mirror frames (enhanced)
        ipcRenderer.on('mirror-frame', (event, data) => {
            const img = new Image();
            img.onload = () => {
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);

                // Show connected state
                if (!isConnected) {
                    isConnected = true;
                    waitingScreen.style.display = 'none';
                    canvas.style.display = 'block';
                    controls.style.display = 'flex';
                    statusText.textContent = 'Connected - You can now control your iPhone!';
                    statusText.classList.remove('pulse');
                }
            };
            img.src = `data:image/jpeg;base64,${data.frame}`;
        });

        // Handle video frames (H.264)
        ipcRenderer.on('mirror-video-frame', (event, data) => {
            // For H.264 data, we would need a proper decoder
            // For now, we'll try to handle it as base64 image data
            console.log('Received H.264 video frame, length:', data.data.length);

            // In a real implementation, you would decode H.264 here
            // For demo purposes, we'll just log it
        });
        
        // Handle touch/click events
        canvas.addEventListener('click', (e) => {
            if (!isConnected) return;

            const rect = canvas.getBoundingClientRect();
            const x = Math.round((e.clientX - rect.left) * (canvas.width / rect.width));
            const y = Math.round((e.clientY - rect.top) * (canvas.height / rect.height));

            ipcRenderer.send('mirror-touch', { x, y, type: 'tap' });

            // Visual feedback
            if (touchFeedbackEnabled) {
                showTouchFeedback(e.clientX - rect.left, e.clientY - rect.top);
            }
        });
        
        // Handle mouse drag for swipe
        let isDragging = false;
        let startX, startY;
        
        canvas.addEventListener('mousedown', (e) => {
            isDragging = true;
            const rect = canvas.getBoundingClientRect();
            startX = (e.clientX - rect.left) / rect.width;
            startY = (e.clientY - rect.top) / rect.height;
        });
        
        canvas.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            
            const rect = canvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) / rect.width;
            const y = (e.clientY - rect.top) / rect.height;
            
            ipcRenderer.send('mirror-touch', {
                type: 'swipe',
                startX, startY,
                endX: x, endY: y
            });
        });
        
        canvas.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Touch feedback function
        function showTouchFeedback(x, y) {
            const feedback = document.createElement('div');
            feedback.style.cssText = `
                position: absolute;
                left: ${x - 10}px;
                top: ${y - 10}px;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: rgba(0, 122, 255, 0.5);
                border: 2px solid #007AFF;
                pointer-events: none;
                animation: touchFeedback 0.3s ease-out forwards;
            `;

            document.getElementById('mirror-screen').appendChild(feedback);

            setTimeout(() => {
                if (feedback.parentNode) {
                    feedback.parentNode.removeChild(feedback);
                }
            }, 300);
        }

        // Add CSS animation for touch feedback
        const style = document.createElement('style');
        style.textContent = `
            @keyframes touchFeedback {
                0% { transform: scale(0.5); opacity: 1; }
                100% { transform: scale(2); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Control functions
        function sendHome() {
            ipcRenderer.send('mirror-control', 'home');
        }
        
        function sendBack() {
            ipcRenderer.send('mirror-control', 'back');
        }
        
        function toggleRotation() {
            ipcRenderer.send('mirror-control', 'rotate');
        }
        
        function takeScreenshot() {
            const link = document.createElement('a');
            link.download = `iphone-screenshot-${Date.now()}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // Settings functions
        function openSettings() {
            document.getElementById('settings-panel').style.display = 'block';
        }

        function closeSettings() {
            document.getElementById('settings-panel').style.display = 'none';
        }

        function changeQuality() {
            currentQuality = document.getElementById('quality-select').value;
            ipcRenderer.send('mirror-setting', { type: 'quality', value: currentQuality });
        }

        function changeFrameRate() {
            currentFrameRate = parseInt(document.getElementById('framerate-select').value);
            ipcRenderer.send('mirror-setting', { type: 'framerate', value: currentFrameRate });
        }

        function changeLatencyMode() {
            currentLatencyMode = document.getElementById('latency-select').value;
            ipcRenderer.send('mirror-setting', { type: 'latency', value: currentLatencyMode });
        }

        function toggleAudio() {
            audioEnabled = document.getElementById('audio-enabled').checked;
            ipcRenderer.send('mirror-setting', { type: 'audio', value: audioEnabled });
        }

        function toggleTouchFeedback() {
            touchFeedbackEnabled = document.getElementById('touch-feedback').checked;
        }

        function toggleAutoRotate() {
            autoRotateEnabled = document.getElementById('auto-rotate').checked;
            ipcRenderer.send('mirror-setting', { type: 'autoRotate', value: autoRotateEnabled });
        }

        // Connection status functions
        function updateConnectionStatus(connected, data = null) {
            const indicator = document.getElementById('status-indicator');
            const text = document.getElementById('connection-text');
            const stats = document.getElementById('stats');

            if (connected) {
                indicator.classList.add('connected');
                text.textContent = 'Connected';
                stats.style.display = 'flex';

                if (data && data.deviceInfo) {
                    text.textContent = `Connected to ${data.deviceInfo.name}`;
                }
            } else {
                indicator.classList.remove('connected');
                text.textContent = 'Disconnected';
                stats.style.display = 'none';
            }
        }

        // Performance monitoring
        let performanceInterval;

        function startPerformanceMonitoring() {
            performanceInterval = setInterval(() => {
                updatePerformanceStats();
            }, 1000);
        }

        function stopPerformanceMonitoring() {
            if (performanceInterval) {
                clearInterval(performanceInterval);
                performanceInterval = null;
            }
        }

        function updatePerformanceStats() {
            const fpsElement = document.getElementById('fps-counter');
            const latencyElement = document.getElementById('latency-counter');

            if (fpsElement) {
                fpsElement.textContent = `${fpsCounter} FPS`;
            }

            if (latencyElement && latencyCount > 0) {
                const avgLatency = Math.round(latencySum / latencyCount);
                latencyElement.textContent = `${avgLatency}ms`;
            }

            // Reset counters
            fpsCounter = frameCount;
            frameCount = 0;
            latencySum = 0;
            latencyCount = 0;
        }

        // Enhanced frame handling with performance tracking
        function handleFrame(frameData, timestamp = Date.now()) {
            frameCount++;

            // Calculate latency if timestamp is provided
            if (timestamp) {
                const latency = Date.now() - timestamp;
                latencySum += latency;
                latencyCount++;
            }
        }
    </script>
</body>
</html>