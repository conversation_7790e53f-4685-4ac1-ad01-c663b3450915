const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const { app } = require('electron');

class DatabaseMigration {
  constructor() {
    this.dbPath = path.join(app.getPath('userData'), 'iphone-beast.db');
    this.db = null;
    this.currentVersion = 1;
    this.targetVersion = 3; // Intel Unison++ version with fixed sync_status
  }

  async initialize() {
    console.log('🔄 Initializing database migration...');
    this.db = new sqlite3.Database(this.dbPath);
    
    // Create version table if it doesn't exist
    await this.run(`
      CREATE TABLE IF NOT EXISTS schema_version (
        version INTEGER PRIMARY KEY,
        applied_at INTEGER DEFAULT (strftime('%s', 'now'))
      )
    `);
    
    const currentVersion = await this.getCurrentVersion();
    console.log(`📊 Current database version: ${currentVersion}`);
    
    if (currentVersion < this.targetVersion) {
      console.log(`🔄 Migrating database from version ${currentVersion} to ${this.targetVersion}`);
      await this.migrate(currentVersion, this.targetVersion);
    } else {
      console.log('✅ Database is already at the latest version');
    }
  }

  async getCurrentVersion() {
    try {
      const result = await this.get('SELECT MAX(version) as version FROM schema_version');
      return result ? result.version || 0 : 0;
    } catch (error) {
      return 0; // No version table exists
    }
  }

  async migrate(from, to) {
    console.log(`🔄 Starting migration from version ${from} to ${to}`);
    
    // Backup existing database
    await this.createBackup();
    
    try {
      // Start transaction
      await this.run('BEGIN TRANSACTION');
      
      if (from < 1) {
        await this.migrateToVersion1();
      }
      
      if (from < 2) {
        await this.migrateToVersion2();
      }
      
      if (from < 3) {
        await this.migrateToVersion3();
      }
      
      // Update version
      await this.run('INSERT INTO schema_version (version) VALUES (?)', [to]);
      
      // Commit transaction
      await this.run('COMMIT');
      
      console.log('✅ Database migration completed successfully');
    } catch (error) {
      console.error('❌ Migration failed, rolling back:', error);
      await this.run('ROLLBACK');
      throw error;
    }
  }

  async migrateToVersion1() {
    console.log('🔄 Migrating to version 1 (basic schema)...');
    
    // Create basic tables if they don't exist
    await this.run(`
      CREATE TABLE IF NOT EXISTS messages_v1 (
        id TEXT PRIMARY KEY,
        conversation_id TEXT NOT NULL,
        contact_name TEXT,
        phone_number TEXT,
        text TEXT,
        timestamp INTEGER,
        is_incoming BOOLEAN,
        is_read BOOLEAN,
        attachments TEXT,
        source TEXT,
        raw_data TEXT,
        created_at INTEGER DEFAULT (strftime('%s', 'now'))
      )
    `);
    
    console.log('✅ Version 1 migration completed');
  }

  async migrateToVersion2() {
    console.log('🔄 Migrating to version 2 (Intel Unison++ schema)...');
    
    // Check if old messages table exists
    const oldTableExists = await this.tableExists('messages');
    
    if (oldTableExists) {
      // Rename old table
      await this.run('ALTER TABLE messages RENAME TO messages_old');
      console.log('✅ Backed up old messages table');
    }
    
    // Create new Intel Unison++ schema
    await this.createNewSchema();
    
    // Migrate data from old table if it exists
    if (oldTableExists) {
      await this.migrateOldData();
    }
    
    console.log('✅ Version 2 migration completed');
  }

  async migrateToVersion3() {
    console.log('🔄 Migrating to version 3 (Fix sync_status table)...');
    
    // Check if sync_status table exists and needs records_synced column
    const syncTableExists = await this.tableExists('sync_status');
    if (syncTableExists) {
      // Check if records_synced column exists
      const hasRecordsSynced = await this.columnExists('sync_status', 'records_synced');
      if (!hasRecordsSynced) {
        console.log('🔄 Adding records_synced column to sync_status table...');
        await this.run('ALTER TABLE sync_status ADD COLUMN records_synced INTEGER DEFAULT 0');
        console.log('✅ Added records_synced column');
      } else {
        console.log('✅ records_synced column already exists');
      }
    } else {
      // Create sync_status table if it doesn't exist
      await this.run(`
        CREATE TABLE IF NOT EXISTS sync_status (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          last_sync INTEGER NOT NULL,
          sync_type TEXT NOT NULL,
          status TEXT DEFAULT 'pending',
          device_id TEXT,
          records_synced INTEGER DEFAULT 0,
          error_message TEXT,
          created_at INTEGER DEFAULT (strftime('%s', 'now'))
        )
      `);
      console.log('✅ Created sync_status table');
    }
    
    console.log('✅ Version 3 migration completed');
  }

  async createNewSchema() {
    console.log('🔄 Creating Intel Unison++ schema...');
    
    // Intel Unison-style Messages table
    await this.run(`
      CREATE TABLE IF NOT EXISTS messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        thread_id TEXT NOT NULL,
        phone_number TEXT NOT NULL,
        contact_name TEXT,
        message_text TEXT,
        timestamp INTEGER NOT NULL,
        is_outgoing BOOLEAN DEFAULT 0,
        is_delivered BOOLEAN DEFAULT 0,
        is_read BOOLEAN DEFAULT 1,
        has_attachment BOOLEAN DEFAULT 0,
        attachment_path TEXT,
        source TEXT DEFAULT 'phonelink',
        raw_data TEXT,
        created_at INTEGER DEFAULT (strftime('%s', 'now')),
        UNIQUE(phone_number, message_text, timestamp)
      )
    `);
    
    // Message threads table
    await this.run(`
      CREATE TABLE IF NOT EXISTS message_threads (
        thread_id TEXT PRIMARY KEY,
        phone_number TEXT NOT NULL,
        contact_name TEXT,
        last_message TEXT,
        last_timestamp INTEGER,
        unread_count INTEGER DEFAULT 0,
        avatar TEXT,
        is_group BOOLEAN DEFAULT 0,
        raw_data TEXT
      )
    `);
    
    // Enhanced calls table
    await this.run(`
      CREATE TABLE IF NOT EXISTS calls (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        phone_number TEXT NOT NULL,
        contact_name TEXT,
        duration INTEGER DEFAULT 0,
        timestamp INTEGER NOT NULL,
        type TEXT DEFAULT 'voice',
        is_incoming BOOLEAN DEFAULT 1,
        is_missed BOOLEAN DEFAULT 0,
        is_voicemail BOOLEAN DEFAULT 0,
        raw_data TEXT,
        created_at INTEGER DEFAULT (strftime('%s', 'now'))
      )
    `);
    
    // Contacts table
    await this.run(`
      CREATE TABLE IF NOT EXISTS contacts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        phone_number TEXT UNIQUE NOT NULL,
        display_name TEXT,
        first_name TEXT,
        last_name TEXT,
        email TEXT,
        avatar TEXT,
        photo_path TEXT,
        last_contacted INTEGER,
        raw_data TEXT,
        created_at INTEGER DEFAULT (strftime('%s', 'now'))
      )
    `);
    
    // Attachments table
    await this.run(`
      CREATE TABLE IF NOT EXISTS attachments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        message_id INTEGER,
        filename TEXT,
        filepath TEXT,
        mime_type TEXT,
        file_size INTEGER,
        timestamp INTEGER,
        FOREIGN KEY (message_id) REFERENCES messages (id)
      )
    `);
    
    // Sync status table
    await this.run(`
      CREATE TABLE IF NOT EXISTS sync_status (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        last_sync INTEGER NOT NULL,
        sync_type TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        device_id TEXT,
        records_synced INTEGER DEFAULT 0,
        error_message TEXT,
        created_at INTEGER DEFAULT (strftime('%s', 'now'))
      )
    `);
    
    // User preferences
    await this.run(`
      CREATE TABLE IF NOT EXISTS preferences (
        key TEXT PRIMARY KEY,
        value TEXT,
        updated_at INTEGER DEFAULT (strftime('%s', 'now'))
      )
    `);
    
    // Create indexes
    await this.run('CREATE INDEX IF NOT EXISTS idx_messages_thread ON messages(thread_id)');
    await this.run('CREATE INDEX IF NOT EXISTS idx_messages_phone_timestamp ON messages(phone_number, timestamp DESC)');
    await this.run('CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp DESC)');
    await this.run('CREATE INDEX IF NOT EXISTS idx_calls_timestamp ON calls(timestamp DESC)');
    await this.run('CREATE INDEX IF NOT EXISTS idx_calls_phone ON calls(phone_number)');
    await this.run('CREATE INDEX IF NOT EXISTS idx_contacts_phone ON contacts(phone_number)');
    await this.run('CREATE INDEX IF NOT EXISTS idx_threads_phone ON message_threads(phone_number)');
    await this.run('CREATE INDEX IF NOT EXISTS idx_threads_timestamp ON message_threads(last_timestamp DESC)');
    
    // Create FTS5 virtual table
    await this.run(`
      CREATE VIRTUAL TABLE IF NOT EXISTS messages_fts USING fts5(
        message_text,
        contact_name,
        phone_number,
        content='messages',
        content_rowid='id'
      )
    `);
    
    // Create FTS5 triggers
    await this.run(`
      CREATE TRIGGER IF NOT EXISTS messages_ai AFTER INSERT ON messages BEGIN
        INSERT INTO messages_fts(rowid, message_text, contact_name, phone_number) 
        VALUES (new.id, new.message_text, new.contact_name, new.phone_number);
      END
    `);
    
    await this.run(`
      CREATE TRIGGER IF NOT EXISTS messages_ad AFTER DELETE ON messages BEGIN
        INSERT INTO messages_fts(messages_fts, rowid, message_text, contact_name, phone_number) 
        VALUES('delete', old.id, old.message_text, old.contact_name, old.phone_number);
      END
    `);
    
    console.log('✅ New schema created successfully');
  }

  async migrateOldData() {
    console.log('🔄 Migrating data from old schema...');
    
    try {
      // Get all old messages
      const oldMessages = await this.all('SELECT * FROM messages_old');
      console.log(`📊 Found ${oldMessages.length} messages to migrate`);
      
      for (const oldMsg of oldMessages) {
        // Convert old message format to new format
        const newMsg = {
          thread_id: oldMsg.phone_number || oldMsg.conversation_id,
          phone_number: oldMsg.phone_number,
          contact_name: oldMsg.contact_name,
          message_text: oldMsg.text,
          timestamp: oldMsg.timestamp,
          is_outgoing: oldMsg.is_incoming ? 0 : 1,
          is_delivered: 1,
          is_read: oldMsg.is_read ? 1 : 0,
          has_attachment: oldMsg.attachments && oldMsg.attachments !== '[]' ? 1 : 0,
          source: oldMsg.source || 'migrated',
          raw_data: oldMsg.raw_data
        };
        
        // Insert into new table
        await this.run(`
          INSERT OR IGNORE INTO messages 
          (thread_id, phone_number, contact_name, message_text, timestamp, 
           is_outgoing, is_delivered, is_read, has_attachment, source, raw_data)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          newMsg.thread_id, newMsg.phone_number, newMsg.contact_name, 
          newMsg.message_text, newMsg.timestamp, newMsg.is_outgoing, 
          newMsg.is_delivered, newMsg.is_read, newMsg.has_attachment, 
          newMsg.source, newMsg.raw_data
        ]);
        
        // Update message thread
        await this.run(`
          INSERT OR REPLACE INTO message_threads 
          (thread_id, phone_number, contact_name, last_message, last_timestamp)
          VALUES (?, ?, ?, ?, ?)
        `, [
          newMsg.thread_id, newMsg.phone_number, newMsg.contact_name, 
          newMsg.message_text, newMsg.timestamp
        ]);
      }
      
      console.log(`✅ Migrated ${oldMessages.length} messages successfully`);
      
      // Drop old table
      await this.run('DROP TABLE messages_old');
      console.log('✅ Cleaned up old messages table');
      
    } catch (error) {
      console.error('❌ Error migrating old data:', error);
      throw error;
    }
  }

  async tableExists(tableName) {
    const result = await this.get(
      "SELECT name FROM sqlite_master WHERE type='table' AND name=?", 
      [tableName]
    );
    return !!result;
  }

  async columnExists(tableName, columnName) {
    try {
      const result = await this.get(`PRAGMA table_info(${tableName})`);
      if (!result) return false;
      
      const columns = await this.all(`PRAGMA table_info(${tableName})`);
      return columns.some(col => col.name === columnName);
    } catch (error) {
      console.warn(`⚠️ Could not check column existence: ${error.message}`);
      return false;
    }
  }

  async createBackup() {
    const backupPath = this.dbPath + '.backup.' + Date.now();
    try {
      fs.copyFileSync(this.dbPath, backupPath);
      console.log(`✅ Database backup created: ${backupPath}`);
    } catch (error) {
      console.warn('⚠️ Could not create backup:', error.message);
    }
  }

  // Database utility methods
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) reject(err);
        else resolve(this);
      });
    });
  }

  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  }

  close() {
    if (this.db) {
      this.db.close();
    }
  }
}

module.exports = { DatabaseMigration };