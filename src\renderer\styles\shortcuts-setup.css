.shortcuts-setup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.shortcuts-setup-content {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  padding: 30px;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  border: 1px solid #333;
}

.setup-header {
  text-align: center;
  margin-bottom: 30px;
}

.setup-header h2 {
  color: #00d4ff;
  font-size: 28px;
  margin-bottom: 10px;
  font-weight: 600;
}

.setup-header p {
  color: #ccc;
  font-size: 16px;
  margin: 0;
}

.setup-status {
  display: flex;
  justify-content: space-around;
  background: #222;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
  border: 1px solid #333;
}

.status-item {
  text-align: center;
}

.status-label {
  display: block;
  color: #888;
  font-size: 14px;
  margin-bottom: 5px;
}

.status-value {
  display: block;
  font-size: 18px;
  font-weight: 600;
}

.status-success {
  color: #00ff88;
}

.status-error {
  color: #ff4444;
}

.setup-steps {
  margin-bottom: 30px;
}

.step-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #1e1e1e;
  border-radius: 12px;
  border: 1px solid #333;
}

.step-section h3 {
  color: #00d4ff;
  font-size: 20px;
  margin-bottom: 15px;
  font-weight: 600;
}

.step-section p {
  color: #ccc;
  margin-bottom: 15px;
  line-height: 1.5;
}

.shortcuts-list {
  display: grid;
  gap: 10px;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #2a2a2a;
  border-radius: 8px;
  border: 1px solid #444;
}

.shortcut-name {
  color: #fff;
  font-weight: 500;
}

.install-btn {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.install-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

.qr-code-container {
  text-align: center;
}

.qr-code-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 12px;
  background: white;
  padding: 10px;
  margin-bottom: 15px;
}

.qr-placeholder,
.qr-error {
  width: 200px;
  height: 200px;
  background: #333;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #888;
  margin: 0 auto 15px;
  border: 2px dashed #555;
}

.qr-error {
  color: #ff4444;
  border-color: #ff4444;
}

.refresh-btn {
  background: #444;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.refresh-btn:hover {
  background: #555;
}

.automation-instructions {
  background: #2a2a2a;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #00d4ff;
}

.automation-instructions ol {
  color: #ccc;
  line-height: 1.6;
  margin: 0;
  padding-left: 20px;
}

.automation-instructions li {
  margin-bottom: 8px;
}

.automation-instructions strong {
  color: #00d4ff;
}

.test-controls {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  align-items: center;
}

.test-btn {
  background: linear-gradient(135deg, #00ff88 0%, #00cc66 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.test-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
}

.test-results {
  flex: 1;
  min-width: 200px;
}

.test-result {
  padding: 10px 15px;
  border-radius: 6px;
  font-weight: 500;
}

.test-result.success {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
  border: 1px solid #00ff88;
}

.test-result.error {
  background: rgba(255, 68, 68, 0.2);
  color: #ff4444;
  border: 1px solid #ff4444;
}

.test-result.info {
  background: rgba(0, 212, 255, 0.2);
  color: #00d4ff;
  border: 1px solid #00d4ff;
}

.setup-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  background: linear-gradient(135deg, #666 0%, #444 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.action-btn.secondary {
  background: linear-gradient(135deg, #ff4444 0%, #cc3333 100%);
}

.action-btn.secondary:hover {
  box-shadow: 0 5px 15px rgba(255, 68, 68, 0.3);
}

.advanced-settings {
  margin-top: 20px;
  padding: 20px;
  background: #1a1a1a;
  border-radius: 12px;
  border: 1px solid #333;
}

.advanced-settings h3 {
  color: #00d4ff;
  margin-bottom: 20px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.setting-item label {
  color: #ccc;
  font-weight: 500;
}

.setting-item input[type="number"],
.setting-item input[type="text"] {
  background: #333;
  border: 1px solid #555;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  width: 150px;
}

.setting-item input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #00d4ff;
}

/* Scrollbar styling */
.shortcuts-setup-content::-webkit-scrollbar {
  width: 8px;
}

.shortcuts-setup-content::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 4px;
}

.shortcuts-setup-content::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

.shortcuts-setup-content::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* Responsive design */
@media (max-width: 768px) {
  .shortcuts-setup-content {
    margin: 20px;
    padding: 20px;
    max-width: none;
  }
  
  .setup-status {
    flex-direction: column;
    gap: 15px;
  }
  
  .test-controls {
    flex-direction: column;
  }
  
  .setup-actions {
    flex-direction: column;
  }
}
