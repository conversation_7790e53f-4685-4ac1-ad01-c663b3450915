const { EventEmitter } = require('events');
const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

class VMManager extends EventEmitter {
  constructor(vmConfig) {
    super();
    this.vmConfig = vmConfig;
    this.vmProcess = null;
    this.vmPid = null;
    this.vmDir = vmConfig.vmDir;
    this.diskPath = vmConfig.diskPath;
  }

  // Create optimized macOS VM with iPhone connectivity
  async createOptimizedMacOSVM() {
    this.emit('status', 'Creating optimized macOS VM for iPhone connectivity...');
    
    // Enhanced QEMU arguments for macOS with iPhone support
    const qemuArgs = this.buildQemuArguments();
    
    // Create VM with enhanced configuration
    return new Promise((resolve, reject) => {
      this.vmProcess = spawn('qemu-system-x86_64', qemuArgs, {
        stdio: ['ignore', 'pipe', 'pipe'],
        cwd: this.vmDir,
        env: { ...process.env, QEMU_AUDIO_DRV: 'none' }
      });

      this.vmProcess.on('spawn', () => {
        this.vmPid = this.vmProcess.pid;
        this.saveVMPid();
        this.emit('vm-created');
        resolve(this.vmProcess);
      });

      this.vmProcess.on('error', (error) => {
        this.emit('vm-error', error);
        reject(new Error(`Failed to create VM: ${error.message}`));
      });

      this.vmProcess.on('exit', (code, signal) => {
        this.emit('vm-exit', { code, signal });
        this.vmPid = null;
        this.removeVMPid();
      });

      // Handle VM output
      this.setupVMLogging();
    });
  }

  // Build comprehensive QEMU arguments
  buildQemuArguments() {
    const args = [
      // Basic VM configuration
      '-enable-kvm',
      '-m', this.vmConfig.memory,
      '-cpu', 'Penryn,vendor=GenuineIntel,+invtsc,vmware-cpuid-freq=on,+pcid,+ssse3,+sse4.2,+popcnt,+avx,+aes,+xsave,+xsaveopt,check',
      '-machine', 'q35,accel=kvm',
      '-smp', `${this.vmConfig.cores},cores=2,threads=2`,
      
      // Storage configuration
      '-drive', `file=${this.diskPath},if=virtio,cache=writethrough,format=qcow2`,
      
      // Network configuration for iPhone connectivity
      '-netdev', this.buildNetworkConfig(),
      '-device', 'virtio-net-pci,netdev=net0,mac=52:54:00:12:34:56',
      
      // USB configuration for iPhone passthrough
      '-device', 'nec-usb-xhci,id=xhci',
      '-device', 'usb-host,vendorid=0x05ac,productid=0x12a8,id=iphone', // iPhone USB IDs
      
      // Audio configuration
      '-device', 'ich9-intel-hda',
      '-device', 'hda-duplex',
      
      // Display configuration
      '-vga', 'vmware',
      '-vnc', `:${this.vmConfig.vncPort - 5900}`,
      
      // BIOS and firmware
      '-bios', this.getOVMFPath(),
      
      // Performance optimizations
      '-rtc', 'base=localtime,clock=host',
      '-no-hpet',
      '-global', 'kvm-pit.lost_tick_policy=discard',
      
      // Daemon mode
      '-daemonize',
      '-pidfile', path.join(this.vmDir, 'vm.pid')
    ];

    // Add macOS-specific optimizations
    if (this.vmConfig.enableAppleFeatures) {
      args.push(
        '-device', 'isa-applesmc,osk=ourhardworkbythesewordsguardedpleasedontsteal(c)AppleComputerInc',
        '-smbios', 'type=2'
      );
    }

    return args;
  }

  // Build network configuration with port forwarding
  buildNetworkConfig() {
    const forwards = [
      `tcp::${this.vmConfig.bridgePort}-:8080`,  // Bridge service
      `tcp::${this.vmConfig.sshPort}-:22`,       // SSH
      `tcp::${this.vmConfig.vncPort}-:5900`,     // VNC
      `tcp::5353-:5353`,                         // Bonjour/mDNS
      `tcp::62078-:62078`,                       // iTunes sync
      `tcp::62087-:62087`                        // iTunes WiFi sync
    ];
    
    return `user,id=net0,${forwards.map(f => `hostfwd=${f}`).join(',')}`;
  }

  // Get OVMF BIOS path
  getOVMFPath() {
    const possiblePaths = [
      'C:\\Program Files\\qemu\\share\\edk2-x86_64-code.fd',
      'C:\\qemu\\share\\edk2-x86_64-code.fd',
      path.join(this.vmDir, 'OVMF_CODE.fd')
    ];
    
    for (const biosPath of possiblePaths) {
      if (fs.existsSync(biosPath)) {
        return biosPath;
      }
    }
    
    // Download OVMF if not found
    this.downloadOVMF();
    return path.join(this.vmDir, 'OVMF_CODE.fd');
  }

  // Download OVMF BIOS
  async downloadOVMF() {
    this.emit('status', 'Downloading OVMF BIOS...');
    
    const ovmfUrl = 'https://github.com/tianocore/edk2/releases/download/edk2-stable202308/OVMF-X64-r202308.zip';
    const ovmfPath = path.join(this.vmDir, 'OVMF_CODE.fd');
    
    // This would need to be implemented with proper download logic
    // For now, emit a warning
    this.emit('warning', 'OVMF BIOS not found. Please install QEMU with OVMF support.');
  }

  // Setup VM logging
  setupVMLogging() {
    const logPath = path.join(this.vmDir, 'logs', 'vm.log');
    
    if (this.vmProcess.stdout) {
      this.vmProcess.stdout.on('data', (data) => {
        fs.appendFileSync(logPath, `[STDOUT] ${data}`);
        this.emit('vm-output', { type: 'stdout', data: data.toString() });
      });
    }

    if (this.vmProcess.stderr) {
      this.vmProcess.stderr.on('data', (data) => {
        fs.appendFileSync(logPath, `[STDERR] ${data}`);
        this.emit('vm-output', { type: 'stderr', data: data.toString() });
      });
    }
  }

  // Save VM PID for management
  saveVMPid() {
    const pidFile = path.join(this.vmDir, 'vm.pid');
    fs.writeFileSync(pidFile, this.vmPid.toString());
  }

  // Remove VM PID file
  removeVMPid() {
    const pidFile = path.join(this.vmDir, 'vm.pid');
    if (fs.existsSync(pidFile)) {
      fs.unlinkSync(pidFile);
    }
  }

  // Check if VM is running
  isVMRunning() {
    const pidFile = path.join(this.vmDir, 'vm.pid');
    
    if (!fs.existsSync(pidFile)) {
      return false;
    }
    
    try {
      const pid = parseInt(fs.readFileSync(pidFile, 'utf8'));
      process.kill(pid, 0); // Check if process exists
      return true;
    } catch (error) {
      return false;
    }
  }

  // Stop VM gracefully
  async stopVM() {
    this.emit('status', 'Stopping VM...');
    
    if (this.vmProcess) {
      // Try graceful shutdown first
      try {
        this.vmProcess.kill('SIGTERM');
        
        // Wait for graceful shutdown
        await new Promise((resolve) => {
          const timeout = setTimeout(() => {
            // Force kill if graceful shutdown fails
            if (this.vmProcess) {
              this.vmProcess.kill('SIGKILL');
            }
            resolve();
          }, 30000);
          
          this.vmProcess.on('exit', () => {
            clearTimeout(timeout);
            resolve();
          });
        });
      } catch (error) {
        this.emit('error', `Error stopping VM: ${error.message}`);
      }
    }
  }

  // Get VM performance metrics
  getVMMetrics() {
    if (!this.vmPid) {
      return null;
    }
    
    return new Promise((resolve) => {
      exec(`wmic process where "ProcessId=${this.vmPid}" get WorkingSetSize,PageFileUsage,CPUTime /format:csv`, (error, stdout) => {
        if (error) {
          resolve(null);
        } else {
          const lines = stdout.trim().split('\n');
          if (lines.length > 1) {
            const data = lines[1].split(',');
            resolve({
              memory: parseInt(data[3]) || 0,
              pageFile: parseInt(data[2]) || 0,
              cpuTime: data[1] || '0'
            });
          } else {
            resolve(null);
          }
        }
      });
    });
  }

  // Configure iPhone USB passthrough
  async configureIPhonePassthrough() {
    this.emit('status', 'Configuring iPhone USB passthrough...');
    
    // Detect connected iPhone
    const iPhoneDevices = await this.detectIPhoneDevices();
    
    if (iPhoneDevices.length === 0) {
      throw new Error('No iPhone detected for passthrough');
    }
    
    // Configure USB passthrough for first detected iPhone
    const device = iPhoneDevices[0];
    this.emit('status', `Configuring passthrough for iPhone: ${device.name}`);
    
    return device;
  }

  // Detect connected iPhone devices
  async detectIPhoneDevices() {
    return new Promise((resolve) => {
      exec('wmic path Win32_PnPEntity where "DeviceID like \'%VID_05AC%\'" get DeviceID,Name /format:csv', (error, stdout) => {
        if (error) {
          resolve([]);
        } else {
          const devices = [];
          const lines = stdout.trim().split('\n').slice(1);
          
          for (const line of lines) {
            const parts = line.split(',');
            if (parts.length >= 3 && parts[1].includes('iPhone')) {
              devices.push({
                deviceId: parts[1],
                name: parts[2]
              });
            }
          }
          
          resolve(devices);
        }
      });
    });
  }

  // Create VM snapshot
  async createSnapshot(name) {
    this.emit('status', `Creating VM snapshot: ${name}`);
    
    return new Promise((resolve, reject) => {
      const cmd = `qemu-img snapshot -c ${name} "${this.diskPath}"`;
      
      exec(cmd, (error, stdout, stderr) => {
        if (error) {
          reject(new Error(`Failed to create snapshot: ${error.message}`));
        } else {
          this.emit('status', `Snapshot '${name}' created successfully`);
          resolve();
        }
      });
    });
  }

  // Restore VM snapshot
  async restoreSnapshot(name) {
    this.emit('status', `Restoring VM snapshot: ${name}`);
    
    return new Promise((resolve, reject) => {
      const cmd = `qemu-img snapshot -a ${name} "${this.diskPath}"`;
      
      exec(cmd, (error, stdout, stderr) => {
        if (error) {
          reject(new Error(`Failed to restore snapshot: ${error.message}`));
        } else {
          this.emit('status', `Snapshot '${name}' restored successfully`);
          resolve();
        }
      });
    });
  }
}

module.exports = VMManager;
