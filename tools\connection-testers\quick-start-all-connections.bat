@echo off
title iPhone Companion Pro - Complete Connection Setup
color 0A

echo.
echo ========================================================
echo  iPhone Companion Pro - Master Connection Setup
echo ========================================================
echo.
echo  This will setup ALL iPhone connection methods:
echo  📱 Bluetooth
echo  📺 AirPlay  
echo  🖥️ macOS VM Bridge
echo  🔗 Phone Link
echo  🔌 USB
echo.
pause

echo.
echo 🔧 Installing required dependencies...
echo.

REM Install Node.js dependencies
npm install @abandonware/noble bonjour-service mdns-js ws sqlite3 chokidar

echo.
echo 🚀 Setting up all connection services...
echo.

REM Run the master setup
node setup-all-connections.js

echo.
echo 🧪 Testing all connections...
echo.

REM Test all connections
node test-all-connections.js

echo.
echo 📋 Setup complete! Check the results above.
echo.
echo 📱 iPhone Setup Instructions:
echo.
echo    BLUETOOTH:
echo    • Settings ^> Bluetooth ^> ON
echo    • Make iPhone discoverable
echo.
echo    AIRPLAY:
echo    • Control Center ^> Screen Mirroring
echo    • Select "iPhone Companion Pro"
echo.
echo    USB:
echo    • Connect iPhone with cable
echo    • Tap "Trust This Computer"
echo.
echo    PHONE LINK:
echo    • Install Phone Link app on iPhone
echo    • Pair with this Windows PC
echo.
echo    VM BRIDGE:
echo    • Setup macOS VM
echo    • Run macos-bridge-setup.sh in VM
echo.
echo 🎯 Priority Order (easiest to hardest):
echo    1. Phone Link (if already setup)
echo    2. AirPlay (wireless screen mirror)
echo    3. USB (with iTunes installed)
echo    4. Bluetooth (limited functionality)
echo    5. VM Bridge (full access, complex setup)
echo.
echo 🚀 To start the app: npm start
echo.
pause

REM Optional: Start the app immediately
set /p start_app="Start iPhone Companion Pro now? (y/n): "
if /i "%start_app%"=="y" (
    echo.
    echo 🚀 Starting iPhone Companion Pro...
    npm start
)

echo.
echo ✅ All done! Your iPhone should now connect automatically.
pause
