const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

class BuildManager {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.buildDir = path.join(this.projectRoot, 'dist');
    this.version = this.getVersion();
  }

  getVersion() {
    const packageJson = JSON.parse(fs.readFileSync(path.join(this.projectRoot, 'package.json'), 'utf8'));
    return packageJson.version || '1.0.0';
  }

  async buildAll() {
    console.log('🚀 Starting iPhone Companion Pro Build Process');
    console.log(`📦 Version: ${this.version}`);
    console.log('─'.repeat(60));

    try {
      // Clean previous builds
      await this.clean();

      // Install dependencies
      await this.installDependencies();

      // Run tests
      await this.runTests();

      // Build Windows app
      await this.buildWindowsApp();

      // Package iOS companion app
      await this.packageiOSApp();

      // Create installers
      await this.createInstallers();

      // Generate documentation
      await this.generateDocumentation();

      console.log('\n✅ Build completed successfully!');
      console.log(`📁 Build artifacts available in: ${this.buildDir}`);

    } catch (error) {
      console.error('\n❌ Build failed:', error.message);
      process.exit(1);
    }
  }

  async clean() {
    console.log('🧹 Cleaning previous builds...');
    
    if (fs.existsSync(this.buildDir)) {
      fs.rmSync(this.buildDir, { recursive: true, force: true });
    }
    
    fs.mkdirSync(this.buildDir, { recursive: true });
    console.log('✅ Clean completed');
  }

  async installDependencies() {
    console.log('📦 Installing dependencies...');
    
    try {
      execSync('npm ci', { 
        cwd: this.projectRoot, 
        stdio: 'inherit' 
      });
      console.log('✅ Dependencies installed');
    } catch (error) {
      throw new Error('Failed to install dependencies: ' + error.message);
    }
  }

  async runTests() {
    console.log('🧪 Running tests...');
    
    try {
      execSync('npm test', { 
        cwd: this.projectRoot, 
        stdio: 'inherit' 
      });
      console.log('✅ Tests passed');
    } catch (error) {
      console.warn('⚠️  Some tests failed, continuing build...');
    }
  }

  async buildWindowsApp() {
    console.log('🖥️  Building Windows application...');
    
    try {
      // Build with electron-builder
      execSync('npm run build:win', { 
        cwd: this.projectRoot, 
        stdio: 'inherit' 
      });
      
      console.log('✅ Windows app built successfully');
    } catch (error) {
      throw new Error('Failed to build Windows app: ' + error.message);
    }
  }

  async packageiOSApp() {
    console.log('📱 Packaging iOS companion app...');
    
    const iosSourceDir = path.join(this.projectRoot, 'companion-ios-app');
    const iosPackageDir = path.join(this.buildDir, 'ios-companion');
    
    // Create iOS package directory
    fs.mkdirSync(iosPackageDir, { recursive: true });
    
    // Copy iOS source files
    this.copyDirectory(iosSourceDir, iosPackageDir);
    
    // Create iOS build instructions
    const iosInstructions = `# iPhone Companion Pro - iOS App

## Prerequisites
- Xcode 14.0 or later
- iOS 15.0 or later
- Apple Developer Account (for device installation)

## Build Instructions

1. Open the project in Xcode:
   \`\`\`bash
   open iPhoneCompanionPro.xcodeproj
   \`\`\`

2. Configure your development team:
   - Select the project in the navigator
   - Go to "Signing & Capabilities"
   - Select your development team

3. Build and run:
   - Select your target device or simulator
   - Press Cmd+R to build and run

## Installation on Device

1. Connect your iPhone via USB
2. Trust the computer if prompted
3. Build and run the app from Xcode
4. On your iPhone, go to Settings > General > VPN & Device Management
5. Trust the developer profile

## Features
- Real-time message synchronization
- Call management integration
- Contact synchronization
- Notification bridging
- QR code connection setup

## Troubleshooting
- Ensure both devices are on the same WiFi network
- Check firewall settings on Windows PC
- Verify iPhone Companion Pro is running on Windows
`;

    fs.writeFileSync(path.join(iosPackageDir, 'README.md'), iosInstructions);
    
    console.log('✅ iOS companion app packaged');
  }

  async createInstallers() {
    console.log('📦 Creating installers...');
    
    // Create Windows installer
    await this.createWindowsInstaller();
    
    // Create portable version
    await this.createPortableVersion();
    
    console.log('✅ Installers created');
  }

  async createWindowsInstaller() {
    console.log('🖥️  Creating Windows installer...');
    
    // The electron-builder should have already created the installer
    // We'll just verify it exists and copy it to our dist folder
    
    const electronBuilderDist = path.join(this.projectRoot, 'dist');
    const installerFiles = fs.readdirSync(electronBuilderDist).filter(file => 
      file.endsWith('.exe') || file.endsWith('.msi')
    );
    
    if (installerFiles.length > 0) {
      installerFiles.forEach(file => {
        const sourcePath = path.join(electronBuilderDist, file);
        const destPath = path.join(this.buildDir, file);
        fs.copyFileSync(sourcePath, destPath);
      });
      console.log(`✅ Windows installer: ${installerFiles.join(', ')}`);
    } else {
      console.warn('⚠️  No Windows installer found');
    }
  }

  async createPortableVersion() {
    console.log('💼 Creating portable version...');
    
    const portableDir = path.join(this.buildDir, 'portable');
    fs.mkdirSync(portableDir, { recursive: true });
    
    // Copy application files
    const appDir = path.join(this.projectRoot, 'src');
    this.copyDirectory(appDir, path.join(portableDir, 'src'));
    
    // Copy package.json and other necessary files
    const filesToCopy = ['package.json', 'README.md', 'LICENSE'];
    filesToCopy.forEach(file => {
      const sourcePath = path.join(this.projectRoot, file);
      if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, path.join(portableDir, file));
      }
    });
    
    // Create startup script
    const startupScript = `@echo off
echo Starting iPhone Companion Pro...
npm install
npm start
pause`;
    
    fs.writeFileSync(path.join(portableDir, 'start.bat'), startupScript);
    
    // Create zip archive
    await this.createZipArchive(portableDir, path.join(this.buildDir, `iPhone-Companion-Pro-${this.version}-portable.zip`));
    
    console.log('✅ Portable version created');
  }

  async generateDocumentation() {
    console.log('📚 Generating documentation...');
    
    const docsDir = path.join(this.buildDir, 'docs');
    fs.mkdirSync(docsDir, { recursive: true });
    
    // Create comprehensive README
    const mainReadme = `# iPhone Companion Pro v${this.version}

## Overview
iPhone Companion Pro is a comprehensive Windows application that provides seamless integration with your iPhone, offering screen mirroring, messaging, calling, and file synchronization capabilities.

## Features
- 📱 **AirPlay Screen Mirroring**: Mirror your iPhone screen to Windows with touch input support
- 💬 **Message Integration**: Send and receive messages directly from your PC
- 📞 **Call Management**: Make and receive calls through your Windows PC
- 🔄 **Real-time Sync**: Automatic synchronization of contacts, messages, and call logs
- 📊 **Performance Monitoring**: Built-in performance optimization and monitoring
- 🔒 **Secure Connection**: Encrypted communication between devices

## System Requirements

### Windows PC
- Windows 10 version 1903 or later
- 4GB RAM minimum (8GB recommended)
- 500MB free disk space
- WiFi connection
- .NET Framework 4.7.2 or later

### iPhone
- iOS 15.0 or later
- WiFi connection (same network as PC)
- iPhone Companion Pro iOS app installed

## Installation

### Windows Application
1. Download the installer from the releases page
2. Run \`iPhone-Companion-Pro-Setup-${this.version}.exe\`
3. Follow the installation wizard
4. Launch the application from the Start menu

### iOS Companion App
1. Install Xcode on a Mac (required for iOS development)
2. Open the iOS project from the \`ios-companion\` folder
3. Build and install on your iPhone
4. Trust the developer profile in iPhone Settings

## Quick Start Guide

1. **Setup Windows App**:
   - Launch iPhone Companion Pro on Windows
   - Click "Connect iPhone" to generate QR code

2. **Connect iPhone**:
   - Open iPhone Companion Pro app on your iPhone
   - Tap "Scan QR Code" and scan the code from Windows
   - Grant necessary permissions when prompted

3. **Start Using**:
   - Use "Mirror Screen" for AirPlay screen mirroring
   - Access "Messages" for text messaging
   - Use "Calls" for phone call management

## Features Guide

### Screen Mirroring
- Click "Mirror Screen" in the dashboard
- Follow on-screen instructions to connect via AirPlay
- Use mouse to interact with your iPhone screen
- Supports gestures and multi-touch

### Messaging
- View all conversations in the Messages window
- Send new messages or reply to existing ones
- Real-time message synchronization
- Search through message history

### Call Management
- Make calls by entering phone numbers
- Answer incoming calls from the PC
- Call history and contact integration
- Audio routing through PC speakers/microphone

### Synchronization
- Automatic contact synchronization
- Message history sync
- Call log synchronization
- Real-time notifications

## Troubleshooting

### Connection Issues
- Ensure both devices are on the same WiFi network
- Check Windows Firewall settings
- Restart both applications
- Verify iPhone Companion Pro iOS app is running

### Performance Issues
- Check system requirements
- Close unnecessary applications
- Use performance monitoring in the app
- Adjust video quality settings for screen mirroring

### AirPlay Issues
- Ensure AirPlay is enabled on iPhone
- Check for iOS updates
- Restart WiFi connection
- Try different video quality settings

## Support
For support and updates, visit: https://github.com/imthebreezy247/iPhone-Companion-Pro

## License
This project is licensed under the MIT License - see the LICENSE file for details.
`;

    fs.writeFileSync(path.join(docsDir, 'README.md'), mainReadme);
    
    // Create setup guide
    const setupGuide = `# Setup Guide - iPhone Companion Pro

## Detailed Installation Instructions

### Step 1: Windows Application Setup
1. Download the latest installer
2. Right-click and "Run as administrator"
3. Accept the license agreement
4. Choose installation directory
5. Complete installation

### Step 2: iOS App Installation
1. Download Xcode from Mac App Store
2. Open Terminal and clone the project
3. Open \`companion-ios-app/iPhoneCompanionPro.xcodeproj\`
4. Select your development team
5. Build and run on your iPhone

### Step 3: First Connection
1. Launch Windows app
2. Click "Connect iPhone"
3. Note the QR code displayed
4. Open iOS app on iPhone
5. Tap "Scan QR Code"
6. Scan the QR code
7. Grant permissions when prompted

### Step 4: Feature Configuration
1. Enable AirPlay for screen mirroring
2. Grant message access permissions
3. Allow call management permissions
4. Configure notification settings

## Advanced Configuration

### Network Settings
- Ensure port 8080 is open on Windows
- Configure Windows Firewall exceptions
- Use static IP addresses for stability

### Performance Optimization
- Close unnecessary background apps
- Use wired internet connection when possible
- Adjust video quality based on network speed
- Enable hardware acceleration if available

### Security Settings
- Use WPA2/WPA3 WiFi encryption
- Keep both apps updated
- Review permission settings regularly
- Use trusted networks only
`;

    fs.writeFileSync(path.join(docsDir, 'SETUP.md'), setupGuide);
    
    console.log('✅ Documentation generated');
  }

  copyDirectory(source, destination) {
    if (!fs.existsSync(destination)) {
      fs.mkdirSync(destination, { recursive: true });
    }

    const items = fs.readdirSync(source);
    
    items.forEach(item => {
      const sourcePath = path.join(source, item);
      const destPath = path.join(destination, item);
      
      if (fs.statSync(sourcePath).isDirectory()) {
        this.copyDirectory(sourcePath, destPath);
      } else {
        fs.copyFileSync(sourcePath, destPath);
      }
    });
  }

  async createZipArchive(sourceDir, outputPath) {
    return new Promise((resolve, reject) => {
      const output = fs.createWriteStream(outputPath);
      const archive = archiver('zip', { zlib: { level: 9 } });

      output.on('close', () => resolve());
      archive.on('error', reject);

      archive.pipe(output);
      archive.directory(sourceDir, false);
      archive.finalize();
    });
  }
}

// Run build if this script is executed directly
if (require.main === module) {
  const buildManager = new BuildManager();
  buildManager.buildAll().catch(console.error);
}

module.exports = { BuildManager };
