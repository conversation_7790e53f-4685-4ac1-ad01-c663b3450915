const { EventEmitter } = require('events');
const winston = require('winston');

/**
 * BidirectionalSyncService - Orchestrates sync between CRM, Phone Link, and local database
 * Ensures all data sources remain in sync with proper conflict resolution
 */
class BidirectionalSyncService extends EventEmitter {
  constructor(messageService, persistence, supabaseCRM, phoneLinkBridge) {
    super();
    this.messageService = messageService;
    this.persistence = persistence;
    this.supabaseCRM = supabaseCRM;
    this.phoneLinkBridge = phoneLinkBridge;
    this.isRunning = false;
    this.syncQueue = [];
    this.conflictResolver = new ConflictResolver();
    this.syncStats = {
      totalSynced: 0,
      lastSync: null,
      conflicts: 0,
      errors: 0
    };
    
    // Configure logger
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ level, message, timestamp }) => {
          return `${timestamp} [${level.toUpperCase()}] BidirectionalSync: ${message}`;
        })
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'bidirectional-sync.log' })
      ]
    });

    this.syncDirections = {
      CRM_TO_LOCAL: 'crm_to_local',
      LOCAL_TO_CRM: 'local_to_crm',
      PHONELINK_TO_LOCAL: 'phonelink_to_local',
      LOCAL_TO_PHONELINK: 'local_to_phonelink',
      CRM_TO_PHONELINK: 'crm_to_phonelink',
      PHONELINK_TO_CRM: 'phonelink_to_crm'
    };
  }

  async initialize() {
    this.logger.info('🔄 INITIALIZING BIDIRECTIONAL SYNC SERVICE 🔄');
    
    // Set up event listeners for all data sources
    this.setupEventListeners();
    
    // Initialize conflict resolver
    await this.conflictResolver.initialize();
    
    this.logger.info('✅ Bidirectional Sync Service initialized');
    return true;
  }

  setupEventListeners() {
    // Listen to MessageService events
    this.messageService.on('message-received', (message) => {
      this.queueSync(message, 'message-received', this.syncDirections.LOCAL_TO_CRM);
    });

    this.messageService.on('message-sent', (message) => {
      this.queueSync(message, 'message-sent', this.syncDirections.LOCAL_TO_CRM);
    });

    // Listen to Supabase CRM events
    if (this.supabaseCRM) {
      this.supabaseCRM.on('communication-received', (communication) => {
        this.queueSync(communication, 'crm-communication', this.syncDirections.CRM_TO_LOCAL);
      });
    }

    // Listen to Phone Link events
    if (this.phoneLinkBridge) {
      this.phoneLinkBridge.on('data-synced', (data) => {
        this.queueSync(data, 'phonelink-sync', this.syncDirections.PHONELINK_TO_LOCAL);
      });

      this.phoneLinkBridge.on('message-extracted', (message) => {
        this.queueSync(message, 'phonelink-message', this.syncDirections.PHONELINK_TO_LOCAL);
      });
    }

    // Listen to persistence events
    this.persistence.on('data-saved', (type) => {
      if (type === 'message') {
        this.emit('sync-event', { type: 'local-data-saved', dataType: type });
      }
    });
  }

  async startBidirectionalSync() {
    if (this.isRunning) {
      this.logger.warn('Bidirectional sync already running');
      return;
    }

    this.logger.info('🚀 Starting bidirectional sync...');
    this.isRunning = true;

    // Start sync queue processor
    this.startSyncProcessor();

    // Start periodic full sync
    this.startPeriodicFullSync();

    // Perform initial sync
    await this.performInitialSync();

    this.emit('sync-started');
    this.logger.info('✅ Bidirectional sync started');
  }

  startSyncProcessor() {
    // Process sync queue every 1 second for near real-time sync
    this.syncInterval = setInterval(async () => {
      if (this.syncQueue.length > 0) {
        const batch = this.syncQueue.splice(0, 5); // Process up to 5 items at once
        await this.processSyncBatch(batch);
      }
    }, 1000);
  }

  startPeriodicFullSync() {
    // Full bidirectional sync every 5 minutes
    setInterval(async () => {
      if (this.isRunning) {
        await this.performFullBidirectionalSync();
      }
    }, 5 * 60 * 1000);
  }

  queueSync(data, eventType, direction) {
    const syncItem = {
      id: this.generateSyncId(),
      data,
      eventType,
      direction,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: 3
    };

    this.syncQueue.push(syncItem);
    this.logger.info(`📤 Queued ${direction} sync: ${eventType} - ${syncItem.id}`);
  }

  async processSyncBatch(batch) {
    this.logger.info(`🔄 Processing sync batch of ${batch.length} items`);

    for (const item of batch) {
      try {
        await this.processSyncItem(item);
        this.syncStats.totalSynced++;
      } catch (error) {
        this.logger.error(`❌ Failed to sync item ${item.id}: ${error.message}`);
        this.syncStats.errors++;
        
        // Retry logic
        if (item.retryCount < item.maxRetries) {
          item.retryCount++;
          this.syncQueue.push(item);
          this.logger.info(`🔄 Re-queued item ${item.id} for retry (${item.retryCount}/${item.maxRetries})`);
        } else {
          this.logger.error(`❌ Item ${item.id} failed after ${item.maxRetries} retries`);
          this.emit('sync-failed', item);
        }
      }
    }

    this.syncStats.lastSync = new Date();
  }

  async processSyncItem(item) {
    const { data, eventType, direction } = item;

    switch (direction) {
      case this.syncDirections.CRM_TO_LOCAL:
        await this.syncCRMToLocal(data, eventType);
        break;
      
      case this.syncDirections.LOCAL_TO_CRM:
        await this.syncLocalToCRM(data, eventType);
        break;
      
      case this.syncDirections.PHONELINK_TO_LOCAL:
        await this.syncPhoneLinkToLocal(data, eventType);
        break;
      
      case this.syncDirections.LOCAL_TO_PHONELINK:
        await this.syncLocalToPhoneLink(data, eventType);
        break;
      
      case this.syncDirections.CRM_TO_PHONELINK:
        await this.syncCRMToPhoneLink(data, eventType);
        break;
      
      case this.syncDirections.PHONELINK_TO_CRM:
        await this.syncPhoneLinkToCRM(data, eventType);
        break;
      
      default:
        this.logger.warn(`Unknown sync direction: ${direction}`);
    }

    this.emit('sync-completed', { item, direction });
  }

  async syncCRMToLocal(data, eventType) {
    this.logger.info(`💼➡️📱 Syncing CRM to Local: ${eventType}`);
    
    // Convert CRM communication to local message format
    const localMessage = this.convertCRMToLocalMessage(data);
    
    // Check for conflicts
    const existing = await this.findExistingMessage(localMessage);
    if (existing) {
      const resolution = await this.conflictResolver.resolve(existing, localMessage, 'crm_to_local');
      if (resolution.action === 'skip') {
        this.logger.info(`⏭️ Skipping duplicate message: ${localMessage.messageText?.substring(0, 50)}...`);
        return;
      }
      localMessage.id = existing.id; // Update existing
    }
    
    // Save to local database
    await this.persistence.saveMessage(localMessage);
    
    // Update message service
    this.messageService.addMessage(localMessage);
  }

  async syncLocalToCRM(data, eventType) {
    if (!this.supabaseCRM) {
      this.logger.warn('Supabase CRM not available, skipping Local to CRM sync');
      return;
    }

    this.logger.info(`📱➡️💼 Syncing Local to CRM: ${eventType}`);
    
    // Convert local message to CRM communication format
    const crmCommunication = this.convertLocalToCRMCommunication(data);
    
    // Check for existing CRM record
    const existing = await this.findExistingCRMCommunication(crmCommunication);
    if (existing) {
      this.logger.info(`⏭️ CRM communication already exists for: ${crmCommunication.phone_number}`);
      return;
    }
    
    // Save to CRM
    await this.supabaseCRM.saveCommunication('default-user', crmCommunication);
  }

  async syncPhoneLinkToLocal(data, eventType) {
    this.logger.info(`📞➡️📱 Syncing Phone Link to Local: ${eventType}`);
    
    // Convert Phone Link data to local format
    const localMessages = this.convertPhoneLinkToLocalMessages(data);
    
    for (const localMessage of localMessages) {
      // Check for conflicts
      const existing = await this.findExistingMessage(localMessage);
      if (existing) {
        const resolution = await this.conflictResolver.resolve(existing, localMessage, 'phonelink_to_local');
        if (resolution.action === 'skip') {
          continue;
        }
        localMessage.id = existing.id;
      }
      
      // Save to local database
      await this.persistence.saveMessage(localMessage);
      
      // Update message service
      this.messageService.addMessage(localMessage);
    }
  }

  async syncLocalToPhoneLink(data, eventType) {
    this.logger.info(`📱➡️📞 Syncing Local to Phone Link: ${eventType}`);
    
    // Phone Link sync is primarily read-only from our perspective
    // We mainly extract data from Phone Link rather than push to it
    this.logger.info('Phone Link is primarily a data source, skipping write operation');
  }

  async syncCRMToPhoneLink(data, eventType) {
    this.logger.info(`💼➡️📞 Syncing CRM to Phone Link: ${eventType}`);
    
    // This would involve sending messages via Phone Link based on CRM triggers
    // Implementation depends on Phone Link's API capabilities
    this.logger.info('CRM to Phone Link sync not implemented - Phone Link is primarily a data source');
  }

  async syncPhoneLinkToCRM(data, eventType) {
    if (!this.supabaseCRM) {
      this.logger.warn('Supabase CRM not available, skipping Phone Link to CRM sync');
      return;
    }

    this.logger.info(`📞➡️💼 Syncing Phone Link to CRM: ${eventType}`);
    
    // Convert Phone Link data to CRM format
    const crmCommunications = this.convertPhoneLinkToCRMCommunications(data);
    
    for (const communication of crmCommunications) {
      // Check for existing CRM record
      const existing = await this.findExistingCRMCommunication(communication);
      if (existing) {
        continue;
      }
      
      // Save to CRM
      await this.supabaseCRM.saveCommunication('default-user', communication);
    }
  }

  async performInitialSync() {
    this.logger.info('🔄 Performing initial bidirectional sync...');
    
    try {
      // Load data from all sources
      const [localMessages, crmCommunications, phoneLinkData] = await Promise.all([
        this.persistence.loadAllMessages(1000),
        this.loadCRMCommunications(),
        this.loadPhoneLinkData()
      ]);
      
      // Sync local to CRM
      for (const message of localMessages) {
        if (message.source !== 'crm_sync') { // Avoid circular sync
          this.queueSync(message, 'initial-sync', this.syncDirections.LOCAL_TO_CRM);
        }
      }
      
      // Sync CRM to local
      for (const communication of crmCommunications) {
        this.queueSync(communication, 'initial-sync', this.syncDirections.CRM_TO_LOCAL);
      }
      
      // Sync Phone Link to local
      if (phoneLinkData && phoneLinkData.length > 0) {
        this.queueSync(phoneLinkData, 'initial-sync', this.syncDirections.PHONELINK_TO_LOCAL);
      }
      
      this.logger.info(`✅ Initial sync queued: ${localMessages.length} local, ${crmCommunications.length} CRM, ${phoneLinkData?.length || 0} Phone Link`);
    } catch (error) {
      this.logger.error(`❌ Initial sync failed: ${error.message}`);
    }
  }

  async performFullBidirectionalSync() {
    this.logger.info('🔄 Performing full bidirectional sync...');
    
    try {
      // Get recent data from all sources (last hour)
      const since = new Date(Date.now() - 60 * 60 * 1000);
      
      const [recentLocal, recentCRM, recentPhoneLink] = await Promise.all([
        this.persistence.loadMessagesAfter(since.getTime(), 100),
        this.loadRecentCRMCommunications(since),
        this.loadRecentPhoneLinkData(since)
      ]);
      
      // Cross-sync all recent data
      this.logger.info(`📊 Full sync: ${recentLocal.length} local, ${recentCRM.length} CRM, ${recentPhoneLink?.length || 0} Phone Link`);
      
      // Queue all syncs
      recentLocal.forEach(msg => this.queueSync(msg, 'full-sync', this.syncDirections.LOCAL_TO_CRM));
      recentCRM.forEach(comm => this.queueSync(comm, 'full-sync', this.syncDirections.CRM_TO_LOCAL));
      if (recentPhoneLink) {
        this.queueSync(recentPhoneLink, 'full-sync', this.syncDirections.PHONELINK_TO_LOCAL);
      }
      
    } catch (error) {
      this.logger.error(`❌ Full bidirectional sync failed: ${error.message}`);
    }
  }

  // Data conversion methods
  convertCRMToLocalMessage(crmData) {
    return {
      threadId: crmData.phone_number,
      phoneNumber: crmData.phone_number,
      contactName: 'CRM Contact',
      messageText: crmData.content,
      timestamp: new Date(crmData.created_at),
      isOutgoing: crmData.direction === 'outbound',
      isDelivered: true,
      isRead: true,
      source: 'crm_sync',
      externalId: crmData.id
    };
  }

  convertLocalToCRMCommunication(localData) {
    return {
      phone_number: localData.phoneNumber,
      direction: localData.isOutgoing ? 'outbound' : 'inbound',
      type: 'sms',
      content: localData.messageText || localData.text,
      external_id: localData.id,
      metadata: {
        source: 'intel_unison_sync',
        original_timestamp: localData.timestamp,
        sync_timestamp: Date.now()
      }
    };
  }

  convertPhoneLinkToLocalMessages(phoneLinkData) {
    if (!Array.isArray(phoneLinkData)) {
      phoneLinkData = [phoneLinkData];
    }
    
    return phoneLinkData.map(item => ({
      threadId: item.phone_number || item.phoneNumber,
      phoneNumber: item.phone_number || item.phoneNumber,
      contactName: item.contact_name || 'Phone Link Contact',
      messageText: item.message_text || item.content,
      timestamp: new Date(item.timestamp || Date.now()),
      isOutgoing: item.is_outgoing || false,
      isDelivered: true,
      isRead: true,
      source: 'phonelink_sync',
      externalId: item.id
    }));
  }

  convertPhoneLinkToCRMCommunications(phoneLinkData) {
    if (!Array.isArray(phoneLinkData)) {
      phoneLinkData = [phoneLinkData];
    }
    
    return phoneLinkData.map(item => ({
      phone_number: item.phone_number || item.phoneNumber,
      direction: item.is_outgoing ? 'outbound' : 'inbound',
      type: 'sms',
      content: item.message_text || item.content,
      external_id: item.id,
      metadata: {
        source: 'phonelink_sync',
        original_timestamp: item.timestamp,
        sync_timestamp: Date.now()
      }
    }));
  }

  // Data lookup methods
  async findExistingMessage(message) {
    try {
      // Look for message by phone number, text, and approximate timestamp
      const existingMessages = await this.persistence.loadMessagesForThread(message.threadId, 50);
      
      return existingMessages.find(existing => {
        const textMatch = existing.messageText === message.messageText;
        const timeMatch = Math.abs(new Date(existing.timestamp) - new Date(message.timestamp)) < 5000; // 5 second tolerance
        return textMatch && timeMatch;
      });
    } catch (error) {
      this.logger.error(`Error finding existing message: ${error.message}`);
      return null;
    }
  }

  async findExistingCRMCommunication(communication) {
    // This would query Supabase to find existing communication
    // Implementation depends on Supabase client availability
    return null;
  }

  // Data loading methods
  async loadCRMCommunications() {
    if (!this.supabaseCRM) return [];
    
    try {
      // Load recent CRM communications
      // This would use the Supabase client to query communications
      return [];
    } catch (error) {
      this.logger.error(`Error loading CRM communications: ${error.message}`);
      return [];
    }
  }

  async loadPhoneLinkData() {
    if (!this.phoneLinkBridge) return [];
    
    try {
      return await this.phoneLinkBridge.getMessagesForCRM();
    } catch (error) {
      this.logger.error(`Error loading Phone Link data: ${error.message}`);
      return [];
    }
  }

  async loadRecentCRMCommunications(since) {
    // Load CRM communications since timestamp
    return [];
  }

  async loadRecentPhoneLinkData(since) {
    // Load Phone Link data since timestamp
    return [];
  }

  generateSyncId() {
    return `bdsync_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  getSyncStatus() {
    return {
      isRunning: this.isRunning,
      queueSize: this.syncQueue.length,
      stats: this.syncStats,
      lastSync: this.syncStats.lastSync,
      syncDirections: Object.keys(this.syncDirections),
      conflictResolver: this.conflictResolver.getStats()
    };
  }

  async stopBidirectionalSync() {
    this.logger.info('⏹️ Stopping bidirectional sync service...');
    this.isRunning = false;

    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.emit('sync-stopped');
    this.logger.info('✅ Bidirectional sync service stopped');
  }
}

/**
 * ConflictResolver - Handles conflicts when the same data exists in multiple sources
 */
class ConflictResolver {
  constructor() {
    this.resolutionStrategies = {
      newest_wins: 'newest_wins',
      source_priority: 'source_priority',
      manual_review: 'manual_review'
    };
    
    this.sourcePriority = {
      'iphone_direct': 1,    // Highest priority - direct from iPhone
      'phonelink_sync': 2,   // Second - Phone Link extraction
      'crm_sync': 3,         // Third - CRM data
      'local_sync': 4        // Lowest - Local modifications
    };
    
    this.conflictStats = {
      total: 0,
      resolved: 0,
      manual: 0
    };
  }

  async initialize() {
    // Initialize conflict resolution system
    return true;
  }

  async resolve(existing, incoming, syncDirection) {
    this.conflictStats.total++;
    
    // Check if they're actually the same
    if (this.areMessagesIdentical(existing, incoming)) {
      return { action: 'skip', reason: 'identical' };
    }
    
    // Use newest wins strategy by default
    const strategy = this.resolutionStrategies.newest_wins;
    
    switch (strategy) {
      case this.resolutionStrategies.newest_wins:
        return this.resolveByNewest(existing, incoming);
      
      case this.resolutionStrategies.source_priority:
        return this.resolveBySourcePriority(existing, incoming);
      
      case this.resolutionStrategies.manual_review:
        return this.resolveManually(existing, incoming);
      
      default:
        return this.resolveByNewest(existing, incoming);
    }
  }

  areMessagesIdentical(msg1, msg2) {
    return msg1.messageText === msg2.messageText &&
           msg1.phoneNumber === msg2.phoneNumber &&
           Math.abs(new Date(msg1.timestamp) - new Date(msg2.timestamp)) < 1000;
  }

  resolveByNewest(existing, incoming) {
    const existingTime = new Date(existing.timestamp).getTime();
    const incomingTime = new Date(incoming.timestamp).getTime();
    
    this.conflictStats.resolved++;
    
    if (incomingTime > existingTime) {
      return { action: 'update', reason: 'incoming_newer', data: incoming };
    } else {
      return { action: 'skip', reason: 'existing_newer' };
    }
  }

  resolveBySourcePriority(existing, incoming) {
    const existingPriority = this.sourcePriority[existing.source] || 999;
    const incomingPriority = this.sourcePriority[incoming.source] || 999;
    
    this.conflictStats.resolved++;
    
    if (incomingPriority < existingPriority) {
      return { action: 'update', reason: 'incoming_higher_priority', data: incoming };
    } else {
      return { action: 'skip', reason: 'existing_higher_priority' };
    }
  }

  resolveManually(existing, incoming) {
    this.conflictStats.manual++;
    
    // For now, defer to newest wins
    // In a full implementation, this would queue for manual review
    return this.resolveByNewest(existing, incoming);
  }

  getStats() {
    return { ...this.conflictStats };
  }
}

module.exports = { BidirectionalSyncService, ConflictResolver };