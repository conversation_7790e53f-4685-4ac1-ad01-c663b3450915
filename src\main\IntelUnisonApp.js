const { EventEmitter } = require('events');
const winston = require('winston');
const path = require('path');

// Import all services
const { MessageService } = require('./services/MessageService');
const { BeastPersistence } = require('./services/BeastPersistence');
const { MessageExtractionService } = require('./services/MessageExtractionService');
const { UnisonProtocolService } = require('./services/UnisonProtocolService');
const SupabaseCRMService = require('./services/SupabaseCRMService');
const { IntelUnisonSyncService } = require('./services/IntelUnisonSyncService');
const { BidirectionalSyncService } = require('./services/BidirectionalSyncService');
const { SearchService } = require('./services/SearchService');
const { PhoneLinkBridge } = require('./services/PhoneLinkBridge');

/**
 * IntelUnisonApp - Main application orchestrator
 * Coordinates all services to create the complete Intel Unison++ experience
 */
class IntelUnisonApp extends EventEmitter {
  constructor() {
    super();
    this.services = new Map();
    this.isInitialized = false;
    this.isRunning = false;
    this.startupTime = null;
    
    // Configure logger
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ level, message, timestamp }) => {
          return `${timestamp} [${level.toUpperCase()}] IntelUnisonApp: ${message}`;
        })
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'intel-unison-app.log' })
      ]
    });

    this.serviceDefinitions = [
      { name: 'persistence', class: BeastPersistence, dependencies: [] },
      { name: 'messageExtraction', class: MessageExtractionService, dependencies: ['persistence'] },
      { name: 'unisonProtocol', class: UnisonProtocolService, dependencies: ['persistence'] },
      { name: 'messageService', class: MessageService, dependencies: ['persistence', 'messageExtraction', 'unisonProtocol'] },
      { name: 'phoneLinkBridge', class: PhoneLinkBridge, dependencies: [] },
      { name: 'searchService', class: SearchService, dependencies: ['persistence'] },
      { name: 'supabaseCRM', class: SupabaseCRMService, dependencies: ['messageService'] },
      { name: 'syncService', class: IntelUnisonSyncService, dependencies: ['messageService', 'persistence', 'supabaseCRM'] },
      { name: 'bidirectionalSync', class: BidirectionalSyncService, dependencies: ['messageService', 'persistence', 'supabaseCRM', 'phoneLinkBridge'] }
    ];
  }

  async initialize() {
    if (this.isInitialized) {
      this.logger.warn('Application already initialized');
      return;
    }

    this.logger.info('🔥 INITIALIZING INTEL UNISON++ APPLICATION 🔥');
    this.startupTime = Date.now();

    try {
      // Initialize services in dependency order
      await this.initializeServices();
      
      // Set up inter-service communication
      this.setupServiceCommunication();
      
      // Verify all services are ready
      await this.verifyServices();
      
      this.isInitialized = true;
      this.logger.info(`✅ Intel Unison++ Application initialized in ${Date.now() - this.startupTime}ms`);
      
      this.emit('initialized');
      
    } catch (error) {
      this.logger.error(`❌ Initialization failed: ${error.message}`);
      throw error;
    }
  }

  async initializeServices() {
    this.logger.info('🔧 Initializing services...');

    for (const serviceDef of this.serviceDefinitions) {
      try {
        this.logger.info(`🔧 Initializing ${serviceDef.name}...`);
        
        // Get dependencies
        const dependencies = serviceDef.dependencies.map(dep => this.services.get(dep));
        
        // Create service instance
        const serviceInstance = new serviceDef.class(...dependencies);
        
        // Initialize the service
        if (typeof serviceInstance.initialize === 'function') {
          await serviceInstance.initialize();
        }
        
        // Store the service
        this.services.set(serviceDef.name, serviceInstance);
        
        this.logger.info(`✅ ${serviceDef.name} initialized`);
        
      } catch (error) {
        this.logger.error(`❌ Failed to initialize ${serviceDef.name}: ${error.message}`);
        throw error;
      }
    }
  }

  setupServiceCommunication() {
    this.logger.info('🔗 Setting up service communication...');

    // Set up event forwarding and communication between services
    const messageService = this.services.get('messageService');
    const syncService = this.services.get('syncService');
    const bidirectionalSync = this.services.get('bidirectionalSync');
    const searchService = this.services.get('searchService');
    const supabaseCRM = this.services.get('supabaseCRM');

    // Forward message events to sync services
    if (messageService && syncService) {
      messageService.on('message-received', (message) => {
        syncService.emit('message-received', message);
      });
      
      messageService.on('message-sent', (message) => {
        syncService.emit('message-sent', message);
      });
    }

    // Forward sync events to bidirectional sync
    if (syncService && bidirectionalSync) {
      syncService.on('sync-update', (data) => {
        bidirectionalSync.emit('sync-update', data);
      });
    }

    // Forward CRM events
    if (supabaseCRM && bidirectionalSync) {
      supabaseCRM.on('communication-received', (communication) => {
        bidirectionalSync.emit('crm-communication', communication);
      });
    }

    // Set up global error handling
    this.services.forEach((service, name) => {
      if (service instanceof EventEmitter) {
        service.on('error', (error) => {
          this.logger.error(`❌ Error in ${name}: ${error.message}`);
          this.emit('service-error', { service: name, error });
        });
      }
    });

    this.logger.info('✅ Service communication established');
  }

  async verifyServices() {
    this.logger.info('🔍 Verifying services...');

    const verificationResults = new Map();

    for (const [name, service] of this.services) {
      try {
        let status = 'unknown';
        
        // Check if service has a health check method
        if (typeof service.getStatus === 'function') {
          const serviceStatus = service.getStatus();
          status = serviceStatus.isRunning !== false ? 'healthy' : 'unhealthy';
        } else if (typeof service.isInitialized !== 'undefined') {
          status = service.isInitialized ? 'healthy' : 'unhealthy';
        } else {
          status = 'healthy'; // Assume healthy if no status method
        }
        
        verificationResults.set(name, { status, healthy: status === 'healthy' });
        
      } catch (error) {
        verificationResults.set(name, { status: 'error', healthy: false, error: error.message });
      }
    }

    // Log verification results
    const healthyServices = Array.from(verificationResults.values()).filter(result => result.healthy);
    const unhealthyServices = Array.from(verificationResults.entries()).filter(([_, result]) => !result.healthy);

    this.logger.info(`✅ Service verification: ${healthyServices.length}/${verificationResults.size} services healthy`);
    
    if (unhealthyServices.length > 0) {
      this.logger.warn('⚠️ Unhealthy services:');
      unhealthyServices.forEach(([name, result]) => {
        this.logger.warn(`   - ${name}: ${result.status} ${result.error ? `(${result.error})` : ''}`);
      });
    }

    return verificationResults;
  }

  async start() {
    if (!this.isInitialized) {
      throw new Error('Application must be initialized before starting');
    }

    if (this.isRunning) {
      this.logger.warn('Application already running');
      return;
    }

    this.logger.info('🚀 Starting Intel Unison++ Application...');

    try {
      // Start core services
      await this.startCoreServices();
      
      // Start sync services
      await this.startSyncServices();
      
      // Start API services
      await this.startAPIServices();
      
      this.isRunning = true;
      this.logger.info('✅ Intel Unison++ Application started successfully');
      
      this.emit('started');
      
      // Log startup summary
      this.logStartupSummary();
      
    } catch (error) {
      this.logger.error(`❌ Failed to start application: ${error.message}`);
      throw error;
    }
  }

  async startCoreServices() {
    this.logger.info('🔧 Starting core services...');

    // Start message extraction
    const messageExtraction = this.services.get('messageExtraction');
    if (messageExtraction && typeof messageExtraction.startExtraction === 'function') {
      await messageExtraction.startExtraction();
    }

    // Start message service
    const messageService = this.services.get('messageService');
    if (messageService && typeof messageService.startSync === 'function') {
      messageService.startSync();
    }

    this.logger.info('✅ Core services started');
  }

  async startSyncServices() {
    this.logger.info('🔄 Starting sync services...');

    // Start real-time sync
    const syncService = this.services.get('syncService');
    if (syncService && typeof syncService.startRealtimeSync === 'function') {
      await syncService.startRealtimeSync();
    }

    // Start bidirectional sync
    const bidirectionalSync = this.services.get('bidirectionalSync');
    if (bidirectionalSync && typeof bidirectionalSync.startBidirectionalSync === 'function') {
      await bidirectionalSync.startBidirectionalSync();
    }

    this.logger.info('✅ Sync services started');
  }

  async startAPIServices() {
    this.logger.info('🌐 Starting API services...');

    // Start Supabase CRM API
    const supabaseCRM = this.services.get('supabaseCRM');
    if (supabaseCRM && typeof supabaseCRM.start === 'function') {
      await supabaseCRM.start(7777);
    }

    this.logger.info('✅ API services started');
  }

  logStartupSummary() {
    const totalStartupTime = Date.now() - this.startupTime;
    
    this.logger.info('📊 INTEL UNISON++ STARTUP SUMMARY');
    this.logger.info(`   ⏱️  Total startup time: ${totalStartupTime}ms`);
    this.logger.info(`   🔧 Services initialized: ${this.services.size}`);
    this.logger.info(`   📡 API endpoints: http://localhost:7777/api/health`);
    this.logger.info(`   💾 Database: SQLite with FTS5 search`);
    this.logger.info(`   🔄 Real-time sync: Active`);
    this.logger.info(`   📱 iPhone integration: Ready`);
    this.logger.info(`   💼 CRM integration: Ready`);
    this.logger.info('🎉 INTEL UNISON++ IS READY TO SYNC YOUR IPHONE DATA!');
  }

  async stop() {
    if (!this.isRunning) {
      this.logger.warn('Application not running');
      return;
    }

    this.logger.info('⏹️ Stopping Intel Unison++ Application...');

    try {
      // Stop services in reverse order
      const serviceOrder = [...this.serviceDefinitions].reverse();
      
      for (const serviceDef of serviceOrder) {
        const service = this.services.get(serviceDef.name);
        if (service && typeof service.stop === 'function') {
          try {
            await service.stop();
            this.logger.info(`✅ ${serviceDef.name} stopped`);
          } catch (error) {
            this.logger.error(`❌ Error stopping ${serviceDef.name}: ${error.message}`);
          }
        }
      }

      this.isRunning = false;
      this.logger.info('✅ Intel Unison++ Application stopped');
      
      this.emit('stopped');
      
    } catch (error) {
      this.logger.error(`❌ Error during shutdown: ${error.message}`);
      throw error;
    }
  }

  // Public API methods
  async sendMessage(phoneNumber, text) {
    const messageService = this.services.get('messageService');
    if (!messageService) {
      throw new Error('Message service not available');
    }
    
    return await messageService.sendMessage(phoneNumber, text);
  }

  async searchMessages(query, options = {}) {
    const searchService = this.services.get('searchService');
    if (!searchService) {
      throw new Error('Search service not available');
    }
    
    return await searchService.search(query, options);
  }

  getConversations() {
    const messageService = this.services.get('messageService');
    if (!messageService) {
      throw new Error('Message service not available');
    }
    
    return messageService.getConversations();
  }

  getApplicationStatus() {
    return {
      isInitialized: this.isInitialized,
      isRunning: this.isRunning,
      startupTime: this.startupTime,
      uptime: this.startupTime ? Date.now() - this.startupTime : 0,
      services: this.getServiceStatuses(),
      memoryUsage: process.memoryUsage(),
      version: '1.0.0'
    };
  }

  getServiceStatuses() {
    const statuses = {};
    
    this.services.forEach((service, name) => {
      try {
        if (typeof service.getStatus === 'function') {
          statuses[name] = service.getStatus();
        } else if (typeof service.getSyncStatus === 'function') {
          statuses[name] = service.getSyncStatus();
        } else {
          statuses[name] = { status: 'running' };
        }
      } catch (error) {
        statuses[name] = { status: 'error', error: error.message };
      }
    });
    
    return statuses;
  }

  getService(serviceName) {
    return this.services.get(serviceName);
  }

  // Event delegation methods
  onMessage(callback) {
    const messageService = this.services.get('messageService');
    if (messageService) {
      messageService.on('message-received', callback);
    }
  }

  onMessageSent(callback) {
    const messageService = this.services.get('messageService');
    if (messageService) {
      messageService.on('message-sent', callback);
    }
  }

  onSyncUpdate(callback) {
    const syncService = this.services.get('syncService');
    if (syncService) {
      syncService.on('sync-update', callback);
    }
  }

  onError(callback) {
    this.on('service-error', callback);
  }
}

module.exports = { IntelUnisonApp };