#!/usr/bin/env node

// Test Phase 1: Data Extraction & Storage
// This tests the Intel Unison++ message extraction and persistence system

const path = require('path');
const fs = require('fs');

// Mock electron app for testing
const mockApp = {
  getPath: (type) => {
    if (type === 'userData') {
      return path.join(__dirname, 'test-data');
    }
    return './';
  }
};

// Set up mock electron
global.app = mockApp;
require.cache[require.resolve('electron')] = { exports: { app: mockApp } };

// Import our services
const { BeastPersistence } = require('./src/main/services/BeastPersistence');
const { MessageExtractionService } = require('./src/main/services/MessageExtractionService');

async function runPhase1Tests() {
  console.log('🔥 TESTING PHASE 1: DATA EXTRACTION & STORAGE 🔥');
  console.log('================================================');

  // Ensure test data directory exists
  const testDataDir = path.join(__dirname, 'test-data');
  if (!fs.existsSync(testDataDir)) {
    fs.mkdirSync(testDataDir, { recursive: true });
  }

  // Test 1: BeastPersistence Initialization
  console.log('\n📊 Test 1: BeastPersistence Initialization');
  console.log('-------------------------------------------');
  
  const persistence = new BeastPersistence();
  await persistence.initialize();
  
  console.log('✅ BeastPersistence initialized successfully');
  console.log('✅ Intel Unison-style database schema created');

  // Test 2: Message Storage
  console.log('\n📬 Test 2: Message Storage & Retrieval');
  console.log('--------------------------------------');
  
  const testMessages = [
    {
      threadId: '+15551234567',
      phoneNumber: '+15551234567',
      contactName: 'John Doe',
      messageText: 'Hey, how are you doing?',
      timestamp: new Date('2024-01-15T10:30:00Z'),
      isOutgoing: false,
      isDelivered: true,
      isRead: false,
      source: 'phonelink'
    },
    {
      threadId: '+15551234567',
      phoneNumber: '+15551234567',
      contactName: 'John Doe',
      messageText: 'I am doing great, thanks for asking!',
      timestamp: new Date('2024-01-15T10:31:00Z'),
      isOutgoing: true,
      isDelivered: true,
      isRead: true,
      source: 'user'
    },
    {
      threadId: '+15559876543',
      phoneNumber: '+15559876543',
      contactName: 'Jane Smith',
      messageText: 'Can we meet tomorrow at 3 PM?',
      timestamp: new Date('2024-01-15T11:00:00Z'),
      isOutgoing: false,
      isDelivered: true,
      isRead: false,
      source: 'phonelink'
    }
  ];

  // Save test messages
  for (const message of testMessages) {
    await persistence.saveMessage(message);
  }
  console.log(`✅ Saved ${testMessages.length} test messages to database`);

  // Test message retrieval
  const allMessages = await persistence.loadAllMessages();
  console.log(`✅ Retrieved ${allMessages.length} messages from database`);

  const johnMessages = await persistence.loadMessagesForThread('+15551234567');
  console.log(`✅ Retrieved ${johnMessages.length} messages for John Doe's thread`);

  // Test 3: Message Threads
  console.log('\n💬 Test 3: Message Threads');
  console.log('--------------------------');
  
  const threads = await persistence.loadMessageThreads();
  console.log(`✅ Retrieved ${threads.length} message threads`);
  
  for (const thread of threads) {
    console.log(`   📱 ${thread.contactName} (${thread.phoneNumber}): ${thread.messageCount} messages, ${thread.unreadCount} unread`);
  }

  // Test 4: Contacts Storage
  console.log('\n👥 Test 4: Contacts Storage');
  console.log('---------------------------');
  
  const testContacts = [
    {
      phoneNumber: '+15551234567',
      displayName: 'John Doe',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>'
    },
    {
      phoneNumber: '+15559876543',
      displayName: 'Jane Smith',
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>'
    }
  ];

  for (const contact of testContacts) {
    await persistence.saveContact(contact);
  }
  console.log(`✅ Saved ${testContacts.length} test contacts`);

  const contacts = await persistence.loadContacts();
  console.log(`✅ Retrieved ${contacts.length} contacts from database`);

  // Test 5: Search Functionality
  console.log('\n🔍 Test 5: Full-Text Search');
  console.log('----------------------------');
  
  const searchResults = await persistence.searchMessages('great');
  console.log(`✅ Search for 'great' returned ${searchResults.length} results`);

  // Test 6: Database Statistics
  console.log('\n📊 Test 6: Database Statistics');
  console.log('-------------------------------');
  
  const stats = await persistence.getMessageStats();
  console.log('✅ Database statistics:', stats);

  // Test 7: Message Extraction Service
  console.log('\n🔍 Test 7: Message Extraction Service');
  console.log('-------------------------------------');
  
  const extractor = new MessageExtractionService(persistence);
  await extractor.initialize();
  console.log('✅ Message Extraction Service initialized');

  const extractorStatus = extractor.getStatus();
  console.log('✅ Extraction status:', extractorStatus);

  // Test 8: Export for CRM
  console.log('\n📤 Test 8: CRM Export');
  console.log('---------------------');
  
  const crmExport = await persistence.exportForCRM('json');
  console.log('✅ CRM export generated successfully');
  console.log(`   📊 Export contains: ${JSON.parse(crmExport).totalRecords} total records`);

  // Test 9: Backup System
  console.log('\n💾 Test 9: Backup System');
  console.log('------------------------');
  
  await persistence.createBackup();
  console.log('✅ Database backup created successfully');

  // Test 10: Bulk Operations
  console.log('\n⚡ Test 10: Bulk Operations');
  console.log('---------------------------');
  
  const bulkMessages = Array.from({ length: 100 }, (_, i) => ({
    threadId: '+15551111111',
    phoneNumber: '+15551111111',
    contactName: 'Bulk Test Contact',
    messageText: `Bulk message ${i + 1}`,
    timestamp: new Date(Date.now() + i * 1000),
    isOutgoing: i % 2 === 0,
    isDelivered: true,
    isRead: true,
    source: 'test'
  }));

  console.time('Bulk save performance');
  await persistence.bulkSaveMessages(bulkMessages);
  console.timeEnd('Bulk save performance');
  
  console.log(`✅ Bulk saved ${bulkMessages.length} messages`);

  // Final Statistics
  console.log('\n📈 Final Database Statistics');
  console.log('============================');
  
  const finalStats = await persistence.getMessageStats();
  console.log('Final stats:', finalStats);

  const finalThreads = await persistence.loadMessageThreads();
  console.log(`Total threads: ${finalThreads.length}`);

  // Clean up
  await extractor.stopExtraction();
  persistence.close();

  console.log('\n🎉 PHASE 1 TESTS COMPLETED SUCCESSFULLY! 🎉');
  console.log('===========================================');
  console.log('✅ Intel Unison++ message extraction and persistence system is working!');
  console.log('✅ Database schema created with proper indexes');
  console.log('✅ Full-text search (FTS5) is operational');
  console.log('✅ Message extraction service is initialized');
  console.log('✅ CRM export functionality is working');
  console.log('✅ Backup system is operational');
  console.log('✅ Bulk operations are performant');
  console.log('');
  console.log('🚀 Ready for Phase 2: Intel Unison Protocol Recreation');
}

// Run the tests
runPhase1Tests().catch(error => {
  console.error('❌ Phase 1 tests failed:', error);
  process.exit(1);
});