const { EventEmitter } = require('events');
const WebSocket = require('ws');
const crypto = require('crypto');
const Store = require('electron-store');

class WebSocketTunnel extends EventEmitter {
  constructor(config) {
    super();
    this.config = config;
    this.store = new Store();
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.reconnectDelay = 5000;
    this.heartbeatInterval = null;
    this.messageQueue = [];
    this.pendingRequests = new Map();
    this.encryptionKey = null;
    this.sessionId = null;
  }

  // Establish secure WebSocket tunnel to macOS VM
  async connect() {
    try {
      this.emit('status', 'Establishing WebSocket tunnel...');
      
      // Generate session encryption key
      this.generateEncryptionKey();
      
      // Create WebSocket connection
      const wsUrl = `ws://localhost:${this.config.bridgePort}/bridge`;
      this.socket = new WebSocket(wsUrl, {
        headers: {
          'X-Client-Type': 'iPhone-Companion-Pro',
          'X-Session-Id': this.sessionId
        }
      });
      
      this.setupSocketHandlers();
      
      // Wait for connection
      await this.waitForConnection();
      
      // Start heartbeat
      this.startHeartbeat();
      
      // Process queued messages
      this.processMessageQueue();
      
      this.emit('connected');
      return true;
      
    } catch (error) {
      this.emit('error', `Failed to establish tunnel: ${error.message}`);
      throw error;
    }
  }

  setupSocketHandlers() {
    this.socket.on('open', () => {
      console.log('WebSocket tunnel established');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      // Send authentication
      this.sendAuthentication();
    });

    this.socket.on('message', (data) => {
      try {
        this.handleIncomingMessage(data);
      } catch (error) {
        console.error('Error handling incoming message:', error);
      }
    });

    this.socket.on('close', (code, reason) => {
      console.log(`WebSocket tunnel closed: ${code} - ${reason}`);
      this.isConnected = false;
      this.stopHeartbeat();
      
      this.emit('disconnected', { code, reason });
      
      // Attempt reconnection
      this.attemptReconnection();
    });

    this.socket.on('error', (error) => {
      console.error('WebSocket tunnel error:', error);
      this.emit('error', error);
    });
  }

  async waitForConnection(timeout = 10000) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, timeout);

      const onOpen = () => {
        clearTimeout(timer);
        resolve();
      };

      const onError = (error) => {
        clearTimeout(timer);
        reject(error);
      };

      this.socket.once('open', onOpen);
      this.socket.once('error', onError);
    });
  }

  generateEncryptionKey() {
    this.encryptionKey = crypto.randomBytes(32);
    this.sessionId = crypto.randomUUID();
  }

  sendAuthentication() {
    const authMessage = {
      type: 'auth',
      sessionId: this.sessionId,
      timestamp: Date.now(),
      clientInfo: {
        name: 'iPhone Companion Pro',
        version: '1.0.0',
        platform: 'Windows'
      }
    };

    this.sendMessage(authMessage);
  }

  handleIncomingMessage(data) {
    let message;
    
    try {
      // Decrypt if encrypted
      if (this.isEncrypted(data)) {
        message = this.decryptMessage(data);
      } else {
        message = JSON.parse(data.toString());
      }
    } catch (error) {
      console.error('Failed to parse incoming message:', error);
      return;
    }

    // Handle different message types
    switch (message.type) {
      case 'welcome':
        this.handleWelcome(message);
        break;
      case 'auth_response':
        this.handleAuthResponse(message);
        break;
      case 'messages':
        this.handleMessages(message);
        break;
      case 'new_message':
        this.handleNewMessage(message);
        break;
      case 'message_sent':
        this.handleMessageSent(message);
        break;
      case 'contacts':
        this.handleContacts(message);
        break;
      case 'pong':
        this.handlePong(message);
        break;
      case 'error':
        this.handleError(message);
        break;
      case 'response':
        this.handleResponse(message);
        break;
      default:
        console.warn('Unknown message type:', message.type);
    }
  }

  handleWelcome(message) {
    console.log('Received welcome from macOS bridge:', message.capabilities);
    this.emit('welcome', message);
  }

  handleAuthResponse(message) {
    if (message.success) {
      console.log('Authentication successful');
      this.emit('authenticated');
    } else {
      console.error('Authentication failed:', message.error);
      this.emit('auth-failed', message.error);
    }
  }

  handleMessages(message) {
    this.emit('messages-received', message.data);
    this.resolveRequest(message.requestId, message.data);
  }

  handleNewMessage(message) {
    this.emit('new-message', message.data);
  }

  handleMessageSent(message) {
    this.emit('message-sent', message);
    this.resolveRequest(message.requestId, message);
  }

  handleContacts(message) {
    this.emit('contacts-received', message.data);
    this.resolveRequest(message.requestId, message.data);
  }

  handlePong(message) {
    // Heartbeat response received
    this.emit('heartbeat-response', message);
  }

  handleError(message) {
    console.error('Bridge error:', message.message);
    this.emit('bridge-error', message);
    this.rejectRequest(message.requestId, new Error(message.message));
  }

  handleResponse(message) {
    this.resolveRequest(message.requestId, message.data);
  }

  // Send message through tunnel
  sendMessage(message, encrypt = false) {
    if (!this.isConnected) {
      this.messageQueue.push({ message, encrypt });
      return;
    }

    try {
      let data;
      
      if (encrypt && this.encryptionKey) {
        data = this.encryptMessage(message);
      } else {
        data = JSON.stringify(message);
      }

      this.socket.send(data);
    } catch (error) {
      console.error('Failed to send message:', error);
      this.emit('send-error', error);
    }
  }

  // Send request and wait for response
  async sendRequest(message, timeout = 30000) {
    const requestId = crypto.randomUUID();
    message.requestId = requestId;

    return new Promise((resolve, reject) => {
      // Set up timeout
      const timer = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        reject(new Error('Request timeout'));
      }, timeout);

      // Store request
      this.pendingRequests.set(requestId, {
        resolve: (data) => {
          clearTimeout(timer);
          resolve(data);
        },
        reject: (error) => {
          clearTimeout(timer);
          reject(error);
        }
      });

      // Send message
      this.sendMessage(message);
    });
  }

  resolveRequest(requestId, data) {
    const request = this.pendingRequests.get(requestId);
    if (request) {
      this.pendingRequests.delete(requestId);
      request.resolve(data);
    }
  }

  rejectRequest(requestId, error) {
    const request = this.pendingRequests.get(requestId);
    if (request) {
      this.pendingRequests.delete(requestId);
      request.reject(error);
    }
  }

  processMessageQueue() {
    while (this.messageQueue.length > 0 && this.isConnected) {
      const { message, encrypt } = this.messageQueue.shift();
      this.sendMessage(message, encrypt);
    }
  }

  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.sendMessage({
          type: 'ping',
          timestamp: Date.now()
        });
      }
    }, 30000); // Send ping every 30 seconds
  }

  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  attemptReconnection() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit('max-reconnect-attempts');
      return;
    }

    this.reconnectAttempts++;
    this.emit('reconnecting', this.reconnectAttempts);

    setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, this.reconnectDelay * this.reconnectAttempts);
  }

  // Encryption methods
  encryptMessage(message) {
    if (!this.encryptionKey) {
      throw new Error('Encryption key not available');
    }

    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
    
    let encrypted = cipher.update(JSON.stringify(message), 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return JSON.stringify({
      encrypted: true,
      iv: iv.toString('hex'),
      data: encrypted
    });
  }

  decryptMessage(data) {
    const encryptedData = JSON.parse(data.toString());
    
    if (!encryptedData.encrypted) {
      return JSON.parse(data.toString());
    }

    if (!this.encryptionKey) {
      throw new Error('Encryption key not available');
    }

    const iv = Buffer.from(encryptedData.iv, 'hex');
    const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
    
    let decrypted = decipher.update(encryptedData.data, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return JSON.parse(decrypted);
  }

  isEncrypted(data) {
    try {
      const parsed = JSON.parse(data.toString());
      return parsed.encrypted === true;
    } catch {
      return false;
    }
  }

  // API methods for common operations
  async getMessages(limit = 100, offset = 0) {
    return this.sendRequest({
      type: 'get_messages',
      limit,
      offset
    });
  }

  async sendMessageToPhone(phoneNumber, text) {
    return this.sendRequest({
      type: 'send_message',
      phoneNumber,
      text
    });
  }

  async getContacts() {
    return this.sendRequest({
      type: 'get_contacts'
    });
  }

  // Subscribe to real-time message updates
  subscribeToMessages() {
    this.sendMessage({
      type: 'subscribe_messages'
    });
  }

  // Disconnect tunnel
  disconnect() {
    this.emit('status', 'Disconnecting WebSocket tunnel...');
    
    this.stopHeartbeat();
    
    if (this.socket) {
      this.socket.close(1000, 'Client disconnect');
      this.socket = null;
    }
    
    this.isConnected = false;
    this.messageQueue = [];
    this.pendingRequests.clear();
    
    this.emit('disconnected');
  }

  // Get tunnel status
  getStatus() {
    return {
      connected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length,
      pendingRequests: this.pendingRequests.size,
      sessionId: this.sessionId
    };
  }
}

module.exports = WebSocketTunnel;
