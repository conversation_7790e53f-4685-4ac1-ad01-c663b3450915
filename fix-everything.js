const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing iPhone Companion Pro - Complete Solution\n');

// Fix 1: Update main.js to properly handle connections
const mainJsPath = path.join(__dirname, 'src/main/main.js');
let mainJs = fs.readFileSync(mainJsPath, 'utf8');

// Add connection handler
const connectionHandler = `
// Handle connection request
ipcMain.handle('connect-device', async () => {
  console.log('🔌 Connection requested - using Phone Link');
  
  try {
    if (focusedConnectionManager) {
      // Use Phone Link specifically
      const success = await focusedConnectionManager.connectPhoneLink();
      
      if (success) {
        // Get real data
        const contacts = await focusedConnectionManager.getContacts();
        const calls = await focusedConnectionManager.getCalls();
        
        // Send data to renderer
        mainWindow.webContents.send('connection-success', {
          method: 'phoneLink',
          contacts: contacts.length,
          calls: calls.length
        });
        
        return { success: true, method: 'phoneLink' };
      }
    }
  } catch (error) {
    console.error('Connection error:', error);
  }
  
  return { success: false };
});

// Make windows actually appear
ipcMain.handle('open-messages', async () => {
  console.log('📱 Opening messages window');
  
  if (!messagesWindow) {
    messagesWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      center: true,
      show: true, // Force show
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    });
    
    messagesWindow.loadFile(path.join(__dirname, '../renderer/views/messages.html'));
    messagesWindow.on('closed', () => {
      messagesWindow = null;
    });
  } else {
    messagesWindow.show();
    messagesWindow.focus();
  }
});

ipcMain.handle('open-calls', async () => {
  console.log('📞 Opening calls window');
  
  if (!callsWindow) {
    callsWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      center: true,
      show: true, // Force show
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    });
    
    callsWindow.loadFile(path.join(__dirname, '../renderer/views/calls.html'));
    callsWindow.on('closed', () => {
      callsWindow = null;
    });
  } else {
    callsWindow.show();
    callsWindow.focus();
  }
});
`;

// Insert the handlers if they don't exist
if (!mainJs.includes('connect-device')) {
  mainJs = mainJs.replace(
    '// Window controls',
    connectionHandler + '\n\n// Window controls'
  );
}

fs.writeFileSync(mainJsPath, mainJs);
console.log('✅ Fixed main.js connection handlers');

// Fix 2: Update FocusedConnectionManager to actually connect to Phone Link
const focusedManagerPath = path.join(__dirname, 'src/main/services/FocusedConnectionManager.js');
let focusedManager = fs.readFileSync(focusedManagerPath, 'utf8');

// Add Phone Link connection method
const phoneLinkMethod = `
  async connectPhoneLink() {
    console.log('📱 Connecting to Phone Link...');
    
    try {
      const { PhoneLinkBridge } = require('./PhoneLinkBridge');
      this.phoneLinkBridge = new PhoneLinkBridge();
      await this.phoneLinkBridge.connect();
      
      this.activeConnections.set('phoneLink', this.phoneLinkBridge);
      this.emit('connected', 'phoneLink');
      
      return true;
    } catch (error) {
      console.error('Phone Link connection failed:', error);
      return false;
    }
  }
  
  async getContacts() {
    if (this.phoneLinkBridge) {
      return await this.phoneLinkBridge.getContacts();
    }
    return [];
  }
  
  async getCalls() {
    if (this.phoneLinkBridge) {
      return await this.phoneLinkBridge.getCallHistory();
    }
    return [];
  }
`;

// Insert before the last closing brace
focusedManager = focusedManager.replace(
  /}\s*module\.exports/,
  phoneLinkMethod + '\n}\n\nmodule.exports'
);

fs.writeFileSync(focusedManagerPath, focusedManager);
console.log('✅ Fixed FocusedConnectionManager');

// Fix 3: Update app.js to handle connection properly
const appJsPath = path.join(__dirname, 'src/renderer/scripts/app.js');
const newAppJs = `
// iPhone Companion Pro - Renderer Script
const { ipcRenderer } = require('electron');

console.log('🚀 App.js loaded successfully!');

// Connection button handler
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM loaded, setting up handlers...');
  
  // Connect button
  const connectBtn = document.getElementById('connect-btn');
  if (connectBtn) {
    connectBtn.addEventListener('click', async () => {
      console.log('Connect button clicked!');
      connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Connecting...';
      connectBtn.disabled = true;
      
      try {
        const result = await ipcRenderer.invoke('connect-device');
        console.log('Connection result:', result);
        
        if (result.success) {
          connectBtn.innerHTML = '<i class="fas fa-check"></i> Connected';
          connectBtn.style.background = '#4CAF50';
          
          // Update status
          updateConnectionStatus('Connected via ' + result.method);
          
          // Update method indicators
          if (result.method === 'phoneLink') {
            updateMethodStatus('phoneLink', 'connected');
          }
        } else {
          connectBtn.innerHTML = '<i class="fas fa-times"></i> Failed';
          connectBtn.style.background = '#f44336';
          setTimeout(() => {
            connectBtn.innerHTML = '🚀 Connect iPhone';
            connectBtn.disabled = false;
            connectBtn.style.background = '';
          }, 3000);
        }
      } catch (error) {
        console.error('Connection error:', error);
        connectBtn.innerHTML = '🚀 Connect iPhone';
        connectBtn.disabled = false;
      }
    });
  }
  
  // Listen for connection success
  ipcRenderer.on('connection-success', (event, data) => {
    console.log('Connection successful:', data);
    
    // Update UI with real data
    const deviceInfo = document.querySelector('.device-info');
    if (deviceInfo) {
      deviceInfo.innerHTML = \`
        <h3>iPhone Connected</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 10px;">
          <div>
            <strong>Contacts:</strong> \${data.contacts}
          </div>
          <div>
            <strong>Recent Calls:</strong> \${data.calls}
          </div>
          <div>
            <strong>Connection:</strong> Phone Link
          </div>
          <div>
            <strong>Status:</strong> Active
          </div>
        </div>
      \`;
    }
  });
});

function updateConnectionStatus(status) {
  const statusEl = document.querySelector('.connection-status');
  if (statusEl) {
    statusEl.textContent = status;
  }
}

function updateMethodStatus(method, status) {
  const methodEl = document.querySelector(\`[data-method="\${method}"]\`);
  if (methodEl) {
    methodEl.textContent = status === 'connected' ? 'Connected' : 'Checking...';
    methodEl.style.color = status === 'connected' ? '#4CAF50' : '#666';
  }
}

// Add data-method attributes to connection methods
document.addEventListener('DOMContentLoaded', () => {
  const methods = document.querySelectorAll('.connection-method');
  methods.forEach((method, index) => {
    const methodNames = ['airplay', 'phoneLink', 'usb', 'vm'];
    if (methodNames[index]) {
      method.setAttribute('data-method', methodNames[index]);
    }
  });
});
`;

fs.writeFileSync(appJsPath, newAppJs);
console.log('✅ Fixed app.js');

// Fix 4: Add window declarations to main.js
const windowDeclarations = `
let messagesWindow = null;
let callsWindow = null;
let settingsWindow = null;
`;

if (!mainJs.includes('let messagesWindow')) {
  mainJs = fs.readFileSync(mainJsPath, 'utf8');
  mainJs = mainJs.replace(
    'let mainWindow;',
    'let mainWindow;\n' + windowDeclarations
  );
  fs.writeFileSync(mainJsPath, mainJs);
  console.log('✅ Added window declarations');
}

console.log('\n🎉 All fixes applied!');
console.log('\n📱 Next steps:');
console.log('1. Run: npm start');
console.log('2. Click "Connect iPhone"');
console.log('3. Your Phone Link data will load!');
console.log('4. Messages/Calls buttons will open windows');