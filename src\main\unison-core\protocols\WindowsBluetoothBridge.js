const net = require('net');
const { spawn } = require('child_process');
const { EventEmitter } = require('events');

class WindowsBluetoothBridge extends EventEmitter {
    constructor() {
        super();
        this.bridgePort = 26820;
        this.bridgeClient = null;
        this.powershellProcess = null;
        this.isConnected = false;
        this.availableDevices = new Map();
    }

    async initialize() {
        console.log('🌉 Initializing Windows Bluetooth Bridge...');
        
        try {
            // Start PowerShell bridge
            await this.startPowerShellBridge();
            
            // Connect to bridge
            await this.connectToBridge();
            
            console.log('✅ Windows Bluetooth Bridge initialized');
            return true;
            
        } catch (error) {
            console.error('❌ Failed to initialize Bluetooth bridge:', error.message);
            return false;
        }
    }

    async startPowerShellBridge() {
        return new Promise((resolve, reject) => {
            console.log('🚀 Starting PowerShell Bluetooth Bridge...');
            
            // Get the path to the PowerShell script
            const scriptPath = require('path').join(__dirname, '../../../windows-bluetooth-bridge.ps1');
            
            // Start PowerShell process
            this.powershellProcess = spawn('powershell.exe', [
                '-ExecutionPolicy', 'Bypass',
                '-File', scriptPath,
                '-Action', 'bridge',
                '-Port', this.bridgePort.toString()
            ], {
                stdio: ['pipe', 'pipe', 'pipe']
            });

            this.powershellProcess.stdout.on('data', (data) => {
                const output = data.toString().trim();
                console.log(`[PowerShell Bridge] ${output}`);
                
                if (output.includes('Bluetooth Bridge listening')) {
                    console.log('✅ PowerShell Bridge started successfully');
                    resolve();
                }
            });

            this.powershellProcess.stderr.on('data', (data) => {
                console.error(`[PowerShell Bridge Error] ${data.toString()}`);
            });

            this.powershellProcess.on('error', (error) => {
                console.error('❌ PowerShell Bridge process error:', error);
                reject(error);
            });

            this.powershellProcess.on('exit', (code) => {
                console.log(`💀 PowerShell Bridge exited with code ${code}`);
                this.isConnected = false;
            });

            // Timeout if bridge doesn't start
            setTimeout(() => {
                if (!this.isConnected) {
                    reject(new Error('PowerShell Bridge startup timeout'));
                }
            }, 10000);
        });
    }

    async connectToBridge() {
        return new Promise((resolve, reject) => {
            console.log(`🔗 Connecting to PowerShell Bridge on port ${this.bridgePort}...`);
            
            this.bridgeClient = new net.Socket();
            
            this.bridgeClient.connect(this.bridgePort, 'localhost', () => {
                console.log('✅ Connected to PowerShell Bluetooth Bridge');
                this.isConnected = true;
                resolve();
            });

            this.bridgeClient.on('data', (data) => {
                try {
                    const response = JSON.parse(data.toString());
                    this.handleBridgeResponse(response);
                } catch (error) {
                    console.error('❌ Bridge response parse error:', error);
                }
            });

            this.bridgeClient.on('error', (error) => {
                console.error('❌ Bridge connection error:', error);
                this.isConnected = false;
                reject(error);
            });

            this.bridgeClient.on('close', () => {
                console.log('🔌 Bridge connection closed');
                this.isConnected = false;
                this.attemptReconnect();
            });
        });
    }

    handleBridgeResponse(response) {
        console.log('📨 Bridge response:', response.action);
        
        switch (response.action) {
            case 'scan_result':
                this.handleScanResult(response);
                break;
                
            case 'connect_result':
                this.handleConnectResult(response);
                break;
                
            case 'status':
                this.handleStatusResponse(response);
                break;
        }
    }

    handleScanResult(response) {
        console.log(`📱 Scan completed. Found ${response.devices.length} Apple devices`);
        
        // Update available devices
        this.availableDevices.clear();
        
        response.devices.forEach(device => {
            this.availableDevices.set(device.Address, {
                address: device.Address,
                name: device.LocalName || 'iPhone',
                rssi: device.RSSI,
                txPower: device.TxPowerLevel,
                serviceUuids: device.ServiceUuids,
                lastSeen: Date.now()
            });
            
            console.log(`📱 iPhone found: ${device.LocalName || 'iPhone'} (${device.Address}) RSSI: ${device.RSSI}dBm`);
        });
        
        this.emit('devices-discovered', Array.from(this.availableDevices.values()));
    }

    handleConnectResult(response) {
        if (response.success) {
            console.log(`✅ Successfully connected to iPhone: ${response.address}`);
            this.emit('device-connected', {
                address: response.address,
                name: 'iPhone',
                connectionType: 'bluetooth'
            });
        } else {
            console.log(`❌ Failed to connect to iPhone: ${response.address}`);
            this.emit('device-connection-failed', {
                address: response.address,
                error: 'Connection failed'
            });
        }
    }

    handleStatusResponse(response) {
        console.log('📊 Bluetooth status:', {
            available: response.bluetooth_available,
            adapters: response.adapter_count
        });
        
        this.emit('status-update', {
            available: response.bluetooth_available,
            adapters: response.adapter_count
        });
    }

    async scanForDevices() {
        if (!this.isConnected) {
            throw new Error('Bridge not connected');
        }
        
        console.log('🔍 Starting iPhone scan via Windows Bluetooth...');
        this.sendCommand('scan');
    }

    async connectToDevice(deviceAddress) {
        if (!this.isConnected) {
            throw new Error('Bridge not connected');
        }
        
        console.log(`🔗 Connecting to iPhone: ${deviceAddress}`);
        this.sendCommand(`connect:${deviceAddress}`);
    }

    async getStatus() {
        if (!this.isConnected) {
            throw new Error('Bridge not connected');
        }
        
        this.sendCommand('status');
    }

    sendCommand(command) {
        if (this.bridgeClient && this.isConnected) {
            try {
                this.bridgeClient.write(command + '\n');
                console.log(`📤 Sent command to bridge: ${command}`);
            } catch (error) {
                console.error('❌ Failed to send command:', error);
            }
        } else {
            console.error('❌ Bridge not connected, cannot send command');
        }
    }

    async attemptReconnect() {
        console.log('🔄 Attempting to reconnect to Bluetooth bridge...');
        
        setTimeout(async () => {
            try {
                await this.connectToBridge();
            } catch (error) {
                console.error('❌ Reconnection failed:', error.message);
                // Try again in 5 seconds
                setTimeout(() => this.attemptReconnect(), 5000);
            }
        }, 2000);
    }

    getDiscoveredDevices() {
        return Array.from(this.availableDevices.values());
    }

    async stop() {
        console.log('⏹️ Stopping Windows Bluetooth Bridge...');
        
        this.isConnected = false;
        
        if (this.bridgeClient) {
            this.bridgeClient.destroy();
        }
        
        if (this.powershellProcess) {
            this.powershellProcess.kill();
        }
        
        console.log('✅ Windows Bluetooth Bridge stopped');
    }
}

module.exports = { WindowsBluetoothBridge };