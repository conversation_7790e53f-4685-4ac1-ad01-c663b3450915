<<<<<<<
<<<<<<<



<<<<<<<















// macOS VM Manager for iPhone Companion Pro































































const { exec, spawn } = require('child_process');































































const fs = require('fs');































































const path = require('path');































































const WebSocket = require('ws');































































const { EventEmitter } = require('events');































































































































class MacOSVMManager extends EventEmitter {































































  constructor() {































































    super();































































    this.vmPath = 'C:\\macOS-VM\\macOS.vmx';































































    this.vmwareExe = 'C:\\Program Files (x86)\\VMware\\VMware Workstation\\vmrun.exe';































































    this.vmIP = null;































































    this.bridgeWS = null;































































    this.isVMRunning = false;































































    this.connectionAttempts = 0;































































    this.maxConnectionAttempts = 20;































































  }































































































































  async initialize() {































































    console.log('🖥️ Initializing macOS VM Manager...');































































    































































    // Check if VMware is installed































































    if (!fs.existsSync(this.vmwareExe)) {































































      throw new Error('VMware Workstation not found. Run setup-macos-vm.bat first');































































    }































































    































































    // Check if VM exists































































    if (!fs.existsSync(this.vmPath)) {































































      throw new Error('macOS VM not found. Run setup-macos-vm.bat first');































































    }































































    































































    console.log('✅ VMware and VM found');































































    































































    // Start VM if not running































































    await this.startVM();































































    































































    // Wait for VM to boot and find IP































































    await this.waitForVM();































































    































































    // Connect to bridge































































    await this.connectToBridge();































































    































































    console.log('✅ macOS VM Manager ready!');































































  }































































































































  async startVM() {































































    console.log('🚀 Starting macOS VM...');































































    































































    // Check if VM is already running































































    const runningVMs = await this.execPromise(`"${this.vmwareExe}" list`);































































    if (runningVMs.includes('macOS.vmx')) {































































      console.log('✅ VM already running');































































      this.isVMRunning = true;































































      return;































































    }































































    































































    // Start the VM































































    try {































































      await this.execPromise(`"${this.vmwareExe}" start "${this.vmPath}" nogui`);































































      console.log('✅ VM started successfully');































































      this.isVMRunning = true;































































    } catch (error) {































































      console.log('⚠️ VM start failed, trying with GUI...');































































      await this.execPromise(`"${this.vmwareExe}" start "${this.vmPath}"`);































































      this.isVMRunning = true;































































    }































































  }































































































































  async waitForVM() {































































    console.log('⏳ Waiting for VM to boot...');































































    































































    const possibleIPs = [































































      '*************',  // VMware NAT default































































      '***************', // Hyper-V default































































      '**********',     // Custom NAT































































      '************'    // VMware bridged































































    ];































































    































































    for (let attempt = 0; attempt < this.maxConnectionAttempts; attempt++) {































































      console.log(`Attempt ${attempt + 1}/${this.maxConnectionAttempts}...`);































































      































































      for (const ip of possibleIPs) {































































        try {































































          // Try to ping the IP































































          await this.execPromise(`ping -n 1 -w 1000 ${ip}`);































































          console.log(`✅ VM responding at ${ip}`);































































          this.vmIP = ip;































































          return;































































        } catch (e) {































































          // Try next IP































































        }































































      }































































      































































      // Wait 5 seconds before next attempt































































      await this.sleep(5000);































































    }































































    































































    throw new Error('VM did not respond after maximum attempts');































































  }































































































































  async connectToBridge() {































































    console.log('🌉 Connecting to macOS bridge...');































































    































































    if (!this.vmIP) {































































      throw new Error('VM IP not found');































































    }































































    































































    const bridgeURL = `ws://${this.vmIP}:8888`;































































    console.log(`Connecting to: ${bridgeURL}`);































































    































































    return new Promise((resolve, reject) => {































































      this.bridgeWS = new WebSocket(bridgeURL);































































      































































      const timeout = setTimeout(() => {































































        reject(new Error('Bridge connection timeout'));































































      }, 30000);































































      































































      this.bridgeWS.on('open', () => {































































        clearTimeout(timeout);































































        console.log('✅ Connected to macOS bridge!');































































        































































        // Request initial sync































































        this.bridgeWS.send(JSON.stringify({































































          type: 'SYNC_ALL',































































          timestamp: Date.now()































































        }));































































        































































        this.emit('connected');































































        resolve();































































      });































































      































































      this.bridgeWS.on('message', (data) => {































































        try {































































          const message = JSON.parse(data);































































          this.handleBridgeMessage(message);































































        } catch (e) {































































          console.log('Invalid bridge message:', data.toString());































































        }































































      });































































      































































      this.bridgeWS.on('close', () => {































































        console.log('❌ Bridge connection lost');































































        this.emit('disconnected');































































        































































        // Try to reconnect after 10 seconds































































        setTimeout(() => {































































          this.connectToBridge().catch(console.error);































































        }, 10000);































































      });































































      































































      this.bridgeWS.on('error', (error) => {































































        clearTimeout(timeout);































































        console.log('❌ Bridge connection error:', error.message);































































        































































        // If bridge not found, try to install it































































        if (error.code === 'ECONNREFUSED') {































































          this.installBridge().then(() => {































































            setTimeout(() => {































































              this.connectToBridge().catch(console.error);































































            }, 5000);































































          });































































        }































































        































































        reject(error);































































      });































































    });































































  }































































































































  async installBridge() {































































    console.log('📦 Installing bridge in macOS VM...');































































    































































    try {































































      // Copy bridge installer to VM































































      const bridgeScript = fs.readFileSync('macos-vm-bridge.sh', 'utf8');































































      































































      // Use VMware tools to copy file































































      const tempScript = path.join(process.env.TEMP, 'install-bridge.sh');































































      fs.writeFileSync(tempScript, bridgeScript);































































      































































      // Copy to VM































































      await this.execPromise(`"${this.vmwareExe}" copyFileFromHostToGuest "${this.vmPath}" "${tempScript}" "/tmp/install-bridge.sh"`);































































      































































      // Make executable and run































































      await this.execPromise(`"${this.vmwareExe}" runProgramInGuest "${this.vmPath}" "/bin/chmod" "+x /tmp/install-bridge.sh"`);































































      await this.execPromise(`"${this.vmwareExe}" runProgramInGuest "${this.vmPath}" "/tmp/install-bridge.sh"`);































































      































































      console.log('✅ Bridge installer copied to VM');































































      console.log('⚠️ Complete the installation manually in the VM');































































      































































    } catch (error) {































































      console.log('❌ Failed to install bridge automatically');































































      console.log('Manual installation required:');































































      console.log('1. Open VMware console');































































      console.log('2. Copy macos-vm-bridge.sh to VM');































































      console.log('3. Run: chmod +x macos-vm-bridge.sh && ./macos-vm-bridge.sh');































































    }































































  }































































































































  handleBridgeMessage(message) {































































    switch (message.type) {































































      case 'FULL_SYNC':































































        console.log(`📥 Received ${message.messages?.length || 0} messages from macOS`);































































        this.emit('messages-synced', message.messages || []);































































        break;































































        































































      case 'NEW_MESSAGE':































































        console.log('📨 New message from macOS:', message.message.text?.substring(0, 50));































































        this.emit('new-message', message.message);































































        break;































































        































































      case 'TEST_RESPONSE':































































        console.log('✅ Bridge test successful');































































        break;































































        































































      default:































































        console.log('Unknown bridge message:', message.type);































































    }































































  }































































































































  async sendMessage(phoneNumber, text) {































































    if (!this.bridgeWS || this.bridgeWS.readyState !== WebSocket.OPEN) {































































      throw new Error('Bridge not connected');































































    }































































    































































    return new Promise((resolve, reject) => {































































      const messageId = Date.now().toString();































































      































































      this.bridgeWS.send(JSON.stringify({































































        type: 'SEND_MESSAGE',































































        id: messageId,































































        phoneNumber,































































        text,































































        timestamp: Date.now()































































      }));































































      































































      // Wait for confirmation (simplified - in real implementation, track message IDs)































































      setTimeout(() => {































































        resolve({ id: messageId, sent: true });































































      }, 1000);































































    });































































  }































































































































  async getVMStatus() {































































    try {































































      const runningVMs = await this.execPromise(`"${this.vmwareExe}" list`);































































      const isRunning = runningVMs.includes('macOS.vmx');































































      































































      return {































































        vmRunning: isRunning,































































        vmIP: this.vmIP,































































        bridgeConnected: this.bridgeWS && this.bridgeWS.readyState === WebSocket.OPEN,































































        connectionAttempts: this.connectionAttempts































































      };































































    } catch (error) {































































      return {































































        vmRunning: false,































































        vmIP: null,































































        bridgeConnected: false,































































        error: error.message































































      };































































    }































































  }































































































































  async stopVM() {































































    console.log('🛑 Stopping macOS VM...');































































    































































    if (this.bridgeWS) {































































      this.bridgeWS.close();































































    }































































    































































    try {































































      await this.execPromise(`"${this.vmwareExe}" stop "${this.vmPath}" soft`);































































      console.log('✅ VM stopped');































































      this.isVMRunning = false;































































    } catch (error) {































































      console.log('⚠️ VM stop failed:', error.message);































































    }































































  }































































































































  execPromise(command) {































































    return new Promise((resolve, reject) => {































































      exec(command, (error, stdout, stderr) => {































































        if (error) {































































          reject(error);































































        } else {































































          resolve(stdout);































































        }































































      });































































    });































































  }































































































































  sleep(ms) {































































    return new Promise(resolve => setTimeout(resolve, ms));































































  }































































}































































































































// Test the VM manager































































async function testVMManager() {































































  const vmManager = new MacOSVMManager();































































  































































  vmManager.on('connected', () => {































































    console.log('🎉 VM Manager connected successfully!');































































  });































































  































































  vmManager.on('messages-synced', (messages) => {































































    console.log(`📱 Synced ${messages.length} messages from iPhone`);































































  });































































  































































  vmManager.on('new-message', (message) => {































































    console.log(`📨 New iPhone message: ${message.text}`);































































  });































































  































































  try {































































    await vmManager.initialize();































































    































































    // Test sending a message































































    // await vmManager.sendMessage('+1234567890', 'Test from Windows!');































































    































































    console.log('\n✅ VM Manager test complete!');































































    console.log('VM Status:', await vmManager.getVMStatus());































































    































































  } catch (error) {































































    console.error('❌ VM Manager test failed:', error.message);































































    































































    if (error.message.includes('VMware')) {































































      console.log('\n🔧 To fix:');































































      console.log('1. Run: setup-macos-vm.bat');































































      console.log('2. Install macOS in VMware');































































      console.log('3. Run this test again');































































    }































































  }































































}































































































































module.exports = { MacOSVMManager };































































































































// Run test if called directly































































if (require.main === module) {































































  testVMManager();































































}































































=======















// macOS VM Manager for iPhone Companion Pro































const { exec, spawn } = require('child_process');































const fs = require('fs');































const path = require('path');































const WebSocket = require('ws');































const { EventEmitter } = require('events');































































class MacOSVMManager extends EventEmitter {































  constructor() {































    super();































    this.vmPath = 'C:\\macOS-VM\\macOS.vmx';































    this.vmwareExe = 'C:\\Program Files (x86)\\VMware\\VMware Workstation\\vmrun.exe';































    this.vmIP = null;































    this.bridgeWS = null;































    this.isVMRunning = false;































    this.connectionAttempts = 0;































    this.maxConnectionAttempts = 20;































  }































































  async initialize() {































    console.log('🖥️ Initializing macOS VM Manager...');































    































    // Check if VMware is installed































    if (!fs.existsSync(this.vmwareExe)) {































      throw new Error('VMware Workstation not found. Run setup-macos-vm.bat first');































    }































    































    // Check if VM exists































    if (!fs.existsSync(this.vmPath)) {































      throw new Error('macOS VM not found. Run setup-macos-vm.bat first');































    }































    































    console.log('✅ VMware and VM found');































    































    // Start VM if not running































    await this.startVM();































    































    // Wait for VM to boot and find IP































    await this.waitForVM();































    































    // Connect to bridge































    await this.connectToBridge();































    































    console.log('✅ macOS VM Manager ready!');































  }































































  async startVM() {































    console.log('🚀 Starting macOS VM...');































    































    // Check if VM is already running































    const runningVMs = await this.execPromise(`"${this.vmwareExe}" list`);































    if (runningVMs.includes('macOS.vmx')) {































      console.log('✅ VM already running');































      this.isVMRunning = true;































      return;































    }































    































    // Start the VM































    try {































      await this.execPromise(`"${this.vmwareExe}" start "${this.vmPath}" nogui`);































      console.log('✅ VM started successfully');































      this.isVMRunning = true;































    } catch (error) {































      console.log('⚠️ VM start failed, trying with GUI...');































      await this.execPromise(`"${this.vmwareExe}" start "${this.vmPath}"`);































      this.isVMRunning = true;































    }































  }































































  async waitForVM() {































    console.log('⏳ Waiting for VM to boot...');































    































    const possibleIPs = [































      '*************',  // VMware NAT default































      '***************', // Hyper-V default































      '**********',     // Custom NAT































      '************'    // VMware bridged































    ];































    































    for (let attempt = 0; attempt < this.maxConnectionAttempts; attempt++) {































      console.log(`Attempt ${attempt + 1}/${this.maxConnectionAttempts}...`);































      































      for (const ip of possibleIPs) {































        try {































          // Try to ping the IP































          await this.execPromise(`ping -n 1 -w 1000 ${ip}`);































          console.log(`✅ VM responding at ${ip}`);































          this.vmIP = ip;































          return;































        } catch (e) {































          // Try next IP































        }































      }































      































      // Wait 5 seconds before next attempt































      await this.sleep(5000);































    }































    































    throw new Error('VM did not respond after maximum attempts');































  }































































  async connectToBridge() {































    console.log('🌉 Connecting to macOS bridge...');































    































    if (!this.vmIP) {































      throw new Error('VM IP not found');































    }































    































    const bridgeURL = `ws://${this.vmIP}:8888`;































    console.log(`Connecting to: ${bridgeURL}`);































    































    return new Promise((resolve, reject) => {































      this.bridgeWS = new WebSocket(bridgeURL);































      































      const timeout = setTimeout(() => {































        reject(new Error('Bridge connection timeout'));































      }, 30000);































      































      this.bridgeWS.on('open', () => {































        clearTimeout(timeout);































        console.log('✅ Connected to macOS bridge!');































        































        // Request initial sync































        this.bridgeWS.send(JSON.stringify({































          type: 'SYNC_ALL',































          timestamp: Date.now()































        }));































        































        this.emit('connected');































        resolve();































      });































      































      this.bridgeWS.on('message', (data) => {































        try {































          const message = JSON.parse(data);































          this.handleBridgeMessage(message);































        } catch (e) {































          console.log('Invalid bridge message:', data.toString());































        }































      });































      































      this.bridgeWS.on('close', () => {































        console.log('❌ Bridge connection lost');































        this.emit('disconnected');































        































        // Try to reconnect after 10 seconds































        setTimeout(() => {































          this.connectToBridge().catch(console.error);































        }, 10000);































      });































      































      this.bridgeWS.on('error', (error) => {































        clearTimeout(timeout);































        console.log('❌ Bridge connection error:', error.message);































        































        // If bridge not found, try to install it































        if (error.code === 'ECONNREFUSED') {































          this.installBridge().then(() => {































            setTimeout(() => {































              this.connectToBridge().catch(console.error);































            }, 5000);































          });































        }































        































        reject(error);































      });































    });































  }































































  async installBridge() {































    console.log('📦 Installing bridge in macOS VM...');































    































    try {































      // Copy bridge installer to VM































      const bridgeScript = fs.readFileSync('macos-vm-bridge.sh', 'utf8');































      































      // Use VMware tools to copy file































      const tempScript = path.join(process.env.TEMP, 'install-bridge.sh');































      fs.writeFileSync(tempScript, bridgeScript);































      































      // Copy to VM































      await this.execPromise(`"${this.vmwareExe}" copyFileFromHostToGuest "${this.vmPath}" "${tempScript}" "/tmp/install-bridge.sh"`);































      































      // Make executable and run































      await this.execPromise(`"${this.vmwareExe}" runProgramInGuest "${this.vmPath}" "/bin/chmod" "+x /tmp/install-bridge.sh"`);































      await this.execPromise(`"${this.vmwareExe}" runProgramInGuest "${this.vmPath}" "/tmp/install-bridge.sh"`);































      































      console.log('✅ Bridge installer copied to VM');































      console.log('⚠️ Complete the installation manually in the VM');































      































    } catch (error) {































      console.log('❌ Failed to install bridge automatically');































      console.log('Manual installation required:');































      console.log('1. Open VMware console');































      console.log('2. Copy macos-vm-bridge.sh to VM');































      console.log('3. Run: chmod +x macos-vm-bridge.sh && ./macos-vm-bridge.sh');































    }































  }































































  handleBridgeMessage(message) {































    switch (message.type) {































      case 'FULL_SYNC':































        console.log(`📥 Received ${message.messages?.length || 0} messages from macOS`);































        this.emit('messages-synced', message.messages || []);































        break;































        































      case 'NEW_MESSAGE':































        console.log('📨 New message from macOS:', message.message.text?.substring(0, 50));































        this.emit('new-message', message.message);































        break;































        































      case 'TEST_RESPONSE':































        console.log('✅ Bridge test successful');































        break;































        































      default:































        console.log('Unknown bridge message:', message.type);































    }































  }































































  async sendMessage(phoneNumber, text) {































    if (!this.bridgeWS || this.bridgeWS.readyState !== WebSocket.OPEN) {































      throw new Error('Bridge not connected');































    }































    































    return new Promise((resolve, reject) => {































      const messageId = Date.now().toString();































      































      this.bridgeWS.send(JSON.stringify({































        type: 'SEND_MESSAGE',































        id: messageId,































        phoneNumber,































        text,































        timestamp: Date.now()































      }));































      































      // Wait for confirmation (simplified - in real implementation, track message IDs)































      setTimeout(() => {































        resolve({ id: messageId, sent: true });































      }, 1000);































    });































  }































































  async getVMStatus() {































    try {































      const runningVMs = await this.execPromise(`"${this.vmwareExe}" list`);































      const isRunning = runningVMs.includes('macOS.vmx');































      































      return {































        vmRunning: isRunning,































        vmIP: this.vmIP,































        bridgeConnected: this.bridgeWS && this.bridgeWS.readyState === WebSocket.OPEN,































        connectionAttempts: this.connectionAttempts































      };































    } catch (error) {































      return {































        vmRunning: false,































        vmIP: null,































        bridgeConnected: false,































        error: error.message































      };































    }































  }































































  async stopVM() {































    console.log('🛑 Stopping macOS VM...');































    































    if (this.bridgeWS) {































      this.bridgeWS.close();































    }































    































    try {































      await this.execPromise(`"${this.vmwareExe}" stop "${this.vmPath}" soft`);































      console.log('✅ VM stopped');































      this.isVMRunning = false;































    } catch (error) {































      console.log('⚠️ VM stop failed:', error.message);































    }































  }































































  execPromise(command) {































    return new Promise((resolve, reject) => {































      exec(command, (error, stdout, stderr) => {































        if (error) {































          reject(error);































        } else {































          resolve(stdout);































        }































      });































    });































  }































































  sleep(ms) {































    return new Promise(resolve => setTimeout(resolve, ms));































  }































}































































// Test the VM manager































async function testVMManager() {































  const vmManager = new MacOSVMManager();































  































  vmManager.on('connected', () => {































    console.log('🎉 VM Manager connected successfully!');































  });































  































  vmManager.on('messages-synced', (messages) => {































    console.log(`📱 Synced ${messages.length} messages from iPhone`);































  });































  































  vmManager.on('new-message', (message) => {































    console.log(`📨 New iPhone message: ${message.text}`);































  });































  































  try {































    await vmManager.initialize();































    































    // Test sending a message































    // await vmManager.sendMessage('+1234567890', 'Test from Windows!');































    































    console.log('\n✅ VM Manager test complete!');































    console.log('VM Status:', await vmManager.getVMStatus());































    































  } catch (error) {































    console.error('❌ VM Manager test failed:', error.message);































    































    if (error.message.includes('VMware')) {































      console.log('\n🔧 To fix:');































      console.log('1. Run: setup-macos-vm.bat');































      console.log('2. Install macOS in VMware');































      console.log('3. Run this test again');































    }































  }































}































































module.exports = { MacOSVMManager };































































// Run test if called directly































if (require.main === module) {































  testVMManager();































}































>>>>>>>















=======



// macOS VM Manager for iPhone Companion Pro







const { exec, spawn } = require('child_process');







const fs = require('fs');







const path = require('path');







const WebSocket = require('ws');







const { EventEmitter } = require('events');















class MacOSVMManager extends EventEmitter {







  constructor() {







    super();







    this.vmPath = 'C:\\macOS-VM\\macOS.vmx';







    this.vmwareExe = 'C:\\Program Files (x86)\\VMware\\VMware Workstation\\vmrun.exe';







    this.vmIP = null;







    this.bridgeWS = null;







    this.isVMRunning = false;







    this.connectionAttempts = 0;







    this.maxConnectionAttempts = 20;







  }















  async initialize() {







    console.log('🖥️ Initializing macOS VM Manager...');







    







    // Check if VMware is installed







    if (!fs.existsSync(this.vmwareExe)) {







      throw new Error('VMware Workstation not found. Run setup-macos-vm.bat first');







    }







    







    // Check if VM exists







    if (!fs.existsSync(this.vmPath)) {







      throw new Error('macOS VM not found. Run setup-macos-vm.bat first');







    }







    







    console.log('✅ VMware and VM found');







    







    // Start VM if not running







    await this.startVM();







    







    // Wait for VM to boot and find IP







    await this.waitForVM();







    







    // Connect to bridge







    await this.connectToBridge();







    







    console.log('✅ macOS VM Manager ready!');







  }















  async startVM() {







    console.log('🚀 Starting macOS VM...');







    







    // Check if VM is already running







    const runningVMs = await this.execPromise(`"${this.vmwareExe}" list`);







    if (runningVMs.includes('macOS.vmx')) {







      console.log('✅ VM already running');







      this.isVMRunning = true;







      return;







    }







    







    // Start the VM







    try {







      await this.execPromise(`"${this.vmwareExe}" start "${this.vmPath}" nogui`);







      console.log('✅ VM started successfully');







      this.isVMRunning = true;







    } catch (error) {







      console.log('⚠️ VM start failed, trying with GUI...');







      await this.execPromise(`"${this.vmwareExe}" start "${this.vmPath}"`);







      this.isVMRunning = true;







    }







  }















  async waitForVM() {







    console.log('⏳ Waiting for VM to boot...');







    







    const possibleIPs = [







      '*************',  // VMware NAT default







      '***************', // Hyper-V default







      '**********',     // Custom NAT







      '************'    // VMware bridged







    ];







    







    for (let attempt = 0; attempt < this.maxConnectionAttempts; attempt++) {







      console.log(`Attempt ${attempt + 1}/${this.maxConnectionAttempts}...`);







      







      for (const ip of possibleIPs) {







        try {







          // Try to ping the IP







          await this.execPromise(`ping -n 1 -w 1000 ${ip}`);







          console.log(`✅ VM responding at ${ip}`);







          this.vmIP = ip;







          return;







        } catch (e) {







          // Try next IP







        }







      }







      







      // Wait 5 seconds before next attempt







      await this.sleep(5000);







    }







    







    throw new Error('VM did not respond after maximum attempts');







  }















  async connectToBridge() {







    console.log('🌉 Connecting to macOS bridge...');







    







    if (!this.vmIP) {







      throw new Error('VM IP not found');







    }







    







    const bridgeURL = `ws://${this.vmIP}:8888`;







    console.log(`Connecting to: ${bridgeURL}`);







    







    return new Promise((resolve, reject) => {







      this.bridgeWS = new WebSocket(bridgeURL);







      







      const timeout = setTimeout(() => {







        reject(new Error('Bridge connection timeout'));







      }, 30000);







      







      this.bridgeWS.on('open', () => {







        clearTimeout(timeout);







        console.log('✅ Connected to macOS bridge!');







        







        // Request initial sync







        this.bridgeWS.send(JSON.stringify({







          type: 'SYNC_ALL',







          timestamp: Date.now()







        }));







        







        this.emit('connected');







        resolve();







      });







      







      this.bridgeWS.on('message', (data) => {







        try {







          const message = JSON.parse(data);







          this.handleBridgeMessage(message);







        } catch (e) {







          console.log('Invalid bridge message:', data.toString());







        }







      });







      







      this.bridgeWS.on('close', () => {







        console.log('❌ Bridge connection lost');







        this.emit('disconnected');







        







        // Try to reconnect after 10 seconds







        setTimeout(() => {







          this.connectToBridge().catch(console.error);







        }, 10000);







      });







      







      this.bridgeWS.on('error', (error) => {







        clearTimeout(timeout);







        console.log('❌ Bridge connection error:', error.message);







        







        // If bridge not found, try to install it







        if (error.code === 'ECONNREFUSED') {







          this.installBridge().then(() => {







            setTimeout(() => {







              this.connectToBridge().catch(console.error);







            }, 5000);







          });







        }







        







        reject(error);







      });







    });







  }















  async installBridge() {







    console.log('📦 Installing bridge in macOS VM...');







    







    try {







      // Copy bridge installer to VM







      const bridgeScript = fs.readFileSync('macos-vm-bridge.sh', 'utf8');







      







      // Use VMware tools to copy file







      const tempScript = path.join(process.env.TEMP, 'install-bridge.sh');







      fs.writeFileSync(tempScript, bridgeScript);







      







      // Copy to VM







      await this.execPromise(`"${this.vmwareExe}" copyFileFromHostToGuest "${this.vmPath}" "${tempScript}" "/tmp/install-bridge.sh"`);







      







      // Make executable and run







      await this.execPromise(`"${this.vmwareExe}" runProgramInGuest "${this.vmPath}" "/bin/chmod" "+x /tmp/install-bridge.sh"`);







      await this.execPromise(`"${this.vmwareExe}" runProgramInGuest "${this.vmPath}" "/tmp/install-bridge.sh"`);







      







      console.log('✅ Bridge installer copied to VM');







      console.log('⚠️ Complete the installation manually in the VM');







      







    } catch (error) {







      console.log('❌ Failed to install bridge automatically');







      console.log('Manual installation required:');







      console.log('1. Open VMware console');







      console.log('2. Copy macos-vm-bridge.sh to VM');







      console.log('3. Run: chmod +x macos-vm-bridge.sh && ./macos-vm-bridge.sh');







    }







  }















  handleBridgeMessage(message) {







    switch (message.type) {







      case 'FULL_SYNC':







        console.log(`📥 Received ${message.messages?.length || 0} messages from macOS`);







        this.emit('messages-synced', message.messages || []);







        break;







        







      case 'NEW_MESSAGE':







        console.log('📨 New message from macOS:', message.message.text?.substring(0, 50));







        this.emit('new-message', message.message);







        break;







        







      case 'TEST_RESPONSE':







        console.log('✅ Bridge test successful');







        break;







        







      default:







        console.log('Unknown bridge message:', message.type);







    }







  }















  async sendMessage(phoneNumber, text) {







    if (!this.bridgeWS || this.bridgeWS.readyState !== WebSocket.OPEN) {







      throw new Error('Bridge not connected');







    }







    







    return new Promise((resolve, reject) => {







      const messageId = Date.now().toString();







      







      this.bridgeWS.send(JSON.stringify({







        type: 'SEND_MESSAGE',







        id: messageId,







        phoneNumber,







        text,







        timestamp: Date.now()







      }));







      







      // Wait for confirmation (simplified - in real implementation, track message IDs)







      setTimeout(() => {







        resolve({ id: messageId, sent: true });







      }, 1000);







    });







  }















  async getVMStatus() {







    try {







      const runningVMs = await this.execPromise(`"${this.vmwareExe}" list`);







      const isRunning = runningVMs.includes('macOS.vmx');







      







      return {







        vmRunning: isRunning,







        vmIP: this.vmIP,







        bridgeConnected: this.bridgeWS && this.bridgeWS.readyState === WebSocket.OPEN,







        connectionAttempts: this.connectionAttempts







      };







    } catch (error) {







      return {







        vmRunning: false,







        vmIP: null,







        bridgeConnected: false,







        error: error.message







      };







    }







  }















  async stopVM() {







    console.log('🛑 Stopping macOS VM...');







    







    if (this.bridgeWS) {







      this.bridgeWS.close();







    }







    







    try {







      await this.execPromise(`"${this.vmwareExe}" stop "${this.vmPath}" soft`);







      console.log('✅ VM stopped');







      this.isVMRunning = false;







    } catch (error) {







      console.log('⚠️ VM stop failed:', error.message);







    }







  }















  execPromise(command) {







    return new Promise((resolve, reject) => {







      exec(command, (error, stdout, stderr) => {







        if (error) {







          reject(error);







        } else {







          resolve(stdout);







        }







      });







    });







  }















  sleep(ms) {







    return new Promise(resolve => setTimeout(resolve, ms));







  }







}















// Test the VM manager







async function testVMManager() {







  const vmManager = new MacOSVMManager();







  







  vmManager.on('connected', () => {







    console.log('🎉 VM Manager connected successfully!');







  });







  







  vmManager.on('messages-synced', (messages) => {







    console.log(`📱 Synced ${messages.length} messages from iPhone`);







  });







  







  vmManager.on('new-message', (message) => {







    console.log(`📨 New iPhone message: ${message.text}`);







  });







  







  try {







    await vmManager.initialize();







    







    // Test sending a message







    // await vmManager.sendMessage('+1234567890', 'Test from Windows!');







    







    console.log('\n✅ VM Manager test complete!');







    console.log('VM Status:', await vmManager.getVMStatus());







    







  } catch (error) {







    console.error('❌ VM Manager test failed:', error.message);







    







    if (error.message.includes('VMware')) {







      console.log('\n🔧 To fix:');







      console.log('1. Run: setup-macos-vm.bat');







      console.log('2. Install macOS in VMware');







      console.log('3. Run this test again');







    }







  }







}















module.exports = { MacOSVMManager };















// Run test if called directly







if (require.main === module) {







  testVMManager();







}







>>>>>>>



=======
// macOS VM Manager for iPhone Companion Pro

const { exec, spawn } = require('child_process');

const fs = require('fs');

const path = require('path');

const WebSocket = require('ws');

const { EventEmitter } = require('events');



class MacOSVMManager extends EventEmitter {

  constructor() {

    super();

    this.vmPath = 'C:\\macOS-VM\\macOS.vmx';

    this.vmwareExe = 'C:\\Program Files (x86)\\VMware\\VMware Workstation\\vmrun.exe';

    this.vmIP = null;

    this.bridgeWS = null;

    this.isVMRunning = false;

    this.connectionAttempts = 0;

    this.maxConnectionAttempts = 20;

  }



  async initialize() {

    console.log('🖥️ Initializing macOS VM Manager...');

    

    // Check if VMware is installed

    if (!fs.existsSync(this.vmwareExe)) {

      throw new Error('VMware Workstation not found. Run setup-macos-vm.bat first');

    }

    

    // Check if VM exists

    if (!fs.existsSync(this.vmPath)) {

      throw new Error('macOS VM not found. Run setup-macos-vm.bat first');

    }

    

    console.log('✅ VMware and VM found');

    

    // Start VM if not running

    await this.startVM();

    

    // Wait for VM to boot and find IP

    await this.waitForVM();

    

    // Connect to bridge

    await this.connectToBridge();

    

    console.log('✅ macOS VM Manager ready!');

  }



  async startVM() {

    console.log('🚀 Starting macOS VM...');

    

    // Check if VM is already running

    const runningVMs = await this.execPromise(`"${this.vmwareExe}" list`);

    if (runningVMs.includes('macOS.vmx')) {

      console.log('✅ VM already running');

      this.isVMRunning = true;

      return;

    }

    

    // Start the VM

    try {

      await this.execPromise(`"${this.vmwareExe}" start "${this.vmPath}" nogui`);

      console.log('✅ VM started successfully');

      this.isVMRunning = true;

    } catch (error) {

      console.log('⚠️ VM start failed, trying with GUI...');

      await this.execPromise(`"${this.vmwareExe}" start "${this.vmPath}"`);

      this.isVMRunning = true;

    }

  }



  async waitForVM() {

    console.log('⏳ Waiting for VM to boot...');

    

    const possibleIPs = [

      '*************',  // VMware NAT default

      '***************', // Hyper-V default

      '**********',     // Custom NAT

      '************'    // VMware bridged

    ];

    

    for (let attempt = 0; attempt < this.maxConnectionAttempts; attempt++) {

      console.log(`Attempt ${attempt + 1}/${this.maxConnectionAttempts}...`);

      

      for (const ip of possibleIPs) {

        try {

          // Try to ping the IP

          await this.execPromise(`ping -n 1 -w 1000 ${ip}`);

          console.log(`✅ VM responding at ${ip}`);

          this.vmIP = ip;

          return;

        } catch (e) {

          // Try next IP

        }

      }

      

      // Wait 5 seconds before next attempt

      await this.sleep(5000);

    }

    

    throw new Error('VM did not respond after maximum attempts');

  }



  async connectToBridge() {

    console.log('🌉 Connecting to macOS bridge...');

    

    if (!this.vmIP) {

      throw new Error('VM IP not found');

    }

    

    const bridgeURL = `ws://${this.vmIP}:8888`;

    console.log(`Connecting to: ${bridgeURL}`);

    

    return new Promise((resolve, reject) => {

      this.bridgeWS = new WebSocket(bridgeURL);

      

      const timeout = setTimeout(() => {

        reject(new Error('Bridge connection timeout'));

      }, 30000);

      

      this.bridgeWS.on('open', () => {

        clearTimeout(timeout);

        console.log('✅ Connected to macOS bridge!');

        

        // Request initial sync

        this.bridgeWS.send(JSON.stringify({

          type: 'SYNC_ALL',

          timestamp: Date.now()

        }));

        

        this.emit('connected');

        resolve();

      });

      

      this.bridgeWS.on('message', (data) => {

        try {

          const message = JSON.parse(data);

          this.handleBridgeMessage(message);

        } catch (e) {

          console.log('Invalid bridge message:', data.toString());

        }

      });

      

      this.bridgeWS.on('close', () => {

        console.log('❌ Bridge connection lost');

        this.emit('disconnected');

        

        // Try to reconnect after 10 seconds

        setTimeout(() => {

          this.connectToBridge().catch(console.error);

        }, 10000);

      });

      

      this.bridgeWS.on('error', (error) => {

        clearTimeout(timeout);

        console.log('❌ Bridge connection error:', error.message);

        

        // If bridge not found, try to install it

        if (error.code === 'ECONNREFUSED') {

          this.installBridge().then(() => {

            setTimeout(() => {

              this.connectToBridge().catch(console.error);

            }, 5000);

          });

        }

        

        reject(error);

      });

    });

  }



  async installBridge() {

    console.log('📦 Installing bridge in macOS VM...');

    

    try {

      // Copy bridge installer to VM

      const bridgeScript = fs.readFileSync('macos-vm-bridge.sh', 'utf8');

      

      // Use VMware tools to copy file

      const tempScript = path.join(process.env.TEMP, 'install-bridge.sh');

      fs.writeFileSync(tempScript, bridgeScript);

      

      // Copy to VM

      await this.execPromise(`"${this.vmwareExe}" copyFileFromHostToGuest "${this.vmPath}" "${tempScript}" "/tmp/install-bridge.sh"`);

      

      // Make executable and run

      await this.execPromise(`"${this.vmwareExe}" runProgramInGuest "${this.vmPath}" "/bin/chmod" "+x /tmp/install-bridge.sh"`);

      await this.execPromise(`"${this.vmwareExe}" runProgramInGuest "${this.vmPath}" "/tmp/install-bridge.sh"`);

      

      console.log('✅ Bridge installer copied to VM');

      console.log('⚠️ Complete the installation manually in the VM');

      

    } catch (error) {

      console.log('❌ Failed to install bridge automatically');

      console.log('Manual installation required:');

      console.log('1. Open VMware console');

      console.log('2. Copy macos-vm-bridge.sh to VM');

      console.log('3. Run: chmod +x macos-vm-bridge.sh && ./macos-vm-bridge.sh');

    }

  }



  handleBridgeMessage(message) {

    switch (message.type) {

      case 'FULL_SYNC':

        console.log(`📥 Received ${message.messages?.length || 0} messages from macOS`);

        this.emit('messages-synced', message.messages || []);

        break;

        

      case 'NEW_MESSAGE':

        console.log('📨 New message from macOS:', message.message.text?.substring(0, 50));

        this.emit('new-message', message.message);

        break;

        

      case 'TEST_RESPONSE':

        console.log('✅ Bridge test successful');

        break;

        

      default:

        console.log('Unknown bridge message:', message.type);

    }

  }



  async sendMessage(phoneNumber, text) {

    if (!this.bridgeWS || this.bridgeWS.readyState !== WebSocket.OPEN) {

      throw new Error('Bridge not connected');

    }

    

    return new Promise((resolve, reject) => {

      const messageId = Date.now().toString();

      

      this.bridgeWS.send(JSON.stringify({

        type: 'SEND_MESSAGE',

        id: messageId,

        phoneNumber,

        text,

        timestamp: Date.now()

      }));

      

      // Wait for confirmation (simplified - in real implementation, track message IDs)

      setTimeout(() => {

        resolve({ id: messageId, sent: true });

      }, 1000);

    });

  }



  async getVMStatus() {

    try {

      const runningVMs = await this.execPromise(`"${this.vmwareExe}" list`);

      const isRunning = runningVMs.includes('macOS.vmx');

      

      return {

        vmRunning: isRunning,

        vmIP: this.vmIP,

        bridgeConnected: this.bridgeWS && this.bridgeWS.readyState === WebSocket.OPEN,

        connectionAttempts: this.connectionAttempts

      };

    } catch (error) {

      return {

        vmRunning: false,

        vmIP: null,

        bridgeConnected: false,

        error: error.message

      };

    }

  }



  async stopVM() {

    console.log('🛑 Stopping macOS VM...');

    

    if (this.bridgeWS) {

      this.bridgeWS.close();

    }

    

    try {

      await this.execPromise(`"${this.vmwareExe}" stop "${this.vmPath}" soft`);

      console.log('✅ VM stopped');

      this.isVMRunning = false;

    } catch (error) {

      console.log('⚠️ VM stop failed:', error.message);

    }

  }



  execPromise(command) {

    return new Promise((resolve, reject) => {

      exec(command, (error, stdout, stderr) => {

        if (error) {

          reject(error);

        } else {

          resolve(stdout);

        }

      });

    });

  }



  sleep(ms) {

    return new Promise(resolve => setTimeout(resolve, ms));

  }

}



// Test the VM manager

async function testVMManager() {

  const vmManager = new MacOSVMManager();

  

  vmManager.on('connected', () => {

    console.log('🎉 VM Manager connected successfully!');

  });

  

  vmManager.on('messages-synced', (messages) => {

    console.log(`📱 Synced ${messages.length} messages from iPhone`);

  });

  

  vmManager.on('new-message', (message) => {

    console.log(`📨 New iPhone message: ${message.text}`);

  });

  

  try {

    await vmManager.initialize();

    

    // Test sending a message

    // await vmManager.sendMessage('+1234567890', 'Test from Windows!');

    

    console.log('\n✅ VM Manager test complete!');

    console.log('VM Status:', await vmManager.getVMStatus());

    

  } catch (error) {

    console.error('❌ VM Manager test failed:', error.message);

    

    if (error.message.includes('VMware')) {

      console.log('\n🔧 To fix:');

      console.log('1. Run: setup-macos-vm.bat');

      console.log('2. Install macOS in VMware');

      console.log('3. Run this test again');

    }

  }

}



module.exports = { MacOSVMManager };



// Run test if called directly

if (require.main === module) {

  testVMManager();

}

>>>>>>>
