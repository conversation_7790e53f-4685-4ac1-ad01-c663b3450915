const fs = require('fs');
const path = require('path');

// Add click debugging to app.js
const appJsPath = path.join(__dirname, 'src/renderer/scripts/app.js');
let appContent = fs.readFileSync(appJsPath, 'utf8');

// Add modern messages UI testing
const debugCode = `
// DEBUG: Modern Messages UI Test
console.log('🎨 Modern Messages UI Debug loaded');

// Override all button clicks with enhanced functionality
document.addEventListener('DOMContentLoaded', () => {
    console.log('🎨 DOM loaded, setting up modern messages test...');

    // Messages button - Test modern UI
    const messagesBtn = document.querySelector('[onclick*="open-messages"]');
    if (messagesBtn) {
        messagesBtn.onclick = async () => {
            console.log('📱 Opening Modern Messages UI...');
            try {
                const result = await require('electron').ipcRenderer.invoke('open-messages');
                console.log('✅ Modern Messages window opened:', result);
            } catch (error) {
                console.error('❌ Error opening messages:', error);
                alert('Error opening messages: ' + error.message);
            }
        };
        console.log('✅ Modern Messages button handler attached');
    }

    // Connect button - Test iPhone connection
    const connectBtn = document.getElementById('connect-btn');
    if (connectBtn) {
        connectBtn.onclick = async () => {
            console.log('🔌 Testing iPhone connection...');
            try {
                const result = await require('electron').ipcRenderer.invoke('connect-device');
                console.log('📱 Connection result:', result);
                if (result.success) {
                    alert('✅ iPhone connected via ' + result.method);
                } else {
                    alert('❌ Connection failed: ' + result.error);
                }
            } catch (error) {
                console.error('❌ Connection error:', error);
                alert('Connection error: ' + error.message);
            }
        };
        console.log('✅ Connect button handler attached');
    }

    // Add test button for modern messages
    const testBtn = document.createElement('button');
    testBtn.textContent = '🎨 Test Modern Messages';
    testBtn.style.cssText = \`
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 9999;
        padding: 10px;
        background: #0084ff;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
    \`;
    testBtn.onclick = async () => {
        console.log('🎨 Testing Modern Messages UI directly...');
        try {
            const result = await require('electron').ipcRenderer.invoke('open-messages');
            console.log('✅ Modern Messages test result:', result);
        } catch (error) {
            console.error('❌ Modern Messages test error:', error);
        }
    };
    document.body.appendChild(testBtn);

    // Log all buttons found
    console.log('🎨 Modern Messages Debug Setup Complete:', {
        messages: !!messagesBtn,
        connect: !!connectBtn,
        testButton: true
    });
});
`;

// Prepend debug code
fs.writeFileSync(appJsPath, debugCode + '\n\n' + appContent);
console.log('✅ Modern Messages UI debug code added! Restart the app to test.');