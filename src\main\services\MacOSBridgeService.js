const WebSocket = require('ws');
const sqlite3 = require('sqlite3').verbose();
const express = require('express');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { EventEmitter } = require('events');

class MacOSBridgeService extends EventEmitter {
  constructor() {
    super();
    this.wss = null;
    this.messagesDb = null;
    this.contactsDb = null;
    this.app = express();
    this.server = null;
    this.clients = new Set();
    this.messageWatcher = null;
    this.lastMessageId = 0;
  }

  async start(port = 8080) {
    console.log('Starting macOS Bridge Service...');
    
    try {
      // Setup Express server
      this.setupExpressServer();
      
      // Start HTTP server
      this.server = require('http').createServer(this.app);
      
      // Start WebSocket server
      this.wss = new WebSocket.Server({ 
        server: this.server,
        path: '/bridge'
      });
      
      this.setupWebSocketHandlers();
      
      // Connect to databases
      await this.connectToDatabases();
      
      // Start message monitoring
      this.startMessageMonitoring();
      
      // Start server
      this.server.listen(port, () => {
        console.log(`macOS Bridge Service started on port ${port}`);
        this.emit('service-started', { port });
      });
      
    } catch (error) {
      console.error('Failed to start bridge service:', error);
      this.emit('service-error', error);
      throw error;
    }
  }

  setupExpressServer() {
    this.app.use(express.json());
    
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        connections: this.clients.size,
        databases: {
          messages: !!this.messagesDb,
          contacts: !!this.contactsDb
        }
      });
    });
    
    // Messages API endpoints
    this.app.get('/api/messages', async (req, res) => {
      try {
        const messages = await this.getRecentMessages(100);
        res.json({ success: true, data: messages });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });
    
    this.app.post('/api/messages/send', async (req, res) => {
      try {
        const { phoneNumber, text } = req.body;
        await this.sendMessage(phoneNumber, text);
        res.json({ success: true });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });
    
    // Contacts API endpoints
    this.app.get('/api/contacts', async (req, res) => {
      try {
        const contacts = await this.getContacts();
        res.json({ success: true, data: contacts });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });
  }

  setupWebSocketHandlers() {
    this.wss.on('connection', (ws, req) => {
      console.log('Windows client connected via WebSocket');
      this.clients.add(ws);
      
      // Send welcome message
      ws.send(JSON.stringify({
        type: 'welcome',
        timestamp: Date.now(),
        capabilities: ['messages', 'contacts', 'calls', 'notifications']
      }));
      
      ws.on('message', async (data) => {
        try {
          const message = JSON.parse(data);
          await this.handleWebSocketMessage(ws, message);
        } catch (error) {
          console.error('WebSocket message handling error:', error);
          ws.send(JSON.stringify({
            type: 'error',
            message: error.message,
            timestamp: Date.now()
          }));
        }
      });
      
      ws.on('close', () => {
        console.log('Windows client disconnected');
        this.clients.delete(ws);
      });
      
      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        this.clients.delete(ws);
      });
    });
  }

  async handleWebSocketMessage(ws, message) {
    switch (message.type) {
      case 'get_messages':
        await this.handleGetMessages(ws, message);
        break;
      case 'send_message':
        await this.handleSendMessage(ws, message);
        break;
      case 'get_contacts':
        await this.handleGetContacts(ws, message);
        break;
      case 'ping':
        ws.send(JSON.stringify({ 
          type: 'pong', 
          timestamp: Date.now(),
          originalTimestamp: message.timestamp 
        }));
        break;
      case 'subscribe_messages':
        // Client wants real-time message updates
        ws.subscribeToMessages = true;
        break;
      default:
        ws.send(JSON.stringify({
          type: 'error',
          message: `Unknown message type: ${message.type}`,
          timestamp: Date.now()
        }));
    }
  }

  async connectToDatabases() {
    console.log('Connecting to macOS databases...');
    
    // Connect to Messages database
    const messagesDbPath = path.join(process.env.HOME, 'Library/Messages/chat.db');
    if (fs.existsSync(messagesDbPath)) {
      this.messagesDb = new sqlite3.Database(messagesDbPath, sqlite3.OPEN_READONLY, (err) => {
        if (err) {
          console.error('Failed to connect to Messages database:', err);
        } else {
          console.log('Connected to Messages database');
        }
      });
    } else {
      console.warn('Messages database not found at:', messagesDbPath);
    }
    
    // Connect to Contacts database
    const contactsDbPath = path.join(process.env.HOME, 'Library/Application Support/AddressBook/AddressBook-v22.abcddb');
    if (fs.existsSync(contactsDbPath)) {
      this.contactsDb = new sqlite3.Database(contactsDbPath, sqlite3.OPEN_READONLY, (err) => {
        if (err) {
          console.error('Failed to connect to Contacts database:', err);
        } else {
          console.log('Connected to Contacts database');
        }
      });
    } else {
      console.warn('Contacts database not found at:', contactsDbPath);
    }
  }

  async handleGetMessages(ws, message) {
    if (!this.messagesDb) {
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Messages database not available',
        timestamp: Date.now()
      }));
      return;
    }

    const limit = message.limit || 100;
    const offset = message.offset || 0;
    
    try {
      const messages = await this.getRecentMessages(limit, offset);
      ws.send(JSON.stringify({
        type: 'messages',
        data: messages,
        timestamp: Date.now(),
        requestId: message.requestId
      }));
    } catch (error) {
      ws.send(JSON.stringify({
        type: 'error',
        message: error.message,
        timestamp: Date.now(),
        requestId: message.requestId
      }));
    }
  }

  async getRecentMessages(limit = 100, offset = 0) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT 
          m.ROWID as id,
          m.text,
          m.date,
          m.date_read,
          m.date_delivered,
          m.is_from_me,
          m.is_read,
          m.service,
          h.id as phone_number,
          h.service as handle_service,
          c.chat_identifier,
          c.display_name as chat_name
        FROM message m
        LEFT JOIN handle h ON m.handle_id = h.ROWID
        LEFT JOIN chat_message_join cmj ON m.ROWID = cmj.message_id
        LEFT JOIN chat c ON cmj.chat_id = c.ROWID
        ORDER BY m.date DESC
        LIMIT ? OFFSET ?
      `;

      this.messagesDb.all(query, [limit, offset], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          // Convert Apple timestamp to Unix timestamp
          const messages = rows.map(row => ({
            ...row,
            date: this.convertAppleTimestamp(row.date),
            date_read: row.date_read ? this.convertAppleTimestamp(row.date_read) : null,
            date_delivered: row.date_delivered ? this.convertAppleTimestamp(row.date_delivered) : null
          }));
          resolve(messages);
        }
      });
    });
  }

  async handleSendMessage(ws, message) {
    try {
      const { phoneNumber, text } = message;
      await this.sendMessage(phoneNumber, text);
      
      ws.send(JSON.stringify({
        type: 'message_sent',
        success: true,
        timestamp: Date.now(),
        requestId: message.requestId
      }));
    } catch (error) {
      ws.send(JSON.stringify({
        type: 'error',
        message: error.message,
        timestamp: Date.now(),
        requestId: message.requestId
      }));
    }
  }

  async sendMessage(phoneNumber, text) {
    // Use AppleScript to send message through Messages.app
    const script = `
      tell application "Messages"
        set targetService to 1st service whose service type = iMessage
        set targetBuddy to buddy "${phoneNumber}" of targetService
        send "${text.replace(/"/g, '\\"')}" to targetBuddy
      end tell
    `;
    
    return new Promise((resolve, reject) => {
      exec(`osascript -e '${script}'`, (error, stdout, stderr) => {
        if (error) {
          reject(new Error(`Failed to send message: ${error.message}`));
        } else {
          console.log(`Message sent to ${phoneNumber}: ${text}`);
          resolve();
        }
      });
    });
  }

  async handleGetContacts(ws, message) {
    try {
      const contacts = await this.getContacts();
      ws.send(JSON.stringify({
        type: 'contacts',
        data: contacts,
        timestamp: Date.now(),
        requestId: message.requestId
      }));
    } catch (error) {
      ws.send(JSON.stringify({
        type: 'error',
        message: error.message,
        timestamp: Date.now(),
        requestId: message.requestId
      }));
    }
  }

  async getContacts() {
    if (!this.contactsDb) {
      // Fallback to system contacts if database not available
      return this.getContactsViaAppleScript();
    }
    
    return new Promise((resolve, reject) => {
      const query = `
        SELECT 
          p.ROWID as id,
          p.First as first_name,
          p.Last as last_name,
          pv.value as phone_number
        FROM ABPerson p
        LEFT JOIN ABMultiValue mv ON p.ROWID = mv.record_id
        LEFT JOIN ABMultiValueEntry pv ON mv.UID = pv.parent_id
        WHERE mv.property = 3
        ORDER BY p.Last, p.First
      `;
      
      this.contactsDb.all(query, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          const contacts = rows.map(row => ({
            id: row.id,
            name: `${row.first_name || ''} ${row.last_name || ''}`.trim(),
            phoneNumber: row.phone_number
          }));
          resolve(contacts);
        }
      });
    });
  }

  async getContactsViaAppleScript() {
    const script = `
      tell application "Contacts"
        set contactList to {}
        repeat with aPerson in people
          set personName to name of aPerson
          set phoneNumbers to value of phones of aPerson
          repeat with aPhone in phoneNumbers
            set end of contactList to {name:personName, phone:aPhone}
          end repeat
        end repeat
        return contactList
      end tell
    `;
    
    return new Promise((resolve, reject) => {
      exec(`osascript -e '${script}'`, (error, stdout, stderr) => {
        if (error) {
          reject(error);
        } else {
          // Parse AppleScript output (simplified)
          const contacts = [];
          resolve(contacts);
        }
      });
    });
  }

  startMessageMonitoring() {
    if (!this.messagesDb) {
      console.warn('Cannot start message monitoring: Messages database not available');
      return;
    }
    
    // Get current latest message ID
    this.messagesDb.get('SELECT MAX(ROWID) as max_id FROM message', (err, row) => {
      if (!err && row) {
        this.lastMessageId = row.max_id || 0;
        console.log(`Starting message monitoring from ID: ${this.lastMessageId}`);
        
        // Poll for new messages every 2 seconds
        this.messageWatcher = setInterval(() => {
          this.checkForNewMessages();
        }, 2000);
      }
    });
  }

  checkForNewMessages() {
    if (!this.messagesDb || this.clients.size === 0) {
      return;
    }
    
    const query = `
      SELECT 
        m.ROWID as id,
        m.text,
        m.date,
        m.is_from_me,
        h.id as phone_number
      FROM message m
      LEFT JOIN handle h ON m.handle_id = h.ROWID
      WHERE m.ROWID > ?
      ORDER BY m.date ASC
    `;
    
    this.messagesDb.all(query, [this.lastMessageId], (err, rows) => {
      if (err) {
        console.error('Error checking for new messages:', err);
        return;
      }
      
      if (rows.length > 0) {
        console.log(`Found ${rows.length} new messages`);
        
        rows.forEach(row => {
          const message = {
            ...row,
            date: this.convertAppleTimestamp(row.date)
          };
          
          // Broadcast to all connected clients
          this.broadcastToClients({
            type: 'new_message',
            data: message,
            timestamp: Date.now()
          });
          
          this.lastMessageId = Math.max(this.lastMessageId, row.id);
        });
      }
    });
  }

  broadcastToClients(message) {
    const messageStr = JSON.stringify(message);
    this.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(messageStr);
      }
    });
  }

  // Convert Apple timestamp (nanoseconds since 2001-01-01) to Unix timestamp
  convertAppleTimestamp(appleTimestamp) {
    if (!appleTimestamp) return null;
    // Apple timestamp is nanoseconds since 2001-01-01 00:00:00 UTC
    // Unix timestamp is seconds since 1970-01-01 00:00:00 UTC
    const appleEpoch = 978307200; // Seconds between 1970-01-01 and 2001-01-01
    return Math.floor(appleTimestamp / 1000000000) + appleEpoch;
  }

  async stop() {
    console.log('Stopping macOS Bridge Service...');
    
    if (this.messageWatcher) {
      clearInterval(this.messageWatcher);
      this.messageWatcher = null;
    }
    
    if (this.wss) {
      this.wss.close();
    }
    
    if (this.server) {
      this.server.close();
    }
    
    if (this.messagesDb) {
      this.messagesDb.close();
    }
    
    if (this.contactsDb) {
      this.contactsDb.close();
    }
    
    console.log('macOS Bridge Service stopped');
  }
}

module.exports = MacOSBridgeService;
