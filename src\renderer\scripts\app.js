
// DEBUG: Modern Messages UI Test
console.log('🎨 Modern Messages UI Debug loaded');

// Override all button clicks with enhanced functionality
document.addEventListener('DOMContentLoaded', () => {
    console.log('🎨 DOM loaded, setting up modern messages test...');

    // Messages button - Test modern UI
    const messagesBtn = document.querySelector('[onclick*="open-messages"]');
    if (messagesBtn) {
        messagesBtn.onclick = async () => {
            console.log('📱 Opening Modern Messages UI...');
            try {
                const result = await require('electron').ipcRenderer.invoke('open-messages');
                console.log('✅ Modern Messages window opened:', result);
            } catch (error) {
                console.error('❌ Error opening messages:', error);
                alert('Error opening messages: ' + error.message);
            }
        };
        console.log('✅ Modern Messages button handler attached');
    }

    // Connect button - Test iPhone connection
    const connectBtn = document.getElementById('connect-btn');
    if (connectBtn) {
        connectBtn.onclick = async () => {
            console.log('🔌 Testing iPhone connection...');
            try {
                const result = await require('electron').ipcRenderer.invoke('connect-device');
                console.log('📱 Connection result:', result);
                if (result.success) {
                    alert('✅ iPhone connected via ' + result.method);
                } else {
                    alert('❌ Connection failed: ' + result.error);
                }
            } catch (error) {
                console.error('❌ Connection error:', error);
                alert('Connection error: ' + error.message);
            }
        };
        console.log('✅ Connect button handler attached');
    }

    // Add test button for modern messages
    const testBtn = document.createElement('button');
    testBtn.textContent = '🎨 Test Modern Messages';
    testBtn.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 9999;
        padding: 10px;
        background: #0084ff;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
    `;
    testBtn.onclick = async () => {
        console.log('🎨 Testing Modern Messages UI directly...');
        try {
            const result = await require('electron').ipcRenderer.invoke('open-messages');
            console.log('✅ Modern Messages test result:', result);
        } catch (error) {
            console.error('❌ Modern Messages test error:', error);
        }
    };
    document.body.appendChild(testBtn);

    // Log all buttons found
    console.log('🎨 Modern Messages Debug Setup Complete:', {
        messages: !!messagesBtn,
        connect: !!connectBtn,
        testButton: true
    });
});



// iPhone Companion Pro - Renderer Script
const { ipcRenderer } = require('electron');

console.log('🚀 App.js loaded successfully!');

// Connection button handler
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM loaded, setting up handlers...');
  
  // Connect button
  const connectBtn = document.getElementById('connect-btn');
  if (connectBtn) {
    connectBtn.addEventListener('click', async () => {
      console.log('Connect button clicked!');
      connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Connecting...';
      connectBtn.disabled = true;
      
      try {
        const result = await ipcRenderer.invoke('connect-device');
        console.log('Connection result:', result);
        
        if (result.success) {
          connectBtn.innerHTML = '<i class="fas fa-check"></i> Connected';
          connectBtn.style.background = '#4CAF50';
          
          // Update status
          updateConnectionStatus('Connected via ' + result.method);
          
          // Update method indicators
          if (result.method === 'phoneLink') {
            updateMethodStatus('phoneLink', 'connected');
          }
        } else {
          connectBtn.innerHTML = '<i class="fas fa-times"></i> Failed';
          connectBtn.style.background = '#f44336';
          setTimeout(() => {
            connectBtn.innerHTML = '🚀 Connect iPhone';
            connectBtn.disabled = false;
            connectBtn.style.background = '';
          }, 3000);
        }
      } catch (error) {
        console.error('Connection error:', error);
        connectBtn.innerHTML = '🚀 Connect iPhone';
        connectBtn.disabled = false;
      }
    });
  }
  
  // Listen for connection success
  ipcRenderer.on('connection-success', (event, data) => {
    console.log('Connection successful:', data);
    
    // Update UI with real data
    const deviceInfo = document.querySelector('.device-info');
    if (deviceInfo) {
      deviceInfo.innerHTML = `
        <h3>iPhone Connected</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 10px;">
          <div>
            <strong>Contacts:</strong> ${data.contacts}
          </div>
          <div>
            <strong>Recent Calls:</strong> ${data.calls}
          </div>
          <div>
            <strong>Connection:</strong> Phone Link
          </div>
          <div>
            <strong>Status:</strong> Active
          </div>
        </div>
      `;
    }
  });
});

function updateConnectionStatus(status) {
  const statusEl = document.querySelector('.connection-status');
  if (statusEl) {
    statusEl.textContent = status;
  }
}

function updateMethodStatus(method, status) {
  const methodEl = document.querySelector(`[data-method="${method}"]`);
  if (methodEl) {
    methodEl.textContent = status === 'connected' ? 'Connected' : 'Checking...';
    methodEl.style.color = status === 'connected' ? '#4CAF50' : '#666';
  }
}

// Add data-method attributes to connection methods
document.addEventListener('DOMContentLoaded', () => {
  const methods = document.querySelectorAll('.connection-method');
  methods.forEach((method, index) => {
    const methodNames = ['airplay', 'phoneLink', 'usb', 'vm'];
    if (methodNames[index]) {
      method.setAttribute('data-method', methodNames[index]);
    }
  });
});
