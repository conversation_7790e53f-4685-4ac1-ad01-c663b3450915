const { EventEmitter } = require('events');
const { exec, spawn } = require('child_process');
const { promisify } = require('util');
const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');
const winston = require('winston');

const execAsync = promisify(exec);

class MessageExtractionService extends EventEmitter {
  constructor(persistence) {
    super();
    this.persistence = persistence;
    this.isRunning = false;
    this.watchers = new Map();
    this.notificationMonitor = null;
    this.extractionMethods = new Map();
    this.lastExtractedMessage = null;
    
    // Configure logger
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ level, message, timestamp }) => {
          return `${timestamp} [${level.toUpperCase()}] MessageExtraction: ${message}`;
        })
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'message-extraction.log' })
      ]
    });
  }

  async initialize() {
    this.logger.info('🔥 INITIALIZING MESSAGE EXTRACTION SERVICE 🔥');
    
    // Initialize all extraction methods
    await this.initializeExtractionMethods();
    
    this.logger.info('✅ Message Extraction Service initialized');
    return true;
  }

  async initializeExtractionMethods() {
    const methods = [
      { name: 'phonelink_files', handler: this.monitorPhoneLinkFiles.bind(this) },
      { name: 'windows_notifications', handler: this.monitorWindowsNotifications.bind(this) },
      { name: 'process_memory', handler: this.monitorProcessMemory.bind(this) },
      { name: 'registry_changes', handler: this.monitorRegistryChanges.bind(this) },
      { name: 'clipboard_monitoring', handler: this.monitorClipboard.bind(this) }
    ];
    
    for (const method of methods) {
      try {
        await method.handler();
        this.extractionMethods.set(method.name, { status: 'active', handler: method.handler });
        this.logger.info(`✅ ${method.name} extraction method initialized`);
      } catch (error) {
        this.logger.warn(`⚠️ ${method.name} extraction method failed: ${error.message}`);
        this.extractionMethods.set(method.name, { status: 'failed', error: error.message });
      }
    }
  }

  async startExtraction() {
    if (this.isRunning) {
      this.logger.warn('Message extraction already running');
      return;
    }

    this.logger.info('🚀 Starting message extraction...');
    this.isRunning = true;
    
    // Start all active extraction methods
    for (const [name, method] of this.extractionMethods) {
      if (method.status === 'active') {
        try {
          await method.handler();
          this.logger.info(`✅ Started ${name} extraction`);
        } catch (error) {
          this.logger.error(`❌ Failed to start ${name} extraction: ${error.message}`);
        }
      }
    }

    // Start continuous monitoring
    this.startContinuousMonitoring();
    
    this.emit('extraction-started');
  }

  async monitorPhoneLinkFiles() {
    const phoneLinkPaths = [
      'C:\\Users\\<USER>\\AppData\\Local\\Packages\\Microsoft.YourPhone_8wekyb3d8bbwe',
      'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Windows\\YourPhone',
      'C:\\Users\\<USER>\\AppData\\Local\\Temp\\YourPhone',
      'C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\YourPhone'
    ];

    for (const basePath of phoneLinkPaths) {
      if (fs.existsSync(basePath)) {
        this.logger.info(`📁 Monitoring Phone Link directory: ${basePath}`);
        
        const watcher = chokidar.watch(basePath, {
          persistent: true,
          ignoreInitial: false,
          usePolling: true,
          interval: 2000,
          depth: 10,
          ignored: /(node_modules|\.git)/
        });

        watcher.on('change', async (filePath) => {
          this.logger.info(`📱 Phone Link file changed: ${filePath}`);
          await this.extractFromFile(filePath);
        });

        watcher.on('add', async (filePath) => {
          this.logger.info(`📱 Phone Link file added: ${filePath}`);
          await this.extractFromFile(filePath);
        });

        this.watchers.set(basePath, watcher);
      }
    }
  }

  async monitorWindowsNotifications() {
    this.logger.info('🔔 Starting Windows notification monitoring...');
    
    // Simplified PowerShell script to avoid complex Windows API calls
    const psScript = `
      try {
        $ErrorActionPreference = "Stop"
        
        # Monitor Phone Link process notifications
        $phoneProcess = Get-Process -Name "YourPhone*" -ErrorAction SilentlyContinue
        
        # Output results as JSON
        $result = @{
          ProcessInfo = if ($phoneProcess) { 
            @{
              ProcessName = $phoneProcess.ProcessName
              Id = $phoneProcess.Id
              WindowTitle = $phoneProcess.MainWindowTitle
              Status = "Active"
            }
          } else { 
            @{
              Status = "NotFound"
            }
          }
          Timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss"
        }
        
        $result | ConvertTo-Json -Depth 2 -Compress
      } catch {
        '{"error": "' + $_.Exception.Message + '"}'
      }
    `;

    try {
      const { stdout, stderr } = await execAsync(`powershell -Command "${psScript.replace(/"/g, '\\"')}"`);
      
      const cleanOutput = stdout.trim();
      if (!cleanOutput) {
        this.logger.debug('📬 No notification data received');
        return;
      }
      
      let notificationData;
      try {
        notificationData = JSON.parse(cleanOutput);
      } catch (parseError) {
        this.logger.debug('⚠️ Invalid JSON in notification output, skipping', { output: cleanOutput.substring(0, 100) });
        return;
      }
      
      if (notificationData.error) {
        this.logger.warn(`⚠️ Notification monitoring error: ${notificationData.error}`);
        return;
      }
      
      if (notificationData.ProcessInfo?.Status === 'Active') {
        this.logger.info(`📬 Phone Link process active: ${notificationData.ProcessInfo.ProcessName} (PID: ${notificationData.ProcessInfo.Id})`);
        await this.extractFromNotification(notificationData);
      }
    } catch (error) {
      this.logger.error(`❌ Windows notification monitoring failed: ${error.message}`);
    }
  }

  async monitorProcessMemory() {
    this.logger.info('🧠 Starting Phone Link process memory monitoring...');
    
    const memoryScript = `
      try {
        $ErrorActionPreference = "Stop"
        
        # Get Phone Link process information
        $phoneProcess = Get-Process -Name "YourPhone*" -ErrorAction SilentlyContinue
        if ($phoneProcess) {
          $processInfo = @{
            ProcessName = $phoneProcess.ProcessName
            Id = $phoneProcess.Id
            WindowTitle = $phoneProcess.MainWindowTitle
            WorkingSet = $phoneProcess.WorkingSet64
            VirtualMemorySize = $phoneProcess.VirtualMemorySize64
            StartTime = $phoneProcess.StartTime.ToString("yyyy-MM-ddTHH:mm:ss")
            Responding = $phoneProcess.Responding
            Status = "Active"
          }
          
          $processInfo | ConvertTo-Json -Depth 2 -Compress
        } else {
          '{"error": "Phone Link process not found"}'
        }
      } catch {
        '{"error": "' + $_.Exception.Message + '"}'
      }
    `;

    try {
      const { stdout, stderr } = await execAsync(`powershell -Command "${memoryScript.replace(/"/g, '\\"')}"`);
      
      const cleanOutput = stdout.trim();
      if (!cleanOutput) {
        this.logger.debug('📱 No process data received');
        return;
      }
      
      let processData;
      try {
        processData = JSON.parse(cleanOutput);
      } catch (parseError) {
        this.logger.debug('⚠️ Invalid JSON in process monitoring output, skipping', { output: cleanOutput.substring(0, 100) });
        return;
      }
      
      if (processData.error) {
        this.logger.debug(`📱 Process monitoring: ${processData.error}`);
        return;
      }
      
      if (processData.ProcessName) {
        this.logger.info(`📱 Phone Link process active: ${processData.ProcessName} (PID: ${processData.Id})`);
        await this.extractFromProcess(processData);
      }
    } catch (error) {
      this.logger.error(`❌ Process memory monitoring failed: ${error.message}`);
    }
  }

  async monitorRegistryChanges() {
    this.logger.info('📝 Starting Windows Registry monitoring...');
    
    const registryScript = `
      $ErrorActionPreference = "SilentlyContinue"
      
      # Monitor Phone Link registry keys
      $registryPaths = @(
        "HKCU:\\Software\\Microsoft\\Windows\\CurrentVersion\\Hologram",
        "HKCU:\\Software\\Microsoft\\Windows\\CurrentVersion\\YourPhone",
        "HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\YourPhone"
      )
      
      $results = @()
      
      foreach ($path in $registryPaths) {
        if (Test-Path $path) {
          $regItems = Get-ItemProperty -Path $path -ErrorAction SilentlyContinue
          if ($regItems) {
            $results += @{
              Path = $path
              Properties = $regItems
              LastModified = (Get-Item $path).LastWriteTime
            }
          }
        }
      }
      
      @{
        RegistryData = $results
        Timestamp = Get-Date
      } | ConvertTo-Json -Depth 4
    `;

    try {
      const { stdout } = await execAsync(`powershell -Command "${registryScript.replace(/"/g, '\\"')}"`);
      const registryData = JSON.parse(stdout);
      
      if (registryData.RegistryData && registryData.RegistryData.length > 0) {
        this.logger.info(`📝 Found ${registryData.RegistryData.length} Phone Link registry entries`);
        await this.extractFromRegistry(registryData);
      }
    } catch (error) {
      this.logger.error(`❌ Registry monitoring failed: ${error.message}`);
    }
  }

  async monitorClipboard() {
    this.logger.info('📋 Starting clipboard monitoring...');
    
    // This would be implemented with a native module or PowerShell monitoring
    // For now, we'll use a simple PowerShell approach
    const clipboardScript = `
      try {
        $ErrorActionPreference = "Stop"
        
        # Check if clipboard contains phone-related data
        $clipboardContent = Get-Clipboard -ErrorAction SilentlyContinue
        if ($clipboardContent -and ($clipboardContent -match "\\+\\d{10,15}|\\(\\d{3}\\)\\s*\\d{3}-\\d{4}|\\d{10}")) {
          @{
            Content = $clipboardContent
            ContainsPhoneNumber = $true
            Timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss"
          } | ConvertTo-Json -Compress
        } else {
          '{"ContainsPhoneNumber": false}'
        }
      } catch {
        '{"error": "' + $_.Exception.Message + '"}'
      }
    `;

    try {
      const { stdout, stderr } = await execAsync(`powershell -Command "${clipboardScript.replace(/"/g, '\\"')}"`);
      
      const cleanOutput = stdout.trim();
      if (!cleanOutput) {
        this.logger.debug('📋 No clipboard data received');
        return;
      }
      
      let clipboardData;
      try {
        clipboardData = JSON.parse(cleanOutput);
      } catch (parseError) {
        this.logger.debug('⚠️ Invalid JSON in clipboard monitoring output, skipping', { output: cleanOutput.substring(0, 100) });
        return;
      }
      
      if (clipboardData.error) {
        this.logger.debug(`📋 Clipboard monitoring: ${clipboardData.error}`);
        return;
      }
      
      if (clipboardData.ContainsPhoneNumber) {
        this.logger.info('📋 Phone number detected in clipboard');
        await this.extractFromClipboard(clipboardData);
      }
    } catch (error) {
      this.logger.error(`❌ Clipboard monitoring failed: ${error.message}`);
    }
  }

  async extractFromFile(filePath) {
    try {
      const fileExtension = path.extname(filePath).toLowerCase();
      const fileName = path.basename(filePath);
      
      // Check for database files
      if (fileExtension === '.db' || fileExtension === '.sqlite') {
        await this.extractFromDatabase(filePath);
      }
      
      // Check for log files
      if (fileExtension === '.log' || fileExtension === '.txt') {
        await this.extractFromLogFile(filePath);
      }
      
      // Check for JSON files
      if (fileExtension === '.json') {
        await this.extractFromJsonFile(filePath);
      }
      
      this.logger.info(`✅ Processed file: ${fileName}`);
    } catch (error) {
      this.logger.error(`❌ Error extracting from file ${filePath}: ${error.message}`);
    }
  }

  async extractFromDatabase(dbPath) {
    const sqlite3 = require('sqlite3').verbose();
    
    return new Promise((resolve) => {
      const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
        if (err) {
          this.logger.error(`❌ Could not open database ${dbPath}: ${err.message}`);
          resolve();
          return;
        }
        
        // Get all tables
        db.all("SELECT name FROM sqlite_master WHERE type='table'", async (err, tables) => {
          if (err) {
            this.logger.error(`❌ Could not read database schema: ${err.message}`);
            db.close();
            resolve();
            return;
          }
          
          this.logger.info(`📊 Database ${path.basename(dbPath)} contains tables: ${tables.map(t => t.name).join(', ')}`);
          
          // Look for message-related tables
          for (const table of tables) {
            if (table.name.toLowerCase().includes('message') || 
                table.name.toLowerCase().includes('sms') || 
                table.name.toLowerCase().includes('chat')) {
              
              db.all(`SELECT * FROM ${table.name} ORDER BY rowid DESC LIMIT 10`, async (err, rows) => {
                if (!err && rows.length > 0) {
                  this.logger.info(`📬 Found ${rows.length} potential messages in ${table.name}`);
                  
                  for (const row of rows) {
                    await this.processExtractedMessage(row, 'database');
                  }
                }
              });
            }
          }
          
          db.close();
          resolve();
        });
      });
    });
  }

  async extractFromLogFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      // Look for phone numbers and message patterns
      const phoneRegex = /(\+\d{1,3}\s?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/g;
      const messagePatterns = [
        /message.*?from.*?(\+?\d{10,15})/gi,
        /sms.*?from.*?(\+?\d{10,15})/gi,
        /text.*?from.*?(\+?\d{10,15})/gi
      ];
      
      for (const line of lines) {
        const phoneMatches = line.match(phoneRegex);
        if (phoneMatches) {
          await this.processExtractedMessage({
            text: line,
            phoneNumbers: phoneMatches,
            timestamp: Date.now(),
            source: 'log_file'
          }, 'log');
        }
      }
      
    } catch (error) {
      this.logger.error(`❌ Error reading log file ${filePath}: ${error.message}`);
    }
  }

  async extractFromJsonFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const data = JSON.parse(content);
      
      // Look for message-like structures
      if (Array.isArray(data)) {
        for (const item of data) {
          if (this.looksLikeMessage(item)) {
            await this.processExtractedMessage(item, 'json_file');
          }
        }
      } else if (this.looksLikeMessage(data)) {
        await this.processExtractedMessage(data, 'json_file');
      }
      
    } catch (error) {
      this.logger.error(`❌ Error reading JSON file ${filePath}: ${error.message}`);
    }
  }

  async extractFromNotification(event) {
    try {
      // Parse notification for message content
      const message = event.Message || '';
      const phoneRegex = /(\+\d{1,3}\s?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/g;
      
      if (message.includes('message') || message.includes('text') || message.includes('SMS')) {
        const phoneMatches = message.match(phoneRegex);
        
        if (phoneMatches) {
          await this.processExtractedMessage({
            text: message,
            phoneNumbers: phoneMatches,
            timestamp: new Date(event.TimeCreated).getTime(),
            source: 'notification'
          }, 'notification');
        }
      }
    } catch (error) {
      this.logger.error(`❌ Error extracting from notification: ${error.message}`);
    }
  }

  async extractFromProcess(processData) {
    try {
      // This would involve more advanced techniques like reading process memory
      // For now, we'll just log the process information
      this.logger.info(`📱 Phone Link process: ${processData.ProcessName} - ${processData.WindowTitle}`);
      
      // In a real implementation, this would use Windows APIs to read process memory
      // and extract message data directly from the running application
      
    } catch (error) {
      this.logger.error(`❌ Error extracting from process: ${error.message}`);
    }
  }

  async extractFromRegistry(registryData) {
    try {
      // Look for message-related registry entries
      for (const entry of registryData.RegistryData) {
        const props = entry.Properties;
        
        // Check for phone numbers or message data in registry values
        for (const [key, value] of Object.entries(props)) {
          if (typeof value === 'string' && (
            value.includes('message') || 
            value.includes('SMS') || 
            /\+?\d{10,15}/.test(value)
          )) {
            await this.processExtractedMessage({
              registryKey: key,
              registryValue: value,
              registryPath: entry.Path,
              timestamp: new Date(entry.LastModified).getTime(),
              source: 'registry'
            }, 'registry');
          }
        }
      }
    } catch (error) {
      this.logger.error(`❌ Error extracting from registry: ${error.message}`);
    }
  }

  async extractFromClipboard(clipboardData) {
    try {
      // Extract phone numbers and potential message content from clipboard
      const phoneRegex = /(\+\d{1,3}\s?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/g;
      const phoneMatches = clipboardData.Content.match(phoneRegex);
      
      if (phoneMatches) {
        await this.processExtractedMessage({
          text: clipboardData.Content,
          phoneNumbers: phoneMatches,
          timestamp: new Date(clipboardData.Timestamp).getTime(),
          source: 'clipboard'
        }, 'clipboard');
      }
    } catch (error) {
      this.logger.error(`❌ Error extracting from clipboard: ${error.message}`);
    }
  }

  looksLikeMessage(obj) {
    if (!obj || typeof obj !== 'object') return false;
    
    const messageKeys = ['message', 'text', 'content', 'body', 'sms'];
    const phoneKeys = ['phone', 'number', 'from', 'to', 'contact'];
    
    const hasMessageKey = messageKeys.some(key => 
      Object.keys(obj).some(objKey => objKey.toLowerCase().includes(key))
    );
    
    const hasPhoneKey = phoneKeys.some(key => 
      Object.keys(obj).some(objKey => objKey.toLowerCase().includes(key))
    );
    
    return hasMessageKey || hasPhoneKey;
  }

  async processExtractedMessage(rawMessage, source) {
    try {
      // Convert raw message to standardized format
      const message = this.standardizeMessage(rawMessage, source);
      
      if (message && message.phoneNumber && message.messageText) {
        // Check if we've already processed this message
        const messageHash = this.createMessageHash(message);
        if (messageHash === this.lastExtractedMessage) {
          return; // Skip duplicate
        }
        
        this.lastExtractedMessage = messageHash;
        
        // Save to database
        await this.persistence.saveMessage(message);
        
        // Emit event
        this.emit('message-extracted', message);
        
        this.logger.info(`📬 Extracted message from ${message.phoneNumber}: ${message.messageText.substring(0, 50)}...`);
      }
    } catch (error) {
      this.logger.error(`❌ Error processing extracted message: ${error.message}`);
    }
  }

  standardizeMessage(rawMessage, source) {
    try {
      let phoneNumber = '';
      let messageText = '';
      let contactName = '';
      let timestamp = Date.now();
      
      // Extract phone number
      if (rawMessage.phoneNumber) {
        phoneNumber = rawMessage.phoneNumber;
      } else if (rawMessage.phoneNumbers && rawMessage.phoneNumbers.length > 0) {
        phoneNumber = rawMessage.phoneNumbers[0];
      } else if (rawMessage.phone) {
        phoneNumber = rawMessage.phone;
      } else if (rawMessage.from) {
        phoneNumber = rawMessage.from;
      }
      
      // Extract message text
      if (rawMessage.messageText) {
        messageText = rawMessage.messageText;
      } else if (rawMessage.text) {
        messageText = rawMessage.text;
      } else if (rawMessage.content) {
        messageText = rawMessage.content;
      } else if (rawMessage.body) {
        messageText = rawMessage.body;
      }
      
      // Extract contact name
      if (rawMessage.contactName) {
        contactName = rawMessage.contactName;
      } else if (rawMessage.contact) {
        contactName = rawMessage.contact;
      } else if (rawMessage.name) {
        contactName = rawMessage.name;
      }
      
      // Extract timestamp
      if (rawMessage.timestamp) {
        timestamp = rawMessage.timestamp;
      } else if (rawMessage.time) {
        timestamp = new Date(rawMessage.time).getTime();
      }
      
      // Clean phone number
      phoneNumber = phoneNumber.replace(/[^\d+]/g, '');
      
      if (phoneNumber && messageText) {
        return {
          threadId: phoneNumber,
          phoneNumber: phoneNumber,
          contactName: contactName || 'Unknown',
          messageText: messageText,
          timestamp: timestamp,
          isOutgoing: false, // Assume incoming unless specified
          isDelivered: true,
          isRead: false,
          hasAttachment: false,
          source: source
        };
      }
      
      return null;
    } catch (error) {
      this.logger.error(`❌ Error standardizing message: ${error.message}`);
      return null;
    }
  }

  createMessageHash(message) {
    const crypto = require('crypto');
    const hashString = `${message.phoneNumber}|${message.messageText}|${message.timestamp}`;
    return crypto.createHash('sha256').update(hashString).digest('hex');
  }

  startContinuousMonitoring() {
    // Run extraction methods every 30 seconds
    setInterval(async () => {
      if (this.isRunning) {
        await this.monitorWindowsNotifications();
        await this.monitorProcessMemory();
        await this.monitorClipboard();
      }
    }, 30000);
    
    this.logger.info('🔄 Continuous monitoring started');
  }

  async stopExtraction() {
    this.logger.info('⏹️ Stopping message extraction...');
    this.isRunning = false;
    
    // Close all watchers
    for (const [path, watcher] of this.watchers) {
      watcher.close();
    }
    this.watchers.clear();
    
    this.emit('extraction-stopped');
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      activeMethods: Array.from(this.extractionMethods.entries())
        .filter(([_, method]) => method.status === 'active')
        .map(([name, _]) => name),
      failedMethods: Array.from(this.extractionMethods.entries())
        .filter(([_, method]) => method.status === 'failed')
        .map(([name, method]) => ({ name, error: method.error })),
      watchedPaths: Array.from(this.watchers.keys())
    };
  }

  // Integration testing methods
  async testExtraction() {
    try {
      this.logger.info('🧪 Testing message extraction system...');
      
      // Test 1: Check if extraction methods are initialized
      const methodCount = this.extractionMethods.size;
      if (methodCount === 0) {
        this.logger.warn('No extraction methods initialized');
        return false;
      }
      
      // Test 2: Check if watchers are active
      const watcherCount = this.watchers.size;
      this.logger.info(`📁 ${watcherCount} file watchers active`);
      
      // Test 3: Test notification monitoring
      try {
        await this.monitorWindowsNotifications();
        this.logger.info('✅ Windows notification monitoring test passed');
      } catch (error) {
        this.logger.warn(`⚠️ Windows notification test failed: ${error.message}`);
      }
      
      // Test 4: Test process monitoring
      try {
        await this.monitorProcessMemory();
        this.logger.info('✅ Process memory monitoring test passed');
      } catch (error) {
        this.logger.warn(`⚠️ Process monitoring test failed: ${error.message}`);
      }
      
      // Test 5: Check extraction status
      const activeMethodsCount = Array.from(this.extractionMethods.entries())
        .filter(([_, method]) => method.status === 'active').length;
      
      this.logger.info(`📊 Extraction Test Results:`);
      this.logger.info(`   - Total methods: ${methodCount}`);
      this.logger.info(`   - Active methods: ${activeMethodsCount}`);
      this.logger.info(`   - File watchers: ${watcherCount}`);
      this.logger.info(`   - Running: ${this.isRunning}`);
      
      return activeMethodsCount > 0 && this.isRunning;
      
    } catch (error) {
      this.logger.error('Message extraction test failed', error);
      return false;
    }
  }
  
  getTestStatus() {
    return {
      isRunning: this.isRunning,
      totalMethods: this.extractionMethods.size,
      activeMethods: Array.from(this.extractionMethods.entries())
        .filter(([_, method]) => method.status === 'active').length,
      failedMethods: Array.from(this.extractionMethods.entries())
        .filter(([_, method]) => method.status === 'failed').length,
      activeWatchers: this.watchers.size,
      lastExtractedMessage: this.lastExtractedMessage ? 'Yes' : 'None'
    };
  }
}

module.exports = { MessageExtractionService };