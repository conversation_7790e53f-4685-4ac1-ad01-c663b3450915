// Complete Real iPhone Data Pipeline Setup
// AirPlay → Companion App → WebSocket → Shortcuts
// NO MORE DEMO/MOCK DATA - ONLY REAL IPHONE DATA

const { MessageService } = require('./src/main/services/MessageService');
const { AirPlayServer } = require('./src/main/services/AirPlayServer');
const { WebBridgeServer } = require('./src/main/services/WebBridgeServer');
const { ShortcutsIntegration } = require('./src/main/services/ShortcutsIntegration');

console.log('🚀 SETTING UP REAL IPHONE DATA PIPELINE');
console.log('=' .repeat(60));
console.log('📱 AirPlay → Companion App → WebSocket → Shortcuts');
console.log('🚫 NO MORE DEMO DATA - ONLY REAL IPHONE DATA');
console.log('=' .repeat(60));

class RealDataPipeline {
    constructor() {
        this.airPlayServer = null;
        this.webBridge = null;
        this.shortcuts = null;
        this.messageService = null;
        this.realDataSources = {
            airplay: false,
            companionApp: false,
            webSocket: false,
            shortcuts: false
        };
    }

    async initialize() {
        console.log('\n📡 Initializing Real Data Pipeline...');

        // 1. Initialize AirPlay Server for real screen mirroring
        console.log('\n📺 Setting up AirPlay Server...');
        this.airPlayServer = new AirPlayServer();
        
        this.airPlayServer.on('mirror-start', (sessionId) => {
            console.log('✅ REAL AIRPLAY CONNECTION:', sessionId);
            this.realDataSources.airplay = true;
            this.updatePipelineStatus();
        });

        this.airPlayServer.on('screen-frame', (data) => {
            // Process real iPhone screen data
            this.handleRealScreenData(data);
        });

        // 2. Initialize WebSocket Server for companion app
        console.log('\n📡 Setting up WebSocket Server...');
        this.webBridge = new WebBridgeServer();
        
        this.webBridge.on('device-connected', (deviceInfo) => {
            console.log('✅ REAL DEVICE CONNECTED:', deviceInfo);
            this.realDataSources.companionApp = true;
            this.realDataSources.webSocket = true;
            this.updatePipelineStatus();
        });

        this.webBridge.on('message-received', (message) => {
            // Process real messages from companion app
            this.handleRealMessage(message);
        });

        // 3. Initialize iOS Shortcuts Integration
        console.log('\n⚡ Setting up iOS Shortcuts...');
        this.shortcuts = new ShortcutsIntegration();
        
        this.shortcuts.on('real-messages-received', (data) => {
            console.log('✅ REAL MESSAGES FROM SHORTCUTS:', data.messages.length);
            this.handleRealMessagesFromShortcuts(data.messages);
        });

        this.shortcuts.on('real-notification', (notification) => {
            console.log('✅ REAL NOTIFICATION:', notification.notification.title);
            this.handleRealNotification(notification);
        });

        this.shortcuts.on('send-real-message', (messageData) => {
            console.log('📤 SENDING REAL MESSAGE:', messageData.phoneNumber);
            this.handleSendRealMessage(messageData);
        });

        // 4. Initialize Message Service (NO DEMO DATA)
        console.log('\n💬 Setting up Message Service (Real Data Only)...');
        this.messageService = new MessageService(this.airPlayServer, this.webBridge);
        
        // Override to prevent any demo data loading
        this.messageService.loadDemoData = () => {
            console.log('🚫 Demo data loading blocked - waiting for real iPhone data');
        };

        // Start all services
        await this.startServices();
    }

    async startServices() {
        console.log('\n🚀 Starting Real Data Services...');

        try {
            // Start AirPlay server
            await this.airPlayServer.start(7000);
            console.log('✅ AirPlay server running on port 7000');

            // Start WebSocket server
            await this.webBridge.start();
            console.log('✅ WebSocket server running on port 8899');

            // Start Shortcuts integration
            await this.shortcuts.start();
            console.log('✅ Shortcuts server running on port 8888');

            // Initialize message service
            this.messageService.initialize();
            console.log('✅ Message service initialized (real data only)');

            this.realDataSources.shortcuts = true;
            this.updatePipelineStatus();

        } catch (error) {
            console.error('❌ Error starting services:', error);
        }
    }

    handleRealScreenData(data) {
        // Process real iPhone screen frames
        console.log('📺 Processing real iPhone screen data...');
        // Forward to mirror window or process for UI interaction
    }

    handleRealMessage(message) {
        if (!message.isReal) {
            console.log('🚫 Blocking non-real message data');
            return;
        }

        console.log('💬 Processing real iPhone message:', {
            from: message.contactName || message.phoneNumber,
            preview: message.text.substring(0, 50) + '...'
        });

        // Add to message service
        this.messageService.addRealMessage(message);
    }

    handleRealMessagesFromShortcuts(messages) {
        messages.forEach(message => {
            if (message.isReal) {
                console.log('⚡ Real message from shortcuts:', message.contactName);
                this.messageService.addRealMessage(message);
            }
        });
    }

    handleRealNotification(notification) {
        console.log('🔔 Real iPhone notification:', {
            app: notification.app.name,
            title: notification.notification.title
        });

        // Forward to notification system
        // Could trigger desktop notifications, etc.
    }

    handleSendRealMessage(messageData) {
        console.log('📤 Sending real message via iPhone:', {
            to: messageData.phoneNumber,
            text: messageData.text.substring(0, 50) + '...'
        });

        // This would trigger the iPhone to send the actual message
        // via the companion app or shortcuts
    }

    updatePipelineStatus() {
        const activeCount = Object.values(this.realDataSources).filter(Boolean).length;
        const totalCount = Object.keys(this.realDataSources).length;

        console.log('\n📊 REAL DATA PIPELINE STATUS:');
        console.log(`📺 AirPlay: ${this.realDataSources.airplay ? '✅ ACTIVE' : '⏳ WAITING'}`);
        console.log(`📱 Companion App: ${this.realDataSources.companionApp ? '✅ ACTIVE' : '⏳ WAITING'}`);
        console.log(`📡 WebSocket: ${this.realDataSources.webSocket ? '✅ ACTIVE' : '⏳ WAITING'}`);
        console.log(`⚡ Shortcuts: ${this.realDataSources.shortcuts ? '✅ ACTIVE' : '⏳ WAITING'}`);
        console.log(`\n🎯 Pipeline Status: ${activeCount}/${totalCount} sources active`);

        if (activeCount === totalCount) {
            console.log('\n🎉 COMPLETE REAL DATA PIPELINE ACTIVE!');
            console.log('📱 Your iPhone is now fully integrated with real data flowing!');
        }
    }

    getStatus() {
        return {
            pipeline: this.realDataSources,
            activeCount: Object.values(this.realDataSources).filter(Boolean).length,
            totalCount: Object.keys(this.realDataSources).length,
            isComplete: Object.values(this.realDataSources).every(Boolean)
        };
    }
}

// Export for use in main application
module.exports = { RealDataPipeline };

// If run directly, start the pipeline
if (require.main === module) {
    const pipeline = new RealDataPipeline();
    pipeline.initialize().catch(console.error);
}
