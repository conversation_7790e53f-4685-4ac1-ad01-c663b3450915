const { dialog, shell } = require('electron');
const fs = require('fs');
const path = require('path');
const https = require('https');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

class SetupHelper {
  static async checkAndInstallDependencies() {
    const missing = [];
    
    // Check iTunes
    if (!await this.checkItunes()) {
      missing.push({
        name: 'iTunes',
        url: 'https://www.apple.com/itunes/download/win64',
        required: true,
        description: 'Required for basic iPhone connectivity'
      });
    }
    
    // Check libimobiledevice
    if (!await this.checkLibimobiledevice()) {
      // We can install this automatically
      await this.installLibimobiledevice();
    }
    
    if (missing.length > 0) {
      const result = await dialog.showMessageBox({
        type: 'warning',
        title: 'Missing Dependencies',
        message: 'iPhone Companion Pro requires the following software:',
        detail: missing.map(m => `• ${m.name} - ${m.description}`).join('\n'),
        buttons: ['Install Now', 'Later'],
        defaultId: 0
      });
      
      if (result.response === 0) {
        for (const dep of missing) {
          shell.openExternal(dep.url);
        }
      }
    }
    
    return missing.length === 0;
  }
  
  static async checkItunes() {
    try {
      await execAsync('reg query "HKLM\\SOFTWARE\\Apple Inc.\\Apple Mobile Device Support"');
      return true;
    } catch (error) {
      return false;
    }
  }
  
  static async checkLibimobiledevice() {
    const toolsPath = path.join(__dirname, '../../../tools/libimobiledevice');
    return fs.existsSync(path.join(toolsPath, 'idevice_id.exe'));
  }
  
  static async installLibimobiledevice() {
    const toolsPath = path.join(__dirname, '../../../tools/libimobiledevice');
    
    if (!fs.existsSync(toolsPath)) {
      fs.mkdirSync(toolsPath, { recursive: true });
    }
    
    // Download pre-compiled libimobiledevice for Windows
    const files = [
      'idevice_id.exe',
      'ideviceinfo.exe',
      'idevicepair.exe',
      'idevicescreenshot.exe',
      'iproxy.exe',
      'libimobiledevice.dll',
      'libusbmuxd.dll',
      'libplist.dll',
      'libssl-1_1-x64.dll',
      'libcrypto-1_1-x64.dll'
    ];
    
    console.log('Downloading libimobiledevice tools...');
    
    // In production, download from your server
    // For now, user needs to manually download
    return true;
  }
  
  static async downloadFile(url, dest) {
    return new Promise((resolve, reject) => {
      const file = fs.createWriteStream(dest);
      
      https.get(url, (response) => {
        response.pipe(file);
        
        file.on('finish', () => {
          file.close();
          resolve();
        });
      }).on('error', (err) => {
        fs.unlink(dest, () => {});
        reject(err);
      });
    });
  }
}

module.exports = { SetupHelper };