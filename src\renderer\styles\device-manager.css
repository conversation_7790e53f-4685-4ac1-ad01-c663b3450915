/* Device Manager Interface Styles */
.device-manager-container {
    padding: 20px;
    background: #0a0a0a;
    min-height: calc(100vh - 40px);
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Device Overview */
.device-overview {
    background: #1a1a1a;
    border-radius: 12px;
    border: 1px solid #333;
    overflow: hidden;
}

.device-card {
    padding: 0;
}

.device-header {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #333;
    gap: 20px;
}

.device-icon {
    font-size: 48px;
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #2a2a2a;
    border-radius: 12px;
}

.device-info {
    flex: 1;
}

.device-info h2 {
    margin: 0 0 5px 0;
    color: #fff;
    font-size: 24px;
    font-weight: 600;
}

.device-info p {
    margin: 0 0 10px 0;
    color: #999;
    font-size: 14px;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #FF3B30;
}

.status-dot.connected {
    background: #34C759;
    animation: pulse 2s infinite;
}

.status-dot.disconnected {
    background: #FF3B30;
}

.connection-status span {
    color: #fff;
    font-weight: 500;
}

.device-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.action-btn.primary {
    background: #007AFF;
    color: white;
}

.action-btn.primary:hover {
    background: #0056CC;
}

.action-btn.secondary {
    background: #2a2a2a;
    color: #fff;
    border: 1px solid #444;
}

.action-btn.secondary:hover {
    background: #3a3a3a;
}

.action-btn.small {
    padding: 4px 8px;
    font-size: 11px;
}

.action-btn.danger {
    background: #FF3B30;
    color: white;
}

.action-btn.danger:hover {
    background: #D70015;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Device Stats */
.device-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #2a2a2a;
    border-radius: 8px;
    border: 1px solid #444;
}

.stat-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #3a3a3a;
    border-radius: 8px;
}

.stat-info {
    flex: 1;
}

.stat-label {
    color: #999;
    font-size: 12px;
    margin-bottom: 4px;
}

.stat-value {
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
}

.stat-bar {
    width: 100%;
    height: 4px;
    background: #444;
    border-radius: 2px;
    overflow: hidden;
}

.stat-progress {
    height: 100%;
    background: #007AFF;
    transition: width 0.3s ease;
}

.stat-progress.battery {
    background: #34C759;
}

/* Device Details */
.device-details {
    background: #1a1a1a;
    border-radius: 12px;
    border: 1px solid #333;
    overflow: hidden;
    flex: 1;
}

.tab-navigation {
    display: flex;
    background: #2a2a2a;
    border-bottom: 1px solid #333;
    overflow-x: auto;
}

.tab-btn {
    padding: 15px 20px;
    background: transparent;
    border: none;
    color: #999;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    border-bottom: 2px solid transparent;
}

.tab-btn:hover {
    color: #fff;
    background: #3a3a3a;
}

.tab-btn.active {
    color: #007AFF;
    border-bottom-color: #007AFF;
    background: #1a1a1a;
}

.tab-content {
    display: none;
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
}

/* Info Sections */
.info-section {
    margin-bottom: 30px;
}

.info-section h3 {
    margin: 0 0 15px 0;
    color: #fff;
    font-size: 18px;
    font-weight: 600;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #2a2a2a;
    border-radius: 6px;
    border: 1px solid #444;
}

.info-label {
    color: #999;
    font-size: 13px;
    font-weight: 500;
}

.info-value {
    color: #fff;
    font-size: 13px;
    font-weight: 600;
}

/* Apps Tab */
.apps-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.apps-header h3 {
    margin: 0;
    color: #fff;
    font-size: 18px;
}

.apps-controls {
    display: flex;
    gap: 10px;
}

.apps-controls input {
    padding: 8px 12px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 6px;
    color: #fff;
    font-size: 13px;
    width: 200px;
}

.apps-controls select {
    padding: 8px 12px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 6px;
    color: #fff;
    font-size: 13px;
}

.apps-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.app-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #2a2a2a;
    border-radius: 8px;
    border: 1px solid #444;
    gap: 15px;
}

.app-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #3a3a3a;
    border-radius: 8px;
}

.app-info {
    flex: 1;
}

.app-name {
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
}

.app-details {
    display: flex;
    gap: 15px;
    font-size: 12px;
}

.app-version {
    color: #999;
}

.app-size {
    color: #999;
}

.app-type {
    color: #007AFF;
    text-transform: capitalize;
}

.app-actions {
    display: flex;
    gap: 8px;
}

/* Storage Tab */
.storage-overview {
    text-align: center;
}

.storage-overview h3 {
    margin-bottom: 20px;
}

.storage-chart {
    margin-bottom: 30px;
}

.storage-breakdown {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 400px;
    margin: 0 auto;
}

.storage-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px;
    background: #2a2a2a;
    border-radius: 6px;
}

.storage-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
}

.storage-label {
    flex: 1;
    color: #fff;
    font-size: 14px;
    text-align: left;
}

.storage-value {
    color: #999;
    font-size: 13px;
    font-weight: 600;
}

/* Network Tab */
.network-sections {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.network-section h4 {
    margin: 0 0 15px 0;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
}

/* Security Tab */
.security-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.security-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: #2a2a2a;
    border-radius: 8px;
    border: 1px solid #444;
    gap: 20px;
}

.security-icon {
    font-size: 32px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #3a3a3a;
    border-radius: 8px;
}

.security-details {
    flex: 1;
}

.security-details h4 {
    margin: 0 0 5px 0;
    color: #fff;
    font-size: 16px;
}

.security-details p {
    margin: 0 0 10px 0;
    color: #999;
    font-size: 13px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.secure {
    background: #34C759;
    color: white;
}

/* Diagnostics Tab */
.diagnostics-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.diagnostics-results {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.diagnostic-item {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    background: #2a2a2a;
    border-radius: 8px;
    border: 1px solid #444;
    gap: 20px;
}

.diagnostic-icon {
    font-size: 24px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.diagnostic-info h4 {
    margin: 0 0 8px 0;
    color: #fff;
    font-size: 14px;
}

.diagnostic-info p {
    margin: 0 0 4px 0;
    color: #999;
    font-size: 13px;
}

/* Animations */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .device-manager-container {
        padding: 15px;
    }
    
    .device-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .device-stats {
        grid-template-columns: 1fr;
    }
    
    .tab-navigation {
        flex-wrap: wrap;
    }
    
    .apps-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .apps-controls {
        width: 100%;
    }
    
    .apps-controls input {
        width: 100%;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .diagnostics-actions {
        flex-direction: column;
    }
}
