const { EventEmitter } = require('events');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);
const dgram = require('dgram');
const net = require('net');
const Store = require('electron-store');

class WirelessConnectionManager extends EventEmitter {
  constructor() {
    super();
    this.store = new Store();
    this.connected = false;
    this.deviceInfo = null;
    this.bluetoothDevice = null;
    this.wifiConnection = null;
  }

  async connect() {
    this.emit('status', 'Searching for iPhone...');
    
    // Try multiple connection methods
    const connected = await this.tryBluetoothFirst() || 
                     await this.tryWiFiDirect() ||
                     await this.tryPhoneLinkBridge();
    
    if (connected) {
      this.emit('connected', this.deviceInfo);
      this.startWirelessSync();
    } else {
      this.emit('error', 'Could not connect wirelessly. Make sure Bluetooth and WiFi are enabled on both devices.');
    }
    
    return connected;
  }

  async tryBluetoothFirst() {
    try {
      this.emit('status', 'Connecting via Bluetooth...');
      
      // Windows Bluetooth API via PowerShell
      const script = `
        Add-Type -AssemblyName System.Runtime.WindowsRuntime
        
        # Function to await async operations
        Function Await($WinRtTask, $ResultType) {
          $asTaskGeneric = ([System.WindowsRuntimeSystemExtensions].GetMethods() | 
            Where-Object { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1 })[0]
          $asTask = $asTaskGeneric.MakeGenericMethod($ResultType)
          $netTask = $asTask.Invoke($null, @($WinRtTask))
          $netTask.Wait() | Out-Null
          $netTask.Result
        }
        
        # Load Bluetooth APIs
        [void][Windows.Devices.Bluetooth.BluetoothLEDevice,Windows.Devices.Bluetooth,ContentType=WindowsRuntime]
        [void][Windows.Devices.Radios.Radio,Windows.System,ContentType=WindowsRuntime]
        [void][Windows.Devices.Bluetooth.BluetoothDevice,Windows.Devices.Bluetooth,ContentType=WindowsRuntime]
        
        # Get Bluetooth radio
        $radios = Await ([Windows.Devices.Radios.Radio]::GetRadiosAsync()) ([System.Collections.Generic.IReadOnlyList[Windows.Devices.Radios.Radio]])
        $bluetooth = $radios | Where-Object { $_.Kind -eq 'Bluetooth' }
        
        if ($bluetooth.State -ne 'On') {
          Write-Output "BLUETOOTH_OFF"
          exit
        }
        
        # Find paired devices
        $selector = [Windows.Devices.Bluetooth.BluetoothDevice]::GetDeviceSelector()
        $devices = Await ([Windows.Devices.Enumeration.DeviceInformation]::FindAllAsync($selector)) ([Windows.Devices.Enumeration.DeviceInformationCollection])
        
        foreach ($device in $devices) {
          if ($device.Name -match "iPhone") {
            # Get Bluetooth device
            $btDevice = Await ([Windows.Devices.Bluetooth.BluetoothDevice]::FromIdAsync($device.Id)) ([Windows.Devices.Bluetooth.BluetoothDevice])
            
            if ($btDevice.ConnectionStatus -eq 'Connected') {
              Write-Output "CONNECTED|$($device.Id)|$($device.Name)|$($btDevice.BluetoothAddress)"
            } else {
              # Try to connect
              $result = Await ($btDevice.RequestAccessAsync()) ([Windows.Devices.Enumeration.DeviceAccessStatus])
              if ($result -eq 'Allowed') {
                Write-Output "PAIRED|$($device.Id)|$($device.Name)|$($btDevice.BluetoothAddress)"
              }
            }
            break
          }
        }
      `;
      
      const { stdout } = await execAsync(`powershell -ExecutionPolicy Bypass -Command "${script}"`);
      const output = stdout.trim();
      
      if (output.startsWith('BLUETOOTH_OFF')) {
        this.emit('error', 'Please turn on Bluetooth');
        return false;
      }
      
      if (output.startsWith('CONNECTED') || output.startsWith('PAIRED')) {
        const [status, id, name, address] = output.split('|');
        
        this.bluetoothDevice = { id, name, address };
        this.deviceInfo = {
          name: name,
          connectionType: 'Bluetooth',
          bluetoothAddress: address
        };
        
        // Set up Bluetooth communication channel
        await this.setupBluetoothChannel();
        
        this.connected = true;
        return true;
      }
    } catch (error) {
      console.error('Bluetooth error:', error);
    }
    
    return false;
  }

  async setupBluetoothChannel() {
    // Create a local server for the iPhone to connect to
    this.btServer = net.createServer((socket) => {
      console.log('iPhone connected via Bluetooth channel');
      
      socket.on('data', (data) => {
        this.handleBluetoothData(data);
      });
    });
    
    this.btServer.listen(8888, () => {
      console.log('Bluetooth bridge server listening on port 8888');
    });
    
    // Advertise service via Bluetooth
    await this.advertiseService();
  }

  async advertiseService() {
    // This would advertise a custom service UUID
    // that a companion app on iPhone could discover
    const serviceUUID = '12345678-1234-5678-1234-************';
    
    // In practice, this would use Bluetooth SDP
    this.emit('service-advertised', serviceUUID);
  }

  async tryWiFiDirect() {
    try {
      this.emit('status', 'Trying WiFi connection...');
      
      // Check if iPhone and PC are on same network
      const localIP = await this.getLocalIP();
      
      // Broadcast discovery message
      const discoverySocket = dgram.createSocket('udp4');
      const discoveryMessage = JSON.stringify({
        type: 'IPHONE_COMPANION_DISCOVERY',
        version: '1.0',
        ip: localIP,
        port: 9999,
        name: require('os').hostname()
      });
      
      discoverySocket.bind(() => {
        discoverySocket.setBroadcast(true);
        
        // Send discovery broadcast
        const broadcastAddress = localIP.replace(/\.\d+$/, '.255');
        discoverySocket.send(
          discoveryMessage,
          0,
          discoveryMessage.length,
          9998,
          broadcastAddress
        );
      });
      
      // Listen for iPhone response
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          discoverySocket.close();
          resolve(false);
        }, 5000);
        
        discoverySocket.on('message', (msg, rinfo) => {
          try {
            const response = JSON.parse(msg.toString());
            
            if (response.type === 'IPHONE_COMPANION_RESPONSE') {
              clearTimeout(timeout);
              discoverySocket.close();
              
              this.deviceInfo = {
                name: response.deviceName,
                ip: rinfo.address,
                connectionType: 'WiFi'
              };
              
              // Connect to iPhone
              this.connectToiPhoneWiFi(rinfo.address, response.port);
              resolve(true);
            }
          } catch (e) {
            // Not our message
          }
        });
      });
    } catch (error) {
      console.error('WiFi discovery error:', error);
      return false;
    }
  }

  async getLocalIP() {
    const { stdout } = await execAsync('ipconfig');
    const match = stdout.match(/IPv4 Address[.\s]+:\s*(\d+\.\d+\.\d+\.\d+)/);
    return match ? match[1] : '127.0.0.1';
  }

  async connectToiPhoneWiFi(ip, port) {
    this.wifiConnection = net.createConnection({ host: ip, port }, () => {
      console.log('Connected to iPhone via WiFi');
      
      // Send handshake
      this.wifiConnection.write(JSON.stringify({
        type: 'HANDSHAKE',
        capabilities: ['messages', 'notifications', 'files']
      }));
    });
    
    this.wifiConnection.on('data', (data) => {
      this.handleWiFiData(data);
    });
  }

  async tryPhoneLinkBridge() {
    try {
      this.emit('status', 'Checking Windows Phone Link...');
      
      // Check if Phone Link is running
      const { stdout } = await execAsync('tasklist /FI "IMAGENAME eq PhoneExperienceHost.exe"');
      
      if (stdout.includes('PhoneExperienceHost.exe')) {
        // Phone Link is running, we can try to bridge through it
        
        // Access Phone Link's local storage
        const phoneLinkData = await this.accessPhoneLinkData();
        
        if (phoneLinkData) {
          this.deviceInfo = {
            name: 'iPhone (via Phone Link)',
            connectionType: 'Phone Link Bridge'
          };
          
          this.connected = true;
          this.startPhoneLinkMonitoring();
          
          return true;
        }
      }
    } catch (error) {
      console.error('Phone Link bridge error:', error);
    }
    
    return false;
  }

  async accessPhoneLinkData() {
    // Phone Link stores data in AppData
    const appDataPath = process.env.LOCALAPPDATA;
    const phoneLinkPath = `${appDataPath}\\Packages\\Microsoft.YourPhone_8wekyb3d8bbwe\\LocalState`;
    
    // This would access Phone Link's database
    // For safety, we'll just check if it exists
    const fs = require('fs');
    
    if (fs.existsSync(phoneLinkPath)) {
      console.log('Phone Link data found');
      return true;
    }
    
    return false;
  }

  startPhoneLinkMonitoring() {
    // Monitor Phone Link for new messages/notifications
    setInterval(() => {
      this.checkPhoneLinkUpdates();
    }, 2000);
  }

  async checkPhoneLinkUpdates() {
    // This would monitor Phone Link's database for changes
    // and sync them to our app
  }

  startWirelessSync() {
    // Set up periodic sync
    this.syncInterval = setInterval(() => {
      this.syncData();
    }, 5000);
  }

  async syncData() {
    // Request data from iPhone
    if (this.wifiConnection) {
      this.wifiConnection.write(JSON.stringify({
        type: 'SYNC_REQUEST',
        data: ['messages', 'battery', 'notifications']
      }));
    }
  }

  handleBluetoothData(data) {
    try {
      const message = JSON.parse(data.toString());
      this.processIncomingData(message);
    } catch (e) {
      // Binary data
    }
  }

  handleWiFiData(data) {
    try {
      const message = JSON.parse(data.toString());
      this.processIncomingData(message);
    } catch (e) {
      // Binary data
    }
  }

  processIncomingData(message) {
    switch (message.type) {
      case 'MESSAGES':
        this.emit('messages-sync', message.data);
        break;
        
      case 'BATTERY':
        this.emit('battery-update', message.data);
        break;
        
      case 'NOTIFICATION':
        this.emit('notification', message.data);
        break;
        
      case 'DEVICE_INFO':
        this.deviceInfo = { ...this.deviceInfo, ...message.data };
        this.emit('device-info-update', this.deviceInfo);
        break;
    }
  }

  // Fallback: Use Windows notification access
  async enableNotificationBridge() {
    // Windows can access notifications from connected phones
    const script = `
      Add-Type @"
      using System;
      using Windows.UI.Notifications.Management;
      using Windows.Foundation;
      
      public class NotificationListener {
        public static async void StartListening() {
          var listener = UserNotificationListener.Current;
          var access = await listener.RequestAccessAsync();
          
          if (access == UserNotificationListenerAccessStatus.Allowed) {
            var notifications = await listener.GetNotificationsAsync(NotificationKinds.Toast);
            
            foreach (var notif in notifications) {
              if (notif.AppInfo.DisplayInfo.DisplayName.Contains("Phone")) {
                Console.WriteLine($"NOTIFICATION|{notif.Id}|{notif.Notification.Visual.GetBinding(KnownNotificationBindings.ToastGeneric).GetTextElements()[0].Text}");
              }
            }
          }
        }
      }
"@ -ReferencedAssemblies PresentationCore,PresentationFramework,WindowsBase,System.Runtime.WindowsRuntime,Windows.UI,Windows.Foundation
      
      [NotificationListener]::StartListening()
    `;
    
    // This would capture notifications from Phone Link
  }

  disconnect() {
    if (this.btServer) {
      this.btServer.close();
    }
    
    if (this.wifiConnection) {
      this.wifiConnection.destroy();
    }
    
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    
    this.connected = false;
    this.emit('disconnected');
  }
}

module.exports = { WirelessConnectionManager };