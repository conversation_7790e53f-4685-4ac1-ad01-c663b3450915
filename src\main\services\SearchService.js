const { EventEmitter } = require('events');
const winston = require('winston');

/**
 * SearchService - Advanced search functionality with FTS5 and intelligent ranking
 * Provides instant search across messages, contacts, and conversation history
 */
class SearchService extends EventEmitter {
  constructor(persistence) {
    super();
    this.persistence = persistence;
    this.searchCache = new Map();
    this.recentSearches = [];
    this.searchStats = {
      totalSearches: 0,
      averageResponseTime: 0,
      popularQueries: new Map()
    };
    
    // Configure logger
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ level, message, timestamp }) => {
          return `${timestamp} [${level.toUpperCase()}] SearchService: ${message}`;
        })
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'search-service.log' })
      ]
    });
  }

  async initialize() {
    this.logger.info('🔍 INITIALIZING SEARCH SERVICE 🔍');
    
    // Verify FTS5 is available
    await this.verifyFTS5();
    
    // Build search index if needed
    await this.buildSearchIndex();
    
    this.logger.info('✅ Search Service initialized');
    return true;
  }

  async verifyFTS5() {
    try {
      // Test FTS5 functionality
      const testResult = await this.persistence.searchMessages('test', 1);
      this.logger.info('✅ FTS5 search verified');
    } catch (error) {
      this.logger.error(`❌ FTS5 verification failed: ${error.message}`);
      throw new Error('FTS5 search not available');
    }
  }

  async buildSearchIndex() {
    try {
      // Check if we need to rebuild the FTS5 index
      const stats = await this.persistence.getMessageStats();
      this.logger.info(`📊 Search index contains ${stats.total_messages} messages`);
      
      // Rebuild index if it seems empty
      if (stats.total_messages > 0) {
        await this.rebuildSearchIndex();
      }
    } catch (error) {
      this.logger.error(`❌ Error building search index: ${error.message}`);
    }
  }

  async rebuildSearchIndex() {
    this.logger.info('🔄 Rebuilding search index...');
    
    try {
      // Clear existing FTS5 index
      await this.persistence.run('DELETE FROM messages_fts');
      
      // Rebuild from messages table
      await this.persistence.run(`
        INSERT INTO messages_fts(rowid, message_text, contact_name, phone_number)
        SELECT id, message_text, contact_name, phone_number FROM messages
      `);
      
      this.logger.info('✅ Search index rebuilt');
    } catch (error) {
      this.logger.error(`❌ Error rebuilding search index: ${error.message}`);
    }
  }

  async search(query, options = {}) {
    const startTime = Date.now();
    
    try {
      // Validate query
      if (!query || query.trim().length < 2) {
        return { results: [], suggestions: this.getSearchSuggestions() };
      }
      
      const normalizedQuery = this.normalizeQuery(query);
      
      // Check cache first
      const cacheKey = this.getCacheKey(normalizedQuery, options);
      if (this.searchCache.has(cacheKey)) {
        const cached = this.searchCache.get(cacheKey);
        this.logger.info(`🎯 Cache hit for query: "${normalizedQuery}"`);
        return cached;
      }
      
      // Perform search
      const results = await this.performSearch(normalizedQuery, options);
      
      // Cache results
      this.cacheResults(cacheKey, results);
      
      // Update stats
      this.updateSearchStats(normalizedQuery, Date.now() - startTime);
      
      this.logger.info(`🔍 Search completed: "${normalizedQuery}" - ${results.messages?.length || 0} results in ${Date.now() - startTime}ms`);
      
      return results;
      
    } catch (error) {
      this.logger.error(`❌ Search failed for "${query}": ${error.message}`);
      return { results: [], error: error.message };
    }
  }

  async performSearch(query, options) {
    const {
      limit = 50,
      searchType = 'all', // 'messages', 'contacts', 'threads', 'all'
      phoneNumber = null,
      dateRange = null,
      includeMetadata = false
    } = options;
    
    const results = {
      messages: [],
      contacts: [],
      threads: [],
      summary: {
        totalResults: 0,
        searchTime: Date.now(),
        query: query
      }
    };

    // Search messages using FTS5
    if (searchType === 'all' || searchType === 'messages') {
      results.messages = await this.searchMessages(query, limit, phoneNumber, dateRange);
    }

    // Search contacts
    if (searchType === 'all' || searchType === 'contacts') {
      results.contacts = await this.searchContacts(query, limit);
    }

    // Search conversation threads
    if (searchType === 'all' || searchType === 'threads') {
      results.threads = await this.searchThreads(query, limit);
    }

    // Calculate total results
    results.summary.totalResults = 
      results.messages.length + 
      results.contacts.length + 
      results.threads.length;

    // Add metadata if requested
    if (includeMetadata) {
      results.metadata = await this.getSearchMetadata(query, results);
    }

    return results;
  }

  async searchMessages(query, limit, phoneNumber = null, dateRange = null) {
    try {
      // Build FTS5 query
      let ftsQuery = this.buildFTSQuery(query);
      
      // Base SQL for FTS5 search
      let sql = `
        SELECT m.*, 
               COALESCE(c.display_name, m.contact_name) as resolved_contact_name,
               messages_fts.rank
        FROM messages_fts
        JOIN messages m ON messages_fts.rowid = m.id
        LEFT JOIN contacts c ON m.phone_number = c.phone_number
        WHERE messages_fts MATCH ?
      `;
      
      const params = [ftsQuery];
      
      // Add phone number filter
      if (phoneNumber) {
        sql += ' AND m.phone_number = ?';
        params.push(phoneNumber);
      }
      
      // Add date range filter
      if (dateRange) {
        if (dateRange.start) {
          sql += ' AND m.timestamp >= ?';
          params.push(new Date(dateRange.start).getTime());
        }
        if (dateRange.end) {
          sql += ' AND m.timestamp <= ?';
          params.push(new Date(dateRange.end).getTime());
        }
      }
      
      sql += ' ORDER BY messages_fts.rank, m.timestamp DESC LIMIT ?';
      params.push(limit);
      
      const rows = await this.persistence.all(sql, params);
      
      return rows.map(row => ({
        id: row.id,
        threadId: row.thread_id,
        phoneNumber: row.phone_number,
        contactName: row.resolved_contact_name || 'Unknown',
        messageText: row.message_text,
        timestamp: new Date(row.timestamp),
        isOutgoing: row.is_outgoing === 1,
        source: row.source,
        rank: row.rank,
        snippet: this.generateSnippet(row.message_text, query)
      }));
      
    } catch (error) {
      this.logger.error(`❌ Message search failed: ${error.message}`);
      return [];
    }
  }

  async searchContacts(query, limit) {
    try {
      const sql = `
        SELECT *, 
               CASE 
                 WHEN display_name LIKE ? THEN 1
                 WHEN phone_number LIKE ? THEN 2
                 WHEN email LIKE ? THEN 3
                 ELSE 4
               END as relevance_score
        FROM contacts
        WHERE display_name LIKE ? 
           OR phone_number LIKE ? 
           OR email LIKE ?
        ORDER BY relevance_score, display_name
        LIMIT ?
      `;
      
      const searchPattern = `%${query}%`;
      const params = [
        searchPattern, searchPattern, searchPattern, // For relevance scoring
        searchPattern, searchPattern, searchPattern, // For WHERE clause
        limit
      ];
      
      const rows = await this.persistence.all(sql, params);
      
      return rows.map(row => ({
        id: row.id,
        phoneNumber: row.phone_number,
        displayName: row.display_name,
        firstName: row.first_name,
        lastName: row.last_name,
        email: row.email,
        photoPath: row.photo_path,
        relevanceScore: row.relevance_score,
        snippet: this.generateContactSnippet(row, query)
      }));
      
    } catch (error) {
      this.logger.error(`❌ Contact search failed: ${error.message}`);
      return [];
    }
  }

  async searchThreads(query, limit) {
    try {
      const sql = `
        SELECT mt.*, 
               COALESCE(c.display_name, mt.contact_name) as resolved_contact_name,
               (SELECT COUNT(*) FROM messages m WHERE m.thread_id = mt.thread_id) as message_count,
               CASE 
                 WHEN COALESCE(c.display_name, mt.contact_name) LIKE ? THEN 1
                 WHEN mt.phone_number LIKE ? THEN 2
                 WHEN mt.last_message LIKE ? THEN 3
                 ELSE 4
               END as relevance_score
        FROM message_threads mt
        LEFT JOIN contacts c ON mt.phone_number = c.phone_number
        WHERE COALESCE(c.display_name, mt.contact_name) LIKE ?
           OR mt.phone_number LIKE ?
           OR mt.last_message LIKE ?
        ORDER BY relevance_score, mt.last_timestamp DESC
        LIMIT ?
      `;
      
      const searchPattern = `%${query}%`;
      const params = [
        searchPattern, searchPattern, searchPattern, // For relevance scoring
        searchPattern, searchPattern, searchPattern, // For WHERE clause
        limit
      ];
      
      const rows = await this.persistence.all(sql, params);
      
      return rows.map(row => ({
        threadId: row.thread_id,
        phoneNumber: row.phone_number,
        contactName: row.resolved_contact_name || 'Unknown',
        lastMessage: row.last_message,
        lastTimestamp: row.last_timestamp ? new Date(row.last_timestamp) : null,
        messageCount: row.message_count || 0,
        relevanceScore: row.relevance_score,
        snippet: this.generateThreadSnippet(row, query)
      }));
      
    } catch (error) {
      this.logger.error(`❌ Thread search failed: ${error.message}`);
      return [];
    }
  }

  buildFTSQuery(query) {
    // Clean and prepare query for FTS5
    const cleanQuery = query.trim().toLowerCase();
    
    // Handle phone numbers
    if (/^\+?\d[\d\s\-\(\)]+$/.test(cleanQuery)) {
      const digits = cleanQuery.replace(/\D/g, '');
      return `phone_number:${digits}*`;
    }
    
    // Handle phrases (quoted text)
    if (cleanQuery.includes('"')) {
      return cleanQuery;
    }
    
    // Handle multiple words - search for phrase and individual words
    const words = cleanQuery.split(/\s+/);
    if (words.length > 1) {
      const phraseQuery = `"${cleanQuery}"`;
      const wordQueries = words.map(word => `${word}*`).join(' OR ');
      return `(${phraseQuery}) OR (${wordQueries})`;
    }
    
    // Single word with prefix matching
    return `${cleanQuery}*`;
  }

  generateSnippet(text, query, maxLength = 150) {
    if (!text || !query) return '';
    
    const queryWords = query.toLowerCase().split(/\s+/);
    const textLower = text.toLowerCase();
    
    // Find the position of the first query word
    let bestPosition = -1;
    let bestWord = '';
    
    for (const word of queryWords) {
      const pos = textLower.indexOf(word.toLowerCase());
      if (pos !== -1 && (bestPosition === -1 || pos < bestPosition)) {
        bestPosition = pos;
        bestWord = word;
      }
    }
    
    if (bestPosition === -1) {
      return text.substring(0, maxLength) + (text.length > maxLength ? '...' : '');
    }
    
    // Extract snippet around the found word
    const start = Math.max(0, bestPosition - 50);
    const end = Math.min(text.length, start + maxLength);
    
    let snippet = text.substring(start, end);
    
    // Add ellipsis if truncated
    if (start > 0) snippet = '...' + snippet;
    if (end < text.length) snippet = snippet + '...';
    
    // Highlight the search terms
    for (const word of queryWords) {
      const regex = new RegExp(`(${this.escapeRegExp(word)})`, 'gi');
      snippet = snippet.replace(regex, '<mark>$1</mark>');
    }
    
    return snippet;
  }

  generateContactSnippet(contact, query) {
    const fields = [contact.display_name, contact.phone_number, contact.email].filter(Boolean);
    const matchingField = fields.find(field => 
      field.toLowerCase().includes(query.toLowerCase())
    );
    
    return matchingField ? this.generateSnippet(matchingField, query, 100) : '';
  }

  generateThreadSnippet(thread, query) {
    if (thread.last_message && thread.last_message.toLowerCase().includes(query.toLowerCase())) {
      return this.generateSnippet(thread.last_message, query, 100);
    }
    
    return this.generateSnippet(thread.resolved_contact_name || thread.phone_number, query, 100);
  }

  async getSearchMetadata(query, results) {
    return {
      queryAnalysis: this.analyzeQuery(query),
      resultDistribution: {
        messagesByThread: this.groupMessagesByThread(results.messages),
        contactsByType: this.groupContactsByType(results.contacts),
        timeDistribution: this.getTimeDistribution(results.messages)
      },
      relatedQueries: this.getRelatedQueries(query),
      searchSuggestions: this.getQuerySuggestions(query, results)
    };
  }

  analyzeQuery(query) {
    return {
      type: this.detectQueryType(query),
      wordCount: query.trim().split(/\s+/).length,
      hasPhoneNumber: /\d{3,}/.test(query),
      hasQuotes: query.includes('"'),
      language: 'en' // Could be enhanced with language detection
    };
  }

  detectQueryType(query) {
    if (/^\+?\d[\d\s\-\(\)]+$/.test(query)) return 'phone_number';
    if (query.includes('@')) return 'email';
    if (query.includes('"')) return 'phrase';
    if (query.split(/\s+/).length > 3) return 'long_query';
    return 'keyword';
  }

  groupMessagesByThread(messages) {
    const threads = new Map();
    messages.forEach(msg => {
      const threadId = msg.threadId;
      if (!threads.has(threadId)) {
        threads.set(threadId, { threadId, contactName: msg.contactName, count: 0 });
      }
      threads.get(threadId).count++;
    });
    return Array.from(threads.values());
  }

  groupContactsByType(contacts) {
    return {
      withPhoto: contacts.filter(c => c.photoPath).length,
      withEmail: contacts.filter(c => c.email).length,
      phoneOnly: contacts.filter(c => !c.email).length
    };
  }

  getTimeDistribution(messages) {
    const now = new Date();
    const distributions = {
      today: 0,
      yesterday: 0,
      thisWeek: 0,
      thisMonth: 0,
      older: 0
    };

    messages.forEach(msg => {
      const msgDate = new Date(msg.timestamp);
      const daysDiff = Math.floor((now - msgDate) / (1000 * 60 * 60 * 24));
      
      if (daysDiff === 0) distributions.today++;
      else if (daysDiff === 1) distributions.yesterday++;
      else if (daysDiff <= 7) distributions.thisWeek++;
      else if (daysDiff <= 30) distributions.thisMonth++;
      else distributions.older++;
    });

    return distributions;
  }

  getRelatedQueries(query) {
    // Return related queries from recent searches
    return this.recentSearches
      .filter(search => search !== query && search.toLowerCase().includes(query.toLowerCase().split(' ')[0]))
      .slice(0, 5);
  }

  getQuerySuggestions(query, results) {
    const suggestions = [];
    
    // Suggest adding filters if many results
    if (results.summary.totalResults > 20) {
      suggestions.push(`${query} today`);
      suggestions.push(`${query} this week`);
    }
    
    // Suggest contact names from results
    const contactNames = [...new Set(results.messages.map(m => m.contactName))];
    contactNames.slice(0, 3).forEach(name => {
      if (!query.includes(name)) {
        suggestions.push(`${query} from:${name}`);
      }
    });
    
    return suggestions;
  }

  normalizeQuery(query) {
    return query.trim().toLowerCase();
  }

  getCacheKey(query, options) {
    return `${query}|${JSON.stringify(options)}`;
  }

  cacheResults(key, results) {
    // Limit cache size
    if (this.searchCache.size >= 100) {
      const firstKey = this.searchCache.keys().next().value;
      this.searchCache.delete(firstKey);
    }
    
    this.searchCache.set(key, results);
    
    // Auto-expire cache entries after 5 minutes
    setTimeout(() => {
      this.searchCache.delete(key);
    }, 5 * 60 * 1000);
  }

  updateSearchStats(query, responseTime) {
    this.searchStats.totalSearches++;
    this.searchStats.averageResponseTime = 
      (this.searchStats.averageResponseTime * (this.searchStats.totalSearches - 1) + responseTime) / 
      this.searchStats.totalSearches;
    
    // Track popular queries
    const count = this.searchStats.popularQueries.get(query) || 0;
    this.searchStats.popularQueries.set(query, count + 1);
    
    // Add to recent searches
    this.recentSearches.unshift(query);
    if (this.recentSearches.length > 50) {
      this.recentSearches.pop();
    }
  }

  getSearchSuggestions() {
    // Return popular and recent searches as suggestions
    const popular = Array.from(this.searchStats.popularQueries.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([query]) => query);
    
    const recent = this.recentSearches.slice(0, 5);
    
    return [...new Set([...popular, ...recent])];
  }

  escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  getSearchStats() {
    return {
      ...this.searchStats,
      cacheSize: this.searchCache.size,
      recentSearchCount: this.recentSearches.length,
      topQueries: Array.from(this.searchStats.popularQueries.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
    };
  }

  clearCache() {
    this.searchCache.clear();
    this.logger.info('🗑️ Search cache cleared');
  }

  clearStats() {
    this.searchStats = {
      totalSearches: 0,
      averageResponseTime: 0,
      popularQueries: new Map()
    };
    this.recentSearches = [];
    this.logger.info('🗑️ Search stats cleared');
  }
}

module.exports = { SearchService };