const fs = require('fs');
const path = require('path');

console.log('🔌 Connecting Phone Link to iPhone Companion Pro...\n');

// Create PhoneLinkBridge service
const phoneLinkBridgeCode = `
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { EventEmitter } = require('events');

class PhoneLinkBridge extends EventEmitter {
  constructor() {
    super();
    this.basePath = 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Packages\\\\Microsoft.YourPhone_8wekyb3d8bbwe\\\\LocalCache\\\\Indexed\\\\e1e8a106-3e02-48fa-b8d7-a055e20bd025\\\\System\\\\Database';
    this.isConnected = false;
  }

  async connect() {
    console.log('🔌 Connecting to Phone Link databases...');
    this.isConnected = true;
    this.emit('connected');
    return true;
  }

  async getContacts() {
    const dbPath = path.join(this.basePath, 'contacts.db');
    
    return new Promise((resolve, reject) => {
      const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
        if (err) {
          console.error('Error opening contacts.db:', err);
          resolve([]);
          return;
        }
        
        db.all("SELECT * FROM contacts WHERE display_name != 'My Number'", (err, rows) => {
          if (err) {
            console.error('Error reading contacts:', err);
            resolve([]);
          } else {
            const contacts = rows.map(row => ({
              id: row.contact_id,
              name: row.display_name || row.family_name || 'Unknown',
              phoneNumber: row.phone_number || '',
              thumbnail: row.thumbnail
            }));
            resolve(contacts);
          }
          db.close();
        });
      });
    });
  }

  async getCallHistory() {
    const dbPath = path.join(this.basePath, 'calling.db');
    
    return new Promise((resolve, reject) => {
      const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
        if (err) {
          console.error('Error opening calling.db:', err);
          resolve([]);
          return;
        }
        
        db.all("SELECT * FROM call_history ORDER BY start_time DESC LIMIT 50", (err, rows) => {
          if (err) {
            console.error('Error reading call history:', err);
            resolve([]);
          } else {
            const calls = rows.map(row => ({
              id: row.id || Date.now(),
              phoneNumber: row.phone_number || '',
              contactName: row.display_name || row.phone_number || 'Unknown',
              timestamp: row.start_time || Date.now(),
              duration: row.duration || 0,
              type: row.call_type || 'unknown',
              isIncoming: row.is_incoming || false,
              isMissed: row.is_missed || false
            }));
            resolve(calls);
          }
          db.close();
        });
      });
    });
  }

  async getMessages() {
    // For now, return empty until we find message database
    console.log('📱 Messages database not found in standard location');
    return [];
  }
}

module.exports = { PhoneLinkBridge };
`;

// Save the PhoneLinkBridge
fs.writeFileSync(
  path.join(__dirname, 'src/main/services/PhoneLinkBridge.js'),
  phoneLinkBridgeCode
);
console.log('✅ PhoneLinkBridge service created');

// Update MessageService to use Phone Link data
const messageServicePath = path.join(__dirname, 'src/main/services/MessageService.js');
let messageService = fs.readFileSync(messageServicePath, 'utf8');

// Add Phone Link integration
if (!messageService.includes('phoneLinkBridge')) {
  const integrationCode = `
    // Initialize Phone Link integration
    async initializePhoneLinkIntegration() {
      try {
        const { PhoneLinkBridge } = require('./PhoneLinkBridge');
        this.phoneLinkBridge = new PhoneLinkBridge();
        await this.phoneLinkBridge.connect();
        
        // Load contacts
        const contacts = await this.phoneLinkBridge.getContacts();
        console.log(\`✅ Loaded \${contacts.length} contacts from Phone Link\`);
        
        // Load call history
        const calls = await this.phoneLinkBridge.getCallHistory();
        console.log(\`✅ Loaded \${calls.length} calls from Phone Link\`);
        
        this.integrationMethods.phoneLink = true;
        this.emit('phonelink-connected', { contacts, calls });
      } catch (error) {
        console.error('Phone Link integration failed:', error);
      }
    }
`;
  
  // Insert after constructor
  messageService = messageService.replace(
    'async initialize() {',
    integrationCode + '\n\n  async initialize() {'
  );
}

fs.writeFileSync(messageServicePath, messageService);
console.log('✅ MessageService updated with Phone Link integration');

console.log('\n🎉 Phone Link connection ready!');
console.log('\n📱 Next steps:');
console.log('1. Run: npm start');
console.log('2. Click "Connect iPhone"');
console.log('3. Your contacts and call history will load!');
console.log('\n⚠️  Note: Still searching for messages location...');
