const { EventEmitter } = require('events');
const http = require('http');
const crypto = require('crypto');
const { Buffer } = require('buffer');
const WebSocket = require('ws');
const sharp = require('sharp');
const dgram = require('dgram');
const fs = require('fs');
const path = require('path');
const ffmpeg = require('@ffmpeg-installer/ffmpeg');
const { spawn } = require('child_process');

class AirPlayServer extends EventEmitter {
  constructor() {
    super();
    this.httpServer = null;
    this.wsServer = null;
    this.udpServer = null;
    this.sessions = new Map();
    this.currentSession = null;
    this.videoDecoder = null;
    this.frameBuffer = [];
    this.touchInputEnabled = false;
    this.mirrorWindow = null;
    this.serverInfo = {
      deviceID: this.generateDeviceID(),
      features: 0x5A7FFFF7,
      model: 'AppleTV6,2',
      protovers: '1.1',
      srcvers: '366.0',
      pi: crypto.randomBytes(8).toString('hex'),
      pk: crypto.randomBytes(32).toString('hex'),
      vv: 2
    };
  }

  generateDeviceID() {
    // Generate a MAC address-like device ID
    const bytes = crypto.randomBytes(6);
    return Array.from(bytes)
      .map(b => b.toString(16).padStart(2, '0'))
      .join(':')
      .toUpperCase();
  }

  async start(port = 7000) {
    console.log('Starting AirPlay server...');
    
    // Create HTTP server for AirPlay protocol
    this.httpServer = http.createServer((req, res) => {
      this.handleHTTPRequest(req, res);
    });

    // Create WebSocket server for screen data
    this.wsServer = new WebSocket.Server({ 
      server: this.httpServer,
      path: '/stream'
    });

    this.wsServer.on('connection', (ws, req) => {
      console.log('WebSocket connection established');
      this.handleWebSocketConnection(ws, req);
    });

    // Create UDP server for timing sync
    this.udpServer = dgram.createSocket('udp4');
    this.udpServer.on('message', (msg, rinfo) => {
      this.handleUDPMessage(msg, rinfo);
    });

    // Start all servers
    return new Promise((resolve, reject) => {
      this.httpServer.listen(port, () => {
        console.log(`AirPlay HTTP server listening on port ${port}`);
        
        this.udpServer.bind(port + 1, () => {
          console.log(`AirPlay UDP server listening on port ${port + 1}`);
          
          // Start advertising via Bonjour/mDNS
          this.advertiseService(port);
          
          resolve(port);
        });
      });

      this.httpServer.on('error', reject);
    });
  }

  advertiseService(port) {
    try {
      // Try using bonjour-service if mdns fails
      const Bonjour = require('bonjour-service');
      const bonjour = new Bonjour();
      
      this.service = bonjour.publish({
        name: 'iPhone Companion Pro',
        type: 'airplay',
        port: port,
        txt: {
          deviceid: this.serverInfo.deviceID,
          features: `0x${this.serverInfo.features.toString(16)}`,
          flags: '0x44',
          model: this.serverInfo.model,
          pi: this.serverInfo.pi,
          pk: this.serverInfo.pk,
          protovers: this.serverInfo.protovers,
          pw: '0', // No password
          srcvers: this.serverInfo.srcvers,
          vv: this.serverInfo.vv.toString()
        }
      });

      console.log('AirPlay service advertised successfully');
      this.emit('advertised', { name: 'iPhone Companion Pro', port });
      
    } catch (error) {
      console.error('Bonjour advertising error:', error);
      
      // Fallback to manual discovery
      this.manualDiscovery(port);
    }
  }

  manualDiscovery(port) {
    // Broadcast our service for discovery
    const socket = dgram.createSocket('udp4');
    
    const message = Buffer.from(JSON.stringify({
      name: 'iPhone Companion Pro',
      type: '_airplay._tcp',
      port: port,
      txt: {
        deviceid: this.serverInfo.deviceID,
        features: this.serverInfo.features.toString(16)
      }
    }));

    socket.bind(() => {
      socket.setBroadcast(true);
      
      // Broadcast every 5 seconds
      setInterval(() => {
        socket.send(message, 5353, '***********');
      }, 5000);
    });
  }

  handleHTTPRequest(req, res) {
    console.log(`AirPlay request: ${req.method} ${req.url}`);
    
    // Add CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Apple-Session-ID');

    switch (req.url) {
      case '/info':
        this.handleInfo(req, res);
        break;
        
      case '/play':
        this.handlePlay(req, res);
        break;
        
      case '/scrub':
        this.handleScrub(req, res);
        break;
        
      case '/rate':
        this.handleRate(req, res);
        break;
        
      case '/stop':
        this.handleStop(req, res);
        break;
        
      case '/reverse':
        this.handleReverse(req, res);
        break;
        
      case '/server-info':
        this.handleServerInfo(req, res);
        break;
        
      case '/slideshow-features':
        this.handleSlideshowFeatures(req, res);
        break;
        
      case '/authorize':
        this.handleAuthorize(req, res);
        break;
        
      case '/fp-setup':
        this.handleFairPlay(req, res);
        break;
        
      case '/feedback':
        this.handleFeedback(req, res);
        break;
        
      default:
        if (req.url.startsWith('/stream')) {
          // WebSocket upgrade will handle this
          res.end();
        } else {
          res.writeHead(404);
          res.end();
        }
    }
  }

  handleInfo(req, res) {
    const info = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>deviceid</key>
  <string>${this.serverInfo.deviceID}</string>
  <key>features</key>
  <integer>${this.serverInfo.features}</integer>
  <key>model</key>
  <string>${this.serverInfo.model}</string>
  <key>pi</key>
  <string>${this.serverInfo.pi}</string>
  <key>pk</key>
  <string>${this.serverInfo.pk}</string>
  <key>protovers</key>
  <string>${this.serverInfo.protovers}</string>
  <key>pw</key>
  <false/>
  <key>srcvers</key>
  <string>${this.serverInfo.srcvers}</string>
  <key>vv</key>
  <integer>${this.serverInfo.vv}</integer>
  <key>audioFormats</key>
  <array>
    <dict>
      <key>type</key>
      <integer>96</integer>
      <key>audioInputFormats</key>
      <integer>67108860</integer>
      <key>audioOutputFormats</key>
      <integer>67108860</integer>
    </dict>
  </array>
  <key>audioLatencies</key>
  <array>
    <dict>
      <key>type</key>
      <integer>96</integer>
      <key>audioType</key>
      <string>default</string>
      <key>inputLatencyMicros</key>
      <integer>0</integer>
      <key>outputLatencyMicros</key>
      <integer>0</integer>
    </dict>
  </array>
  <key>displays</key>
  <array>
    <dict>
      <key>uuid</key>
      <string>e0ff8a27-6738-3d56-8a16-cc53aacee925</string>
      <key>widthPixels</key>
      <integer>1920</integer>
      <key>heightPixels</key>
      <integer>1080</integer>
      <key>widthPhysical</key>
      <integer>1920</integer>
      <key>heightPhysical</key>
      <integer>1080</integer>
    </dict>
  </array>
</dict>
</plist>`;

    res.writeHead(200, {
      'Content-Type': 'text/x-apple-plist+xml',
      'Content-Length': Buffer.byteLength(info)
    });
    res.end(info);
  }

  handlePlay(req, res) {
    let body = '';
    
    req.on('data', chunk => {
      body += chunk;
    });
    
    req.on('end', () => {
      console.log('Play request:', body);
      
      // Parse the play request
      const sessionId = req.headers['x-apple-session-id'] || crypto.randomUUID();
      
      // Create session
      this.currentSession = {
        id: sessionId,
        startTime: Date.now(),
        position: 0,
        rate: 1
      };
      
      this.sessions.set(sessionId, this.currentSession);
      
      // Check if this is screen mirroring
      if (body.includes('Content-Location') && body.includes('/stream')) {
        console.log('Screen mirroring request detected');
        this.emit('mirror-start', sessionId);
      }
      
      res.writeHead(200, {
        'Content-Type': 'text/x-apple-plist+xml'
      });
      res.end();
    });
  }

  handleWebSocketConnection(ws, req) {
    const sessionId = req.headers['x-apple-session-id'] || this.currentSession?.id;
    
    console.log('Screen mirroring WebSocket connected for session:', sessionId);
    
    ws.on('message', async (data) => {
      try {
        // Handle different types of messages
        if (data.length > 128) {
          // This is likely screen data
          await this.handleScreenData(data, sessionId);
        } else {
          // Control message
          const message = data.toString();
          console.log('Control message:', message);
        }
      } catch (error) {
        console.error('WebSocket message error:', error);
      }
    });

    ws.on('close', () => {
      console.log('Screen mirroring WebSocket disconnected');
      this.emit('mirror-stop', sessionId);
    });

    // Send ready signal
    ws.send(Buffer.from([0x00, 0x00, 0x00, 0x01]));
  }

  async handleScreenData(data, sessionId) {
    try {
      // Parse the screen data format
      // AirPlay sends H.264 video frames or JPEG images

      // Check if it's a JPEG image (starts with FFD8)
      if (data[0] === 0xFF && data[1] === 0xD8) {
        // Process JPEG frame
        const processedFrame = await this.processJPEGFrame(data);
        this.emit('screen-frame', {
          sessionId,
          frame: processedFrame,
          timestamp: Date.now()
        });
      } else if (this.isH264Data(data)) {
        // Handle H.264 video stream
        await this.processH264Frame(data, sessionId);
      } else if (this.isRTPPacket(data)) {
        // Handle RTP video packets
        await this.processRTPPacket(data, sessionId);
      } else if (this.isMJPEGData(data)) {
        // Handle MJPEG stream
        await this.processMJPEGFrame(data, sessionId);
      } else {
        // Try to process as raw video data
        this.emit('video-data', {
          sessionId,
          data: data,
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('Error processing screen data:', error);
    }
  }

  isH264Data(data) {
    // Check for H.264 NAL unit start codes
    if (data.length < 4) return false;

    // Look for NAL unit start code (0x00000001 or 0x000001)
    return (data[0] === 0x00 && data[1] === 0x00 &&
            ((data[2] === 0x00 && data[3] === 0x01) || data[2] === 0x01));
  }

  isRTPPacket(data) {
    // Check for RTP packet header
    if (data.length < 12) return false;

    // RTP version should be 2 (bits 0-1 of first byte)
    const version = (data[0] >> 6) & 0x03;
    return version === 2;
  }

  isMJPEGData(data) {
    // Check for MJPEG frame markers
    if (data.length < 4) return false;

    // Look for JPEG SOI marker (0xFFD8) and potential MJPEG markers
    return (data[0] === 0xFF && data[1] === 0xD8) ||
           (data.includes(Buffer.from([0xFF, 0xC0])) || data.includes(Buffer.from([0xFF, 0xC2])));
  }

  async processH264Frame(h264Data, sessionId) {
    try {
      // Add frame to buffer for processing
      this.frameBuffer.push({
        data: h264Data,
        timestamp: Date.now(),
        sessionId
      });

      // Process frame with FFmpeg if decoder is available
      if (this.videoDecoder) {
        this.videoDecoder.stdin.write(h264Data);
      } else {
        // Start video decoder if not running
        this.startVideoDecoder(sessionId);
      }

      this.emit('h264-frame', {
        sessionId,
        data: h264Data,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error processing H.264 frame:', error);
    }
  }

  startVideoDecoder(sessionId) {
    try {
      // Use FFmpeg to decode H.264 stream to images
      this.videoDecoder = spawn(ffmpeg.path, [
        '-f', 'h264',           // Input format
        '-i', 'pipe:0',         // Input from stdin
        '-f', 'image2pipe',     // Output format
        '-vcodec', 'mjpeg',     // Output codec
        '-q:v', '2',            // High quality
        '-r', '30',             // Frame rate
        'pipe:1'                // Output to stdout
      ]);

      this.videoDecoder.stdout.on('data', (frameData) => {
        // Emit decoded frame
        this.emit('decoded-frame', {
          sessionId,
          frame: frameData,
          timestamp: Date.now()
        });
      });

      this.videoDecoder.stderr.on('data', (data) => {
        console.log('FFmpeg stderr:', data.toString());
      });

      this.videoDecoder.on('close', (code) => {
        console.log('Video decoder closed with code:', code);
        this.videoDecoder = null;
      });

      this.videoDecoder.on('error', (error) => {
        console.error('Video decoder error:', error);
        this.videoDecoder = null;
      });

    } catch (error) {
      console.error('Failed to start video decoder:', error);
    }
  }

  async processJPEGFrame(jpegData) {
    try {
      // Use sharp to process/optimize the JPEG
      const processed = await sharp(jpegData)
        .resize(1170, 2532, { fit: 'inside' }) // iPhone resolution
        .jpeg({ quality: 90 })
        .toBuffer();

      return processed;
    } catch (error) {
      // Return original if processing fails
      return jpegData;
    }
  }

  async processRTPPacket(rtpData, sessionId) {
    try {
      // Parse RTP header
      const version = (rtpData[0] >> 6) & 0x03;
      const padding = (rtpData[0] >> 5) & 0x01;
      const extension = (rtpData[0] >> 4) & 0x01;
      const csrcCount = rtpData[0] & 0x0F;
      const marker = (rtpData[1] >> 7) & 0x01;
      const payloadType = rtpData[1] & 0x7F;
      const sequenceNumber = (rtpData[2] << 8) | rtpData[3];
      const timestamp = (rtpData[4] << 24) | (rtpData[5] << 16) | (rtpData[6] << 8) | rtpData[7];
      const ssrc = (rtpData[8] << 24) | (rtpData[9] << 16) | (rtpData[10] << 8) | rtpData[11];

      // Calculate payload offset
      let payloadOffset = 12 + (csrcCount * 4);

      if (extension) {
        const extLength = (rtpData[payloadOffset + 2] << 8) | rtpData[payloadOffset + 3];
        payloadOffset += 4 + (extLength * 4);
      }

      // Extract payload
      const payload = rtpData.slice(payloadOffset);

      // Handle different payload types
      if (payloadType === 96) { // H.264
        await this.processH264Frame(payload, sessionId);
      } else if (payloadType === 26) { // MJPEG
        await this.processMJPEGFrame(payload, sessionId);
      }

      this.emit('rtp-packet', {
        sessionId,
        sequenceNumber,
        timestamp,
        payloadType,
        payload: payload,
        marker: marker === 1
      });

    } catch (error) {
      console.error('Error processing RTP packet:', error);
    }
  }

  async processMJPEGFrame(mjpegData, sessionId) {
    try {
      // MJPEG frames are essentially JPEG images
      const processedFrame = await this.processJPEGFrame(mjpegData);

      this.emit('screen-frame', {
        sessionId,
        frame: processedFrame,
        timestamp: Date.now()
      });

    } catch (error) {
      console.error('Error processing MJPEG frame:', error);
    }
  }

  handleScrub(req, res) {
    if (this.currentSession) {
      const position = this.currentSession.position || 0;
      const duration = 0; // Live stream
      
      const response = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>duration</key>
  <real>${duration}</real>
  <key>position</key>
  <real>${position}</real>
</dict>
</plist>`;

      res.writeHead(200, { 'Content-Type': 'text/x-apple-plist+xml' });
      res.end(response);
    } else {
      res.writeHead(404);
      res.end();
    }
  }

  handleRate(req, res) {
    let body = '';
    
    req.on('data', chunk => {
      body += chunk;
    });
    
    req.on('end', () => {
      // Parse rate from query string
      const url = new URL(req.url, `http://localhost`);
      const rate = parseFloat(url.searchParams.get('value') || '1');
      
      if (this.currentSession) {
        this.currentSession.rate = rate;
        
        if (rate === 0) {
          this.emit('pause', this.currentSession.id);
        } else {
          this.emit('resume', this.currentSession.id);
        }
      }
      
      res.writeHead(200);
      res.end();
    });
  }

  handleStop(req, res) {
    if (this.currentSession) {
      this.emit('stop', this.currentSession.id);
      this.sessions.delete(this.currentSession.id);
      this.currentSession = null;
    }
    
    res.writeHead(200);
    res.end();
  }

  handleReverse(req, res) {
    // Reverse HTTP connection for events
    res.writeHead(101, {
      'Upgrade': 'PTTH/1.0',
      'Connection': 'Upgrade'
    });
    
    // Keep connection open for server-to-client events
    this.reverseConnection = res;
  }

  handleServerInfo(req, res) {
    const info = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>deviceid</key>
  <string>${this.serverInfo.deviceID}</string>
  <key>features</key>
  <integer>${this.serverInfo.features}</integer>
  <key>model</key>
  <string>${this.serverInfo.model}</string>
  <key>protovers</key>
  <string>${this.serverInfo.protovers}</string>
  <key>srcvers</key>
  <string>${this.serverInfo.srcvers}</string>
</dict>
</plist>`;

    res.writeHead(200, { 'Content-Type': 'text/x-apple-plist+xml' });
    res.end(info);
  }

  handleSlideshowFeatures(req, res) {
    res.writeHead(200, { 'Content-Type': 'text/x-apple-plist+xml' });
    res.end(`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict/>
</plist>`);
  }

  handleAuthorize(req, res) {
    // No authorization required
    res.writeHead(200);
    res.end();
  }

  handleFairPlay(req, res) {
    // FairPlay DRM - not needed for screen mirroring
    res.writeHead(200);
    res.end();
  }

  handleFeedback(req, res) {
    // Keep connection open for feedback
    res.writeHead(200);
    // Don't end the response, keep it open
  }

  handleUDPMessage(msg, rinfo) {
    // Handle timing sync messages
    if (msg.length >= 32) {
      // Echo back timing packet with our timestamp
      const response = Buffer.alloc(32);
      msg.copy(response, 0, 0, 16); // Copy identifier
      
      const now = Date.now();
      response.writeBigInt64BE(BigInt(now), 16);
      response.writeBigInt64BE(BigInt(now), 24);
      
      this.udpServer.send(response, rinfo.port, rinfo.address);
    }
  }

  sendEvent(event, data) {
    if (this.reverseConnection && !this.reverseConnection.destroyed) {
      const eventData = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>category</key>
  <string>${event}</string>
  <key>state</key>
  <string>${data.state || 'playing'}</string>
</dict>
</plist>`;

      this.reverseConnection.write(`POST /event HTTP/1.1\r\n`);
      this.reverseConnection.write(`Content-Type: text/x-apple-plist+xml\r\n`);
      this.reverseConnection.write(`Content-Length: ${Buffer.byteLength(eventData)}\r\n`);
      this.reverseConnection.write(`\r\n`);
      this.reverseConnection.write(eventData);
    }
  }

  // Touch input handling methods
  enableTouchInput(mirrorWindow) {
    this.touchInputEnabled = true;
    this.mirrorWindow = mirrorWindow;
    console.log('Touch input enabled for mirror window');
  }

  disableTouchInput() {
    this.touchInputEnabled = false;
    this.mirrorWindow = null;
    console.log('Touch input disabled');
  }

  sendTouchEvent(x, y, type = 'tap') {
    if (!this.touchInputEnabled || !this.reverseConnection) {
      return false;
    }

    try {
      // Convert screen coordinates to iPhone coordinates
      const iPhoneCoords = this.convertToiPhoneCoordinates(x, y);

      // Create touch event data
      const touchEvent = {
        type: type,
        x: iPhoneCoords.x,
        y: iPhoneCoords.y,
        timestamp: Date.now()
      };

      // Send touch event through reverse connection
      const eventData = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>category</key>
  <string>touch</string>
  <key>type</key>
  <string>${type}</string>
  <key>x</key>
  <real>${iPhoneCoords.x}</real>
  <key>y</key>
  <real>${iPhoneCoords.y}</real>
</dict>
</plist>`;

      if (this.reverseConnection && !this.reverseConnection.destroyed) {
        this.reverseConnection.write(`POST /touch HTTP/1.1\r\n`);
        this.reverseConnection.write(`Content-Type: text/x-apple-plist+xml\r\n`);
        this.reverseConnection.write(`Content-Length: ${Buffer.byteLength(eventData)}\r\n`);
        this.reverseConnection.write(`\r\n`);
        this.reverseConnection.write(eventData);
      }

      return true;
    } catch (error) {
      console.error('Error sending touch event:', error);
      return false;
    }
  }

  convertToiPhoneCoordinates(x, y) {
    // Convert from mirror window coordinates to iPhone screen coordinates
    // Assuming iPhone 14 Pro resolution: 1179x2556
    const iPhoneWidth = 1179;
    const iPhoneHeight = 2556;

    // Get mirror window dimensions (assuming it's available)
    const windowWidth = this.mirrorWindow ? this.mirrorWindow.getBounds().width : 1170;
    const windowHeight = this.mirrorWindow ? this.mirrorWindow.getBounds().height : 2532;

    // Calculate scale factors
    const scaleX = iPhoneWidth / windowWidth;
    const scaleY = iPhoneHeight / windowHeight;

    return {
      x: Math.round(x * scaleX),
      y: Math.round(y * scaleY)
    };
  }

  stop() {
    // Stop video decoder
    if (this.videoDecoder) {
      this.videoDecoder.kill();
      this.videoDecoder = null;
    }

    // Clear frame buffer
    this.frameBuffer = [];

    // Disable touch input
    this.disableTouchInput();

    if (this.service) {
      this.service.stop();
    }

    if (this.httpServer) {
      this.httpServer.close();
    }

    if (this.wsServer) {
      this.wsServer.close();
    }

    if (this.udpServer) {
      this.udpServer.close();
    }

    console.log('AirPlay server stopped');
  }
}

module.exports = { AirPlayServer };