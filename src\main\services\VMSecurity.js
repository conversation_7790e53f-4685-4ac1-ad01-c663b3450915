const crypto = require('crypto');
const { EventEmitter } = require('events');
const Store = require('electron-store');
const fs = require('fs');
const path = require('path');

class VMSecurity extends EventEmitter {
  constructor(vmBridge) {
    super();
    this.vmBridge = vmBridge;
    this.store = new Store({ encryptionKey: 'vm-bridge-security' });
    
    // Security configuration
    this.securityConfig = {
      encryptionEnabled: true,
      authenticationRequired: true,
      certificateValidation: true,
      sessionTimeout: 3600000, // 1 hour
      maxFailedAttempts: 5,
      lockoutDuration: 900000, // 15 minutes
      keyRotationInterval: 86400000, // 24 hours
      auditLogging: true
    };
    
    // Security state
    this.encryptionKeys = new Map();
    this.activeSessions = new Map();
    this.failedAttempts = new Map();
    this.lockedIPs = new Map();
    this.auditLog = [];
    this.certificates = new Map();
    
    // Initialize security
    this.initializeSecurity();
    this.startSecurityMonitoring();
  }

  // Initialize security system
  async initializeSecurity() {
    try {
      // Load or generate master encryption key
      await this.initializeMasterKey();
      
      // Load or generate certificates
      await this.initializeCertificates();
      
      // Load security configuration
      this.loadSecurityConfig();
      
      // Setup key rotation
      this.setupKeyRotation();
      
      // Load audit log
      this.loadAuditLog();
      
      this.emit('security-initialized');
      this.auditLog.push({
        timestamp: Date.now(),
        event: 'SECURITY_INITIALIZED',
        details: 'VM Bridge security system initialized'
      });
      
    } catch (error) {
      this.emit('security-error', error);
      throw error;
    }
  }

  // Initialize master encryption key
  async initializeMasterKey() {
    let masterKey = this.store.get('masterKey');
    
    if (!masterKey) {
      // Generate new master key
      masterKey = crypto.randomBytes(32).toString('hex');
      this.store.set('masterKey', masterKey);
      
      this.auditLog.push({
        timestamp: Date.now(),
        event: 'MASTER_KEY_GENERATED',
        details: 'New master encryption key generated'
      });
    }
    
    this.masterKey = Buffer.from(masterKey, 'hex');
  }

  // Initialize certificates
  async initializeCertificates() {
    const certPath = path.join(require('os').homedir(), 'iPhone-Companion-Pro', 'certs');
    
    if (!fs.existsSync(certPath)) {
      fs.mkdirSync(certPath, { recursive: true });
    }
    
    // Check for existing certificates
    const certFile = path.join(certPath, 'vm-bridge.crt');
    const keyFile = path.join(certPath, 'vm-bridge.key');
    
    if (!fs.existsSync(certFile) || !fs.existsSync(keyFile)) {
      await this.generateSelfSignedCertificate(certPath);
    }
    
    // Load certificates
    this.certificates.set('vm-bridge', {
      cert: fs.readFileSync(certFile, 'utf8'),
      key: fs.readFileSync(keyFile, 'utf8'),
      created: Date.now()
    });
  }

  // Generate self-signed certificate
  async generateSelfSignedCertificate(certPath) {
    return new Promise((resolve, reject) => {
      const { exec } = require('child_process');
      
      const opensslCmd = `openssl req -x509 -newkey rsa:4096 -keyout "${path.join(certPath, 'vm-bridge.key')}" -out "${path.join(certPath, 'vm-bridge.crt')}" -days 365 -nodes -subj "/C=US/ST=State/L=City/O=iPhone-Companion-Pro/CN=vm-bridge"`;
      
      exec(opensslCmd, (error, stdout, stderr) => {
        if (error) {
          // Fallback to Node.js crypto if OpenSSL not available
          this.generateCertificateWithNodeCrypto(certPath);
          resolve();
        } else {
          this.auditLog.push({
            timestamp: Date.now(),
            event: 'CERTIFICATE_GENERATED',
            details: 'Self-signed certificate generated'
          });
          resolve();
        }
      });
    });
  }

  // Generate certificate using Node.js crypto (fallback)
  generateCertificateWithNodeCrypto(certPath) {
    // Simplified certificate generation
    const keyPair = crypto.generateKeyPairSync('rsa', {
      modulusLength: 2048,
      publicKeyEncoding: { type: 'spki', format: 'pem' },
      privateKeyEncoding: { type: 'pkcs8', format: 'pem' }
    });
    
    fs.writeFileSync(path.join(certPath, 'vm-bridge.key'), keyPair.privateKey);
    fs.writeFileSync(path.join(certPath, 'vm-bridge.crt'), keyPair.publicKey);
  }

  // Load security configuration
  loadSecurityConfig() {
    const savedConfig = this.store.get('securityConfig');
    if (savedConfig) {
      this.securityConfig = { ...this.securityConfig, ...savedConfig };
    }
  }

  // Save security configuration
  saveSecurityConfig() {
    this.store.set('securityConfig', this.securityConfig);
  }

  // Setup key rotation
  setupKeyRotation() {
    if (this.keyRotationInterval) {
      clearInterval(this.keyRotationInterval);
    }
    
    this.keyRotationInterval = setInterval(() => {
      this.rotateEncryptionKeys();
    }, this.securityConfig.keyRotationInterval);
  }

  // Start security monitoring
  startSecurityMonitoring() {
    // Monitor for security events
    setInterval(() => {
      this.cleanupExpiredSessions();
      this.cleanupLockedIPs();
      this.monitorSecurityThreats();
    }, 60000); // Every minute
  }

  // Generate session token
  generateSessionToken(clientInfo) {
    const sessionId = crypto.randomUUID();
    const token = crypto.randomBytes(32).toString('hex');
    
    const session = {
      id: sessionId,
      token,
      clientInfo,
      created: Date.now(),
      lastActivity: Date.now(),
      encrypted: true
    };
    
    this.activeSessions.set(sessionId, session);
    
    this.auditLog.push({
      timestamp: Date.now(),
      event: 'SESSION_CREATED',
      sessionId,
      clientInfo,
      details: 'New session token generated'
    });
    
    return { sessionId, token };
  }

  // Validate session token
  validateSessionToken(sessionId, token) {
    const session = this.activeSessions.get(sessionId);
    
    if (!session) {
      this.auditLog.push({
        timestamp: Date.now(),
        event: 'INVALID_SESSION',
        sessionId,
        details: 'Session not found'
      });
      return false;
    }
    
    if (session.token !== token) {
      this.auditLog.push({
        timestamp: Date.now(),
        event: 'INVALID_TOKEN',
        sessionId,
        details: 'Invalid session token'
      });
      return false;
    }
    
    // Check session timeout
    if (Date.now() - session.lastActivity > this.securityConfig.sessionTimeout) {
      this.activeSessions.delete(sessionId);
      this.auditLog.push({
        timestamp: Date.now(),
        event: 'SESSION_EXPIRED',
        sessionId,
        details: 'Session expired'
      });
      return false;
    }
    
    // Update last activity
    session.lastActivity = Date.now();
    return true;
  }

  // Encrypt data
  encryptData(data, sessionId = null) {
    if (!this.securityConfig.encryptionEnabled) {
      return data;
    }
    
    try {
      const key = sessionId ? this.getSessionKey(sessionId) : this.masterKey;
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher('aes-256-gcm', key);
      
      let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      return {
        encrypted: true,
        data: encrypted,
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex'),
        algorithm: 'aes-256-gcm'
      };
      
    } catch (error) {
      this.emit('encryption-error', error);
      throw new Error('Encryption failed');
    }
  }

  // Decrypt data
  decryptData(encryptedData, sessionId = null) {
    if (!encryptedData.encrypted) {
      return encryptedData;
    }
    
    try {
      const key = sessionId ? this.getSessionKey(sessionId) : this.masterKey;
      const decipher = crypto.createDecipher('aes-256-gcm', key);
      
      decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
      
      let decrypted = decipher.update(encryptedData.data, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return JSON.parse(decrypted);
      
    } catch (error) {
      this.emit('decryption-error', error);
      throw new Error('Decryption failed');
    }
  }

  // Get session-specific encryption key
  getSessionKey(sessionId) {
    let sessionKey = this.encryptionKeys.get(sessionId);
    
    if (!sessionKey) {
      sessionKey = crypto.pbkdf2Sync(this.masterKey, sessionId, 10000, 32, 'sha256');
      this.encryptionKeys.set(sessionId, sessionKey);
    }
    
    return sessionKey;
  }

  // Authenticate client
  authenticateClient(credentials, clientIP) {
    if (!this.securityConfig.authenticationRequired) {
      return this.generateSessionToken(credentials);
    }
    
    // Check if IP is locked
    if (this.isIPLocked(clientIP)) {
      this.auditLog.push({
        timestamp: Date.now(),
        event: 'AUTHENTICATION_BLOCKED',
        clientIP,
        details: 'IP address is locked due to failed attempts'
      });
      throw new Error('IP address is temporarily locked');
    }
    
    try {
      // Validate credentials (simplified)
      const isValid = this.validateCredentials(credentials);
      
      if (isValid) {
        // Reset failed attempts
        this.failedAttempts.delete(clientIP);
        
        const session = this.generateSessionToken(credentials);
        
        this.auditLog.push({
          timestamp: Date.now(),
          event: 'AUTHENTICATION_SUCCESS',
          clientIP,
          sessionId: session.sessionId,
          details: 'Client authenticated successfully'
        });
        
        return session;
      } else {
        this.recordFailedAttempt(clientIP);
        
        this.auditLog.push({
          timestamp: Date.now(),
          event: 'AUTHENTICATION_FAILED',
          clientIP,
          details: 'Invalid credentials'
        });
        
        throw new Error('Authentication failed');
      }
      
    } catch (error) {
      this.recordFailedAttempt(clientIP);
      throw error;
    }
  }

  // Validate credentials
  validateCredentials(credentials) {
    // Simplified credential validation
    // In a real implementation, this would check against stored credentials
    const validCredentials = this.store.get('validCredentials', {
      username: 'admin',
      password: crypto.createHash('sha256').update('admin123').digest('hex')
    });
    
    if (!credentials.username || !credentials.password) {
      return false;
    }
    
    const hashedPassword = crypto.createHash('sha256').update(credentials.password).digest('hex');
    
    return credentials.username === validCredentials.username &&
           hashedPassword === validCredentials.password;
  }

  // Record failed authentication attempt
  recordFailedAttempt(clientIP) {
    const attempts = this.failedAttempts.get(clientIP) || 0;
    const newAttempts = attempts + 1;
    
    this.failedAttempts.set(clientIP, newAttempts);
    
    if (newAttempts >= this.securityConfig.maxFailedAttempts) {
      this.lockIP(clientIP);
    }
  }

  // Lock IP address
  lockIP(clientIP) {
    this.lockedIPs.set(clientIP, Date.now() + this.securityConfig.lockoutDuration);
    
    this.auditLog.push({
      timestamp: Date.now(),
      event: 'IP_LOCKED',
      clientIP,
      details: `IP locked for ${this.securityConfig.lockoutDuration / 1000} seconds`
    });
    
    this.emit('ip-locked', { ip: clientIP, duration: this.securityConfig.lockoutDuration });
  }

  // Check if IP is locked
  isIPLocked(clientIP) {
    const lockExpiry = this.lockedIPs.get(clientIP);
    
    if (!lockExpiry) {
      return false;
    }
    
    if (Date.now() > lockExpiry) {
      this.lockedIPs.delete(clientIP);
      return false;
    }
    
    return true;
  }

  // Rotate encryption keys
  rotateEncryptionKeys() {
    // Generate new master key
    const newMasterKey = crypto.randomBytes(32);
    const oldMasterKey = this.masterKey;
    
    this.masterKey = newMasterKey;
    this.store.set('masterKey', newMasterKey.toString('hex'));
    
    // Clear session keys to force regeneration
    this.encryptionKeys.clear();
    
    this.auditLog.push({
      timestamp: Date.now(),
      event: 'KEY_ROTATION',
      details: 'Encryption keys rotated'
    });
    
    this.emit('keys-rotated');
  }

  // Clean up expired sessions
  cleanupExpiredSessions() {
    const now = Date.now();
    const expiredSessions = [];
    
    for (const [sessionId, session] of this.activeSessions) {
      if (now - session.lastActivity > this.securityConfig.sessionTimeout) {
        expiredSessions.push(sessionId);
      }
    }
    
    expiredSessions.forEach(sessionId => {
      this.activeSessions.delete(sessionId);
      this.encryptionKeys.delete(sessionId);
      
      this.auditLog.push({
        timestamp: Date.now(),
        event: 'SESSION_CLEANUP',
        sessionId,
        details: 'Expired session cleaned up'
      });
    });
  }

  // Clean up locked IPs
  cleanupLockedIPs() {
    const now = Date.now();
    const unlockedIPs = [];
    
    for (const [ip, lockExpiry] of this.lockedIPs) {
      if (now > lockExpiry) {
        unlockedIPs.push(ip);
      }
    }
    
    unlockedIPs.forEach(ip => {
      this.lockedIPs.delete(ip);
      
      this.auditLog.push({
        timestamp: Date.now(),
        event: 'IP_UNLOCKED',
        clientIP: ip,
        details: 'IP address unlocked'
      });
    });
  }

  // Monitor security threats
  monitorSecurityThreats() {
    // Check for suspicious activity patterns
    const recentEvents = this.auditLog.filter(
      event => Date.now() - event.timestamp < 300000 // Last 5 minutes
    );
    
    // Check for brute force attacks
    const failedAttempts = recentEvents.filter(
      event => event.event === 'AUTHENTICATION_FAILED'
    );
    
    if (failedAttempts.length > 10) {
      this.emit('security-threat', {
        type: 'BRUTE_FORCE_ATTACK',
        details: `${failedAttempts.length} failed authentication attempts in 5 minutes`
      });
    }
    
    // Check for unusual session activity
    const sessionEvents = recentEvents.filter(
      event => event.event.includes('SESSION')
    );
    
    if (sessionEvents.length > 50) {
      this.emit('security-threat', {
        type: 'UNUSUAL_SESSION_ACTIVITY',
        details: `${sessionEvents.length} session events in 5 minutes`
      });
    }
  }

  // Load audit log
  loadAuditLog() {
    const savedLog = this.store.get('auditLog', []);
    
    // Keep only recent entries (last 7 days)
    const weekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    this.auditLog = savedLog.filter(entry => entry.timestamp > weekAgo);
  }

  // Save audit log
  saveAuditLog() {
    // Keep only last 1000 entries
    const recentLog = this.auditLog.slice(-1000);
    this.store.set('auditLog', recentLog);
  }

  // Get security status
  getSecurityStatus() {
    return {
      encryptionEnabled: this.securityConfig.encryptionEnabled,
      authenticationRequired: this.securityConfig.authenticationRequired,
      activeSessions: this.activeSessions.size,
      lockedIPs: this.lockedIPs.size,
      failedAttempts: this.failedAttempts.size,
      lastKeyRotation: this.store.get('lastKeyRotation'),
      certificateExpiry: this.getCertificateExpiry(),
      auditLogSize: this.auditLog.length
    };
  }

  // Get certificate expiry
  getCertificateExpiry() {
    const cert = this.certificates.get('vm-bridge');
    if (cert) {
      // Simplified - in real implementation, parse certificate
      return cert.created + (365 * 24 * 60 * 60 * 1000); // 1 year
    }
    return null;
  }

  // Update security configuration
  updateSecurityConfig(newConfig) {
    this.securityConfig = { ...this.securityConfig, ...newConfig };
    this.saveSecurityConfig();
    
    // Restart key rotation if interval changed
    if (newConfig.keyRotationInterval) {
      this.setupKeyRotation();
    }
    
    this.auditLog.push({
      timestamp: Date.now(),
      event: 'SECURITY_CONFIG_UPDATED',
      details: 'Security configuration updated'
    });
    
    this.emit('security-config-updated', this.securityConfig);
  }

  // Export audit log
  exportAuditLog() {
    return {
      exportTime: Date.now(),
      entries: this.auditLog,
      summary: {
        totalEntries: this.auditLog.length,
        timeRange: {
          start: Math.min(...this.auditLog.map(e => e.timestamp)),
          end: Math.max(...this.auditLog.map(e => e.timestamp))
        },
        eventTypes: this.getEventTypeSummary()
      }
    };
  }

  // Get event type summary
  getEventTypeSummary() {
    const summary = {};
    this.auditLog.forEach(entry => {
      summary[entry.event] = (summary[entry.event] || 0) + 1;
    });
    return summary;
  }

  // Cleanup and stop
  stop() {
    if (this.keyRotationInterval) {
      clearInterval(this.keyRotationInterval);
    }
    
    this.saveAuditLog();
    this.emit('security-stopped');
  }
}

module.exports = VMSecurity;
