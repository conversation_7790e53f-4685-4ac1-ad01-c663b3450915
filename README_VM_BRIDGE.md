# iPhone Companion Pro - macOS VM Bridge

## Overview

The macOS VM Bridge is a revolutionary feature that enables iPhone Companion Pro to provide the most comprehensive iPhone integration possible on Windows. By running a macOS virtual machine, the application gains direct access to iPhone data through native macOS APIs, including real-time message synchronization, contacts access, and full iOS ecosystem integration.

## Architecture

### Core Components

1. **MacOSVMBridge** - Main orchestrator for VM operations
2. **VMManager** - Handles VM creation, configuration, and lifecycle
3. **WebSocketTunnel** - Secure communication channel between Windows and macOS VM
4. **MessagesDBAccess** - Direct access to macOS Messages.app database
5. **VMHealthMonitor** - Continuous monitoring of VM and bridge health
6. **VMErrorRecovery** - Automated error detection and recovery system
7. **VMPerformanceOptimizer** - Dynamic performance optimization
8. **VMSecurity** - Comprehensive security and encryption layer

### System Flow

```
Windows Host (iPhone Companion Pro)
    ↓
MacOSVMBridge (Orchestrator)
    ↓
QEMU Virtual Machine (macOS)
    ↓
Bridge Service (Node.js)
    ↓
Messages.app Database + AppleScript
    ↓
iPhone Data (Messages, Contacts, etc.)
```

## Features

### 🔄 Real-time Message Synchronization
- Direct access to Messages.app SQLite database
- Real-time message monitoring and sync
- Support for iMessage, SMS, and group conversations
- Message attachments and media handling
- Read receipts and delivery status

### 📱 Native iPhone Integration
- Access to Contacts database
- AppleScript automation for sending messages
- iPhone USB passthrough support
- iTunes sync integration
- Bonjour/mDNS service discovery

### 🛡️ Advanced Security
- End-to-end encryption for all communications
- Session-based authentication
- Certificate-based security
- Audit logging and threat detection
- IP-based access control and rate limiting

### ⚡ Performance Optimization
- Dynamic resource allocation
- Multiple performance profiles (Power Saver, Balanced, Performance, Maximum)
- Adaptive optimization based on system usage
- Resource monitoring and automatic tuning
- CPU and memory optimization

### 🔧 Health Monitoring & Recovery
- Continuous health monitoring
- Automated error detection and recovery
- Multiple recovery strategies for different failure types
- Performance metrics and alerting
- Comprehensive logging and diagnostics

## Installation & Setup

### Prerequisites
- Windows 10/11 (64-bit)
- 12GB+ RAM (16GB+ recommended)
- Intel VT-x or AMD-V virtualization support
- 100GB+ free disk space
- QEMU for Windows

### Quick Start
1. Enable virtualization in BIOS/UEFI
2. Install QEMU for Windows
3. Open iPhone Companion Pro
4. Go to Settings → macOS VM Bridge
5. Click "Enable VM Bridge"
6. Follow the setup wizard

For detailed installation instructions, see [VM_BRIDGE_SETUP.md](docs/VM_BRIDGE_SETUP.md)

## Configuration

### VM Settings
- **Memory**: 4GB - 16GB (8GB recommended)
- **CPU Cores**: 2-8 cores (4 cores recommended)
- **Disk Size**: 60GB - 160GB (80GB recommended)
- **Network Ports**: Bridge (8080), SSH (2222), VNC (5900)

### Performance Profiles
- **Power Saver**: 4GB RAM, 2 cores - Minimal resource usage
- **Balanced**: 6GB RAM, 4 cores - Optimal balance (default)
- **Performance**: 8GB RAM, 6 cores - High performance
- **Maximum**: 12GB RAM, 8 cores - Maximum performance

### Security Options
- WebSocket encryption (enabled by default)
- Session authentication
- Certificate validation
- Audit logging
- IP access control

## API Reference

### IPC Handlers

#### VM Bridge Control
```javascript
// Enable/disable VM Bridge
ipcRenderer.invoke('enable-vm-bridge')
ipcRenderer.invoke('disable-vm-bridge')

// Get VM Bridge status
ipcRenderer.invoke('get-vm-bridge-status')

// Restart VM Bridge
ipcRenderer.invoke('restart-vm-bridge')
```

#### Performance Management
```javascript
// Get available performance profiles
ipcRenderer.invoke('get-performance-profiles')

// Apply performance profile
ipcRenderer.invoke('apply-performance-profile', 'balanced')

// Get performance statistics
ipcRenderer.invoke('get-performance-stats')
```

#### Security Management
```javascript
// Get security status
ipcRenderer.invoke('get-security-status')

// Update security configuration
ipcRenderer.invoke('update-security-config', config)

// Export audit log
ipcRenderer.invoke('export-audit-log')
```

#### Health Monitoring
```javascript
// Get health metrics
ipcRenderer.invoke('get-health-metrics')

// Generate health report
ipcRenderer.invoke('generate-health-report')
```

### Events

#### VM Bridge Events
```javascript
// Status updates
ipcRenderer.on('vm-bridge-status', (event, status) => {})
ipcRenderer.on('vm-bridge-ready', () => {})
ipcRenderer.on('vm-bridge-connected', () => {})
ipcRenderer.on('vm-bridge-error', (event, error) => {})

// Health events
ipcRenderer.on('vm-health-update', (event, data) => {})
ipcRenderer.on('vm-health-critical', (event, data) => {})

// Performance events
ipcRenderer.on('vm-performance-profile-applied', (event, data) => {})
ipcRenderer.on('vm-performance-recommendation', (event, data) => {})

// Security events
ipcRenderer.on('vm-security-threat', (event, data) => {})
ipcRenderer.on('vm-security-ip-locked', (event, data) => {})

// Recovery events
ipcRenderer.on('vm-recovery-started', (event, data) => {})
ipcRenderer.on('vm-recovery-success', (event, data) => {})
```

## Testing

### Running Tests
```bash
# Run VM Bridge test suite
npm test -- vm-bridge

# Run specific test category
npm test -- vm-bridge --grep "Security"

# Generate test report
npm run test:vm-bridge:report
```

### Test Coverage
- Unit tests for all core components
- Integration tests for component interaction
- Security tests for encryption and authentication
- Performance tests for optimization algorithms
- End-to-end tests for complete workflows

## Troubleshooting

### Common Issues

#### VM Won't Start
- Check virtualization is enabled in BIOS
- Verify QEMU installation
- Ensure sufficient system resources
- Check Windows Hyper-V is disabled

#### Bridge Connection Failed
- Verify VM is running
- Check firewall settings
- Restart bridge service
- Review network configuration

#### Poor Performance
- Increase VM memory allocation
- Add more CPU cores
- Use SSD storage
- Apply performance profile

#### Messages Not Syncing
- Check macOS permissions
- Verify Messages.app is signed in
- Restart bridge service
- Check database access permissions

### Error Codes
- **VM_001**: Virtualization not supported
- **VM_002**: Insufficient memory
- **VM_003**: QEMU not found
- **VM_004**: VM creation failed
- **VM_005**: Bridge connection timeout

### Log Files
- VM Logs: `%USERPROFILE%/iPhone-Companion-Pro/macos-vm/logs/`
- Bridge Logs: Available in VM Bridge UI
- System Logs: Windows Event Viewer

## Development

### Building from Source
```bash
# Install dependencies
npm install

# Build VM Bridge components
npm run build:vm-bridge

# Run development server
npm run dev
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Submit a pull request

### Architecture Guidelines
- Follow event-driven architecture patterns
- Implement comprehensive error handling
- Add security considerations for all components
- Include performance monitoring
- Write comprehensive tests

## Legal & Compliance

### macOS Licensing
- Only use legally obtained macOS installers
- Comply with Apple's Software License Agreement
- VM Bridge is for personal use only
- Respect intellectual property rights

### Privacy & Security
- All data remains on local system
- No external data transmission
- User responsible for data security
- Regular security updates recommended

## Support

### Documentation
- [Setup Guide](docs/VM_BRIDGE_SETUP.md)
- [API Reference](docs/API_REFERENCE.md)
- [Troubleshooting Guide](docs/TROUBLESHOOTING.md)

### Community
- GitHub Issues for bug reports
- Discussions for feature requests
- Discord for community support

### Professional Support
- Enterprise support available
- Custom configuration assistance
- Training and consultation services

## Roadmap

### Upcoming Features
- Enhanced iPhone app integration
- FaceTime call handling
- Photo library access
- Calendar and reminder sync
- Advanced automation features

### Performance Improvements
- GPU acceleration support
- Optimized disk I/O
- Enhanced network performance
- Reduced memory footprint

---

**Note**: This is an advanced feature requiring technical knowledge and adequate system resources. Please review the setup guide and system requirements before proceeding.
