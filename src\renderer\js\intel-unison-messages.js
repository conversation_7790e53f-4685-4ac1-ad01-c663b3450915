/**
 * Intel Unison++ Messages - Modern UI JavaScript
 * Handles all messaging interface interactions with real-time sync
 */

class IntelUnisonMessages {
  constructor() {
    this.conversations = new Map();
    this.currentConversation = null;
    this.messageCache = new Map();
    this.searchResults = [];
    this.isTyping = false;
    this.typingTimeout = null;
    this.syncStatus = 'disconnected';
    this.wsConnection = null;
    this.selectedMessage = null;
    
    // Initialize the app
    this.init();
  }

  async init() {
    console.log('🔥 Initializing Intel Unison++ Messages UI');
    
    // Set up event listeners
    this.setupEventListeners();
    
    // Initialize WebSocket connection
    this.initializeWebSocket();
    
    // Load initial data
    await this.loadConversations();
    
    // Start periodic sync
    this.startPeriodicSync();
    
    console.log('✅ Intel Unison++ Messages UI ready');
  }

  setupEventListeners() {
    // Search functionality
    const searchBtn = document.getElementById('searchBtn');
    const searchContainer = document.getElementById('searchContainer');
    const searchInput = document.getElementById('searchInput');
    const searchClear = document.getElementById('searchClear');
    
    searchBtn.addEventListener('click', () => {
      const isVisible = searchContainer.style.display !== 'none';
      searchContainer.style.display = isVisible ? 'none' : 'block';
      if (!isVisible) {
        searchInput.focus();
      }
    });
    
    searchInput.addEventListener('input', (e) => {
      this.handleSearch(e.target.value);
      searchClear.style.display = e.target.value ? 'block' : 'none';
    });
    
    searchClear.addEventListener('click', () => {
      searchInput.value = '';
      searchClear.style.display = 'none';
      this.clearSearch();
    });

    // Message input
    const messageInput = document.getElementById('messageInput');
    const sendBtn = document.getElementById('sendBtn');
    
    messageInput.addEventListener('input', () => {
      this.handleTyping();
      this.updateSendButton();
      this.autoResizeTextarea();
    });
    
    messageInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.sendMessage();
      }
    });
    
    sendBtn.addEventListener('click', () => {
      this.sendMessage();
    });

    // Quick replies
    document.querySelectorAll('.quick-reply').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const reply = e.target.dataset.reply;
        this.sendQuickReply(reply);
      });
    });

    // Contact actions
    document.getElementById('callBtn').addEventListener('click', () => {
      this.initiateCall();
    });
    
    document.getElementById('videoChatBtn').addEventListener('click', () => {
      this.initiateVideoCall();
    });

    // New message button
    document.getElementById('newMessageBtn').addEventListener('click', () => {
      this.showNewMessageDialog();
    });

    // Context menu
    document.addEventListener('click', () => {
      this.hideContextMenu();
    });
    
    document.addEventListener('contextmenu', (e) => {
      if (e.target.closest('.message-bubble')) {
        e.preventDefault();
        this.showContextMenu(e, e.target.closest('.message'));
      }
    });

    // Window resize
    window.addEventListener('resize', () => {
      this.updateLayout();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      this.handleKeyboardShortcuts(e);
    });
  }

  initializeWebSocket() {
    // Try connecting to WebSocket server
    const ports = [8765, 8766, 8767, 8768, 8769];
    
    const tryConnect = (portIndex = 0) => {
      if (portIndex >= ports.length) {
        console.warn('⚠️ Could not connect to WebSocket server');
        this.updateSyncStatus('disconnected');
        return;
      }
      
      const port = ports[portIndex];
      const ws = new WebSocket(`ws://localhost:${port}`);
      
      ws.onopen = () => {
        console.log(`✅ Connected to WebSocket server on port ${port}`);
        this.wsConnection = ws;
        this.updateSyncStatus('connected');
        this.startHeartbeat();
      };
      
      ws.onmessage = (event) => {
        this.handleWebSocketMessage(JSON.parse(event.data));
      };
      
      ws.onclose = () => {
        console.log('🔌 WebSocket connection closed');
        this.wsConnection = null;
        this.updateSyncStatus('disconnected');
        
        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          this.initializeWebSocket();
        }, 5000);
      };
      
      ws.onerror = () => {
        // Try next port
        tryConnect(portIndex + 1);
      };
    };
    
    tryConnect();
  }

  handleWebSocketMessage(message) {
    switch (message.type) {
      case 'sync-update':
        this.handleSyncUpdate(message);
        break;
      
      case 'initial-sync':
        this.handleInitialSync(message);
        break;
      
      case 'sync-response':
        this.handleSyncResponse(message);
        break;
      
      case 'pong':
        // Heartbeat response
        break;
      
      default:
        console.log('Unknown WebSocket message:', message.type);
    }
  }

  handleSyncUpdate(message) {
    const { eventType, data } = message;
    
    switch (eventType) {
      case 'message-received':
      case 'message-extracted':
        this.addMessage(data, false);
        this.showNotification('New message received', data.messageText);
        break;
      
      case 'message-sent':
        this.addMessage(data, true);
        break;
    }
  }

  handleInitialSync(message) {
    console.log(`📥 Received initial sync: ${message.messages.length} messages`);
    
    // Clear existing conversations
    this.conversations.clear();
    
    // Process messages
    message.messages.forEach(msg => {
      this.addMessage(msg, false, false); // Don't trigger UI updates yet
    });
    
    // Update UI
    this.renderConversations();
    this.updateSyncStatus('synced');
  }

  startHeartbeat() {
    setInterval(() => {
      if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
        this.wsConnection.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
      }
    }, 30000); // Ping every 30 seconds
  }

  async loadConversations() {
    try {
      this.showLoading(true);
      
      // Load from API
      const response = await fetch('/api/conversations');
      const data = await response.json();
      
      if (data.success) {
        data.conversations.forEach(conv => {
          this.conversations.set(conv.phoneNumber, conv);
        });
        
        this.renderConversations();
        console.log(`📥 Loaded ${data.conversations.length} conversations`);
      }
    } catch (error) {
      console.error('❌ Failed to load conversations:', error);
      this.showNotification('Error loading conversations', 'error');
    } finally {
      this.showLoading(false);
    }
  }

  renderConversations() {
    const conversationsList = document.getElementById('conversationsList');
    const conversationsCount = document.getElementById('conversationsCount');
    
    // Clear existing conversations
    conversationsList.innerHTML = '';
    
    // Sort conversations by last message time
    const sortedConversations = Array.from(this.conversations.values())
      .sort((a, b) => new Date(b.lastActivity || 0) - new Date(a.lastActivity || 0));
    
    sortedConversations.forEach(conversation => {
      const conversationElement = this.createConversationElement(conversation);
      conversationsList.appendChild(conversationElement);
    });
    
    // Update count
    conversationsCount.textContent = sortedConversations.length;
    
    // Show welcome screen if no conversations
    if (sortedConversations.length === 0) {
      this.showWelcomeScreen();
    }
  }

  createConversationElement(conversation) {
    const div = document.createElement('div');
    div.className = 'conversation-item';
    div.dataset.phoneNumber = conversation.phoneNumber;
    
    const avatar = this.getContactInitials(conversation.contact_name || conversation.phoneNumber);
    const lastMessage = conversation.last_message || 'No messages';
    const timestamp = conversation.last_activity ? this.formatTimestamp(new Date(conversation.last_activity)) : '';
    const unreadCount = conversation.unread_count || 0;
    
    div.innerHTML = `
      <div class="conversation-avatar">${avatar}</div>
      <div class="conversation-content">
        <div class="conversation-header">
          <div class="conversation-name">${conversation.contact_name || conversation.phoneNumber}</div>
          <div class="conversation-time">${timestamp}</div>
        </div>
        <div class="conversation-preview">${lastMessage}</div>
        ${unreadCount > 0 ? `<div class="conversation-meta">
          <div class="unread-badge">${unreadCount}</div>
        </div>` : ''}
      </div>
    `;
    
    div.addEventListener('click', () => {
      this.selectConversation(conversation.phoneNumber);
    });
    
    return div;
  }

  async selectConversation(phoneNumber) {
    // Update UI state
    document.querySelectorAll('.conversation-item').forEach(item => {
      item.classList.remove('active');
    });
    
    const selectedItem = document.querySelector(`[data-phone-number="${phoneNumber}"]`);
    if (selectedItem) {
      selectedItem.classList.add('active');
    }
    
    // Load conversation messages
    await this.loadConversationMessages(phoneNumber);
    
    // Show chat container
    document.getElementById('welcomeScreen').style.display = 'none';
    document.getElementById('chatContainer').style.display = 'flex';
    
    // Update chat header
    this.updateChatHeader(phoneNumber);
    
    // Mark as current conversation
    this.currentConversation = phoneNumber;
    
    // Mark messages as read
    this.markMessagesAsRead(phoneNumber);
    
    // Focus message input
    document.getElementById('messageInput').focus();
  }

  async loadConversationMessages(phoneNumber) {
    try {
      this.showLoading(true);
      
      const response = await fetch(`/api/messages/${phoneNumber}`);
      const data = await response.json();
      
      if (data.success) {
        this.messageCache.set(phoneNumber, data.messages);
        this.renderMessages(data.messages);
        console.log(`📥 Loaded ${data.messages.length} messages for ${phoneNumber}`);
      }
    } catch (error) {
      console.error('❌ Failed to load messages:', error);
      this.showNotification('Error loading messages', 'error');
    } finally {
      this.showLoading(false);
    }
  }

  renderMessages(messages) {
    const messagesScroll = document.getElementById('messagesScroll');
    messagesScroll.innerHTML = '';
    
    messages.forEach(message => {
      const messageElement = this.createMessageElement(message);
      messagesScroll.appendChild(messageElement);
    });
    
    // Scroll to bottom
    this.scrollToBottom();
  }

  createMessageElement(message) {
    const div = document.createElement('div');
    div.className = `message ${message.isIncoming ? 'incoming' : 'outgoing'}`;
    div.dataset.messageId = message.id;
    
    const timestamp = this.formatMessageTime(new Date(message.timestamp));
    const statusIcon = this.getMessageStatusIcon(message);
    
    div.innerHTML = `
      <div class="message-bubble">
        <p class="message-text">${this.escapeHtml(message.text || message.content)}</p>
        <div class="message-meta">
          <span class="message-time">${timestamp}</span>
          ${!message.isIncoming ? `<div class="message-status">${statusIcon}</div>` : ''}
        </div>
      </div>
    `;
    
    return div;
  }

  addMessage(messageData, isOutgoing = false, updateUI = true) {
    const phoneNumber = messageData.phoneNumber || messageData.threadId;
    
    // Add to conversation
    if (!this.conversations.has(phoneNumber)) {
      this.conversations.set(phoneNumber, {
        phoneNumber,
        contact_name: messageData.contactName || phoneNumber,
        messages: [],
        last_message: '',
        last_activity: new Date(),
        unread_count: 0
      });
    }
    
    const conversation = this.conversations.get(phoneNumber);
    const message = {
      id: messageData.id || Date.now().toString(),
      text: messageData.messageText || messageData.text || messageData.content,
      timestamp: new Date(messageData.timestamp || Date.now()),
      isIncoming: !isOutgoing,
      isDelivered: true,
      isRead: isOutgoing
    };
    
    // Add to conversation
    if (!conversation.messages) conversation.messages = [];
    conversation.messages.push(message);
    
    // Update conversation metadata
    conversation.last_message = message.text;
    conversation.last_activity = message.timestamp;
    
    if (!isOutgoing && phoneNumber !== this.currentConversation) {
      conversation.unread_count = (conversation.unread_count || 0) + 1;
    }
    
    // Update cache
    if (this.messageCache.has(phoneNumber)) {
      this.messageCache.get(phoneNumber).push(message);
    }
    
    if (updateUI) {
      // Update UI if this is the current conversation
      if (phoneNumber === this.currentConversation) {
        const messageElement = this.createMessageElement(message);
        document.getElementById('messagesScroll').appendChild(messageElement);
        this.scrollToBottom();
      }
      
      // Update conversations list
      this.renderConversations();
    }
  }

  async sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const text = messageInput.value.trim();
    
    if (!text || !this.currentConversation) return;
    
    try {
      // Clear input immediately
      messageInput.value = '';
      this.updateSendButton();
      
      // Add optimistic message
      this.addMessage({
        phoneNumber: this.currentConversation,
        messageText: text,
        timestamp: new Date()
      }, true);
      
      // Send via API
      const response = await fetch('/api/send', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: this.currentConversation,
          message: text
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        console.log('✅ Message sent successfully');
        this.showNotification('Message sent', 'success');
      } else {
        throw new Error(data.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('❌ Failed to send message:', error);
      this.showNotification('Failed to send message', 'error');
    }
  }

  sendQuickReply(reply) {
    if (!this.currentConversation) return;
    
    const messageInput = document.getElementById('messageInput');
    messageInput.value = reply;
    this.sendMessage();
  }

  handleSearch(query) {
    if (!query.trim()) {
      this.clearSearch();
      return;
    }
    
    // Filter conversations by contact name or phone number
    const filteredConversations = Array.from(this.conversations.values())
      .filter(conv => 
        (conv.contact_name || '').toLowerCase().includes(query.toLowerCase()) ||
        conv.phoneNumber.includes(query) ||
        (conv.last_message || '').toLowerCase().includes(query.toLowerCase())
      );
    
    this.renderFilteredConversations(filteredConversations);
  }

  clearSearch() {
    this.renderConversations();
  }

  renderFilteredConversations(conversations) {
    const conversationsList = document.getElementById('conversationsList');
    conversationsList.innerHTML = '';
    
    conversations.forEach(conversation => {
      const conversationElement = this.createConversationElement(conversation);
      conversationsList.appendChild(conversationElement);
    });
  }

  handleTyping() {
    if (!this.isTyping && this.wsConnection) {
      this.isTyping = true;
      // Send typing indicator
    }
    
    // Reset typing timeout
    clearTimeout(this.typingTimeout);
    this.typingTimeout = setTimeout(() => {
      this.isTyping = false;
      // Send stop typing indicator
    }, 2000);
  }

  updateSendButton() {
    const messageInput = document.getElementById('messageInput');
    const sendBtn = document.getElementById('sendBtn');
    
    sendBtn.disabled = !messageInput.value.trim() || !this.currentConversation;
  }

  autoResizeTextarea() {
    const messageInput = document.getElementById('messageInput');
    messageInput.style.height = 'auto';
    messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
  }

  updateChatHeader(phoneNumber) {
    const conversation = this.conversations.get(phoneNumber);
    if (!conversation) return;
    
    const contactName = document.getElementById('contactName');
    const contactAvatar = document.getElementById('contactAvatar');
    
    contactName.textContent = conversation.contact_name || phoneNumber;
    contactAvatar.textContent = this.getContactInitials(conversation.contact_name || phoneNumber);
  }

  markMessagesAsRead(phoneNumber) {
    const conversation = this.conversations.get(phoneNumber);
    if (conversation) {
      conversation.unread_count = 0;
      this.renderConversations();
    }
  }

  scrollToBottom() {
    const messagesScroll = document.getElementById('messagesScroll');
    messagesScroll.scrollTop = messagesScroll.scrollHeight;
  }

  showContextMenu(event, messageElement) {
    const contextMenu = document.getElementById('contextMenu');
    this.selectedMessage = messageElement;
    
    contextMenu.style.display = 'block';
    contextMenu.style.left = event.pageX + 'px';
    contextMenu.style.top = event.pageY + 'px';
    
    // Add event listeners for context menu items
    contextMenu.querySelectorAll('.context-menu-item').forEach(item => {
      item.onclick = (e) => {
        this.handleContextMenuAction(e.target.dataset.action);
        this.hideContextMenu();
      };
    });
  }

  hideContextMenu() {
    document.getElementById('contextMenu').style.display = 'none';
    this.selectedMessage = null;
  }

  handleContextMenuAction(action) {
    if (!this.selectedMessage) return;
    
    const messageText = this.selectedMessage.querySelector('.message-text').textContent;
    
    switch (action) {
      case 'copy':
        navigator.clipboard.writeText(messageText);
        this.showNotification('Message copied to clipboard');
        break;
      
      case 'reply':
        const messageInput = document.getElementById('messageInput');
        messageInput.value = `"${messageText}"\n\n`;
        messageInput.focus();
        break;
      
      case 'forward':
        this.showForwardDialog(messageText);
        break;
      
      case 'delete':
        this.deleteMessage(this.selectedMessage.dataset.messageId);
        break;
    }
  }

  showForwardDialog(messageText) {
    // Implementation for forward dialog
    this.showNotification('Forward feature coming soon');
  }

  deleteMessage(messageId) {
    // Implementation for message deletion
    this.showNotification('Delete feature coming soon');
  }

  initiateCall() {
    if (this.currentConversation) {
      this.showNotification(`Calling ${this.currentConversation}...`);
      // Implementation for phone call
    }
  }

  initiateVideoCall() {
    if (this.currentConversation) {
      this.showNotification(`Starting video call with ${this.currentConversation}...`);
      // Implementation for video call
    }
  }

  showNewMessageDialog() {
    // Implementation for new message dialog
    this.showNotification('New message feature coming soon');
  }

  handleKeyboardShortcuts(event) {
    // Ctrl/Cmd + F for search
    if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
      event.preventDefault();
      document.getElementById('searchBtn').click();
    }
    
    // Escape to close search or context menu
    if (event.key === 'Escape') {
      const searchContainer = document.getElementById('searchContainer');
      if (searchContainer.style.display !== 'none') {
        searchContainer.style.display = 'none';
      }
      this.hideContextMenu();
    }
  }

  updateSyncStatus(status) {
    const syncStatusElement = document.getElementById('syncStatus');
    this.syncStatus = status;
    
    switch (status) {
      case 'connected':
        syncStatusElement.textContent = 'Connected';
        syncStatusElement.style.color = 'var(--success-color)';
        break;
      
      case 'synced':
        syncStatusElement.textContent = 'Synced';
        syncStatusElement.style.color = 'var(--success-color)';
        break;
      
      case 'syncing':
        syncStatusElement.textContent = 'Syncing...';
        syncStatusElement.style.color = 'var(--warning-color)';
        break;
      
      case 'disconnected':
        syncStatusElement.textContent = 'Disconnected';
        syncStatusElement.style.color = 'var(--danger-color)';
        break;
    }
  }

  showLoading(show) {
    const loadingOverlay = document.getElementById('loadingOverlay');
    loadingOverlay.style.display = show ? 'flex' : 'none';
  }

  showNotification(message, type = 'info') {
    const toast = document.getElementById('notificationToast');
    const toastMessage = toast.querySelector('.toast-message');
    const toastIcon = toast.querySelector('.toast-icon');
    
    toastMessage.textContent = message;
    
    // Set icon based on type
    switch (type) {
      case 'success':
        toastIcon.textContent = '✅';
        break;
      case 'error':
        toastIcon.textContent = '❌';
        break;
      case 'warning':
        toastIcon.textContent = '⚠️';
        break;
      default:
        toastIcon.textContent = 'ℹ️';
    }
    
    toast.style.display = 'block';
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
      toast.style.display = 'none';
    }, 3000);
    
    // Close button
    toast.querySelector('.toast-close').onclick = () => {
      toast.style.display = 'none';
    };
  }

  showWelcomeScreen() {
    document.getElementById('welcomeScreen').style.display = 'flex';
    document.getElementById('chatContainer').style.display = 'none';
  }

  updateLayout() {
    // Handle responsive layout changes
    if (window.innerWidth <= 768) {
      document.body.classList.add('mobile');
    } else {
      document.body.classList.remove('mobile');
    }
  }

  startPeriodicSync() {
    // Sync every 30 seconds
    setInterval(async () => {
      if (this.syncStatus === 'connected') {
        this.updateSyncStatus('syncing');
        
        try {
          await this.loadConversations();
          this.updateSyncStatus('synced');
        } catch (error) {
          console.error('Periodic sync failed:', error);
          this.updateSyncStatus('connected');
        }
      }
    }, 30000);
  }

  // Utility functions
  getContactInitials(name) {
    if (!name) return '?';
    
    if (name.match(/^\+?\d+$/)) {
      // Phone number - return first and last digit
      const digits = name.replace(/\D/g, '');
      return digits.length >= 2 ? digits[0] + digits[digits.length - 1] : digits[0] || '?';
    }
    
    // Name - return initials
    const words = name.trim().split(/\s+/);
    if (words.length >= 2) {
      return (words[0][0] + words[words.length - 1][0]).toUpperCase();
    }
    return words[0][0].toUpperCase();
  }

  formatTimestamp(date) {
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'now';
    if (diff < 3600000) return Math.floor(diff / 60000) + 'm';
    if (diff < 86400000) return Math.floor(diff / 3600000) + 'h';
    if (diff < 604800000) return Math.floor(diff / 86400000) + 'd';
    
    return date.toLocaleDateString();
  }

  formatMessageTime(date) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  getMessageStatusIcon(message) {
    if (message.isRead) return '✓✓';
    if (message.isDelivered) return '✓';
    return '○';
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.intelUnisonMessages = new IntelUnisonMessages();
});