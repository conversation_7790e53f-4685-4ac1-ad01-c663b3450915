# Real iPhone Message Sync Shortcut
# This shortcut accesses actual Messages app data and sends it to iPhone Companion Pro

# Shortcut Name: "Sync Real Messages"
# Description: Syncs actual iPhone messages with Windows PC
# Trigger: Manual or Automation

# Actions:
1. Get Recent Messages from Messages App
   - Use "Get Messages" action
   - Filter: Last 50 messages
   - Include: Text, Sender, Timestamp, Read Status

2. Format Message Data
   - Convert to JSON format
   - Include real phone numbers and contact names
   - Add message metadata (read status, timestamp, direction)

3. Send to PC via HTTP
   - URL: http://YOUR_PC_IP:8888/sync-messages
   - Method: POST
   - Headers: Content-Type: application/json
   - Body: Formatted message data

4. Handle Response
   - Show notification if successful
   - Log errors for debugging

# JSON Format:
{
  "type": "message_sync",
  "timestamp": "2024-01-01T12:00:00Z",
  "messages": [
    {
      "id": "unique_message_id",
      "text": "Actual message content",
      "sender": "+1234567890",
      "contactName": "Real Contact Name",
      "timestamp": "2024-01-01T11:59:00Z",
      "isIncoming": true,
      "isRead": false,
      "conversationId": "+1234567890"
    }
  ]
}

# Installation Instructions:
1. Open Shortcuts app on iPhone
2. Tap "+" to create new shortcut
3. Add "Get Messages" action
4. Add "Get Contents of URL" action
5. Configure URL to point to your PC
6. Set up automation trigger (optional)

# Automation Triggers:
- When new message received
- Every 5 minutes (background)
- When opening iPhone Companion Pro app
- Manual trigger from widget
