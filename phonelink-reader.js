const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class PhoneLinkReader {
    constructor() {
        this.basePath = 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\Microsoft.YourPhone_8wekyb3d8bbwe\\LocalCache\\Indexed\\e1e8a106-3e02-48fa-b8d7-a055e20bd025\\System\\Database';
    }

    async readContacts() {
        const dbPath = path.join(this.basePath, 'contacts.db');
        console.log('📱 Reading contacts from Phone Link...');
        
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
                if (err) {
                    reject(err);
                    return;
                }
                
                // Try common contact table names
                const queries = [
                    "SELECT * FROM contacts LIMIT 10",
                    "SELECT * FROM contact LIMIT 10",
                    "SELECT * FROM people LIMIT 10"
                ];
                
                tryQuery(0);
                
                function tryQuery(index) {
                    if (index >= queries.length) {
                        db.close();
                        reject(new Error('No contact table found'));
                        return;
                    }
                    
                    db.all(queries[index], (err, rows) => {
                        if (err) {
                            tryQuery(index + 1);
                        } else {
                            console.log(`✅ Found ${rows.length} contacts`);
                            console.log('Sample:', rows[0]);
                            db.close();
                            resolve(rows);
                        }
                    });
                }
            });
        });
    }

    async readCallHistory() {
        const dbPath = path.join(this.basePath, 'calling.db');
        console.log('📞 Reading call history...');
        
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
                if (err) {
                    reject(err);
                    return;
                }
                
                db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {
                    if (!err && tables.length > 0) {
                        console.log('Tables in calling.db:', tables.map(t => t.name));
                    }
                    db.close();
                    resolve(tables);
                });
            });
        });
    }
}

// Test it
const reader = new PhoneLinkReader();
reader.readContacts().catch(console.error);
reader.readCallHistory().catch(console.error);

module.exports = PhoneLinkReader;