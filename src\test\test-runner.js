const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class TestRunner {
  constructor() {
    this.tests = [];
    this.results = {
      passed: 0,
      failed: 0,
      skipped: 0,
      total: 0,
      details: []
    };
    this.startTime = null;
    this.endTime = null;
  }

  addTest(name, testFunction, options = {}) {
    this.tests.push({
      name,
      testFunction,
      timeout: options.timeout || 10000,
      skip: options.skip || false,
      category: options.category || 'general'
    });
  }

  async runAllTests() {
    console.log('🧪 Starting iPhone Companion Pro Test Suite\n');
    this.startTime = Date.now();
    
    // Group tests by category
    const categories = this.groupTestsByCategory();
    
    for (const [category, tests] of Object.entries(categories)) {
      console.log(`\n📂 Running ${category} tests:`);
      console.log('─'.repeat(50));
      
      for (const test of tests) {
        await this.runSingleTest(test);
      }
    }
    
    this.endTime = Date.now();
    this.printSummary();
    
    return this.results;
  }

  groupTestsByCategory() {
    const categories = {};
    
    this.tests.forEach(test => {
      if (!categories[test.category]) {
        categories[test.category] = [];
      }
      categories[test.category].push(test);
    });
    
    return categories;
  }

  async runSingleTest(test) {
    this.results.total++;
    
    if (test.skip) {
      console.log(`⏭️  ${test.name} - SKIPPED`);
      this.results.skipped++;
      this.results.details.push({
        name: test.name,
        status: 'skipped',
        duration: 0,
        error: null
      });
      return;
    }

    const startTime = Date.now();
    
    try {
      // Run test with timeout
      await Promise.race([
        test.testFunction(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Test timeout')), test.timeout)
        )
      ]);
      
      const duration = Date.now() - startTime;
      console.log(`✅ ${test.name} - PASSED (${duration}ms)`);
      this.results.passed++;
      this.results.details.push({
        name: test.name,
        status: 'passed',
        duration,
        error: null
      });
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(`❌ ${test.name} - FAILED (${duration}ms)`);
      console.log(`   Error: ${error.message}`);
      this.results.failed++;
      this.results.details.push({
        name: test.name,
        status: 'failed',
        duration,
        error: error.message
      });
    }
  }

  printSummary() {
    const duration = this.endTime - this.startTime;
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${this.results.total}`);
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`⏭️  Skipped: ${this.results.skipped}`);
    console.log(`⏱️  Duration: ${duration}ms`);
    
    const successRate = this.results.total > 0 ? 
      ((this.results.passed / this.results.total) * 100).toFixed(1) : 0;
    console.log(`📈 Success Rate: ${successRate}%`);
    
    if (this.results.failed > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.results.details
        .filter(test => test.status === 'failed')
        .forEach(test => {
          console.log(`   • ${test.name}: ${test.error}`);
        });
    }
    
    console.log('\n' + '='.repeat(60));
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      duration: this.endTime - this.startTime,
      summary: {
        total: this.results.total,
        passed: this.results.passed,
        failed: this.results.failed,
        skipped: this.results.skipped,
        successRate: this.results.total > 0 ? 
          ((this.results.passed / this.results.total) * 100) : 0
      },
      tests: this.results.details,
      environment: {
        platform: process.platform,
        nodeVersion: process.version,
        electronVersion: process.versions.electron || 'N/A'
      }
    };
    
    // Save report to file
    const reportPath = path.join(__dirname, 'reports', `test-report-${Date.now()}.json`);
    const reportsDir = path.dirname(reportPath);
    
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`📄 Test report saved to: ${reportPath}`);
    
    return report;
  }
}

// Test utilities
class TestUtils {
  static async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  static async waitFor(condition, timeout = 5000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      if (await condition()) {
        return true;
      }
      await this.delay(100);
    }
    
    throw new Error('Condition not met within timeout');
  }

  static mockElectronApp() {
    return {
      isReady: () => true,
      quit: () => {},
      getPath: (name) => `/mock/path/${name}`,
      on: () => {},
      once: () => {}
    };
  }

  static mockBrowserWindow() {
    return {
      loadFile: () => Promise.resolve(),
      webContents: {
        send: () => {},
        on: () => {},
        executeJavaScript: () => Promise.resolve()
      },
      on: () => {},
      show: () => {},
      hide: () => {},
      close: () => {}
    };
  }

  static createMockService(methods = {}) {
    const mockService = {
      initialize: () => Promise.resolve(),
      start: () => Promise.resolve(),
      stop: () => Promise.resolve(),
      on: () => {},
      emit: () => {},
      ...methods
    };
    
    return mockService;
  }

  static assert(condition, message) {
    if (!condition) {
      throw new Error(message || 'Assertion failed');
    }
  }

  static assertEqual(actual, expected, message) {
    if (actual !== expected) {
      throw new Error(message || `Expected ${expected}, got ${actual}`);
    }
  }

  static assertNotEqual(actual, unexpected, message) {
    if (actual === unexpected) {
      throw new Error(message || `Expected not ${unexpected}, got ${actual}`);
    }
  }

  static assertThrows(fn, message) {
    try {
      fn();
      throw new Error(message || 'Expected function to throw');
    } catch (error) {
      if (error.message === (message || 'Expected function to throw')) {
        throw error;
      }
      // Function threw as expected
    }
  }

  static async assertAsync(asyncFn, message) {
    try {
      const result = await asyncFn();
      if (!result) {
        throw new Error(message || 'Async assertion failed');
      }
    } catch (error) {
      throw new Error(message || `Async assertion failed: ${error.message}`);
    }
  }
}

// Performance test utilities
class PerformanceTest {
  static async measureExecutionTime(fn) {
    const start = Date.now();
    await fn();
    return Date.now() - start;
  }

  static async measureMemoryUsage(fn) {
    const initialMemory = process.memoryUsage();
    await fn();
    const finalMemory = process.memoryUsage();
    
    return {
      heapUsedDelta: finalMemory.heapUsed - initialMemory.heapUsed,
      heapTotalDelta: finalMemory.heapTotal - initialMemory.heapTotal,
      externalDelta: finalMemory.external - initialMemory.external,
      rss: finalMemory.rss
    };
  }

  static async stressTest(fn, iterations = 100, concurrency = 10) {
    const results = [];
    const batches = Math.ceil(iterations / concurrency);
    
    for (let batch = 0; batch < batches; batch++) {
      const batchPromises = [];
      const batchSize = Math.min(concurrency, iterations - (batch * concurrency));
      
      for (let i = 0; i < batchSize; i++) {
        batchPromises.push(this.measureExecutionTime(fn));
      }
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }
    
    return {
      iterations: results.length,
      totalTime: results.reduce((sum, time) => sum + time, 0),
      averageTime: results.reduce((sum, time) => sum + time, 0) / results.length,
      minTime: Math.min(...results),
      maxTime: Math.max(...results),
      results
    };
  }
}

module.exports = {
  TestRunner,
  TestUtils,
  PerformanceTest
};
