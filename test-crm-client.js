
const axios = require('axios');

class TestCRMClient {
  constructor(baseURL = 'http://localhost:7777') {
    this.baseURL = baseURL;
  }

  async getContacts() {
    try {
      const response = await axios.get(`${this.baseURL}/api/crm/contacts`);
      return response.data;
    } catch (error) {
      console.error('Error getting contacts:', error.message);
      return null;
    }
  }

  async getMessages(phone) {
    try {
      const response = await axios.get(`${this.baseURL}/api/crm/messages/${phone}`);
      return response.data;
    } catch (error) {
      console.error('Error getting messages:', error.message);
      return null;
    }
  }

  async sendMessage(phone, text, campaignId = 'test') {
    try {
      const response = await axios.post(`${this.baseURL}/api/crm/send`, {
        phone,
        text,
        campaignId
      });
      return response.data;
    } catch (error) {
      console.error('Error sending message:', error.message);
      return null;
    }
  }

  async getStatus() {
    try {
      const response = await axios.get(`${this.baseURL}/api/crm/status`);
      return response.data;
    } catch (error) {
      console.error('Error getting status:', error.message);
      return null;
    }
  }
}

// Example usage
async function testCRM() {
  const crm = new TestCRMClient();
  
  console.log('🧪 Testing CRM API...');
  
  // Test status
  const status = await crm.getStatus();
  console.log('Status:', status);
  
  // Test contacts
  const contacts = await crm.getContacts();
  console.log('Contacts:', contacts?.count || 0);
  
  // Test sending a message (uncomment to test)
  // const result = await crm.sendMessage('+1234567890', 'Test message from CRM', 'test-campaign');
  // console.log('Send result:', result);
}

if (require.main === module) {
  testCRM();
}

module.exports = TestCRMClient;
