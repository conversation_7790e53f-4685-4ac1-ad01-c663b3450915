const fs = require('fs');
const path = require('path');

console.log('🧹 Completely cleaning main.js...\n');

const mainJsPath = path.join(__dirname, 'src/main/main.js');
let mainJs = fs.readFileSync(mainJsPath, 'utf8');

// Step 1: Remove ALL IPC handlers for windows
const handlers = ['open-messages', 'open-calls', 'open-mirror', 'open-photos', 'open-files', 'open-settings'];

handlers.forEach(handler => {
  // Remove any ipcMain.handle for this handler
  const regex = new RegExp(`ipcMain\\.handle\\(['"]\${handler}['"][^}]+\\}\\);?[\\s\\n]*`, 'gms');
  mainJs = mainJs.replace(regex, '');
  console.log(`Removed all instances of ${handler}`);
});

// Step 2: Remove all window variable declarations
mainJs = mainJs.replace(/let\s+(messagesWindow|callsWindow|settingsWindow|photosWindow|filesWindow)\s*=\s*null;\s*/g, '');
mainJs = mainJs.replace(/global\.(messagesWindow|callsWindow|settingsWindow|photosWindow|filesWindow)\s*=\s*null;\s*/g, '');

// Step 3: Add clean handlers ONCE at the very end of the file, before the last module.exports if it exists
const cleanHandlers = `
// ===== WINDOW HANDLERS - CLEAN VERSION =====
const windowManager = {
  messages: null,
  calls: null,
  mirror: null,
  photos: null,
  files: null,
  settings: null
};

// Messages Window
ipcMain.handle('open-messages', async () => {
  console.log('📱 Opening Messages window');
  
  if (windowManager.messages && !windowManager.messages.isDestroyed()) {
    windowManager.messages.show();
    windowManager.messages.focus();
    return;
  }
  
  windowManager.messages = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'iPhone Messages',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  windowManager.messages.loadFile(path.join(__dirname, '../renderer/views/messages.html'));
  windowManager.messages.on('closed', () => { windowManager.messages = null; });
});

// Calls Window
ipcMain.handle('open-calls', async () => {
  console.log('📞 Opening Calls window');
  
  if (windowManager.calls && !windowManager.calls.isDestroyed()) {
    windowManager.calls.show();
    windowManager.calls.focus();
    return;
  }
  
  windowManager.calls = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'iPhone Calls',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  windowManager.calls.loadFile(path.join(__dirname, '../renderer/views/calls.html'));
  windowManager.calls.on('closed', () => { windowManager.calls = null; });
});

// Photos Window
ipcMain.handle('open-photos', async () => {
  console.log('📸 Opening Photos window');
  
  if (windowManager.photos && !windowManager.photos.isDestroyed()) {
    windowManager.photos.show();
    windowManager.photos.focus();
    return;
  }
  
  windowManager.photos = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'iPhone Photos',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  windowManager.photos.loadFile(path.join(__dirname, '../renderer/views/photos.html'));
  windowManager.photos.on('closed', () => { windowManager.photos = null; });
});

// Files Window
ipcMain.handle('open-files', async () => {
  console.log('📁 Opening Files window');
  
  if (windowManager.files && !windowManager.files.isDestroyed()) {
    windowManager.files.show();
    windowManager.files.focus();
    return;
  }
  
  windowManager.files = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'iPhone Files',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  windowManager.files.loadFile(path.join(__dirname, '../renderer/views/files.html'));
  windowManager.files.on('closed', () => { windowManager.files = null; });
});

// Settings Window
ipcMain.handle('open-settings', async () => {
  console.log('⚙️ Opening Settings window');
  
  if (windowManager.settings && !windowManager.settings.isDestroyed()) {
    windowManager.settings.show();
    windowManager.settings.focus();
    return;
  }
  
  windowManager.settings = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'Settings',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  windowManager.settings.loadFile(path.join(__dirname, '../renderer/views/settings.html'));
  windowManager.settings.on('closed', () => { windowManager.settings = null; });
});

// Mirror Window
ipcMain.handle('open-mirror', async () => {
  console.log('🖥️ Opening Screen Mirror window');
  
  if (windowManager.mirror && !windowManager.mirror.isDestroyed()) {
    windowManager.mirror.show();
    windowManager.mirror.focus();
    return;
  }
  
  windowManager.mirror = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'Screen Mirror',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  windowManager.mirror.loadFile(path.join(__dirname, '../renderer/views/mirror.html'));
  windowManager.mirror.on('closed', () => { windowManager.mirror = null; });
});
// ===== END WINDOW HANDLERS =====
`;

// Find the best place to insert - right before the last closing of the file
// Remove any existing module.exports at the end if it exists
mainJs = mainJs.replace(/module\.exports\s*=\s*{[^}]*};\s*$/, '');

// Add our handlers at the end
mainJs = mainJs.trimEnd() + '\n\n' + cleanHandlers;

// Save the cleaned file
fs.writeFileSync(mainJsPath, mainJs);
console.log('✅ Cleaned main.js completely');

// Also create missing view files if they don't exist
const viewsDir = path.join(__dirname, 'src/renderer/views');
const viewFiles = ['messages.html', 'calls.html', 'photos.html', 'files.html', 'settings.html', 'mirror.html'];

viewFiles.forEach(file => {
  const filePath = path.join(viewsDir, file);
  if (!fs.existsSync(filePath)) {
    const title = file.replace('.html', '').charAt(0).toUpperCase() + file.replace('.html', '').slice(1);
    const content = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${title}</title>
    <link rel="stylesheet" href="../styles/unified.css">
    <style>
        body {
            background: #1a1a1a;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #007AFF;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>${title}</h1>
        <p>This feature is coming soon...</p>
    </div>
</body>
</html>`;
    
    fs.writeFileSync(filePath, content);
    console.log(`✅ Created ${file}`);
  }
});

console.log('\n🎉 Complete! The app should now work without errors.');
console.log('\n📱 All buttons will open their respective windows.');