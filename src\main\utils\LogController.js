/**
 * 🎛️ LOG CONTROLLER - Runtime logging control for iPhone Companion Pro
 * Allows users to change log levels and view logging statistics
 */

const logger = require('./Logger');

class LogController {
    constructor() {
        this.availableLevels = ['ERROR', 'WARN', 'INFO', 'DEBUG', 'TRACE'];
    }
    
    setLogLevel(level) {
        const upperLevel = level.toUpperCase();
        if (!this.availableLevels.includes(upperLevel)) {
            logger.warn('LOGCONTROL', `Invalid log level: ${level}. Available: ${this.availableLevels.join(', ')}`);
            return false;
        }
        
        logger.currentLogLevel = logger.logLevels[upperLevel];
        logger.info('LOGCONTROL', `Log level changed to ${upperLevel}`);
        return true;
    }
    
    getCurrentLevel() {
        return logger.getLevelName(logger.currentLogLevel);
    }
    
    getStats() {
        return logger.getStats();
    }
    
    printStats() {
        logger.printStats();
    }
    
    // Emergency quiet mode - only show errors
    quietMode() {
        this.setLogLevel('ERROR');
        logger.error('LOGCONTROL', '🔇 QUIET MODE ACTIVATED - Only showing errors');
    }
    
    // Verbose mode - show everything
    verboseMode() {
        this.setLogLevel('TRACE');
        logger.info('LOGCONTROL', '📢 VERBOSE MODE ACTIVATED - Showing all logs');
    }
    
    // Normal mode - balanced logging
    normalMode() {
        this.setLogLevel('INFO');
        logger.info('LOGCONTROL', '📊 NORMAL MODE ACTIVATED - Balanced logging');
    }
    
    // Debug mode for troubleshooting
    debugMode() {
        this.setLogLevel('DEBUG');
        logger.debug('LOGCONTROL', '🔍 DEBUG MODE ACTIVATED - Detailed logging for troubleshooting');
    }
}

module.exports = new LogController();