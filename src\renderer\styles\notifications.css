/* Notifications Interface Styles */
.notifications-container {
    padding: 20px;
    background: #0a0a0a;
    min-height: calc(100vh - 40px);
}

/* Header */
.notifications-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #333;
}

.header-left h1 {
    margin: 0 0 5px 0;
    color: #fff;
    font-size: 24px;
    font-weight: 600;
}

.subtitle {
    margin: 0;
    color: #999;
    font-size: 14px;
}

.header-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    padding: 8px 16px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 6px;
    color: #fff;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.control-btn:hover {
    background: #3a3a3a;
    border-color: #555;
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Filters */
.notification-filters {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    overflow-x: auto;
    padding-bottom: 5px;
}

.filter-btn {
    padding: 8px 16px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 20px;
    color: #999;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 6px;
}

.filter-btn:hover {
    color: #fff;
    border-color: #555;
}

.filter-btn.active {
    background: #007AFF;
    border-color: #007AFF;
    color: white;
}

.filter-btn .count {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
    min-width: 16px;
    text-align: center;
}

.filter-btn.active .count {
    background: rgba(255, 255, 255, 0.3);
}

/* Search */
.search-container {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.search-container input {
    flex: 1;
    padding: 12px 16px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 8px;
    color: #fff;
    font-size: 14px;
}

.search-container input:focus {
    outline: none;
    border-color: #007AFF;
}

.search-btn {
    padding: 12px 16px;
    background: #007AFF;
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    font-size: 14px;
}

.search-btn:hover {
    background: #0056CC;
}

/* Notifications Content */
.notifications-content {
    background: #1a1a1a;
    border-radius: 12px;
    border: 1px solid #333;
    overflow: hidden;
}

.notifications-list {
    max-height: 600px;
    overflow-y: auto;
}

.date-header {
    padding: 15px 20px;
    background: #2a2a2a;
    border-bottom: 1px solid #333;
    position: sticky;
    top: 0;
    z-index: 10;
}

.date-header h3 {
    margin: 0;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 15px 20px;
    border-bottom: 1px solid #2a2a2a;
    transition: background 0.2s ease;
    cursor: pointer;
}

.notification-item:hover {
    background: #2a2a2a;
}

.notification-item.unread {
    background: rgba(0, 122, 255, 0.05);
    border-left: 3px solid #007AFF;
}

.notification-icon {
    position: relative;
    margin-right: 15px;
    flex-shrink: 0;
}

.app-icon {
    font-size: 24px;
    display: block;
}

.unread-indicator {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #007AFF;
    border-radius: 50%;
    border: 2px solid #1a1a1a;
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.app-name {
    font-size: 12px;
    color: #999;
    font-weight: 500;
}

.notification-time {
    font-size: 11px;
    color: #666;
}

.notification-title {
    font-size: 14px;
    font-weight: 600;
    color: #fff;
    margin-bottom: 3px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.notification-body {
    font-size: 13px;
    color: #ccc;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.notification-actions {
    display: flex;
    gap: 5px;
    margin-left: 10px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions {
    opacity: 1;
}

.action-btn.small {
    width: 32px;
    height: 32px;
    padding: 0;
    background: #3a3a3a;
    border: 1px solid #555;
    border-radius: 6px;
    color: #fff;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.action-btn.small:hover {
    background: #4a4a4a;
}

/* Empty State */
.empty-notifications {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #666;
    text-align: center;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-notifications h3 {
    margin: 0 0 10px 0;
    color: #999;
}

.empty-notifications p {
    margin: 0;
    color: #666;
}

/* Loading State */
.loading-notifications {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #666;
}

.spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #333;
    border-top: 2px solid #007AFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: #1a1a1a;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80%;
    overflow: hidden;
    border: 1px solid #333;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    color: #fff;
    font-size: 18px;
}

.modal-close {
    background: none;
    border: none;
    color: #999;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
}

.modal-close:hover {
    color: #fff;
}

.modal-body {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
}

/* Notification Details Modal */
.notification-details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.app-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.app-icon-large {
    font-size: 32px;
}

.app-details h3 {
    margin: 0;
    color: #fff;
    font-size: 16px;
}

.notification-timestamp {
    margin: 5px 0 0 0;
    color: #999;
    font-size: 13px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.status-badge.read {
    background: #34C759;
    color: white;
}

.status-badge.unread {
    background: #007AFF;
    color: white;
}

.notification-details-body h4 {
    margin: 0 0 10px 0;
    color: #fff;
    font-size: 16px;
}

.notification-details-body p {
    margin: 0;
    color: #ccc;
    line-height: 1.5;
}

.notification-details-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.notification-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn.primary {
    background: #007AFF;
    color: white;
}

.action-btn.primary:hover {
    background: #0056CC;
}

.action-btn.secondary {
    background: #2a2a2a;
    color: #fff;
    border: 1px solid #444;
}

.action-btn.secondary:hover {
    background: #3a3a3a;
}

/* Settings Modal */
.settings-section {
    margin-bottom: 30px;
}

.settings-section h3 {
    margin: 0 0 15px 0;
    color: #fff;
    font-size: 16px;
}

.setting-item {
    margin-bottom: 15px;
}

.setting-label {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
}

.setting-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 3px;
    position: relative;
}

.setting-label input[type="checkbox"]:checked + .checkmark {
    background: #007AFF;
    border-color: #007AFF;
}

.setting-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.setting-item label {
    display: block;
    color: #fff;
    margin-bottom: 5px;
    font-size: 14px;
}

.setting-item input[type="time"] {
    padding: 8px 12px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 6px;
    color: #fff;
    font-size: 14px;
}

.settings-actions {
    margin-top: 30px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Quick Reply Modal */
.reply-notification {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    background: #2a2a2a;
    border-radius: 8px;
    margin-bottom: 20px;
}

.reply-notification .app-icon {
    font-size: 20px;
}

.reply-details strong {
    color: #fff;
    display: block;
    margin-bottom: 5px;
}

.reply-details p {
    margin: 0;
    color: #ccc;
    font-size: 13px;
}

.reply-input-container textarea {
    width: 100%;
    padding: 12px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 8px;
    color: #fff;
    font-size: 14px;
    resize: vertical;
    min-height: 80px;
    margin-bottom: 15px;
}

.reply-input-container textarea:focus {
    outline: none;
    border-color: #007AFF;
}

.reply-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Responsive Design */
@media (max-width: 768px) {
    .notifications-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .header-controls {
        flex-wrap: wrap;
    }
    
    .notification-filters {
        flex-wrap: wrap;
    }
    
    .notification-item {
        padding: 12px 15px;
    }
    
    .notification-actions {
        position: static;
        opacity: 1;
        margin-top: 10px;
        margin-left: 0;
    }
}
