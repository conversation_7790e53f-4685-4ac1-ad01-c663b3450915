<<<<<<<
<<<<<<<



<<<<<<<















// Fix All Connection Issues - iPhone Companion Pro































































const { exec, spawn } = require('child_process');































































const fs = require('fs');































































const path = require('path');































































const http = require('http');































































































































class ConnectionFixer {































































  constructor() {































































    this.fixes = [];































































    this.errors = [];































































  }































































































































  async fixAllConnections() {































































    console.log('🔧 Fixing ALL iPhone connection issues...\n');































































    































































    // Fix Phone Link































































    await this.fixPhoneLink();































































    































































    // Fix AirPlay































































    await this.fixAirPlay();































































    































































    // Fix USB































































    await this.fixUSB();































































    































































    // Setup macOS VM































































    await this.setupMacOSVM();































































    































































    // Generate report































































    this.generateFixReport();































































  }































































































































  async fixPhoneLink() {































































    console.log('🔗 FIXING PHONE LINK...');































































    































































    try {































































      // Check if Phone Link is installed































































      const phoneLink = await this.findPhoneLinkInstallation();































































      































































      if (!phoneLink) {































































        console.log('📦 Installing Phone Link...');































































        await this.installPhoneLink();































































      }































































      































































      // Start Phone Link service































































      await this.startPhoneLinkService();































































      































































      // Create enhanced Phone Link bridge































































      await this.createPhoneLinkBridge();































































      































































      this.fixes.push('✅ Phone Link fixed and enhanced');































































      































































    } catch (error) {































































      console.log('❌ Phone Link fix failed:', error.message);































































      this.errors.push('Phone Link: ' + error.message);































































    }































































    console.log('');































































  }































































































































  async findPhoneLinkInstallation() {































































    const possiblePaths = [































































      'C:\\Program Files\\WindowsApps\\Microsoft.YourPhone_*',































































      'C:\\Program Files (x86)\\Microsoft\\YourPhone',































































      path.join(process.env.LOCALAPPDATA, 'Microsoft\\WindowsApps\\YourPhone.exe')































































    ];































































    































































    for (const searchPath of possiblePaths) {































































      try {































































        if (searchPath.includes('*')) {































































          // Use dir command for wildcard search































































          const result = await this.execPromise(`dir "${searchPath}" /b`);































































          if (result.trim()) {































































            console.log('✅ Found Phone Link installation');































































            return true;































































          }































































        } else if (fs.existsSync(searchPath)) {































































          console.log('✅ Found Phone Link at:', searchPath);































































          return true;































































        }































































      } catch (e) {































































        // Continue searching































































      }































































    }































































    































































    return false;































































  }































































































































  async installPhoneLink() {































































    try {































































      // Try to install via Microsoft Store































































      await this.execPromise('start ms-windows-store://pdp/?productid=9NMPJ99VJBWV');































































      console.log('📱 Opening Microsoft Store for Phone Link installation...');































































      console.log('⚠️ Please install Phone Link from the Store and run this script again');































































      































































      // Alternative: try winget































































      try {































































        await this.execPromise('winget install Microsoft.YourPhone');































































        console.log('✅ Phone Link installed via winget');































































      } catch (e) {































































        console.log('⚠️ Manual installation required from Microsoft Store');































































      }































































      































































    } catch (error) {































































      throw new Error('Failed to install Phone Link');































































    }































































  }































































































































  async startPhoneLinkService() {































































    try {































































      // Start Phone Link app































































      await this.execPromise('start YourPhone:');































































      console.log('✅ Phone Link app started');































































      































































      // Wait for it to initialize































































      await this.sleep(3000);































































      































































      // Verify it's running































































      const processes = await this.execPromise('tasklist | findstr YourPhone');































































      if (processes.includes('YourPhone')) {































































        console.log('✅ Phone Link is running');































































      } else {































































        throw new Error('Phone Link failed to start');































































      }































































      































































    } catch (error) {































































      console.log('⚠️ Starting Phone Link manually...');































































      // Try alternative methods































































      try {































































        await this.execPromise('start shell:AppsFolder\\Microsoft.YourPhone_8wekyb3d8bbwe!App');































































      } catch (e) {































































        throw new Error('Could not start Phone Link');































































      }































































    }































































  }































































































































  async createPhoneLinkBridge() {































































    const bridgeCode = `// Enhanced Phone Link Bridge































































const sqlite3 = require('sqlite3');































































const fs = require('fs');































































const path = require('path');































































const { EventEmitter } = require('events');































































































































class EnhancedPhoneLinkBridge extends EventEmitter {































































  constructor() {































































    super();































































    this.isConnected = false;































































    this.databases = [];































































    this.watchedPaths = [];































































  }































































































































  async connect() {































































    console.log('🔗 Connecting to Phone Link...');































































    































































    // Find all possible Phone Link data locations































































    const searchPaths = [































































      path.join(process.env.LOCALAPPDATA, 'Packages'),































































      path.join(process.env.APPDATA, 'Microsoft'),































































      path.join(process.env.PROGRAMDATA, 'Microsoft')































































    ];































































    































































    for (const basePath of searchPaths) {































































      await this.scanForPhoneLinkData(basePath);































































    }































































    































































    if (this.databases.length > 0) {































































      this.isConnected = true;































































      console.log(\`✅ Found \${this.databases.length} Phone Link databases\`);































































      this.startMonitoring();































































    } else {































































      console.log('❌ No Phone Link data found');































































    }































































  }































































































































  async scanForPhoneLinkData(basePath) {































































    if (!fs.existsSync(basePath)) return;































































    































































    try {































































      const items = fs.readdirSync(basePath);































































      































































      for (const item of items) {































































        if (item.includes('YourPhone') || item.includes('WindowsCommunications')) {































































          const fullPath = path.join(basePath, item);































































          































































          if (fs.statSync(fullPath).isDirectory()) {































































            await this.scanDirectory(fullPath);































































          }































































        }































































      }































































    } catch (e) {































































      // Permission denied or other error































































    }































































  }































































































































  async scanDirectory(dirPath) {































































    try {































































      const files = fs.readdirSync(dirPath, { recursive: true });































































      































































      for (const file of files) {































































        if (file.endsWith('.db') || file.endsWith('.sqlite')) {































































          const fullPath = path.join(dirPath, file);































































          































































          try {































































            // Test if it's a valid SQLite database































































            const db = new sqlite3.Database(fullPath, sqlite3.OPEN_READONLY);































































            































































            await new Promise((resolve) => {































































              db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {































































                if (!err && tables.length > 0) {































































                  console.log(\`📁 Found database: \${fullPath}\`);































































                  console.log(\`   Tables: \${tables.map(t => t.name).join(', ')}\`);































































                  































































                  this.databases.push({































































                    path: fullPath,































































                    tables: tables.map(t => t.name),































































                    type: this.detectDatabaseType(tables.map(t => t.name))































































                  });































































                }































































                db.close();































































                resolve();































































              });































































            });































































            































































          } catch (e) {































































            // Not a valid SQLite database































































          }































































        }































































      }































































    } catch (e) {































































      // Permission denied or other error































































    }































































  }































































































































  detectDatabaseType(tableNames) {































































    const tables = tableNames.join(' ').toLowerCase();































































    































































    if (tables.includes('message') || tables.includes('sms')) {































































      return 'messages';































































    } else if (tables.includes('call') || tables.includes('phone')) {































































      return 'calls';































































    } else if (tables.includes('contact')) {































































      return 'contacts';































































    } else {































































      return 'unknown';































































    }































































  }































































































































  async getMessages() {































































    const messages = [];































































    































































    for (const dbInfo of this.databases) {































































      if (dbInfo.type === 'messages') {































































        try {































































          const dbMessages = await this.extractMessagesFromDB(dbInfo);































































          messages.push(...dbMessages);































































        } catch (e) {































































          console.log(\`Error reading \${dbInfo.path}: \${e.message}\`);































































        }































































      }































































    }































































    































































    return messages;































































  }































































































































  async extractMessagesFromDB(dbInfo) {































































    return new Promise((resolve) => {































































      const db = new sqlite3.Database(dbInfo.path, sqlite3.OPEN_READONLY);































































      const messages = [];































































      































































      // Try common message table patterns































































      const queries = [































































        "SELECT * FROM messages ORDER BY timestamp DESC LIMIT 100",































































        "SELECT * FROM message ORDER BY date DESC LIMIT 100",































































        "SELECT * FROM sms ORDER BY time DESC LIMIT 100"































































      ];































































      































































      let queryIndex = 0;































































      































































      const tryNextQuery = () => {































































        if (queryIndex >= queries.length) {































































          db.close();































































          resolve(messages);































































          return;































































        }































































        































































        const query = queries[queryIndex++];































































        































































        db.all(query, (err, rows) => {































































          if (!err && rows.length > 0) {































































            console.log(\`✅ Found \${rows.length} messages in \${dbInfo.path}\`);































































            































































            rows.forEach(row => {































































              messages.push({































































                id: row.id || row.rowid || Date.now(),































































                text: row.text || row.body || row.content,































































                phoneNumber: row.phone || row.number || row.address,































































                timestamp: new Date(row.timestamp || row.date || row.time || Date.now()),































































                isIncoming: !row.is_outgoing && !row.is_sent,































































                source: 'phoneLink'































































              });































































            });































































            































































            db.close();































































            resolve(messages);































































          } else {































































            tryNextQuery();































































          }































































        });































































      };































































      































































      tryNextQuery();































































    });































































  }































































































































  startMonitoring() {































































    // Monitor database files for changes































































    this.databases.forEach(dbInfo => {































































      try {































































        fs.watchFile(dbInfo.path, (curr, prev) => {































































          if (curr.mtime > prev.mtime) {































































            console.log('📨 Phone Link database updated');































































            this.emit('database-updated', dbInfo);































































          }































































        });































































        































































        this.watchedPaths.push(dbInfo.path);































































      } catch (e) {































































        // File watching failed































































      }































































    });































































  }































































































































  stop() {































































    this.watchedPaths.forEach(path => {































































      fs.unwatchFile(path);































































    });































































    this.watchedPaths = [];































































  }































































}































































































































module.exports = { EnhancedPhoneLinkBridge };`;































































































































    fs.writeFileSync('src/main/services/EnhancedPhoneLinkBridge.js', bridgeCode);































































    console.log('✅ Enhanced Phone Link bridge created');































































  }































































































































  async fixAirPlay() {































































    console.log('📺 FIXING AIRPLAY...');































































    































































    try {































































      // Check and fix port 7000































































      await this.fixAirPlayPort();































































      































































      // Create enhanced AirPlay server































































      await this.createEnhancedAirPlayServer();































































      































































      // Configure firewall































































      await this.configureAirPlayFirewall();































































      































































      this.fixes.push('✅ AirPlay fixed and enhanced');































































      































































    } catch (error) {































































      console.log('❌ AirPlay fix failed:', error.message);































































      this.errors.push('AirPlay: ' + error.message);































































    }































































    console.log('');































































  }































































































































  async fixAirPlayPort() {































































    try {































































      // Check what's using port 7000































































      const netstat = await this.execPromise('netstat -ano | findstr :7000');































































      































































      if (netstat.trim()) {































































        console.log('⚠️ Port 7000 in use:', netstat.trim());































































        































































        // Extract PID and kill if necessary































































        const lines = netstat.split('\n');































































        for (const line of lines) {































































          const parts = line.trim().split(/\s+/);































































          if (parts.length >= 5) {































































            const pid = parts[4];































































            console.log(`Killing process ${pid} using port 7000...`);































































            try {































































              await this.execPromise(`taskkill /PID ${pid} /F`);































































            } catch (e) {































































              // Process might already be dead































































            }































































          }































































        }































































      }































































      































































      console.log('✅ Port 7000 is now available');































































      































































    } catch (error) {































































      // Port is available































































      console.log('✅ Port 7000 is available');































































    }































































  }































































































































  async createEnhancedAirPlayServer() {































































    const airplayCode = `// Enhanced AirPlay Server































































const http = require('http');































































const { EventEmitter } = require('events');































































































































class EnhancedAirPlayServer extends EventEmitter {































































  constructor() {































































    super();































































    this.server = null;































































    this.isRunning = false;































































    this.connectedDevices = new Set();































































  }































































































































  start() {































































    console.log('📺 Starting Enhanced AirPlay Server...');































































    































































    this.server = http.createServer((req, res) => {































































      console.log(\`AirPlay request: \${req.method} \${req.url}\`);































































      































































      // Set CORS headers































































      res.setHeader('Access-Control-Allow-Origin', '*');































































      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');































































      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');































































      































































      if (req.method === 'OPTIONS') {































































        res.writeHead(200);































































        res.end();































































        return;































































      }































































      































































      switch (req.url) {































































        case '/server-info':































































          this.handleServerInfo(req, res);































































          break;































































        case '/reverse':































































          this.handleReverse(req, res);































































          break;































































        case '/play':































































          this.handlePlay(req, res);































































          break;































































        case '/scrub':































































          this.handleScrub(req, res);































































          break;































































        case '/stop':































































          this.handleStop(req, res);































































          break;































































        default:































































          res.writeHead(404);































































          res.end('Not Found');































































      }































































    });































































    































































    this.server.listen(7000, '0.0.0.0', () => {































































      console.log('✅ AirPlay server listening on port 7000');































































      this.isRunning = true;































































      this.emit('server-started');































































      































































      // Start advertising































































      this.startAdvertising();































































    });































































    































































    this.server.on('error', (error) => {































































      console.error('AirPlay server error:', error);































































      this.emit('server-error', error);































































    });































































  }































































































































  handleServerInfo(req, res) {































































    const serverInfo = \`<?xml version="1.0" encoding="UTF-8"?>































































<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">































































<plist version="1.0">































































<dict>































































  <key>deviceid</key>































































  <string>AA:BB:CC:DD:EE:FF</string>































































  <key>features</key>































































  <integer>0x5A7FFEE6</integer>































































  <key>model</key>































































  <string>AppleTV3,1</string>































































  <key>protovers</key>































































  <string>1.0</string>































































  <key>srcvers</key>































































  <string>220.68</string>































































  <key>vv</key>































































  <integer>2</integer>































































</dict>































































</plist>\`;































































    































































    res.writeHead(200, { 'Content-Type': 'text/x-apple-plist+xml' });































































    res.end(serverInfo);































































  }































































































































  handleReverse(req, res) {































































    console.log('📱 iPhone requesting reverse connection');































































    































































    let body = '';































































    req.on('data', chunk => {































































      body += chunk.toString();































































    });































































    































































    req.on('end', () => {































































      console.log('Reverse connection data:', body);































































      this.emit('reverse-connection', body);































































      































































      res.writeHead(101, {































































        'Upgrade': 'PTTH/1.0',































































        'Connection': 'Upgrade'































































      });































































      res.end();































































    });































































  }































































































































  handlePlay(req, res) {































































    console.log('▶️ iPhone starting playback');































































    































































    let body = '';































































    req.on('data', chunk => {































































      body += chunk.toString();































































    });































































    































































    req.on('end', () => {































































      this.emit('playback-started', body);































































      res.writeHead(200);































































      res.end();































































    });































































  }































































































































  handleScrub(req, res) {































































    console.log('⏯️ iPhone scrubbing');































































    this.emit('scrub');































































    res.writeHead(200);































































    res.end();































































  }































































































































  handleStop(req, res) {































































    console.log('⏹️ iPhone stopping playback');































































    this.emit('playback-stopped');































































    res.writeHead(200);































































    res.end();































































  }































































































































  startAdvertising() {































































    try {































































      const bonjour = require('bonjour-service')();































































      































































      const service = bonjour.publish({































































        name: 'iPhone Companion Pro',































































        type: 'airplay',































































        port: 7000,































































        txt: {































































          deviceid: 'AA:BB:CC:DD:EE:FF',































































          features: '0x5A7FFEE6',































































          flags: '0x4',































































          model: 'AppleTV3,1',































































          pi: '2e388006-13ba-4041-9a67-25dd4a43d536',































































          pk: '99d0c7dc673a69f5e0d8b2b7b8b1b1b1b1b1b1b1b1b1b1b1b1b1b1b1',































































          srcvers: '220.68',































































          vv: '2'































































        }































































      });































































      































































      console.log('✅ AirPlay service advertised via Bonjour');































































      































































      service.on('up', () => {































































        console.log('📡 AirPlay service is up');































































      });































































      































































    } catch (error) {































































      console.log('⚠️ Bonjour advertising failed:', error.message);































































      console.log('AirPlay server running but not advertised');































































    }































































  }































































































































  stop() {































































    if (this.server) {































































      this.server.close();































































      this.isRunning = false;































































      console.log('🛑 AirPlay server stopped');































































    }































































  }































































}































































































































module.exports = { EnhancedAirPlayServer };`;































































































































    fs.writeFileSync('src/main/services/EnhancedAirPlayServer.js', airplayCode);































































    console.log('✅ Enhanced AirPlay server created');































































  }































































































































  async configureAirPlayFirewall() {































































    try {































































      // Add firewall rules for AirPlay































































      await this.execPromise('netsh advfirewall firewall add rule name="iPhone Companion AirPlay" dir=in action=allow protocol=TCP localport=7000');































































      await this.execPromise('netsh advfirewall firewall add rule name="iPhone Companion Bonjour" dir=in action=allow protocol=UDP localport=5353');































































      































































      console.log('✅ Firewall rules configured for AirPlay');































































    } catch (error) {































































      console.log('⚠️ Firewall configuration failed (run as administrator)');































































    }































































  }































































































































  async fixUSB() {































































    console.log('🔌 FIXING USB CONNECTION...');































































    































































    try {































































      // Check iTunes installation































































      const itunesInstalled = await this.checkiTunes();































































      































































      if (!itunesInstalled) {































































        console.log('📦 iTunes not found - downloading...');































































        await this.downloadiTunes();































































      }































































      































































      // Start Apple Mobile Device Service































































      await this.startAppleServices();































































      































































      this.fixes.push('✅ USB connection fixed');































































      































































    } catch (error) {































































      console.log('❌ USB fix failed:', error.message);































































      this.errors.push('USB: ' + error.message);































































    }































































    console.log('');































































  }































































































































  async checkiTunes() {































































    try {































































      await this.execPromise('reg query "HKLM\\SOFTWARE\\Apple Inc.\\Apple Mobile Device Support"');































































      console.log('✅ iTunes/Apple Mobile Device Support found');































































      return true;































































    } catch (error) {































































      return false;































































    }































































  }































































































































  async downloadiTunes() {































































    console.log('🍎 Opening iTunes download page...');































































    await this.execPromise('start https://www.apple.com/itunes/download/win64');































































    console.log('⚠️ Please download and install iTunes, then run this script again');































































  }































































































































  async startAppleServices() {































































    const services = [































































      'Apple Mobile Device Service',































































      'iPod Service',































































      'Bonjour Service'































































    ];































































    































































    for (const service of services) {































































      try {































































        await this.execPromise(`sc start "${service}"`);































































        console.log(`✅ Started ${service}`);































































      } catch (error) {































































        console.log(`⚠️ Could not start ${service}`);































































      }































































    }































































  }































































































































  async setupMacOSVM() {































































    console.log('🖥️ SETTING UP MACOS VM...');































































    































































    try {































































      // Check if VMware is installed































































      const vmwareInstalled = await this.checkVMware();































































      































































      if (!vmwareInstalled) {































































        console.log('📦 VMware not found');































































        console.log('⚠️ Run setup-macos-vm.bat to install VMware and create VM');































































      } else {































































        console.log('✅ VMware found');































































        































































        // Test VM manager































































        const { MacOSVMManager } = require('./vm-manager');































































        const vmManager = new MacOSVMManager();































































        































































        try {































































          await vmManager.initialize();































































          this.fixes.push('✅ macOS VM connected');































































        } catch (error) {































































          console.log('⚠️ VM not ready:', error.message);































































          this.errors.push('macOS VM: ' + error.message);































































        }































































      }































































      































































    } catch (error) {































































      console.log('❌ macOS VM setup failed:', error.message);































































      this.errors.push('macOS VM: ' + error.message);































































    }































































    console.log('');































































  }































































































































  async checkVMware() {































































    try {































































      const vmwarePath = 'C:\\Program Files (x86)\\VMware\\VMware Workstation\\vmware.exe';































































      return fs.existsSync(vmwarePath);































































    } catch (error) {































































      return false;































































    }































































  }































































































































  generateFixReport() {































































    console.log('📊 CONNECTION FIX REPORT\n');































































    































































    if (this.fixes.length > 0) {































































      console.log('✅ FIXES APPLIED:');































































      this.fixes.forEach(fix => console.log(`   ${fix}`));































































    }































































    































































    if (this.errors.length > 0) {































































      console.log('\n❌ REMAINING ISSUES:');































































      this.errors.forEach(error => console.log(`   ${error}`));































































    }































































    































































    console.log('\n🎯 NEXT STEPS:');































































    console.log('1. Run: node complete-connection-test.js');































































    console.log('2. If all connections work: npm start');































































    console.log('3. For macOS VM: run setup-macos-vm.bat');































































    































































    console.log('\n📱 IPHONE CHECKLIST:');































































    console.log('□ iPhone connected via USB');































































    console.log('□ "Trust This Computer" tapped');































































    console.log('□ Personal Hotspot enabled');































































    console.log('□ Bluetooth enabled');































































    console.log('□ Check AirPlay in Control Center');































































  }































































































































  execPromise(command) {































































    return new Promise((resolve, reject) => {































































      exec(command, (error, stdout, stderr) => {































































        if (error) {































































          reject(error);































































        } else {































































          resolve(stdout);































































        }































































      });































































    });































































  }































































































































  sleep(ms) {































































    return new Promise(resolve => setTimeout(resolve, ms));































































  }































































}































































































































// Run the fixer































































const fixer = new ConnectionFixer();































































fixer.fixAllConnections().catch(console.error);































































=======















// Fix All Connection Issues - iPhone Companion Pro































const { exec, spawn } = require('child_process');































const fs = require('fs');































const path = require('path');































const http = require('http');































































class ConnectionFixer {































  constructor() {































    this.fixes = [];































    this.errors = [];































  }































































  async fixAllConnections() {































    console.log('🔧 Fixing ALL iPhone connection issues...\n');































    































    // Fix Phone Link































    await this.fixPhoneLink();































    































    // Fix AirPlay































    await this.fixAirPlay();































    































    // Fix USB































    await this.fixUSB();































    































    // Setup macOS VM































    await this.setupMacOSVM();































    































    // Generate report































    this.generateFixReport();































  }































































  async fixPhoneLink() {































    console.log('🔗 FIXING PHONE LINK...');































    































    try {































      // Check if Phone Link is installed































      const phoneLink = await this.findPhoneLinkInstallation();































      































      if (!phoneLink) {































        console.log('📦 Installing Phone Link...');































        await this.installPhoneLink();































      }































      































      // Start Phone Link service































      await this.startPhoneLinkService();































      































      // Create enhanced Phone Link bridge































      await this.createPhoneLinkBridge();































      































      this.fixes.push('✅ Phone Link fixed and enhanced');































      































    } catch (error) {































      console.log('❌ Phone Link fix failed:', error.message);































      this.errors.push('Phone Link: ' + error.message);































    }































    console.log('');































  }































































  async findPhoneLinkInstallation() {































    const possiblePaths = [































      'C:\\Program Files\\WindowsApps\\Microsoft.YourPhone_*',































      'C:\\Program Files (x86)\\Microsoft\\YourPhone',































      path.join(process.env.LOCALAPPDATA, 'Microsoft\\WindowsApps\\YourPhone.exe')































    ];































    































    for (const searchPath of possiblePaths) {































      try {































        if (searchPath.includes('*')) {































          // Use dir command for wildcard search































          const result = await this.execPromise(`dir "${searchPath}" /b`);































          if (result.trim()) {































            console.log('✅ Found Phone Link installation');































            return true;































          }































        } else if (fs.existsSync(searchPath)) {































          console.log('✅ Found Phone Link at:', searchPath);































          return true;































        }































      } catch (e) {































        // Continue searching































      }































    }































    































    return false;































  }































































  async installPhoneLink() {































    try {































      // Try to install via Microsoft Store































      await this.execPromise('start ms-windows-store://pdp/?productid=9NMPJ99VJBWV');































      console.log('📱 Opening Microsoft Store for Phone Link installation...');































      console.log('⚠️ Please install Phone Link from the Store and run this script again');































      































      // Alternative: try winget































      try {































        await this.execPromise('winget install Microsoft.YourPhone');































        console.log('✅ Phone Link installed via winget');































      } catch (e) {































        console.log('⚠️ Manual installation required from Microsoft Store');































      }































      































    } catch (error) {































      throw new Error('Failed to install Phone Link');































    }































  }































































  async startPhoneLinkService() {































    try {































      // Start Phone Link app































      await this.execPromise('start YourPhone:');































      console.log('✅ Phone Link app started');































      































      // Wait for it to initialize































      await this.sleep(3000);































      































      // Verify it's running































      const processes = await this.execPromise('tasklist | findstr YourPhone');































      if (processes.includes('YourPhone')) {































        console.log('✅ Phone Link is running');































      } else {































        throw new Error('Phone Link failed to start');































      }































      































    } catch (error) {































      console.log('⚠️ Starting Phone Link manually...');































      // Try alternative methods































      try {































        await this.execPromise('start shell:AppsFolder\\Microsoft.YourPhone_8wekyb3d8bbwe!App');































      } catch (e) {































        throw new Error('Could not start Phone Link');































      }































    }































  }































































  async createPhoneLinkBridge() {































    const bridgeCode = `// Enhanced Phone Link Bridge































const sqlite3 = require('sqlite3');































const fs = require('fs');































const path = require('path');































const { EventEmitter } = require('events');































































class EnhancedPhoneLinkBridge extends EventEmitter {































  constructor() {































    super();































    this.isConnected = false;































    this.databases = [];































    this.watchedPaths = [];































  }































































  async connect() {































    console.log('🔗 Connecting to Phone Link...');































    































    // Find all possible Phone Link data locations































    const searchPaths = [































      path.join(process.env.LOCALAPPDATA, 'Packages'),































      path.join(process.env.APPDATA, 'Microsoft'),































      path.join(process.env.PROGRAMDATA, 'Microsoft')































    ];































    































    for (const basePath of searchPaths) {































      await this.scanForPhoneLinkData(basePath);































    }































    































    if (this.databases.length > 0) {































      this.isConnected = true;































      console.log(\`✅ Found \${this.databases.length} Phone Link databases\`);































      this.startMonitoring();































    } else {































      console.log('❌ No Phone Link data found');































    }































  }































































  async scanForPhoneLinkData(basePath) {































    if (!fs.existsSync(basePath)) return;































    































    try {































      const items = fs.readdirSync(basePath);































      































      for (const item of items) {































        if (item.includes('YourPhone') || item.includes('WindowsCommunications')) {































          const fullPath = path.join(basePath, item);































          































          if (fs.statSync(fullPath).isDirectory()) {































            await this.scanDirectory(fullPath);































          }































        }































      }































    } catch (e) {































      // Permission denied or other error































    }































  }































































  async scanDirectory(dirPath) {































    try {































      const files = fs.readdirSync(dirPath, { recursive: true });































      































      for (const file of files) {































        if (file.endsWith('.db') || file.endsWith('.sqlite')) {































          const fullPath = path.join(dirPath, file);































          































          try {































            // Test if it's a valid SQLite database































            const db = new sqlite3.Database(fullPath, sqlite3.OPEN_READONLY);































            































            await new Promise((resolve) => {































              db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {































                if (!err && tables.length > 0) {































                  console.log(\`📁 Found database: \${fullPath}\`);































                  console.log(\`   Tables: \${tables.map(t => t.name).join(', ')}\`);































                  































                  this.databases.push({































                    path: fullPath,































                    tables: tables.map(t => t.name),































                    type: this.detectDatabaseType(tables.map(t => t.name))































                  });































                }































                db.close();































                resolve();































              });































            });































            































          } catch (e) {































            // Not a valid SQLite database































          }































        }































      }































    } catch (e) {































      // Permission denied or other error































    }































  }































































  detectDatabaseType(tableNames) {































    const tables = tableNames.join(' ').toLowerCase();































    































    if (tables.includes('message') || tables.includes('sms')) {































      return 'messages';































    } else if (tables.includes('call') || tables.includes('phone')) {































      return 'calls';































    } else if (tables.includes('contact')) {































      return 'contacts';































    } else {































      return 'unknown';































    }































  }































































  async getMessages() {































    const messages = [];































    































    for (const dbInfo of this.databases) {































      if (dbInfo.type === 'messages') {































        try {































          const dbMessages = await this.extractMessagesFromDB(dbInfo);































          messages.push(...dbMessages);































        } catch (e) {































          console.log(\`Error reading \${dbInfo.path}: \${e.message}\`);































        }































      }































    }































    































    return messages;































  }































































  async extractMessagesFromDB(dbInfo) {































    return new Promise((resolve) => {































      const db = new sqlite3.Database(dbInfo.path, sqlite3.OPEN_READONLY);































      const messages = [];































      































      // Try common message table patterns































      const queries = [































        "SELECT * FROM messages ORDER BY timestamp DESC LIMIT 100",































        "SELECT * FROM message ORDER BY date DESC LIMIT 100",































        "SELECT * FROM sms ORDER BY time DESC LIMIT 100"































      ];































      































      let queryIndex = 0;































      































      const tryNextQuery = () => {































        if (queryIndex >= queries.length) {































          db.close();































          resolve(messages);































          return;































        }































        































        const query = queries[queryIndex++];































        































        db.all(query, (err, rows) => {































          if (!err && rows.length > 0) {































            console.log(\`✅ Found \${rows.length} messages in \${dbInfo.path}\`);































            































            rows.forEach(row => {































              messages.push({































                id: row.id || row.rowid || Date.now(),































                text: row.text || row.body || row.content,































                phoneNumber: row.phone || row.number || row.address,































                timestamp: new Date(row.timestamp || row.date || row.time || Date.now()),































                isIncoming: !row.is_outgoing && !row.is_sent,































                source: 'phoneLink'































              });































            });































            































            db.close();































            resolve(messages);































          } else {































            tryNextQuery();































          }































        });































      };































      































      tryNextQuery();































    });































  }































































  startMonitoring() {































    // Monitor database files for changes































    this.databases.forEach(dbInfo => {































      try {































        fs.watchFile(dbInfo.path, (curr, prev) => {































          if (curr.mtime > prev.mtime) {































            console.log('📨 Phone Link database updated');































            this.emit('database-updated', dbInfo);































          }































        });































        































        this.watchedPaths.push(dbInfo.path);































      } catch (e) {































        // File watching failed































      }































    });































  }































































  stop() {































    this.watchedPaths.forEach(path => {































      fs.unwatchFile(path);































    });































    this.watchedPaths = [];































  }































}































































module.exports = { EnhancedPhoneLinkBridge };`;































































    fs.writeFileSync('src/main/services/EnhancedPhoneLinkBridge.js', bridgeCode);































    console.log('✅ Enhanced Phone Link bridge created');































  }































































  async fixAirPlay() {































    console.log('📺 FIXING AIRPLAY...');































    































    try {































      // Check and fix port 7000































      await this.fixAirPlayPort();































      































      // Create enhanced AirPlay server































      await this.createEnhancedAirPlayServer();































      































      // Configure firewall































      await this.configureAirPlayFirewall();































      































      this.fixes.push('✅ AirPlay fixed and enhanced');































      































    } catch (error) {































      console.log('❌ AirPlay fix failed:', error.message);































      this.errors.push('AirPlay: ' + error.message);































    }































    console.log('');































  }































































  async fixAirPlayPort() {































    try {































      // Check what's using port 7000































      const netstat = await this.execPromise('netstat -ano | findstr :7000');































      































      if (netstat.trim()) {































        console.log('⚠️ Port 7000 in use:', netstat.trim());































        































        // Extract PID and kill if necessary































        const lines = netstat.split('\n');































        for (const line of lines) {































          const parts = line.trim().split(/\s+/);































          if (parts.length >= 5) {































            const pid = parts[4];































            console.log(`Killing process ${pid} using port 7000...`);































            try {































              await this.execPromise(`taskkill /PID ${pid} /F`);































            } catch (e) {































              // Process might already be dead































            }































          }































        }































      }































      































      console.log('✅ Port 7000 is now available');































      































    } catch (error) {































      // Port is available































      console.log('✅ Port 7000 is available');































    }































  }































































  async createEnhancedAirPlayServer() {































    const airplayCode = `// Enhanced AirPlay Server































const http = require('http');































const { EventEmitter } = require('events');































































class EnhancedAirPlayServer extends EventEmitter {































  constructor() {































    super();































    this.server = null;































    this.isRunning = false;































    this.connectedDevices = new Set();































  }































































  start() {































    console.log('📺 Starting Enhanced AirPlay Server...');































    































    this.server = http.createServer((req, res) => {































      console.log(\`AirPlay request: \${req.method} \${req.url}\`);































      































      // Set CORS headers































      res.setHeader('Access-Control-Allow-Origin', '*');































      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');































      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');































      































      if (req.method === 'OPTIONS') {































        res.writeHead(200);































        res.end();































        return;































      }































      































      switch (req.url) {































        case '/server-info':































          this.handleServerInfo(req, res);































          break;































        case '/reverse':































          this.handleReverse(req, res);































          break;































        case '/play':































          this.handlePlay(req, res);































          break;































        case '/scrub':































          this.handleScrub(req, res);































          break;































        case '/stop':































          this.handleStop(req, res);































          break;































        default:































          res.writeHead(404);































          res.end('Not Found');































      }































    });































    































    this.server.listen(7000, '0.0.0.0', () => {































      console.log('✅ AirPlay server listening on port 7000');































      this.isRunning = true;































      this.emit('server-started');































      































      // Start advertising































      this.startAdvertising();































    });































    































    this.server.on('error', (error) => {































      console.error('AirPlay server error:', error);































      this.emit('server-error', error);































    });































  }































































  handleServerInfo(req, res) {































    const serverInfo = \`<?xml version="1.0" encoding="UTF-8"?>































<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">































<plist version="1.0">































<dict>































  <key>deviceid</key>































  <string>AA:BB:CC:DD:EE:FF</string>































  <key>features</key>































  <integer>0x5A7FFEE6</integer>































  <key>model</key>































  <string>AppleTV3,1</string>































  <key>protovers</key>































  <string>1.0</string>































  <key>srcvers</key>































  <string>220.68</string>































  <key>vv</key>































  <integer>2</integer>































</dict>































</plist>\`;































    































    res.writeHead(200, { 'Content-Type': 'text/x-apple-plist+xml' });































    res.end(serverInfo);































  }































































  handleReverse(req, res) {































    console.log('📱 iPhone requesting reverse connection');































    































    let body = '';































    req.on('data', chunk => {































      body += chunk.toString();































    });































    































    req.on('end', () => {































      console.log('Reverse connection data:', body);































      this.emit('reverse-connection', body);































      































      res.writeHead(101, {































        'Upgrade': 'PTTH/1.0',































        'Connection': 'Upgrade'































      });































      res.end();































    });































  }































































  handlePlay(req, res) {































    console.log('▶️ iPhone starting playback');































    































    let body = '';































    req.on('data', chunk => {































      body += chunk.toString();































    });































    































    req.on('end', () => {































      this.emit('playback-started', body);































      res.writeHead(200);































      res.end();































    });































  }































































  handleScrub(req, res) {































    console.log('⏯️ iPhone scrubbing');































    this.emit('scrub');































    res.writeHead(200);































    res.end();































  }































































  handleStop(req, res) {































    console.log('⏹️ iPhone stopping playback');































    this.emit('playback-stopped');































    res.writeHead(200);































    res.end();































  }































































  startAdvertising() {































    try {































      const bonjour = require('bonjour-service')();































      































      const service = bonjour.publish({































        name: 'iPhone Companion Pro',































        type: 'airplay',































        port: 7000,































        txt: {































          deviceid: 'AA:BB:CC:DD:EE:FF',































          features: '0x5A7FFEE6',































          flags: '0x4',































          model: 'AppleTV3,1',































          pi: '2e388006-13ba-4041-9a67-25dd4a43d536',































          pk: '99d0c7dc673a69f5e0d8b2b7b8b1b1b1b1b1b1b1b1b1b1b1b1b1b1b1',































          srcvers: '220.68',































          vv: '2'































        }































      });































      































      console.log('✅ AirPlay service advertised via Bonjour');































      































      service.on('up', () => {































        console.log('📡 AirPlay service is up');































      });































      































    } catch (error) {































      console.log('⚠️ Bonjour advertising failed:', error.message);































      console.log('AirPlay server running but not advertised');































    }































  }































































  stop() {































    if (this.server) {































      this.server.close();































      this.isRunning = false;































      console.log('🛑 AirPlay server stopped');































    }































  }































}































































module.exports = { EnhancedAirPlayServer };`;































































    fs.writeFileSync('src/main/services/EnhancedAirPlayServer.js', airplayCode);































    console.log('✅ Enhanced AirPlay server created');































  }































































  async configureAirPlayFirewall() {































    try {































      // Add firewall rules for AirPlay































      await this.execPromise('netsh advfirewall firewall add rule name="iPhone Companion AirPlay" dir=in action=allow protocol=TCP localport=7000');































      await this.execPromise('netsh advfirewall firewall add rule name="iPhone Companion Bonjour" dir=in action=allow protocol=UDP localport=5353');































      































      console.log('✅ Firewall rules configured for AirPlay');































    } catch (error) {































      console.log('⚠️ Firewall configuration failed (run as administrator)');































    }































  }































































  async fixUSB() {































    console.log('🔌 FIXING USB CONNECTION...');































    































    try {































      // Check iTunes installation































      const itunesInstalled = await this.checkiTunes();































      































      if (!itunesInstalled) {































        console.log('📦 iTunes not found - downloading...');































        await this.downloadiTunes();































      }































      































      // Start Apple Mobile Device Service































      await this.startAppleServices();































      































      this.fixes.push('✅ USB connection fixed');































      































    } catch (error) {































      console.log('❌ USB fix failed:', error.message);































      this.errors.push('USB: ' + error.message);































    }































    console.log('');































  }































































  async checkiTunes() {































    try {































      await this.execPromise('reg query "HKLM\\SOFTWARE\\Apple Inc.\\Apple Mobile Device Support"');































      console.log('✅ iTunes/Apple Mobile Device Support found');































      return true;































    } catch (error) {































      return false;































    }































  }































































  async downloadiTunes() {































    console.log('🍎 Opening iTunes download page...');































    await this.execPromise('start https://www.apple.com/itunes/download/win64');































    console.log('⚠️ Please download and install iTunes, then run this script again');































  }































































  async startAppleServices() {































    const services = [































      'Apple Mobile Device Service',































      'iPod Service',































      'Bonjour Service'































    ];































    































    for (const service of services) {































      try {































        await this.execPromise(`sc start "${service}"`);































        console.log(`✅ Started ${service}`);































      } catch (error) {































        console.log(`⚠️ Could not start ${service}`);































      }































    }































  }































































  async setupMacOSVM() {































    console.log('🖥️ SETTING UP MACOS VM...');































    































    try {































      // Check if VMware is installed































      const vmwareInstalled = await this.checkVMware();































      































      if (!vmwareInstalled) {































        console.log('📦 VMware not found');































        console.log('⚠️ Run setup-macos-vm.bat to install VMware and create VM');































      } else {































        console.log('✅ VMware found');































        































        // Test VM manager































        const { MacOSVMManager } = require('./vm-manager');































        const vmManager = new MacOSVMManager();































        































        try {































          await vmManager.initialize();































          this.fixes.push('✅ macOS VM connected');































        } catch (error) {































          console.log('⚠️ VM not ready:', error.message);































          this.errors.push('macOS VM: ' + error.message);































        }































      }































      































    } catch (error) {































      console.log('❌ macOS VM setup failed:', error.message);































      this.errors.push('macOS VM: ' + error.message);































    }































    console.log('');































  }































































  async checkVMware() {































    try {































      const vmwarePath = 'C:\\Program Files (x86)\\VMware\\VMware Workstation\\vmware.exe';































      return fs.existsSync(vmwarePath);































    } catch (error) {































      return false;































    }































  }































































  generateFixReport() {































    console.log('📊 CONNECTION FIX REPORT\n');































    































    if (this.fixes.length > 0) {































      console.log('✅ FIXES APPLIED:');































      this.fixes.forEach(fix => console.log(`   ${fix}`));































    }































    































    if (this.errors.length > 0) {































      console.log('\n❌ REMAINING ISSUES:');































      this.errors.forEach(error => console.log(`   ${error}`));































    }































    































    console.log('\n🎯 NEXT STEPS:');































    console.log('1. Run: node complete-connection-test.js');































    console.log('2. If all connections work: npm start');































    console.log('3. For macOS VM: run setup-macos-vm.bat');































    































    console.log('\n📱 IPHONE CHECKLIST:');































    console.log('□ iPhone connected via USB');































    console.log('□ "Trust This Computer" tapped');































    console.log('□ Personal Hotspot enabled');































    console.log('□ Bluetooth enabled');































    console.log('□ Check AirPlay in Control Center');































  }































































  execPromise(command) {































    return new Promise((resolve, reject) => {































      exec(command, (error, stdout, stderr) => {































        if (error) {































          reject(error);































        } else {































          resolve(stdout);































        }































      });































    });































  }































































  sleep(ms) {































    return new Promise(resolve => setTimeout(resolve, ms));































  }































}































































// Run the fixer































const fixer = new ConnectionFixer();































fixer.fixAllConnections().catch(console.error);































>>>>>>>















=======



// Fix All Connection Issues - iPhone Companion Pro







const { exec, spawn } = require('child_process');







const fs = require('fs');







const path = require('path');







const http = require('http');















class ConnectionFixer {







  constructor() {







    this.fixes = [];







    this.errors = [];







  }















  async fixAllConnections() {







    console.log('🔧 Fixing ALL iPhone connection issues...\n');







    







    // Fix Phone Link







    await this.fixPhoneLink();







    







    // Fix AirPlay







    await this.fixAirPlay();







    







    // Fix USB







    await this.fixUSB();







    







    // Setup macOS VM







    await this.setupMacOSVM();







    







    // Generate report







    this.generateFixReport();







  }















  async fixPhoneLink() {







    console.log('🔗 FIXING PHONE LINK...');







    







    try {







      // Check if Phone Link is installed







      const phoneLink = await this.findPhoneLinkInstallation();







      







      if (!phoneLink) {







        console.log('📦 Installing Phone Link...');







        await this.installPhoneLink();







      }







      







      // Start Phone Link service







      await this.startPhoneLinkService();







      







      // Create enhanced Phone Link bridge







      await this.createPhoneLinkBridge();







      







      this.fixes.push('✅ Phone Link fixed and enhanced');







      







    } catch (error) {







      console.log('❌ Phone Link fix failed:', error.message);







      this.errors.push('Phone Link: ' + error.message);







    }







    console.log('');







  }















  async findPhoneLinkInstallation() {







    const possiblePaths = [







      'C:\\Program Files\\WindowsApps\\Microsoft.YourPhone_*',







      'C:\\Program Files (x86)\\Microsoft\\YourPhone',







      path.join(process.env.LOCALAPPDATA, 'Microsoft\\WindowsApps\\YourPhone.exe')







    ];







    







    for (const searchPath of possiblePaths) {







      try {







        if (searchPath.includes('*')) {







          // Use dir command for wildcard search







          const result = await this.execPromise(`dir "${searchPath}" /b`);







          if (result.trim()) {







            console.log('✅ Found Phone Link installation');







            return true;







          }







        } else if (fs.existsSync(searchPath)) {







          console.log('✅ Found Phone Link at:', searchPath);







          return true;







        }







      } catch (e) {







        // Continue searching







      }







    }







    







    return false;







  }















  async installPhoneLink() {







    try {







      // Try to install via Microsoft Store







      await this.execPromise('start ms-windows-store://pdp/?productid=9NMPJ99VJBWV');







      console.log('📱 Opening Microsoft Store for Phone Link installation...');







      console.log('⚠️ Please install Phone Link from the Store and run this script again');







      







      // Alternative: try winget







      try {







        await this.execPromise('winget install Microsoft.YourPhone');







        console.log('✅ Phone Link installed via winget');







      } catch (e) {







        console.log('⚠️ Manual installation required from Microsoft Store');







      }







      







    } catch (error) {







      throw new Error('Failed to install Phone Link');







    }







  }















  async startPhoneLinkService() {







    try {







      // Start Phone Link app







      await this.execPromise('start YourPhone:');







      console.log('✅ Phone Link app started');







      







      // Wait for it to initialize







      await this.sleep(3000);







      







      // Verify it's running







      const processes = await this.execPromise('tasklist | findstr YourPhone');







      if (processes.includes('YourPhone')) {







        console.log('✅ Phone Link is running');







      } else {







        throw new Error('Phone Link failed to start');







      }







      







    } catch (error) {







      console.log('⚠️ Starting Phone Link manually...');







      // Try alternative methods







      try {







        await this.execPromise('start shell:AppsFolder\\Microsoft.YourPhone_8wekyb3d8bbwe!App');







      } catch (e) {







        throw new Error('Could not start Phone Link');







      }







    }







  }















  async createPhoneLinkBridge() {







    const bridgeCode = `// Enhanced Phone Link Bridge







const sqlite3 = require('sqlite3');







const fs = require('fs');







const path = require('path');







const { EventEmitter } = require('events');















class EnhancedPhoneLinkBridge extends EventEmitter {







  constructor() {







    super();







    this.isConnected = false;







    this.databases = [];







    this.watchedPaths = [];







  }















  async connect() {







    console.log('🔗 Connecting to Phone Link...');







    







    // Find all possible Phone Link data locations







    const searchPaths = [







      path.join(process.env.LOCALAPPDATA, 'Packages'),







      path.join(process.env.APPDATA, 'Microsoft'),







      path.join(process.env.PROGRAMDATA, 'Microsoft')







    ];







    







    for (const basePath of searchPaths) {







      await this.scanForPhoneLinkData(basePath);







    }







    







    if (this.databases.length > 0) {







      this.isConnected = true;







      console.log(\`✅ Found \${this.databases.length} Phone Link databases\`);







      this.startMonitoring();







    } else {







      console.log('❌ No Phone Link data found');







    }







  }















  async scanForPhoneLinkData(basePath) {







    if (!fs.existsSync(basePath)) return;







    







    try {







      const items = fs.readdirSync(basePath);







      







      for (const item of items) {







        if (item.includes('YourPhone') || item.includes('WindowsCommunications')) {







          const fullPath = path.join(basePath, item);







          







          if (fs.statSync(fullPath).isDirectory()) {







            await this.scanDirectory(fullPath);







          }







        }







      }







    } catch (e) {







      // Permission denied or other error







    }







  }















  async scanDirectory(dirPath) {







    try {







      const files = fs.readdirSync(dirPath, { recursive: true });







      







      for (const file of files) {







        if (file.endsWith('.db') || file.endsWith('.sqlite')) {







          const fullPath = path.join(dirPath, file);







          







          try {







            // Test if it's a valid SQLite database







            const db = new sqlite3.Database(fullPath, sqlite3.OPEN_READONLY);







            







            await new Promise((resolve) => {







              db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {







                if (!err && tables.length > 0) {







                  console.log(\`📁 Found database: \${fullPath}\`);







                  console.log(\`   Tables: \${tables.map(t => t.name).join(', ')}\`);







                  







                  this.databases.push({







                    path: fullPath,







                    tables: tables.map(t => t.name),







                    type: this.detectDatabaseType(tables.map(t => t.name))







                  });







                }







                db.close();







                resolve();







              });







            });







            







          } catch (e) {







            // Not a valid SQLite database







          }







        }







      }







    } catch (e) {







      // Permission denied or other error







    }







  }















  detectDatabaseType(tableNames) {







    const tables = tableNames.join(' ').toLowerCase();







    







    if (tables.includes('message') || tables.includes('sms')) {







      return 'messages';







    } else if (tables.includes('call') || tables.includes('phone')) {







      return 'calls';







    } else if (tables.includes('contact')) {







      return 'contacts';







    } else {







      return 'unknown';







    }







  }















  async getMessages() {







    const messages = [];







    







    for (const dbInfo of this.databases) {







      if (dbInfo.type === 'messages') {







        try {







          const dbMessages = await this.extractMessagesFromDB(dbInfo);







          messages.push(...dbMessages);







        } catch (e) {







          console.log(\`Error reading \${dbInfo.path}: \${e.message}\`);







        }







      }







    }







    







    return messages;







  }















  async extractMessagesFromDB(dbInfo) {







    return new Promise((resolve) => {







      const db = new sqlite3.Database(dbInfo.path, sqlite3.OPEN_READONLY);







      const messages = [];







      







      // Try common message table patterns







      const queries = [







        "SELECT * FROM messages ORDER BY timestamp DESC LIMIT 100",







        "SELECT * FROM message ORDER BY date DESC LIMIT 100",







        "SELECT * FROM sms ORDER BY time DESC LIMIT 100"







      ];







      







      let queryIndex = 0;







      







      const tryNextQuery = () => {







        if (queryIndex >= queries.length) {







          db.close();







          resolve(messages);







          return;







        }







        







        const query = queries[queryIndex++];







        







        db.all(query, (err, rows) => {







          if (!err && rows.length > 0) {







            console.log(\`✅ Found \${rows.length} messages in \${dbInfo.path}\`);







            







            rows.forEach(row => {







              messages.push({







                id: row.id || row.rowid || Date.now(),







                text: row.text || row.body || row.content,







                phoneNumber: row.phone || row.number || row.address,







                timestamp: new Date(row.timestamp || row.date || row.time || Date.now()),







                isIncoming: !row.is_outgoing && !row.is_sent,







                source: 'phoneLink'







              });







            });







            







            db.close();







            resolve(messages);







          } else {







            tryNextQuery();







          }







        });







      };







      







      tryNextQuery();







    });







  }















  startMonitoring() {







    // Monitor database files for changes







    this.databases.forEach(dbInfo => {







      try {







        fs.watchFile(dbInfo.path, (curr, prev) => {







          if (curr.mtime > prev.mtime) {







            console.log('📨 Phone Link database updated');







            this.emit('database-updated', dbInfo);







          }







        });







        







        this.watchedPaths.push(dbInfo.path);







      } catch (e) {







        // File watching failed







      }







    });







  }















  stop() {







    this.watchedPaths.forEach(path => {







      fs.unwatchFile(path);







    });







    this.watchedPaths = [];







  }







}















module.exports = { EnhancedPhoneLinkBridge };`;















    fs.writeFileSync('src/main/services/EnhancedPhoneLinkBridge.js', bridgeCode);







    console.log('✅ Enhanced Phone Link bridge created');







  }















  async fixAirPlay() {







    console.log('📺 FIXING AIRPLAY...');







    







    try {







      // Check and fix port 7000







      await this.fixAirPlayPort();







      







      // Create enhanced AirPlay server







      await this.createEnhancedAirPlayServer();







      







      // Configure firewall







      await this.configureAirPlayFirewall();







      







      this.fixes.push('✅ AirPlay fixed and enhanced');







      







    } catch (error) {







      console.log('❌ AirPlay fix failed:', error.message);







      this.errors.push('AirPlay: ' + error.message);







    }







    console.log('');







  }















  async fixAirPlayPort() {







    try {







      // Check what's using port 7000







      const netstat = await this.execPromise('netstat -ano | findstr :7000');







      







      if (netstat.trim()) {







        console.log('⚠️ Port 7000 in use:', netstat.trim());







        







        // Extract PID and kill if necessary







        const lines = netstat.split('\n');







        for (const line of lines) {







          const parts = line.trim().split(/\s+/);







          if (parts.length >= 5) {







            const pid = parts[4];







            console.log(`Killing process ${pid} using port 7000...`);







            try {







              await this.execPromise(`taskkill /PID ${pid} /F`);







            } catch (e) {







              // Process might already be dead







            }







          }







        }







      }







      







      console.log('✅ Port 7000 is now available');







      







    } catch (error) {







      // Port is available







      console.log('✅ Port 7000 is available');







    }







  }















  async createEnhancedAirPlayServer() {







    const airplayCode = `// Enhanced AirPlay Server







const http = require('http');







const { EventEmitter } = require('events');















class EnhancedAirPlayServer extends EventEmitter {







  constructor() {







    super();







    this.server = null;







    this.isRunning = false;







    this.connectedDevices = new Set();







  }















  start() {







    console.log('📺 Starting Enhanced AirPlay Server...');







    







    this.server = http.createServer((req, res) => {







      console.log(\`AirPlay request: \${req.method} \${req.url}\`);







      







      // Set CORS headers







      res.setHeader('Access-Control-Allow-Origin', '*');







      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');







      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');







      







      if (req.method === 'OPTIONS') {







        res.writeHead(200);







        res.end();







        return;







      }







      







      switch (req.url) {







        case '/server-info':







          this.handleServerInfo(req, res);







          break;







        case '/reverse':







          this.handleReverse(req, res);







          break;







        case '/play':







          this.handlePlay(req, res);







          break;







        case '/scrub':







          this.handleScrub(req, res);







          break;







        case '/stop':







          this.handleStop(req, res);







          break;







        default:







          res.writeHead(404);







          res.end('Not Found');







      }







    });







    







    this.server.listen(7000, '0.0.0.0', () => {







      console.log('✅ AirPlay server listening on port 7000');







      this.isRunning = true;







      this.emit('server-started');







      







      // Start advertising







      this.startAdvertising();







    });







    







    this.server.on('error', (error) => {







      console.error('AirPlay server error:', error);







      this.emit('server-error', error);







    });







  }















  handleServerInfo(req, res) {







    const serverInfo = \`<?xml version="1.0" encoding="UTF-8"?>







<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">







<plist version="1.0">







<dict>







  <key>deviceid</key>







  <string>AA:BB:CC:DD:EE:FF</string>







  <key>features</key>







  <integer>0x5A7FFEE6</integer>







  <key>model</key>







  <string>AppleTV3,1</string>







  <key>protovers</key>







  <string>1.0</string>







  <key>srcvers</key>







  <string>220.68</string>







  <key>vv</key>







  <integer>2</integer>







</dict>







</plist>\`;







    







    res.writeHead(200, { 'Content-Type': 'text/x-apple-plist+xml' });







    res.end(serverInfo);







  }















  handleReverse(req, res) {







    console.log('📱 iPhone requesting reverse connection');







    







    let body = '';







    req.on('data', chunk => {







      body += chunk.toString();







    });







    







    req.on('end', () => {







      console.log('Reverse connection data:', body);







      this.emit('reverse-connection', body);







      







      res.writeHead(101, {







        'Upgrade': 'PTTH/1.0',







        'Connection': 'Upgrade'







      });







      res.end();







    });







  }















  handlePlay(req, res) {







    console.log('▶️ iPhone starting playback');







    







    let body = '';







    req.on('data', chunk => {







      body += chunk.toString();







    });







    







    req.on('end', () => {







      this.emit('playback-started', body);







      res.writeHead(200);







      res.end();







    });







  }















  handleScrub(req, res) {







    console.log('⏯️ iPhone scrubbing');







    this.emit('scrub');







    res.writeHead(200);







    res.end();







  }















  handleStop(req, res) {







    console.log('⏹️ iPhone stopping playback');







    this.emit('playback-stopped');







    res.writeHead(200);







    res.end();







  }















  startAdvertising() {







    try {







      const bonjour = require('bonjour-service')();







      







      const service = bonjour.publish({







        name: 'iPhone Companion Pro',







        type: 'airplay',







        port: 7000,







        txt: {







          deviceid: 'AA:BB:CC:DD:EE:FF',







          features: '0x5A7FFEE6',







          flags: '0x4',







          model: 'AppleTV3,1',







          pi: '2e388006-13ba-4041-9a67-25dd4a43d536',







          pk: '99d0c7dc673a69f5e0d8b2b7b8b1b1b1b1b1b1b1b1b1b1b1b1b1b1b1',







          srcvers: '220.68',







          vv: '2'







        }







      });







      







      console.log('✅ AirPlay service advertised via Bonjour');







      







      service.on('up', () => {







        console.log('📡 AirPlay service is up');







      });







      







    } catch (error) {







      console.log('⚠️ Bonjour advertising failed:', error.message);







      console.log('AirPlay server running but not advertised');







    }







  }















  stop() {







    if (this.server) {







      this.server.close();







      this.isRunning = false;







      console.log('🛑 AirPlay server stopped');







    }







  }







}















module.exports = { EnhancedAirPlayServer };`;















    fs.writeFileSync('src/main/services/EnhancedAirPlayServer.js', airplayCode);







    console.log('✅ Enhanced AirPlay server created');







  }















  async configureAirPlayFirewall() {







    try {







      // Add firewall rules for AirPlay







      await this.execPromise('netsh advfirewall firewall add rule name="iPhone Companion AirPlay" dir=in action=allow protocol=TCP localport=7000');







      await this.execPromise('netsh advfirewall firewall add rule name="iPhone Companion Bonjour" dir=in action=allow protocol=UDP localport=5353');







      







      console.log('✅ Firewall rules configured for AirPlay');







    } catch (error) {







      console.log('⚠️ Firewall configuration failed (run as administrator)');







    }







  }















  async fixUSB() {







    console.log('🔌 FIXING USB CONNECTION...');







    







    try {







      // Check iTunes installation







      const itunesInstalled = await this.checkiTunes();







      







      if (!itunesInstalled) {







        console.log('📦 iTunes not found - downloading...');







        await this.downloadiTunes();







      }







      







      // Start Apple Mobile Device Service







      await this.startAppleServices();







      







      this.fixes.push('✅ USB connection fixed');







      







    } catch (error) {







      console.log('❌ USB fix failed:', error.message);







      this.errors.push('USB: ' + error.message);







    }







    console.log('');







  }















  async checkiTunes() {







    try {







      await this.execPromise('reg query "HKLM\\SOFTWARE\\Apple Inc.\\Apple Mobile Device Support"');







      console.log('✅ iTunes/Apple Mobile Device Support found');







      return true;







    } catch (error) {







      return false;







    }







  }















  async downloadiTunes() {







    console.log('🍎 Opening iTunes download page...');







    await this.execPromise('start https://www.apple.com/itunes/download/win64');







    console.log('⚠️ Please download and install iTunes, then run this script again');







  }















  async startAppleServices() {







    const services = [







      'Apple Mobile Device Service',







      'iPod Service',







      'Bonjour Service'







    ];







    







    for (const service of services) {







      try {







        await this.execPromise(`sc start "${service}"`);







        console.log(`✅ Started ${service}`);







      } catch (error) {







        console.log(`⚠️ Could not start ${service}`);







      }







    }







  }















  async setupMacOSVM() {







    console.log('🖥️ SETTING UP MACOS VM...');







    







    try {







      // Check if VMware is installed







      const vmwareInstalled = await this.checkVMware();







      







      if (!vmwareInstalled) {







        console.log('📦 VMware not found');







        console.log('⚠️ Run setup-macos-vm.bat to install VMware and create VM');







      } else {







        console.log('✅ VMware found');







        







        // Test VM manager







        const { MacOSVMManager } = require('./vm-manager');







        const vmManager = new MacOSVMManager();







        







        try {







          await vmManager.initialize();







          this.fixes.push('✅ macOS VM connected');







        } catch (error) {







          console.log('⚠️ VM not ready:', error.message);







          this.errors.push('macOS VM: ' + error.message);







        }







      }







      







    } catch (error) {







      console.log('❌ macOS VM setup failed:', error.message);







      this.errors.push('macOS VM: ' + error.message);







    }







    console.log('');







  }















  async checkVMware() {







    try {







      const vmwarePath = 'C:\\Program Files (x86)\\VMware\\VMware Workstation\\vmware.exe';







      return fs.existsSync(vmwarePath);







    } catch (error) {







      return false;







    }







  }















  generateFixReport() {







    console.log('📊 CONNECTION FIX REPORT\n');







    







    if (this.fixes.length > 0) {







      console.log('✅ FIXES APPLIED:');







      this.fixes.forEach(fix => console.log(`   ${fix}`));







    }







    







    if (this.errors.length > 0) {







      console.log('\n❌ REMAINING ISSUES:');







      this.errors.forEach(error => console.log(`   ${error}`));







    }







    







    console.log('\n🎯 NEXT STEPS:');







    console.log('1. Run: node complete-connection-test.js');







    console.log('2. If all connections work: npm start');







    console.log('3. For macOS VM: run setup-macos-vm.bat');







    







    console.log('\n📱 IPHONE CHECKLIST:');







    console.log('□ iPhone connected via USB');







    console.log('□ "Trust This Computer" tapped');







    console.log('□ Personal Hotspot enabled');







    console.log('□ Bluetooth enabled');







    console.log('□ Check AirPlay in Control Center');







  }















  execPromise(command) {







    return new Promise((resolve, reject) => {







      exec(command, (error, stdout, stderr) => {







        if (error) {







          reject(error);







        } else {







          resolve(stdout);







        }







      });







    });







  }















  sleep(ms) {







    return new Promise(resolve => setTimeout(resolve, ms));







  }







}















// Run the fixer







const fixer = new ConnectionFixer();







fixer.fixAllConnections().catch(console.error);







>>>>>>>



=======
// Fix All Connection Issues - iPhone Companion Pro

const { exec, spawn } = require('child_process');

const fs = require('fs');

const path = require('path');

const http = require('http');



class ConnectionFixer {

  constructor() {

    this.fixes = [];

    this.errors = [];

  }



  async fixAllConnections() {

    console.log('🔧 Fixing ALL iPhone connection issues...\n');

    

    // Fix Phone Link

    await this.fixPhoneLink();

    

    // Fix AirPlay

    await this.fixAirPlay();

    

    // Fix USB

    await this.fixUSB();

    

    // Setup macOS VM

    await this.setupMacOSVM();

    

    // Generate report

    this.generateFixReport();

  }



  async fixPhoneLink() {

    console.log('🔗 FIXING PHONE LINK...');

    

    try {

      // Check if Phone Link is installed

      const phoneLink = await this.findPhoneLinkInstallation();

      

      if (!phoneLink) {

        console.log('📦 Installing Phone Link...');

        await this.installPhoneLink();

      }

      

      // Start Phone Link service

      await this.startPhoneLinkService();

      

      // Create enhanced Phone Link bridge

      await this.createPhoneLinkBridge();

      

      this.fixes.push('✅ Phone Link fixed and enhanced');

      

    } catch (error) {

      console.log('❌ Phone Link fix failed:', error.message);

      this.errors.push('Phone Link: ' + error.message);

    }

    console.log('');

  }



  async findPhoneLinkInstallation() {

    const possiblePaths = [

      'C:\\Program Files\\WindowsApps\\Microsoft.YourPhone_*',

      'C:\\Program Files (x86)\\Microsoft\\YourPhone',

      path.join(process.env.LOCALAPPDATA, 'Microsoft\\WindowsApps\\YourPhone.exe')

    ];

    

    for (const searchPath of possiblePaths) {

      try {

        if (searchPath.includes('*')) {

          // Use dir command for wildcard search

          const result = await this.execPromise(`dir "${searchPath}" /b`);

          if (result.trim()) {

            console.log('✅ Found Phone Link installation');

            return true;

          }

        } else if (fs.existsSync(searchPath)) {

          console.log('✅ Found Phone Link at:', searchPath);

          return true;

        }

      } catch (e) {

        // Continue searching

      }

    }

    

    return false;

  }



  async installPhoneLink() {

    try {

      // Try to install via Microsoft Store

      await this.execPromise('start ms-windows-store://pdp/?productid=9NMPJ99VJBWV');

      console.log('📱 Opening Microsoft Store for Phone Link installation...');

      console.log('⚠️ Please install Phone Link from the Store and run this script again');

      

      // Alternative: try winget

      try {

        await this.execPromise('winget install Microsoft.YourPhone');

        console.log('✅ Phone Link installed via winget');

      } catch (e) {

        console.log('⚠️ Manual installation required from Microsoft Store');

      }

      

    } catch (error) {

      throw new Error('Failed to install Phone Link');

    }

  }



  async startPhoneLinkService() {

    try {

      // Start Phone Link app

      await this.execPromise('start YourPhone:');

      console.log('✅ Phone Link app started');

      

      // Wait for it to initialize

      await this.sleep(3000);

      

      // Verify it's running

      const processes = await this.execPromise('tasklist | findstr YourPhone');

      if (processes.includes('YourPhone')) {

        console.log('✅ Phone Link is running');

      } else {

        throw new Error('Phone Link failed to start');

      }

      

    } catch (error) {

      console.log('⚠️ Starting Phone Link manually...');

      // Try alternative methods

      try {

        await this.execPromise('start shell:AppsFolder\\Microsoft.YourPhone_8wekyb3d8bbwe!App');

      } catch (e) {

        throw new Error('Could not start Phone Link');

      }

    }

  }



  async createPhoneLinkBridge() {

    const bridgeCode = `// Enhanced Phone Link Bridge

const sqlite3 = require('sqlite3');

const fs = require('fs');

const path = require('path');

const { EventEmitter } = require('events');



class EnhancedPhoneLinkBridge extends EventEmitter {

  constructor() {

    super();

    this.isConnected = false;

    this.databases = [];

    this.watchedPaths = [];

  }



  async connect() {

    console.log('🔗 Connecting to Phone Link...');

    

    // Find all possible Phone Link data locations

    const searchPaths = [

      path.join(process.env.LOCALAPPDATA, 'Packages'),

      path.join(process.env.APPDATA, 'Microsoft'),

      path.join(process.env.PROGRAMDATA, 'Microsoft')

    ];

    

    for (const basePath of searchPaths) {

      await this.scanForPhoneLinkData(basePath);

    }

    

    if (this.databases.length > 0) {

      this.isConnected = true;

      console.log(\`✅ Found \${this.databases.length} Phone Link databases\`);

      this.startMonitoring();

    } else {

      console.log('❌ No Phone Link data found');

    }

  }



  async scanForPhoneLinkData(basePath) {

    if (!fs.existsSync(basePath)) return;

    

    try {

      const items = fs.readdirSync(basePath);

      

      for (const item of items) {

        if (item.includes('YourPhone') || item.includes('WindowsCommunications')) {

          const fullPath = path.join(basePath, item);

          

          if (fs.statSync(fullPath).isDirectory()) {

            await this.scanDirectory(fullPath);

          }

        }

      }

    } catch (e) {

      // Permission denied or other error

    }

  }



  async scanDirectory(dirPath) {

    try {

      const files = fs.readdirSync(dirPath, { recursive: true });

      

      for (const file of files) {

        if (file.endsWith('.db') || file.endsWith('.sqlite')) {

          const fullPath = path.join(dirPath, file);

          

          try {

            // Test if it's a valid SQLite database

            const db = new sqlite3.Database(fullPath, sqlite3.OPEN_READONLY);

            

            await new Promise((resolve) => {

              db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {

                if (!err && tables.length > 0) {

                  console.log(\`📁 Found database: \${fullPath}\`);

                  console.log(\`   Tables: \${tables.map(t => t.name).join(', ')}\`);

                  

                  this.databases.push({

                    path: fullPath,

                    tables: tables.map(t => t.name),

                    type: this.detectDatabaseType(tables.map(t => t.name))

                  });

                }

                db.close();

                resolve();

              });

            });

            

          } catch (e) {

            // Not a valid SQLite database

          }

        }

      }

    } catch (e) {

      // Permission denied or other error

    }

  }



  detectDatabaseType(tableNames) {

    const tables = tableNames.join(' ').toLowerCase();

    

    if (tables.includes('message') || tables.includes('sms')) {

      return 'messages';

    } else if (tables.includes('call') || tables.includes('phone')) {

      return 'calls';

    } else if (tables.includes('contact')) {

      return 'contacts';

    } else {

      return 'unknown';

    }

  }



  async getMessages() {

    const messages = [];

    

    for (const dbInfo of this.databases) {

      if (dbInfo.type === 'messages') {

        try {

          const dbMessages = await this.extractMessagesFromDB(dbInfo);

          messages.push(...dbMessages);

        } catch (e) {

          console.log(\`Error reading \${dbInfo.path}: \${e.message}\`);

        }

      }

    }

    

    return messages;

  }



  async extractMessagesFromDB(dbInfo) {

    return new Promise((resolve) => {

      const db = new sqlite3.Database(dbInfo.path, sqlite3.OPEN_READONLY);

      const messages = [];

      

      // Try common message table patterns

      const queries = [

        "SELECT * FROM messages ORDER BY timestamp DESC LIMIT 100",

        "SELECT * FROM message ORDER BY date DESC LIMIT 100",

        "SELECT * FROM sms ORDER BY time DESC LIMIT 100"

      ];

      

      let queryIndex = 0;

      

      const tryNextQuery = () => {

        if (queryIndex >= queries.length) {

          db.close();

          resolve(messages);

          return;

        }

        

        const query = queries[queryIndex++];

        

        db.all(query, (err, rows) => {

          if (!err && rows.length > 0) {

            console.log(\`✅ Found \${rows.length} messages in \${dbInfo.path}\`);

            

            rows.forEach(row => {

              messages.push({

                id: row.id || row.rowid || Date.now(),

                text: row.text || row.body || row.content,

                phoneNumber: row.phone || row.number || row.address,

                timestamp: new Date(row.timestamp || row.date || row.time || Date.now()),

                isIncoming: !row.is_outgoing && !row.is_sent,

                source: 'phoneLink'

              });

            });

            

            db.close();

            resolve(messages);

          } else {

            tryNextQuery();

          }

        });

      };

      

      tryNextQuery();

    });

  }



  startMonitoring() {

    // Monitor database files for changes

    this.databases.forEach(dbInfo => {

      try {

        fs.watchFile(dbInfo.path, (curr, prev) => {

          if (curr.mtime > prev.mtime) {

            console.log('📨 Phone Link database updated');

            this.emit('database-updated', dbInfo);

          }

        });

        

        this.watchedPaths.push(dbInfo.path);

      } catch (e) {

        // File watching failed

      }

    });

  }



  stop() {

    this.watchedPaths.forEach(path => {

      fs.unwatchFile(path);

    });

    this.watchedPaths = [];

  }

}



module.exports = { EnhancedPhoneLinkBridge };`;



    fs.writeFileSync('src/main/services/EnhancedPhoneLinkBridge.js', bridgeCode);

    console.log('✅ Enhanced Phone Link bridge created');

  }



  async fixAirPlay() {

    console.log('📺 FIXING AIRPLAY...');

    

    try {

      // Check and fix port 7000

      await this.fixAirPlayPort();

      

      // Create enhanced AirPlay server

      await this.createEnhancedAirPlayServer();

      

      // Configure firewall

      await this.configureAirPlayFirewall();

      

      this.fixes.push('✅ AirPlay fixed and enhanced');

      

    } catch (error) {

      console.log('❌ AirPlay fix failed:', error.message);

      this.errors.push('AirPlay: ' + error.message);

    }

    console.log('');

  }



  async fixAirPlayPort() {

    try {

      // Check what's using port 7000

      const netstat = await this.execPromise('netstat -ano | findstr :7000');

      

      if (netstat.trim()) {

        console.log('⚠️ Port 7000 in use:', netstat.trim());

        

        // Extract PID and kill if necessary

        const lines = netstat.split('\n');

        for (const line of lines) {

          const parts = line.trim().split(/\s+/);

          if (parts.length >= 5) {

            const pid = parts[4];

            console.log(`Killing process ${pid} using port 7000...`);

            try {

              await this.execPromise(`taskkill /PID ${pid} /F`);

            } catch (e) {

              // Process might already be dead

            }

          }

        }

      }

      

      console.log('✅ Port 7000 is now available');

      

    } catch (error) {

      // Port is available

      console.log('✅ Port 7000 is available');

    }

  }



  async createEnhancedAirPlayServer() {

    const airplayCode = `// Enhanced AirPlay Server

const http = require('http');

const { EventEmitter } = require('events');



class EnhancedAirPlayServer extends EventEmitter {

  constructor() {

    super();

    this.server = null;

    this.isRunning = false;

    this.connectedDevices = new Set();

  }



  start() {

    console.log('📺 Starting Enhanced AirPlay Server...');

    

    this.server = http.createServer((req, res) => {

      console.log(\`AirPlay request: \${req.method} \${req.url}\`);

      

      // Set CORS headers

      res.setHeader('Access-Control-Allow-Origin', '*');

      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');

      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

      

      if (req.method === 'OPTIONS') {

        res.writeHead(200);

        res.end();

        return;

      }

      

      switch (req.url) {

        case '/server-info':

          this.handleServerInfo(req, res);

          break;

        case '/reverse':

          this.handleReverse(req, res);

          break;

        case '/play':

          this.handlePlay(req, res);

          break;

        case '/scrub':

          this.handleScrub(req, res);

          break;

        case '/stop':

          this.handleStop(req, res);

          break;

        default:

          res.writeHead(404);

          res.end('Not Found');

      }

    });

    

    this.server.listen(7000, '0.0.0.0', () => {

      console.log('✅ AirPlay server listening on port 7000');

      this.isRunning = true;

      this.emit('server-started');

      

      // Start advertising

      this.startAdvertising();

    });

    

    this.server.on('error', (error) => {

      console.error('AirPlay server error:', error);

      this.emit('server-error', error);

    });

  }



  handleServerInfo(req, res) {

    const serverInfo = \`<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">

<plist version="1.0">

<dict>

  <key>deviceid</key>

  <string>AA:BB:CC:DD:EE:FF</string>

  <key>features</key>

  <integer>0x5A7FFEE6</integer>

  <key>model</key>

  <string>AppleTV3,1</string>

  <key>protovers</key>

  <string>1.0</string>

  <key>srcvers</key>

  <string>220.68</string>

  <key>vv</key>

  <integer>2</integer>

</dict>

</plist>\`;

    

    res.writeHead(200, { 'Content-Type': 'text/x-apple-plist+xml' });

    res.end(serverInfo);

  }



  handleReverse(req, res) {

    console.log('📱 iPhone requesting reverse connection');

    

    let body = '';

    req.on('data', chunk => {

      body += chunk.toString();

    });

    

    req.on('end', () => {

      console.log('Reverse connection data:', body);

      this.emit('reverse-connection', body);

      

      res.writeHead(101, {

        'Upgrade': 'PTTH/1.0',

        'Connection': 'Upgrade'

      });

      res.end();

    });

  }



  handlePlay(req, res) {

    console.log('▶️ iPhone starting playback');

    

    let body = '';

    req.on('data', chunk => {

      body += chunk.toString();

    });

    

    req.on('end', () => {

      this.emit('playback-started', body);

      res.writeHead(200);

      res.end();

    });

  }



  handleScrub(req, res) {

    console.log('⏯️ iPhone scrubbing');

    this.emit('scrub');

    res.writeHead(200);

    res.end();

  }



  handleStop(req, res) {

    console.log('⏹️ iPhone stopping playback');

    this.emit('playback-stopped');

    res.writeHead(200);

    res.end();

  }



  startAdvertising() {

    try {

      const bonjour = require('bonjour-service')();

      

      const service = bonjour.publish({

        name: 'iPhone Companion Pro',

        type: 'airplay',

        port: 7000,

        txt: {

          deviceid: 'AA:BB:CC:DD:EE:FF',

          features: '0x5A7FFEE6',

          flags: '0x4',

          model: 'AppleTV3,1',

          pi: '2e388006-13ba-4041-9a67-25dd4a43d536',

          pk: '99d0c7dc673a69f5e0d8b2b7b8b1b1b1b1b1b1b1b1b1b1b1b1b1b1b1',

          srcvers: '220.68',

          vv: '2'

        }

      });

      

      console.log('✅ AirPlay service advertised via Bonjour');

      

      service.on('up', () => {

        console.log('📡 AirPlay service is up');

      });

      

    } catch (error) {

      console.log('⚠️ Bonjour advertising failed:', error.message);

      console.log('AirPlay server running but not advertised');

    }

  }



  stop() {

    if (this.server) {

      this.server.close();

      this.isRunning = false;

      console.log('🛑 AirPlay server stopped');

    }

  }

}



module.exports = { EnhancedAirPlayServer };`;



    fs.writeFileSync('src/main/services/EnhancedAirPlayServer.js', airplayCode);

    console.log('✅ Enhanced AirPlay server created');

  }



  async configureAirPlayFirewall() {

    try {

      // Add firewall rules for AirPlay

      await this.execPromise('netsh advfirewall firewall add rule name="iPhone Companion AirPlay" dir=in action=allow protocol=TCP localport=7000');

      await this.execPromise('netsh advfirewall firewall add rule name="iPhone Companion Bonjour" dir=in action=allow protocol=UDP localport=5353');

      

      console.log('✅ Firewall rules configured for AirPlay');

    } catch (error) {

      console.log('⚠️ Firewall configuration failed (run as administrator)');

    }

  }



  async fixUSB() {

    console.log('🔌 FIXING USB CONNECTION...');

    

    try {

      // Check iTunes installation

      const itunesInstalled = await this.checkiTunes();

      

      if (!itunesInstalled) {

        console.log('📦 iTunes not found - downloading...');

        await this.downloadiTunes();

      }

      

      // Start Apple Mobile Device Service

      await this.startAppleServices();

      

      this.fixes.push('✅ USB connection fixed');

      

    } catch (error) {

      console.log('❌ USB fix failed:', error.message);

      this.errors.push('USB: ' + error.message);

    }

    console.log('');

  }



  async checkiTunes() {

    try {

      await this.execPromise('reg query "HKLM\\SOFTWARE\\Apple Inc.\\Apple Mobile Device Support"');

      console.log('✅ iTunes/Apple Mobile Device Support found');

      return true;

    } catch (error) {

      return false;

    }

  }



  async downloadiTunes() {

    console.log('🍎 Opening iTunes download page...');

    await this.execPromise('start https://www.apple.com/itunes/download/win64');

    console.log('⚠️ Please download and install iTunes, then run this script again');

  }



  async startAppleServices() {

    const services = [

      'Apple Mobile Device Service',

      'iPod Service',

      'Bonjour Service'

    ];

    

    for (const service of services) {

      try {

        await this.execPromise(`sc start "${service}"`);

        console.log(`✅ Started ${service}`);

      } catch (error) {

        console.log(`⚠️ Could not start ${service}`);

      }

    }

  }



  async setupMacOSVM() {

    console.log('🖥️ SETTING UP MACOS VM...');

    

    try {

      // Check if VMware is installed

      const vmwareInstalled = await this.checkVMware();

      

      if (!vmwareInstalled) {

        console.log('📦 VMware not found');

        console.log('⚠️ Run setup-macos-vm.bat to install VMware and create VM');

      } else {

        console.log('✅ VMware found');

        

        // Test VM manager

        const { MacOSVMManager } = require('./vm-manager');

        const vmManager = new MacOSVMManager();

        

        try {

          await vmManager.initialize();

          this.fixes.push('✅ macOS VM connected');

        } catch (error) {

          console.log('⚠️ VM not ready:', error.message);

          this.errors.push('macOS VM: ' + error.message);

        }

      }

      

    } catch (error) {

      console.log('❌ macOS VM setup failed:', error.message);

      this.errors.push('macOS VM: ' + error.message);

    }

    console.log('');

  }



  async checkVMware() {

    try {

      const vmwarePath = 'C:\\Program Files (x86)\\VMware\\VMware Workstation\\vmware.exe';

      return fs.existsSync(vmwarePath);

    } catch (error) {

      return false;

    }

  }



  generateFixReport() {

    console.log('📊 CONNECTION FIX REPORT\n');

    

    if (this.fixes.length > 0) {

      console.log('✅ FIXES APPLIED:');

      this.fixes.forEach(fix => console.log(`   ${fix}`));

    }

    

    if (this.errors.length > 0) {

      console.log('\n❌ REMAINING ISSUES:');

      this.errors.forEach(error => console.log(`   ${error}`));

    }

    

    console.log('\n🎯 NEXT STEPS:');

    console.log('1. Run: node complete-connection-test.js');

    console.log('2. If all connections work: npm start');

    console.log('3. For macOS VM: run setup-macos-vm.bat');

    

    console.log('\n📱 IPHONE CHECKLIST:');

    console.log('□ iPhone connected via USB');

    console.log('□ "Trust This Computer" tapped');

    console.log('□ Personal Hotspot enabled');

    console.log('□ Bluetooth enabled');

    console.log('□ Check AirPlay in Control Center');

  }



  execPromise(command) {

    return new Promise((resolve, reject) => {

      exec(command, (error, stdout, stderr) => {

        if (error) {

          reject(error);

        } else {

          resolve(stdout);

        }

      });

    });

  }



  sleep(ms) {

    return new Promise(resolve => setTimeout(resolve, ms));

  }

}



// Run the fixer

const fixer = new ConnectionFixer();

fixer.fixAllConnections().catch(console.error);

>>>>>>>
