// Comprehensive Real iPhone Data Connection Verification
const net = require('net');
const fs = require('fs');
const path = require('path');

console.log('🔍 COMPREHENSIVE IPHONE DATA VERIFICATION');
console.log('=' .repeat(60));

async function verifyConnections() {
    const results = {
        phoneLink: false,
        webSocket: false,
        airPlay: false,
        shortcuts: false,
        companionApp: false
    };
    
    // 1. Check Phone Link Database Access
    console.log('\n📱 TESTING PHONE LINK INTEGRATION...');
    try {
        const phoneLinkPath = path.join(
            process.env.LOCALAPPDATA,
            'Packages',
            'Microsoft.YourPhone_8wekyb3d8bbwe',
            'LocalCache',
            'Indexed'
        );
        
        if (fs.existsSync(phoneLinkPath)) {
            const dirs = fs.readdirSync(phoneLinkPath);
            const indexedDir = dirs.find(dir => dir.match(/^[0-9a-f-]{36}$/i));
            
            if (indexedDir) {
                const dbPath = path.join(phoneLinkPath, indexedDir, 'System', 'Database');
                const dbFiles = ['calling.db', 'contacts.db', 'phoneapps.db', 'settings.db'];
                
                console.log('✅ Phone Link installation found');
                console.log(`📁 Database path: ${dbPath}`);
                
                const existingDbs = dbFiles.filter(file => 
                    fs.existsSync(path.join(dbPath, file))
                );
                
                console.log(`📊 Found ${existingDbs.length}/4 database files:`, existingDbs);
                results.phoneLink = existingDbs.length > 0;
            } else {
                console.log('❌ Phone Link indexed directory not found');
            }
        } else {
            console.log('❌ Phone Link not installed or not accessible');
        }
    } catch (error) {
        console.log('❌ Phone Link check failed:', error.message);
    }
    
    // 2. Check WebSocket Server (Companion App)
    console.log('\n📡 TESTING WEBSOCKET SERVER (PORT 8080)...');
    try {
        const isPortOpen = await checkPort('localhost', 8080);
        if (isPortOpen) {
            console.log('✅ WebSocket server is running on port 8080');
            console.log('📱 Ready for iOS companion app connection');
            results.webSocket = true;
        } else {
            console.log('❌ WebSocket server not running on port 8080');
        }
    } catch (error) {
        console.log('❌ WebSocket check failed:', error.message);
    }
    
    // 3. Check AirPlay Server
    console.log('\n📺 TESTING AIRPLAY SERVER (PORT 7000)...');
    try {
        const isAirPlayOpen = await checkPort('localhost', 7000);
        if (isAirPlayOpen) {
            console.log('✅ AirPlay server is running on port 7000');
            console.log('📱 Ready for iPhone screen mirroring');
            results.airPlay = true;
        } else {
            console.log('⚠️  AirPlay server not started (starts on demand)');
            console.log('💡 Will start when iPhone attempts to connect');
        }
    } catch (error) {
        console.log('❌ AirPlay check failed:', error.message);
    }
    
    // 4. Check iOS Shortcuts Server
    console.log('\n⚡ TESTING IOS SHORTCUTS SERVER (PORT 8888)...');
    try {
        const isShortcutsOpen = await checkPort('localhost', 8888);
        if (isShortcutsOpen) {
            console.log('✅ iOS Shortcuts server is running on port 8888');
            console.log('🔗 Ready for shortcuts integration');
            results.shortcuts = true;
        } else {
            console.log('❌ iOS Shortcuts server not running on port 8888');
        }
    } catch (error) {
        console.log('❌ Shortcuts check failed:', error.message);
    }
    
    // 5. Check Companion App Files
    console.log('\n📱 CHECKING IOS COMPANION APP...');
    try {
        const companionPath = path.join(__dirname, 'companion-ios-app');
        const requiredFiles = [
            'iPhoneCompanionPro/ContentView.swift',
            'iPhoneCompanionPro/ConnectionManager.swift',
            'iPhoneCompanionPro/MessageBridge.swift',
            'iPhoneCompanionPro/QRScannerView.swift'
        ];
        
        const existingFiles = requiredFiles.filter(file => 
            fs.existsSync(path.join(companionPath, file))
        );
        
        console.log(`✅ Found ${existingFiles.length}/${requiredFiles.length} companion app files`);
        console.log('📱 iOS app ready for Xcode build and installation');
        results.companionApp = existingFiles.length === requiredFiles.length;
    } catch (error) {
        console.log('❌ Companion app check failed:', error.message);
    }
    
    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 REAL DATA CONNECTION SUMMARY');
    console.log('='.repeat(60));
    
    const workingCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    
    console.log(`\n🎯 Integration Status: ${workingCount}/${totalCount} methods ready`);
    console.log('\nDetailed Status:');
    console.log(`📱 Phone Link Bridge:     ${results.phoneLink ? '✅ READY' : '❌ NOT READY'}`);
    console.log(`📡 WebSocket Server:      ${results.webSocket ? '✅ RUNNING' : '❌ NOT RUNNING'}`);
    console.log(`📺 AirPlay Server:        ${results.airPlay ? '✅ RUNNING' : '⚠️  ON DEMAND'}`);
    console.log(`⚡ Shortcuts Server:      ${results.shortcuts ? '✅ RUNNING' : '❌ NOT RUNNING'}`);
    console.log(`📱 Companion App:         ${results.companionApp ? '✅ READY' : '❌ NOT READY'}`);
    
    if (workingCount >= 3) {
        console.log('\n🎉 EXCELLENT! Multiple integration methods are ready!');
        console.log('\n🚀 RECOMMENDED NEXT STEPS:');
        console.log('1. 📺 Test AirPlay: iPhone Control Center → Screen Mirroring');
        console.log('2. 📱 Install companion app: Build with Xcode from companion-ios-app/');
        console.log('3. 📡 Connect via QR code: Scan QR from the dashboard');
        console.log('4. ⚡ Setup shortcuts: Install iOS shortcuts for automation');
    } else {
        console.log('\n⚠️  Some integration methods need setup.');
        console.log('\n🔧 TROUBLESHOOTING:');
        console.log('- Ensure iPhone Companion Pro is running');
        console.log('- Check that iPhone and PC are on same WiFi network');
        console.log('- Verify Phone Link is installed and iPhone is connected');
    }
    
    console.log('\n📊 Open the dashboard for detailed status: real-data-dashboard.html');
    
    return results;
}

function checkPort(host, port) {
    return new Promise((resolve) => {
        const socket = new net.Socket();
        
        socket.setTimeout(3000);
        
        socket.on('connect', () => {
            socket.destroy();
            resolve(true);
        });
        
        socket.on('timeout', () => {
            socket.destroy();
            resolve(false);
        });
        
        socket.on('error', () => {
            resolve(false);
        });
        
        socket.connect(port, host);
    });
}

// Run verification
verifyConnections().catch(console.error);
