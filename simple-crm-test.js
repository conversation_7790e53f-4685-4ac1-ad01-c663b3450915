const http = require('http');

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 7777,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (error) {
          resolve(data);
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function testCRMAPI() {
  console.log('🧪 Testing CRM API with simple HTTP...');
  
  try {
    // Test health endpoint
    console.log('\n📊 Testing health endpoint...');
    const health = await makeRequest('/api/crm/health');
    console.log('Health:', health);
    
    // Test status endpoint
    console.log('\n📊 Testing status endpoint...');
    const status = await makeRequest('/api/crm/status');
    console.log('Status:', status);
    
    // Test contacts endpoint
    console.log('\n📊 Testing contacts endpoint...');
    const contacts = await makeRequest('/api/crm/contacts');
    console.log('Contacts:', contacts);
    
    // Test messages endpoint
    console.log('\n📊 Testing messages endpoint...');
    const messages = await makeRequest('/api/crm/messages');
    console.log('Messages:', messages);
    
    // Test conversations endpoint
    console.log('\n📊 Testing conversations endpoint...');
    const conversations = await makeRequest('/api/crm/conversations');
    console.log('Conversations:', conversations);
    
    console.log('\n✅ All tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testCRMAPI();
