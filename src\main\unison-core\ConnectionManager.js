const { EventEmitter } = require('events');
const net = require('net');
const { UnisonDiscovery } = require('./protocols/UnisonDiscovery');

/**
 * Multi-Protocol Connection Manager
 * Handles Bluetooth, WiFi Direct, USB, and Network connections exactly like Intel Unison
 */
class ConnectionManager extends EventEmitter {
  constructor() {
    super();
    
    // Connection protocols
    this.discovery = new UnisonDiscovery();
    this.connections = new Map();
    this.activeConnection = null;
    this.messageQueue = [];
    
    // Connection state
    this.isInitialized = false;
    this.isConnecting = false;
    
    // Protocol handlers
    this.protocolHandlers = new Map();
    
    // Intel Unison constants
    this.HANDSHAKE_TIMEOUT = 10000; // 10 seconds
    this.KEEPALIVE_INTERVAL = 30000; // 30 seconds
    this.MAX_RECONNECT_ATTEMPTS = 5;
    
    this.setupDiscoveryEvents();
  }

  async initialize() {
    console.log('🔗 Initializing Connection Manager...');
    
    try {
      // Initialize discovery service
      await this.discovery.initialize();
      
      // Set up protocol handlers
      this.setupProtocolHandlers();
      
      // Set up connection monitoring
      this.setupConnectionMonitoring();
      
      this.isInitialized = true;
      console.log('✅ Connection Manager initialized');
      
    } catch (error) {
      console.error('❌ Connection Manager initialization failed:', error);
      throw error;
    }
  }

  setupDiscoveryEvents() {
    this.discovery.on('device-discovered', (device) => {
      console.log(`📱 Device discovered: ${device.name} via ${device.protocol}`);
      this.emit('device-discovered', device);
    });
    
    this.discovery.on('device-removed', (device) => {
      console.log(`📱 Device removed: ${device.name}`);
      this.emit('device-removed', device);
    });
  }

  setupProtocolHandlers() {
    console.log('🔧 Setting up protocol handlers...');
    
    // Bluetooth LE handler
    this.protocolHandlers.set('bluetooth', {
      connect: this.connectBluetooth.bind(this),
      disconnect: this.disconnectBluetooth.bind(this),
      send: this.sendBluetooth.bind(this)
    });
    
    // WiFi Direct/TCP handler
    this.protocolHandlers.set('network', {
      connect: this.connectNetwork.bind(this),
      disconnect: this.disconnectNetwork.bind(this),
      send: this.sendNetwork.bind(this)
    });
    
    // USB handler (future implementation)
    this.protocolHandlers.set('usb', {
      connect: this.connectUSB.bind(this),
      disconnect: this.disconnectUSB.bind(this),
      send: this.sendUSB.bind(this)
    });
  }

  setupConnectionMonitoring() {
    console.log('📊 Setting up connection monitoring...');
    
    // Keep-alive monitoring
    this.keepaliveInterval = setInterval(() => {
      this.sendKeepalive();
    }, this.KEEPALIVE_INTERVAL);
    
    // Stale device cleanup
    setInterval(() => {
      this.discovery.removeStaleDevices();
    }, 60000); // Every minute
  }

  startDiscovery() {
    console.log('🔍 Starting device discovery...');
    
    if (!this.isInitialized) {
      throw new Error('Connection Manager not initialized');
    }
    
    this.discovery.startDiscovery();
    this.emit('discovery-started');
  }

  stopDiscovery() {
    console.log('⏹️ Stopping device discovery...');
    this.discovery.stopDiscovery();
    this.emit('discovery-stopped');
  }

  async connectToDevice(deviceId, preferredProtocol = null) {
    console.log(`🔗 Attempting to connect to device: ${deviceId}`);
    
    if (this.isConnecting) {
      throw new Error('Connection already in progress');
    }
    
    const device = this.discovery.getDevice(deviceId);
    if (!device) {
      throw new Error(`Device ${deviceId} not found`);
    }
    
    this.isConnecting = true;
    
    try {
      // Choose best protocol
      const protocol = preferredProtocol || this.selectBestProtocol(device);
      console.log(`📡 Using protocol: ${protocol}`);
      
      // Get protocol handler
      const handler = this.protocolHandlers.get(protocol);
      if (!handler) {
        throw new Error(`Protocol ${protocol} not supported`);
      }
      
      // Attempt connection
      const connection = await handler.connect(device);
      
      // Store connection
      this.connections.set(deviceId, {
        device,
        protocol,
        connection,
        handler,
        connectedAt: Date.now(),
        lastActivity: Date.now(),
        reconnectAttempts: 0
      });
      
      // Set as active connection
      this.activeConnection = this.connections.get(deviceId);
      
      console.log(`✅ Connected to ${device.name} via ${protocol}`);
      this.emit('device-connected', device, protocol);
      
      // Process queued messages
      await this.processMessageQueue();
      
      return connection;
      
    } catch (error) {
      console.error(`❌ Connection failed: ${error.message}`);
      this.emit('connection-failed', device, error);
      throw error;
      
    } finally {
      this.isConnecting = false;
    }
  }

  selectBestProtocol(device) {
    // Priority order: Network (fastest) > Bluetooth > USB
    if (device.protocol === 'udp' || device.address) {
      return 'network';
    } else if (device.protocol === 'ble') {
      return 'bluetooth';
    } else if (device.protocol === 'usb') {
      return 'usb';
    }
    
    // Fallback to network
    return 'network';
  }

  // Network/WiFi Direct connection
  async connectNetwork(device) {
    console.log(`🌐 Connecting via network to ${device.address}...`);
    
    return new Promise((resolve, reject) => {
      const socket = new net.Socket();
      let isConnected = false;
      
      // Set timeout
      const timeout = setTimeout(() => {
        if (!isConnected) {
          socket.destroy();
          reject(new Error('Connection timeout'));
        }
      }, this.HANDSHAKE_TIMEOUT);
      
      socket.connect(26818, device.address, () => {
        isConnected = true;
        clearTimeout(timeout);
        
        console.log(`✅ TCP connection established to ${device.address}`);
        
        // Send Intel Unison handshake
        this.sendHandshake(socket)
          .then(() => {
            // Set up data handlers
            this.setupSocketHandlers(socket, device);
            resolve(socket);
          })
          .catch(reject);
      });
      
      socket.on('error', (error) => {
        clearTimeout(timeout);
        if (!isConnected) {
          reject(error);
        }
      });
    });
  }

  // Bluetooth LE connection
  async connectBluetooth(device) {
    console.log(`📡 Connecting via Bluetooth to ${device.id}...`);
    
    const peripheral = device.peripheral;
    if (!peripheral) {
      throw new Error('Bluetooth peripheral not available');
    }
    
    return new Promise((resolve, reject) => {
      peripheral.connect((error) => {
        if (error) {
          reject(error);
          return;
        }
        
        console.log(`✅ Bluetooth connection established to ${device.name}`);
        
        // Discover services and characteristics
        peripheral.discoverServices([], (error, services) => {
          if (error) {
            reject(error);
            return;
          }
          
          // Find Intel Unison service
          const unisonService = services.find(service => 
            service.uuid === this.discovery.BLUETOOTH_SERVICE_UUID.replace(/-/g, '').toLowerCase()
          );
          
          if (!unisonService) {
            reject(new Error('Intel Unison service not found'));
            return;
          }
          
          // Set up Bluetooth handlers
          this.setupBluetoothHandlers(peripheral, unisonService, device);
          resolve(peripheral);
        });
      });
    });
  }

  // USB connection (placeholder)
  async connectUSB(device) {
    console.log(`🔌 USB connection not yet implemented for ${device.id}`);
    throw new Error('USB connection not implemented');
  }

  // Protocol disconnect methods
  async disconnectBluetooth(connection) {
    console.log('📡 Disconnecting Bluetooth...');
    if (connection.connection && connection.connection.disconnect) {
      connection.connection.disconnect();
    }
  }

  async disconnectNetwork(connection) {
    console.log('🌐 Disconnecting network...');
    if (connection.connection && connection.connection.destroy) {
      connection.connection.destroy();
    }
  }

  async disconnectUSB(connection) {
    console.log('🔌 Disconnecting USB...');
    // USB disconnect implementation
  }

  async sendHandshake(socket) {
    console.log('🤝 Sending Intel Unison handshake...');
    
    const handshake = {
      magic: 'IUNI',
      version: '2.0',
      deviceType: 'PC',
      hostname: require('os').hostname(),
      capabilities: ['messaging', 'calls', 'notifications', 'files'],
      timestamp: Date.now()
    };
    
    return new Promise((resolve, reject) => {
      const handshakeData = JSON.stringify(handshake);
      const magicBytes = Buffer.from([0x49, 0x55, 0x4E, 0x49]); // "IUNI"
      const packet = Buffer.concat([magicBytes, Buffer.from(handshakeData)]);
      
      socket.write(packet);
      
      // Wait for handshake response
      const timeout = setTimeout(() => {
        reject(new Error('Handshake timeout'));
      }, 5000);
      
      socket.once('data', (data) => {
        clearTimeout(timeout);
        
        try {
          // Verify magic bytes
          if (data.length >= 4 && data.slice(0, 4).equals(magicBytes)) {
            const responseData = data.slice(4).toString();
            const response = JSON.parse(responseData);
            
            console.log('✅ Handshake successful');
            console.log(`📱 Connected to: ${response.deviceType} ${response.hostname || ''}`);
            
            resolve(response);
          } else {
            reject(new Error('Invalid handshake response'));
          }
        } catch (error) {
          reject(new Error('Handshake parse error: ' + error.message));
        }
      });
    });
  }

  setupSocketHandlers(socket, device) {
    socket.on('data', (data) => {
      this.handleIncomingData(data, device);
    });
    
    socket.on('close', () => {
      console.log(`🔌 Connection closed to ${device.name}`);
      this.handleConnectionLost(device);
    });
    
    socket.on('error', (error) => {
      console.error(`❌ Socket error for ${device.name}:`, error);
      this.handleConnectionError(device, error);
    });
  }

  setupBluetoothHandlers(peripheral, service, device) {
    peripheral.on('disconnect', () => {
      console.log(`📡 Bluetooth disconnected from ${device.name}`);
      this.handleConnectionLost(device);
    });
    
    // Set up characteristic notifications
    service.discoverCharacteristics([], (error, characteristics) => {
      if (error) {
        console.error('❌ Characteristic discovery error:', error);
        return;
      }
      
      characteristics.forEach(characteristic => {
        if (characteristic.properties.includes('notify')) {
          characteristic.subscribe((error) => {
            if (!error) {
              characteristic.on('data', (data) => {
                this.handleIncomingData(data, device);
              });
            }
          });
        }
      });
    });
  }

  handleIncomingData(data, device) {
    try {
      // Update last activity
      const connection = this.connections.get(device.id);
      if (connection) {
        connection.lastActivity = Date.now();
      }
      
      // Parse message
      const message = JSON.parse(data.toString());
      console.log(`📥 Received ${message.type} from ${device.name}`);
      
      this.emit('message-received', message, device);
      
    } catch (error) {
      console.error('❌ Data parsing error:', error);
    }
  }

  handleConnectionLost(device) {
    const connection = this.connections.get(device.id);
    if (connection) {
      console.log(`💔 Connection lost to ${device.name}`);
      
      // Clear active connection if this was it
      if (this.activeConnection && this.activeConnection.device.id === device.id) {
        this.activeConnection = null;
      }
      
      // Attempt reconnection
      this.attemptReconnection(device);
    }
    
    this.emit('connection-lost', device);
  }

  handleConnectionError(device, error) {
    console.error(`❌ Connection error for ${device.name}:`, error);
    this.emit('connection-error', device, error);
  }

  async attemptReconnection(device) {
    const connection = this.connections.get(device.id);
    if (!connection) return;
    
    connection.reconnectAttempts++;
    
    if (connection.reconnectAttempts > this.MAX_RECONNECT_ATTEMPTS) {
      console.log(`❌ Max reconnection attempts reached for ${device.name}`);
      this.connections.delete(device.id);
      return;
    }
    
    console.log(`🔄 Reconnection attempt ${connection.reconnectAttempts} for ${device.name}`);
    
    // Wait before reconnecting
    setTimeout(async () => {
      try {
        await this.connectToDevice(device.id, connection.protocol);
      } catch (error) {
        console.error(`❌ Reconnection failed: ${error.message}`);
      }
    }, 5000 * connection.reconnectAttempts); // Exponential backoff
  }

  async sendPacket(packet) {
    if (!this.activeConnection) {
      // Queue message if no active connection
      this.messageQueue.push(packet);
      throw new Error('No active connection - message queued');
    }
    
    try {
      const data = JSON.stringify(packet);
      const connection = this.activeConnection;
      
      switch (connection.protocol) {
        case 'network':
          await this.sendNetwork(data, connection);
          break;
        case 'bluetooth':
          await this.sendBluetooth(data, connection);
          break;
        case 'usb':
          await this.sendUSB(data, connection);
          break;
        default:
          throw new Error(`Unknown protocol: ${connection.protocol}`);
      }
      
      console.log(`📤 Sent ${packet.type} via ${connection.protocol}`);
      
    } catch (error) {
      console.error('❌ Send packet error:', error);
      throw error;
    }
  }

  async sendNetwork(data, connection) {
    return new Promise((resolve, reject) => {
      connection.connection.write(data, (error) => {
        if (error) reject(error);
        else resolve();
      });
    });
  }

  async sendBluetooth(data, connection) {
    // Implement Bluetooth characteristic write
    throw new Error('Bluetooth send not implemented');
  }

  async sendUSB(data, connection) {
    throw new Error('USB send not implemented');
  }

  async processMessageQueue() {
    if (this.messageQueue.length === 0) return;
    
    console.log(`📤 Processing ${this.messageQueue.length} queued messages`);
    
    while (this.messageQueue.length > 0) {
      const packet = this.messageQueue.shift();
      try {
        await this.sendPacket(packet);
      } catch (error) {
        console.error('❌ Queued message send failed:', error);
        // Re-queue on failure
        this.messageQueue.unshift(packet);
        break;
      }
    }
  }

  sendKeepalive() {
    if (this.activeConnection) {
      const keepalive = {
        type: 'keepalive',
        timestamp: Date.now()
      };
      
      this.sendPacket(keepalive).catch(error => {
        console.error('❌ Keepalive failed:', error);
      });
    }
  }

  getConnections() {
    return Array.from(this.connections.values()).map(conn => ({
      device: conn.device,
      protocol: conn.protocol,
      connectedAt: conn.connectedAt,
      lastActivity: conn.lastActivity,
      isActive: this.activeConnection?.device.id === conn.device.id
    }));
  }

  getConnectionStatus() {
    return {
      discovered: this.discovery.getDiscoveredDevices().length,
      connected: this.connections.size,
      active: this.activeConnection ? 1 : 0,
      queued: this.messageQueue.length,
      discovery: this.discovery.getDiscoveryStatus()
    };
  }

  async cleanup() {
    console.log('🧹 Cleaning up Connection Manager...');
    
    // Stop discovery
    this.stopDiscovery();
    
    // Close all connections
    for (const [deviceId, connection] of this.connections) {
      try {
        await connection.handler.disconnect(connection);
      } catch (error) {
        console.error(`❌ Error closing connection to ${deviceId}:`, error);
      }
    }
    
    // Clear intervals
    if (this.keepaliveInterval) {
      clearInterval(this.keepaliveInterval);
    }
    
    // Cleanup discovery
    await this.discovery.cleanup();
    
    this.connections.clear();
    this.activeConnection = null;
    this.messageQueue = [];
    
    console.log('✅ Connection Manager cleanup complete');
  }
}

module.exports = { ConnectionManager };