const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing duplicate IPC handlers...\n');

const mainJsPath = path.join(__dirname, 'src/main/main.js');
let mainJs = fs.readFileSync(mainJsPath, 'utf8');

// Remove ALL duplicate handlers first
const handlersToRemove = [
  'open-messages',
  'open-calls',
  'open-mirror',
  'open-photos',
  'open-files',
  'open-settings'
];

// Count and remove duplicates
handlersToRemove.forEach(handler => {
  const regex = new RegExp(`ipcMain\\.handle\\(['"\`]${handler}['"\`][^}]+\\}\\);?`, 'gs');
  const matches = mainJs.match(regex) || [];
  
  if (matches.length > 1) {
    console.log(`Found ${matches.length} instances of '${handler}' handler, removing duplicates...`);
    // Keep only the first occurrence
    let count = 0;
    mainJs = mainJs.replace(regex, (match) => {
      count++;
      return count === 1 ? match : '';
    });
  }
});

// Now ensure we have exactly ONE handler for each
const cleanHandlers = `
// ===== WINDOW HANDLERS - SINGLE INSTANCE =====
let messagesWindow = null;
let callsWindow = null;

ipcMain.handle('open-messages', async () => {
  console.log('📱 Opening Messages window');
  
  if (messagesWindow) {
    messagesWindow.show();
    messagesWindow.focus();
    return;
  }
  
  messagesWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'iPhone Messages',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  messagesWindow.loadFile(path.join(__dirname, '../renderer/views/messages.html'));
  
  messagesWindow.on('closed', () => {
    messagesWindow = null;
  });
  
  messagesWindow.webContents.on('did-finish-load', () => {
    console.log('Messages window loaded');
    // Send any available data
    messagesWindow.webContents.send('messages-ready');
  });
});

ipcMain.handle('open-calls', async () => {
  console.log('📞 Opening Calls window');
  
  if (callsWindow) {
    callsWindow.show();
    callsWindow.focus();
    return;
  }
  
  callsWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    center: true,
    title: 'iPhone Calls',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  
  callsWindow.loadFile(path.join(__dirname, '../renderer/views/calls.html'));
  
  callsWindow.on('closed', () => {
    callsWindow = null;
  });
  
  callsWindow.webContents.on('did-finish-load', () => {
    console.log('Calls window loaded');
    // Send any available data
    callsWindow.webContents.send('calls-ready');
  });
});
// ===== END WINDOW HANDLERS =====
`;

// Remove all existing handlers first
handlersToRemove.forEach(handler => {
  const regex = new RegExp(`ipcMain\\.handle\\(['"\`]${handler}['"\`][^}]+\\}\\);?\\s*`, 'gs');
  mainJs = mainJs.replace(regex, '');
});

// Also remove any window variable declarations that might be duplicated
mainJs = mainJs.replace(/let messagesWindow\s*=\s*null;\s*/g, '');
mainJs = mainJs.replace(/let callsWindow\s*=\s*null;\s*/g, '');

// Insert our clean handlers once, right after the app initialization
const insertPoint = mainJs.indexOf('// Window controls');
if (insertPoint > -1) {
  mainJs = mainJs.slice(0, insertPoint) + cleanHandlers + '\n\n' + mainJs.slice(insertPoint);
} else {
  // If we can't find that comment, insert after app.whenReady
  const appReadyIndex = mainJs.indexOf('app.whenReady()');
  if (appReadyIndex > -1) {
    const nextLineIndex = mainJs.indexOf('\n', appReadyIndex);
    mainJs = mainJs.slice(0, nextLineIndex + 1) + cleanHandlers + '\n' + mainJs.slice(nextLineIndex + 1);
  }
}

fs.writeFileSync(mainJsPath, mainJs);
console.log('✅ Removed duplicate handlers');

// Now update the HTML buttons to use simple onclick
const indexPath = path.join(__dirname, 'src/renderer/views/index.html');
let indexHtml = fs.readFileSync(indexPath, 'utf8');

// Update the control center grid with proper onclick handlers
const controlCenterHtml = `
        <div class="control-grid">
          <div class="control-item" onclick="require('electron').ipcRenderer.invoke('open-mirror')" style="cursor: pointer;">
            <i class="fas fa-desktop"></i>
            Screen Mirror
          </div>
          <div class="control-item" onclick="require('electron').ipcRenderer.invoke('open-messages')" style="cursor: pointer;">
            <i class="fas fa-comment"></i>
            Messages
          </div>
          <div class="control-item" onclick="require('electron').ipcRenderer.invoke('open-calls')" style="cursor: pointer;">
            <i class="fas fa-phone"></i>
            Calls
          </div>
          <div class="control-item" onclick="require('electron').ipcRenderer.invoke('open-photos')" style="cursor: pointer;">
            <i class="fas fa-images"></i>
            Photos
          </div>
          <div class="control-item" onclick="require('electron').ipcRenderer.invoke('open-files')" style="cursor: pointer;">
            <i class="fas fa-folder"></i>
            Files
          </div>
          <div class="control-item" onclick="require('electron').ipcRenderer.invoke('open-settings')" style="cursor: pointer;">
            <i class="fas fa-cog"></i>
            Settings
          </div>
        </div>
`;

// Replace the control grid section
indexHtml = indexHtml.replace(/<div class="control-grid">[\s\S]*?<\/div>\s*<\/div>/, controlCenterHtml + '</div>');

fs.writeFileSync(indexPath, indexHtml);
console.log('✅ Updated button handlers');

console.log('\n🎉 Fixed! The app should now start without errors.');
console.log('\n📱 Test the buttons:');
console.log('   - Messages button → Opens messages window');
console.log('   - Calls button → Opens calls window');