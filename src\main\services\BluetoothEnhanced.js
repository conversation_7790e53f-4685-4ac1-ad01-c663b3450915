const { EventEmitter } = require('events');

// Try to load noble, fallback to mock if not available
let noble;
try {
  noble = require('@abandonware/noble');
} catch (error) {
  console.log('⚠️ Bluetooth not available in this environment, using mock');
  noble = {
    on: () => {},
    startScanning: () => {},
    stopScanning: () => {},
    state: 'poweredOff'
  };
}

class BluetoothEnhanced extends EventEmitter {
  constructor() {
    super();
    this.connectedDevice = null;
    this.isScanning = false;
  }

  async findIPhone() {
    return new Promise((resolve) => {
      console.log('🔍 Scanning for iPhone via Bluetooth...');

      // Check if noble is available
      if (noble.state === 'poweredOff' && !noble.startScanning.toString().includes('function')) {
        console.log('⚠️ Bluetooth not available - simulating connection for testing');
        setTimeout(() => {
          console.log('✅ Bluetooth test mode - simulated iPhone connection');
          this.emit('connected', { address: 'test:device', name: 'Test iPhone' });
          resolve({ address: 'test:device', name: 'Test iPhone' });
        }, 2000);
        return;
      }

      noble.on('stateChange', (state) => {
        if (state === 'poweredOn') {
          this.startScanning();
        }
      });

      noble.on('discover', (peripheral) => {
        const name = peripheral.advertisement.localName;
        if (name && (name.includes('iPhone') || name.includes('Apple'))) {
          console.log('📱 Found iPhone:', name, peripheral.address);

          peripheral.connect((error) => {
            if (!error) {
              console.log('✅ Bluetooth connected to iPhone!');
              this.connectedDevice = peripheral;
              this.emit('connected', peripheral);

              // Discover services for data access
              peripheral.discoverAllServicesAndCharacteristics((err, services) => {
                if (!err) {
                  console.log('🔍 Discovered services:', services.length);
                  resolve(peripheral);
                }
              });
            } else {
              console.log('❌ Connection failed:', error.message);
            }
          });
        }
      });

      if (noble.state === 'poweredOn') {
        this.startScanning();
      } else {
        // Timeout for testing
        setTimeout(() => {
          console.log('⏰ Bluetooth scan timeout');
          resolve(null);
        }, 10000);
      }
    });
  }

  startScanning() {
    if (!this.isScanning) {
      noble.startScanning();
      this.isScanning = true;
      console.log('🔄 Bluetooth scanning started...');
    }
  }

  stopScanning() {
    noble.stopScanning();
    this.isScanning = false;
  }
}

module.exports = { BluetoothEnhanced };