const express = require('express');
const { Server } = require('socket.io');
const QRCode = require('qrcode');
const Store = require('electron-store');
const { EventEmitter } = require('events');

class WebBridgeServer extends EventEmitter {
  constructor() {
    super(); // This is important!
    this.app = express();
    this.store = new Store();
    this.connections = new Map();
    this.server = null;
    this.io = null;
  }

  async start() {
    if (this.server) {
      console.log('Web bridge server already running');
      return;
    }

    this.server = require('http').createServer(this.app);
    this.io = new Server(this.server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });

    // Serve connection page
    this.app.get('/', (req, res) => {
      res.send(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <title>iPhone Companion Connect</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, sans-serif;
              display: flex;
              align-items: center;
              justify-content: center;
              min-height: 100vh;
              margin: 0;
              background: #000;
              color: white;
            }
            .container {
              text-align: center;
              padding: 20px;
            }
            button {
              background: #007AFF;
              color: white;
              border: none;
              padding: 15px 30px;
              font-size: 18px;
              border-radius: 10px;
              margin: 10px;
              cursor: pointer;
            }
            button:hover {
              background: #0051D5;
            }
            .status {
              margin-top: 20px;
              padding: 20px;
              background: #1a1a1a;
              border-radius: 10px;
            }
            .success {
              color: #34C759;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>📱 iPhone Companion Pro</h1>
            <p>Connect your iPhone wirelessly to your PC</p>
            
            <button onclick="requestPermissions()">Connect This iPhone</button>
            
            <div class="status" id="status"></div>
          </div>
          
          <script src="/socket.io/socket.io.js"></script>
          <script>
            const socket = io();
            const status = document.getElementById('status');
            
            async function requestPermissions() {
              status.innerHTML = 'Requesting permissions...';
              
              // Request notification permission
              if ('Notification' in window) {
                const permission = await Notification.requestPermission();
                console.log('Notification permission:', permission);
              }
              
              // Get device info
              const deviceInfo = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                screenWidth: screen.width,
                screenHeight: screen.height,
                deviceName: 'iPhone',
                connectionType: 'WiFi Bridge'
              };
              
              // Check if this is actually an iPhone
              const isIPhone = /iPhone/i.test(navigator.userAgent);
              if (isIPhone) {
                deviceInfo.name = 'iPhone';
                
                // Get iOS version
                const match = navigator.userAgent.match(/OS (\\d+)_(\\d+)/);
                if (match) {
                  deviceInfo.osVersion = 'iOS ' + match[1] + '.' + match[2];
                }
              }
              
              socket.emit('connect-device', deviceInfo);
              
              status.innerHTML = '<span class="success">✅ Connected!</span><br>You can now use iPhone Companion Pro on your PC.';
              
              // Keep connection alive
              setInterval(() => {
                socket.emit('ping', { timestamp: Date.now() });
              }, 5000);
            }
            
            socket.on('request-data', (type) => {
              // Handle data requests from PC
              if (type === 'battery') {
                // We can't get real battery from web API, but we can simulate
                socket.emit('battery-update', { level: 85 });
              }
            });
          </script>
        </body>
        </html>
      `);
    });

    // Handle socket connections
    this.io.on('connection', (socket) => {
      console.log('Device connected via web bridge');
      
      socket.on('connect-device', (deviceInfo) => {
        this.connections.set(socket.id, {
          socket,
          deviceInfo,
          connectedAt: new Date()
        });
        
        console.log('iPhone connected:', deviceInfo);
        this.emit('device-connected', deviceInfo);
      });
      
      socket.on('ping', (data) => {
        // Keep-alive ping
        socket.emit('pong', { timestamp: Date.now() });
      });
      
      socket.on('battery-update', (data) => {
        this.emit('battery-update', data.level);
      });
      
      socket.on('disconnect', () => {
        this.connections.delete(socket.id);
        console.log('Device disconnected');
        this.emit('device-disconnected');
      });
    });

    // Start server
    const port = 8899;
    
    return new Promise((resolve, reject) => {
      this.server.listen(port, async () => {
        try {
          console.log(`Web bridge server running on port ${port}`);
          
          // Generate QR code
          const localIP = await this.getLocalIP();
          const url = `http://${localIP}:${port}`;
          
          console.log('Connection URL:', url);
          
          const qr = await QRCode.toDataURL(url);
          this.emit('qr-code', { url, qr });
          
          resolve({ success: true, url, qr });
        } catch (error) {
          reject(error);
        }
      });
      
      this.server.on('error', (error) => {
        console.error('Server error:', error);
        reject(error);
      });
    });
  }

  async getLocalIP() {
    const { networkInterfaces } = require('os');
    const nets = networkInterfaces();
    
    for (const name of Object.keys(nets)) {
      for (const net of nets[name]) {
        // Skip internal and IPv6 addresses
        if (net.family === 'IPv4' && !net.internal) {
          return net.address;
        }
      }
    }
    
    return 'localhost';
  }

  stop() {
    if (this.server) {
      this.server.close();
      this.server = null;
    }
    
    if (this.io) {
      this.io.close();
      this.io = null;
    }
    
    this.connections.clear();
  }
}

module.exports = { WebBridgeServer };