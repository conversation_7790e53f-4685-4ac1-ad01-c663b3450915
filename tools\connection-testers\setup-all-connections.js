// setup-all-connections.js - Master iPhone Connection Setup
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const os = require('os');

class ConnectionMaster {
  constructor() {
    this.connections = {
      bluetooth: false,
      airplay: false,
      vm: false,
      phoneLink: false,
      usb: false
    };
  }

  async setupAllConnections() {
    console.log('🔥 iPhone Companion Pro - Master Connection Setup\n');
    console.log('Setting up ALL iPhone connection methods...\n');
    
    // Create services directory if it doesn't exist
    const servicesDir = path.join(__dirname, 'src', 'main', 'services');
    if (!fs.existsSync(servicesDir)) {
      fs.mkdirSync(servicesDir, { recursive: true });
    }
    
    // Setup all connection methods
    await this.setupBluetooth();
    await this.setupAirPlay();
    await this.setupMacOSBridge();
    await this.setupPhoneLink();
    await this.setupUSB();
    
    // Create master connection manager
    await this.createMasterManager();
    
    console.log('\n🎉 ALL CONNECTION METHODS READY!');
    console.log('\nNext steps:');
    console.log('1. Run: node test-all-connections.js');
    console.log('2. Check iPhone settings for each method');
    console.log('3. Start app: npm start');
  }

  async setupBluetooth() {
    console.log('📱 BLUETOOTH SETUP:');
    
    // Enable Bluetooth service on Windows
    exec('net start bthserv', (err) => {
      if (!err) console.log('✅ Bluetooth service started');
    });
    
    // Enhanced Bluetooth connection with proper pairing
    const btConfig = `const noble = require('@abandonware/noble');
const { EventEmitter } = require('events');

class BluetoothEnhanced extends EventEmitter {
  constructor() {
    super();
    this.connectedDevice = null;
    this.isScanning = false;
  }

  async findIPhone() {
    return new Promise((resolve) => {
      console.log('🔍 Scanning for iPhone via Bluetooth...');
      
      noble.on('stateChange', (state) => {
        if (state === 'poweredOn') {
          this.startScanning();
        }
      });
      
      noble.on('discover', (peripheral) => {
        const name = peripheral.advertisement.localName;
        if (name && (name.includes('iPhone') || name.includes('Apple'))) {
          console.log('📱 Found iPhone:', name, peripheral.address);
          
          peripheral.connect((error) => {
            if (!error) {
              console.log('✅ Bluetooth connected to iPhone!');
              this.connectedDevice = peripheral;
              this.emit('connected', peripheral);
              
              // Discover services for data access
              peripheral.discoverAllServicesAndCharacteristics((err, services) => {
                if (!err) {
                  console.log('🔍 Discovered services:', services.length);
                  resolve(peripheral);
                }
              });
            } else {
              console.log('❌ Connection failed:', error.message);
            }
          });
        }
      });
      
      if (noble.state === 'poweredOn') {
        this.startScanning();
      }
    });
  }

  startScanning() {
    if (!this.isScanning) {
      noble.startScanning();
      this.isScanning = true;
      console.log('🔄 Bluetooth scanning started...');
    }
  }

  stopScanning() {
    noble.stopScanning();
    this.isScanning = false;
  }
}

module.exports = { BluetoothEnhanced };`;
    
    fs.writeFileSync('src/main/services/BluetoothEnhanced.js', btConfig);
    console.log('✅ Enhanced Bluetooth service created');
    this.connections.bluetooth = true;
    console.log('');
  }

  async setupAirPlay() {
    console.log('📺 AIRPLAY SETUP:');
    
    // Fixed AirPlay server with proper advertisement
    const airplayFix = `const bonjour = require('bonjour-service')();
const http = require('http');
const { EventEmitter } = require('events');

class AirPlayFixed extends EventEmitter {
  constructor() {
    super();
    this.server = null;
    this.advertisement = null;
  }

  start() {
    console.log('🚀 Starting AirPlay server...');
    
    // Create HTTP server for AirPlay protocol
    this.server = http.createServer((req, res) => {
      console.log('📡 AirPlay request:', req.method, req.url);
      
      if (req.url === '/server-info') {
        res.writeHead(200, { 
          'Content-Type': 'text/x-apple-plist+xml',
          'Access-Control-Allow-Origin': '*'
        });
        res.end(\`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>deviceid</key>
  <string>AA:BB:CC:DD:EE:FF</string>
  <key>features</key>
  <integer>0x5A7FFEE6</integer>
  <key>model</key>
  <string>AppleTV3,1</string>
  <key>protovers</key>
  <string>1.0</string>
  <key>srcvers</key>
  <string>220.68</string>
  <key>vv</key>
  <integer>2</integer>
</dict>
</plist>\`);
      } else if (req.url === '/photo') {
        // Handle photo streaming
        this.emit('photo', req);
        res.writeHead(200);
        res.end();
      } else if (req.url === '/play') {
        // Handle video streaming
        this.emit('video', req);
        res.writeHead(200);
        res.end();
      }
    });
    
    this.server.listen(7000, '0.0.0.0', () => {
      console.log('✅ AirPlay HTTP server running on port 7000');
      
      // Advertise service with Bonjour
      this.advertisement = bonjour.publish({
        name: 'iPhone Companion Pro',
        type: 'airplay',
        port: 7000,
        txt: {
          deviceid: 'AA:BB:CC:DD:EE:FF',
          features: '0x5A7FFEE6',
          model: 'AppleTV3,1',
          srcvers: '220.68',
          vv: '2'
        }
      });
      
      console.log('📡 AirPlay service advertised');
      this.emit('ready');
    });
  }

  stop() {
    if (this.server) {
      this.server.close();
    }
    if (this.advertisement) {
      this.advertisement.destroy();
    }
  }
}

module.exports = { AirPlayFixed };`;
    
    fs.writeFileSync('src/main/services/AirPlayFixed.js', airplayFix);
    
    // Open firewall ports for AirPlay
    exec('netsh advfirewall firewall add rule name="AirPlay-7000" dir=in action=allow protocol=TCP localport=7000', (err) => {
      if (!err) console.log('✅ Firewall rule added for port 7000');
    });
    
    console.log('✅ AirPlay service created');
    this.connections.airplay = true;
    console.log('');
  }

  async setupMacOSBridge() {
    console.log('🖥️ MACOS VM BRIDGE SETUP:');
    
    // Enhanced VM Bridge with network discovery
    const vmBridge = `const WebSocket = require('ws');
const { EventEmitter } = require('events');
const { exec } = require('child_process');

class VMBridge extends EventEmitter {
  constructor() {
    super();
    this.ws = null;
    this.vmIP = null;
    this.reconnectInterval = null;
    this.isConnected = false;
  }

  async findVM() {
    console.log('🔍 Scanning network for macOS VM...');
    
    // Common VM network ranges
    const ranges = [
      '192.168.1.',   // Common home network
      '192.168.0.',   // Alternative home network
      '10.0.0.',      // Corporate network
      '172.16.0.',    // VMware default
      '192.168.56.',  // VirtualBox default
      '192.168.122.'  // QEMU default
    ];
    
    for (const prefix of ranges) {
      for (let i = 1; i < 255; i++) {
        const ip = prefix + i;
        try {
          const ws = new WebSocket(\`ws://\${ip}:8888\`, { 
            timeout: 200,
            handshakeTimeout: 200
          });
          
          const found = await new Promise((resolve) => {
            const timer = setTimeout(() => resolve(false), 200);
            
            ws.on('open', () => {
              clearTimeout(timer);
              console.log('✅ Found macOS VM at:', ip);
              this.vmIP = ip;
              ws.close();
              resolve(true);
            });
            
            ws.on('error', () => {
              clearTimeout(timer);
              resolve(false);
            });
          });
          
          if (found) return this.vmIP;
        } catch (e) {
          // Continue scanning
        }
      }
    }
    
    // Try localhost as fallback
    console.log('🔄 Trying localhost...');
    this.vmIP = 'localhost';
    return this.vmIP;
  }

  async connect() {
    if (!this.vmIP) {
      await this.findVM();
    }
    
    console.log(\`🔗 Connecting to VM at \${this.vmIP}...\`);
    this.setupConnection();
  }

  setupConnection() {
    try {
      this.ws = new WebSocket(\`ws://\${this.vmIP}:8888\`);
      
      this.ws.on('open', () => {
        console.log('✅ Connected to macOS VM!');
        this.isConnected = true;
        this.emit('connected');
        
        // Request initial sync
        this.ws.send(JSON.stringify({ 
          type: 'SYNC_ALL',
          timestamp: Date.now()
        }));
      });
      
      this.ws.on('message', (data) => {
        try {
          const msg = JSON.parse(data);
          console.log('📨 VM data:', msg.type);
          this.emit('data', msg);
        } catch (e) {
          console.log('❌ Invalid VM message:', e.message);
        }
      });
      
      this.ws.on('close', () => {
        console.log('🔄 VM disconnected, reconnecting in 5s...');
        this.isConnected = false;
        setTimeout(() => this.connect(), 5000);
      });
      
      this.ws.on('error', (error) => {
        console.log('❌ VM connection error:', error.message);
      });
    } catch (e) {
      console.log('❌ Failed to connect to VM:', e.message);
    }
  }

  sendCommand(command) {
    if (this.isConnected && this.ws) {
      this.ws.send(JSON.stringify(command));
    }
  }
}

module.exports = { VMBridge };`;
    
    fs.writeFileSync('src/main/services/VMBridge.js', vmBridge);
    console.log('✅ VM Bridge service created');
    this.connections.vm = true;
    console.log('');
  }

  async setupPhoneLink() {
    console.log('🔗 PHONE LINK SETUP:');

    // Enhanced Phone Link bridge with database discovery
    const phoneLinkBridge = `const sqlite3 = require('sqlite3');
const fs = require('fs');
const path = require('path');
const { EventEmitter } = require('events');

class PhoneLinkBridge extends EventEmitter {
  constructor() {
    super();
    this.databases = [];
    this.isConnected = false;
  }

  async findDatabase() {
    console.log('🔍 Searching for Phone Link databases...');

    const possiblePaths = [
      path.join(process.env.LOCALAPPDATA, 'Packages/Microsoft.YourPhone_8wekyb3d8bbwe/LocalState'),
      path.join(process.env.LOCALAPPDATA, 'Packages/Microsoft.WindowsCommunicationsApps_8wekyb3d8bbwe/LocalState'),
      path.join(process.env.APPDATA, 'Microsoft/YourPhone'),
      path.join(process.env.APPDATA, 'Microsoft/PhoneLink'),
      path.join(process.env.LOCALAPPDATA, 'Microsoft/YourPhone'),
      path.join(process.env.PROGRAMDATA, 'Microsoft/YourPhone')
    ];

    for (const basePath of possiblePaths) {
      if (fs.existsSync(basePath)) {
        console.log('📁 Found Phone Link directory:', basePath);
        await this.scanDirectory(basePath);
      }
    }

    if (this.databases.length > 0) {
      console.log(\`✅ Found \${this.databases.length} Phone Link databases\`);
      await this.connectToDatabase();
    } else {
      console.log('⚠️ No Phone Link databases found');
      console.log('   Make sure Phone Link is installed and paired');
    }
  }

  async scanDirectory(dirPath) {
    try {
      const files = fs.readdirSync(dirPath);

      for (const file of files) {
        const fullPath = path.join(dirPath, file);

        if (fs.statSync(fullPath).isDirectory()) {
          await this.scanDirectory(fullPath);
        } else if (file.endsWith('.db') || file.endsWith('.sqlite')) {
          console.log('🗄️ Found database:', file);
          this.databases.push(fullPath);
        }
      }
    } catch (e) {
      // Permission denied or other error
    }
  }

  async connectToDatabase() {
    for (const dbPath of this.databases) {
      try {
        const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY);

        await new Promise((resolve) => {
          db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {
            if (!err && tables.length > 0) {
              console.log(\`📊 Database \${path.basename(dbPath)} has tables:\`, tables.map(t => t.name));

              // Look for message-related tables
              const msgTables = tables.filter(t =>
                t.name.toLowerCase().includes('message') ||
                t.name.toLowerCase().includes('sms') ||
                t.name.toLowerCase().includes('conversation') ||
                t.name.toLowerCase().includes('chat')
              );

              if (msgTables.length > 0) {
                console.log('💬 Found message tables:', msgTables.map(t => t.name));
                this.extractMessages(db, msgTables[0].name);
              }
            }
            resolve();
          });
        });

        db.close();
      } catch (e) {
        console.log(\`❌ Could not read database \${path.basename(dbPath)}: \${e.message}\`);
      }
    }
  }

  extractMessages(db, tableName) {
    db.all(\`SELECT * FROM \${tableName} LIMIT 10\`, (err, rows) => {
      if (!err && rows.length > 0) {
        console.log(\`✅ Found \${rows.length} messages in Phone Link!\`);
        this.isConnected = true;
        this.emit('messages', rows);
      }
    });
  }
}

module.exports = { PhoneLinkBridge };`;

    fs.writeFileSync('src/main/services/PhoneLinkBridge.js', phoneLinkBridge);

    // Check if Phone Link is running
    exec('tasklist | findstr YourPhone', (err, stdout) => {
      if (stdout && stdout.includes('YourPhone')) {
        console.log('✅ Phone Link is running');
      } else {
        console.log('⚠️ Phone Link not running - start it from Start Menu');
      }
    });

    console.log('✅ Phone Link bridge created');
    this.connections.phoneLink = true;
    console.log('');
  }

  async setupUSB() {
    console.log('🔌 USB SETUP:');

    // Check for iTunes/Apple Mobile Device Support
    exec('reg query "HKLM\\SOFTWARE\\Apple Inc.\\Apple Mobile Device Support"', (err) => {
      if (err) {
        console.log('❌ iTunes/Apple Mobile Device Support not found');
        console.log('   Download iTunes from: https://www.apple.com/itunes/download/win64');
      } else {
        console.log('✅ Apple Mobile Device Support found');
      }
    });

    // Enhanced USB connection with device detection
    const usbScript = `const { exec } = require('child_process');
const { EventEmitter } = require('events');

class USBConnection extends EventEmitter {
  constructor() {
    super();
    this.connectedDevices = [];
    this.isMonitoring = false;
  }

  async detectiPhone() {
    console.log('🔍 Scanning for iPhone via USB...');

    return new Promise((resolve) => {
      // Use Windows WMI to detect Apple devices
      const wmiCmd = \`
        wmic path Win32_PnPEntity where "Name like '%Apple%' OR Name like '%iPhone%'" get Name,Status,DeviceID
      \`;

      exec(wmiCmd, (err, stdout) => {
        if (!err && stdout.includes('Apple')) {
          console.log('✅ Apple device detected via USB!');
          console.log(stdout);

          // Try to get device info with libimobiledevice
          exec('idevice_id -l', (err2, stdout2) => {
            if (!err2 && stdout2.trim()) {
              const deviceId = stdout2.trim();
              console.log('📱 Device UDID:', deviceId);

              // Get device info
              exec(\`ideviceinfo -u \${deviceId}\`, (err3, stdout3) => {
                if (!err3) {
                  console.log('📋 Device info retrieved');
                  this.emit('connected', { id: deviceId, info: stdout3 });
                }
              });

              resolve(deviceId);
            } else {
              console.log('⚠️ libimobiledevice not found - install for full USB support');
              resolve(null);
            }
          });
        } else {
          console.log('❌ No iPhone detected via USB');
          console.log('   Make sure iPhone is connected and "Trust This Computer" is accepted');
          resolve(null);
        }
      });
    });
  }

  startMonitoring() {
    if (!this.isMonitoring) {
      this.isMonitoring = true;
      console.log('👀 Starting USB device monitoring...');

      // Poll for device changes every 5 seconds
      setInterval(() => {
        this.detectiPhone();
      }, 5000);
    }
  }

  stopMonitoring() {
    this.isMonitoring = false;
  }
}

module.exports = { USBConnection };`;

    fs.writeFileSync('src/main/services/USBConnection.js', usbScript);
    console.log('✅ USB connection service created');
    this.connections.usb = true;
    console.log('');
  }

  async createMasterManager() {
    console.log('🎯 CREATING MASTER CONNECTION MANAGER:');

    const masterManager = `const { BluetoothEnhanced } = require('./BluetoothEnhanced');
const { AirPlayFixed } = require('./AirPlayFixed');
const { VMBridge } = require('./VMBridge');
const { PhoneLinkBridge } = require('./PhoneLinkBridge');
const { USBConnection } = require('./USBConnection');
const { EventEmitter } = require('events');

class MasterConnectionManager extends EventEmitter {
  constructor() {
    super();
    this.connections = {
      bluetooth: new BluetoothEnhanced(),
      airplay: new AirPlayFixed(),
      vm: new VMBridge(),
      phoneLink: new PhoneLinkBridge(),
      usb: new USBConnection()
    };

    this.activeConnections = new Set();
    this.dataStreams = new Map();
  }

  async initializeAll() {
    console.log('🚀 Initializing ALL iPhone connections...');

    // Setup event listeners for each connection
    Object.entries(this.connections).forEach(([name, connection]) => {
      connection.on('connected', () => {
        console.log(\`✅ \${name.toUpperCase()} connected!\`);
        this.activeConnections.add(name);
        this.emit('connectionAdded', name);
      });

      connection.on('data', (data) => {
        this.handleData(name, data);
      });

      connection.on('error', (error) => {
        console.log(\`❌ \${name.toUpperCase()} error:\`, error.message);
        this.activeConnections.delete(name);
      });
    });

    // Try all connections simultaneously
    const results = await Promise.allSettled([
      this.connections.bluetooth.findIPhone(),
      this.connections.airplay.start(),
      this.connections.vm.connect(),
      this.connections.phoneLink.findDatabase(),
      this.connections.usb.detectiPhone()
    ]);

    // Report results
    const methods = ['Bluetooth', 'AirPlay', 'VM Bridge', 'Phone Link', 'USB'];
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        console.log(\`✅ \${methods[index]} setup completed\`);
      } else {
        console.log(\`⚠️ \${methods[index]} setup failed:\`, result.reason?.message || 'Unknown error');
      }
    });

    console.log(\`\\n🎉 Active connections: \${this.activeConnections.size}/5\`);
    return this.activeConnections;
  }

  handleData(source, data) {
    console.log(\`📨 Data from \${source}:\`, data.type || 'unknown');

    // Store data stream
    if (!this.dataStreams.has(source)) {
      this.dataStreams.set(source, []);
    }
    this.dataStreams.get(source).push({
      timestamp: Date.now(),
      data: data
    });

    // Emit unified data event
    this.emit('data', {
      source: source,
      data: data,
      timestamp: Date.now()
    });
  }

  getConnectionStatus() {
    return {
      active: Array.from(this.activeConnections),
      total: Object.keys(this.connections).length,
      dataStreams: Object.fromEntries(this.dataStreams)
    };
  }

  async testAllConnections() {
    console.log('🧪 Testing all connections...');

    for (const [name, connection] of Object.entries(this.connections)) {
      try {
        if (name === 'bluetooth' && connection.findIPhone) {
          await connection.findIPhone();
        } else if (name === 'airplay' && connection.start) {
          connection.start();
        } else if (name === 'vm' && connection.connect) {
          await connection.connect();
        } else if (name === 'phoneLink' && connection.findDatabase) {
          await connection.findDatabase();
        } else if (name === 'usb' && connection.detectiPhone) {
          await connection.detectiPhone();
        }
      } catch (error) {
        console.log(\`❌ \${name} test failed:\`, error.message);
      }
    }
  }
}

module.exports = { MasterConnectionManager };`;

    fs.writeFileSync('src/main/services/MasterConnectionManager.js', masterManager);
    console.log('✅ Master Connection Manager created');
    console.log('');
  }
}

// Run the setup
const master = new ConnectionMaster();
master.setupAllConnections().catch(console.error);
