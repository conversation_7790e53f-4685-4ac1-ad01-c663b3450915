/**
 * 🎯 CENTRALIZED LOGGING SYSTEM FOR IPHONE COMPANION PRO
 * Professional logging with levels, filtering, and terminal buffering
 * Fixes terminal spam and improves debugging experience
 */

const fs = require('fs');
const path = require('path');
const { app } = require('electron');

class Logger {
    constructor() {
        this.logLevels = {
            ERROR: 0,
            WARN: 1,
            INFO: 2,
            DEBUG: 3,
            TRACE: 4
        };
        
        this.currentLogLevel = process.env.LOG_LEVEL 
            ? this.logLevels[process.env.LOG_LEVEL.toUpperCase()] || this.logLevels.INFO
            : this.logLevels.INFO;
        
        this.colors = {
            ERROR: '\x1b[31m',   // Red
            WARN: '\x1b[33m',    // Yellow
            INFO: '\x1b[36m',    // Cyan
            DEBUG: '\x1b[35m',   // Magenta
            TRACE: '\x1b[37m',   // White
            RESET: '\x1b[0m'     // Reset
        };
        
        this.icons = {
            ERROR: '❌',
            WARN: '⚠️',
            INFO: 'ℹ️',
            DEBUG: '🔍',
            TRACE: '📝'
        };
        
        // Initialize log file
        this.setupLogFile();
        
        // Buffer for batching console output
        this.logBuffer = [];
        this.bufferTimer = null;
        this.bufferDelay = 100; // ms
        
        // Statistics
        this.stats = {
            ERROR: 0,
            WARN: 0,
            INFO: 0,
            DEBUG: 0,
            TRACE: 0
        };
        
        console.log('🎯 Logger initialized with level:', this.getLevelName(this.currentLogLevel));
    }
    
    setupLogFile() {
        try {
            const userDataPath = app.getPath('userData');
            const logsDir = path.join(userDataPath, 'logs');
            
            // Create logs directory if it doesn't exist
            if (!fs.existsSync(logsDir)) {
                fs.mkdirSync(logsDir, { recursive: true });
            }
            
            // Create log file with timestamp
            const timestamp = new Date().toISOString().split('T')[0];
            this.logFilePath = path.join(logsDir, `iphone-companion-${timestamp}.log`);
            
            // Write initial log entry
            this.writeToFile(`[${new Date().toISOString()}] [INFO] 🎯 Logger initialized - iPhone Companion Pro\n`);
            
        } catch (error) {
            console.error('Failed to setup log file:', error);
            this.logFilePath = null;
        }
    }
    
    writeToFile(content) {
        if (this.logFilePath) {
            try {
                fs.appendFileSync(this.logFilePath, content);
            } catch (error) {
                // Silent fail to prevent log loops
            }
        }
    }
    
    getLevelName(level) {
        return Object.keys(this.logLevels).find(key => this.logLevels[key] === level) || 'UNKNOWN';
    }
    
    shouldLog(level) {
        return this.logLevels[level] <= this.currentLogLevel;
    }
    
    formatMessage(level, category, message, data = null) {
        const timestamp = new Date().toISOString();
        const icon = this.icons[level] || '📝';
        const color = this.colors[level] || '';
        const reset = this.colors.RESET;
        
        // Create clean, readable format
        let formattedMessage = `${color}[${timestamp}] ${icon} [${level}] [${category}]${reset} ${message}`;
        
        // Add data if provided
        if (data) {
            if (typeof data === 'object') {
                formattedMessage += `\n${JSON.stringify(data, null, 2)}`;
            } else {
                formattedMessage += ` - ${data}`;
            }
        }
        
        return formattedMessage;
    }
    
    addToBuffer(formattedMessage, level) {
        this.logBuffer.push({ message: formattedMessage, level, timestamp: Date.now() });
        
        // Clear existing timer
        if (this.bufferTimer) {
            clearTimeout(this.bufferTimer);
        }
        
        // Set new timer to flush buffer
        this.bufferTimer = setTimeout(() => {
            this.flushBuffer();
        }, this.bufferDelay);
        
        // Force flush on errors
        if (level === 'ERROR') {
            this.flushBuffer();
        }
    }
    
    flushBuffer() {
        if (this.logBuffer.length === 0) return;
        
        // Group similar messages to reduce spam
        const grouped = this.groupSimilarMessages(this.logBuffer);
        
        // Output to console
        grouped.forEach(item => {
            if (item.count > 1) {
                console.log(`${item.message} (${item.count} times)`);
            } else {
                console.log(item.message);
            }
            
            // Write to file (without colors)
            const cleanMessage = item.message.replace(/\x1b\[[0-9;]*m/g, '');
            this.writeToFile(`${cleanMessage}\n`);
        });
        
        // Clear buffer
        this.logBuffer = [];
        this.bufferTimer = null;
    }
    
    groupSimilarMessages(messages) {
        const groups = new Map();
        
        messages.forEach(item => {
            // Remove timestamp for grouping
            const key = item.message.replace(/\[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z\]/, '[TIMESTAMP]');
            
            if (groups.has(key)) {
                groups.get(key).count++;
            } else {
                groups.set(key, { message: item.message, count: 1 });
            }
        });
        
        return Array.from(groups.values());
    }
    
    log(level, category, message, data = null) {
        if (!this.shouldLog(level)) return;
        
        // Update statistics
        this.stats[level]++;
        
        // Format message
        const formattedMessage = this.formatMessage(level, category, message, data);
        
        // Add to buffer for batched output
        this.addToBuffer(formattedMessage, level);
    }
    
    // Convenience methods
    error(category, message, data = null) {
        this.log('ERROR', category, message, data);
    }
    
    warn(category, message, data = null) {
        this.log('WARN', category, message, data);
    }
    
    info(category, message, data = null) {
        this.log('INFO', category, message, data);
    }
    
    debug(category, message, data = null) {
        this.log('DEBUG', category, message, data);
    }
    
    trace(category, message, data = null) {
        this.log('TRACE', category, message, data);
    }
    
    // Special methods for specific categories
    database(level, message, data = null) {
        this.log(level, 'DATABASE', message, data);
    }
    
    iphone(level, message, data = null) {
        this.log(level, 'IPHONE', message, data);
    }
    
    sync(level, message, data = null) {
        this.log(level, 'SYNC', message, data);
    }
    
    ui(level, message, data = null) {
        this.log(level, 'UI', message, data);
    }
    
    network(level, message, data = null) {
        this.log(level, 'NETWORK', message, data);
    }
    
    crm(level, message, data = null) {
        this.log(level, 'CRM', message, data);
    }
    
    // Performance monitoring
    startTimer(label) {
        this.timers = this.timers || new Map();
        this.timers.set(label, Date.now());
        this.debug('PERFORMANCE', `Timer started: ${label}`);
    }
    
    endTimer(label) {
        if (!this.timers || !this.timers.has(label)) {
            this.warn('PERFORMANCE', `Timer not found: ${label}`);
            return 0;
        }
        
        const duration = Date.now() - this.timers.get(label);
        this.timers.delete(label);
        this.info('PERFORMANCE', `Timer ${label}: ${duration}ms`);
        return duration;
    }
    
    // System status
    getStats() {
        return {
            ...this.stats,
            totalLogs: Object.values(this.stats).reduce((a, b) => a + b, 0),
            logLevel: this.getLevelName(this.currentLogLevel),
            logFile: this.logFilePath
        };
    }
    
    printStats() {
        const stats = this.getStats();
        this.info('LOGGER', 'Logging Statistics:', stats);
    }
    
    // Graceful shutdown
    shutdown() {
        this.info('LOGGER', 'Logger shutting down...');
        this.flushBuffer();
        this.printStats();
        
        if (this.bufferTimer) {
            clearTimeout(this.bufferTimer);
        }
    }
}

// Create singleton instance
const logger = new Logger();

// Handle process shutdown
process.on('exit', () => {
    logger.shutdown();
});

process.on('SIGINT', () => {
    logger.shutdown();
    process.exit(0);
});

process.on('uncaughtException', (error) => {
    logger.error('SYSTEM', 'Uncaught Exception:', error);
    logger.shutdown();
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('SYSTEM', 'Unhandled Promise Rejection:', { reason, promise: promise.toString() });
});

module.exports = logger;