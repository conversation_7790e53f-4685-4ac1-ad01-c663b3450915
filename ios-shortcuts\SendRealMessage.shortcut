# Send Real Message Shortcut
# This shortcut sends actual messages through iPhone Messages app

# Shortcut Name: "Send Message from PC"
# Description: Receives message requests from <PERSON> and sends via Messages app
# Trigger: URL scheme or HTTP request

# Actions:
1. Receive Message Request
   - Get input from URL or HTTP request
   - Parse JSON data containing:
     - recipient: phone number or contact
     - message: text content
     - requestId: unique identifier

2. Validate Input
   - Check if recipient is valid
   - Ensure message content exists
   - Verify request authenticity

3. Send via Messages App
   - Use "Send Message" action
   - Recipient: Real phone number/contact
   - Message: Actual text content
   - Wait for delivery confirmation

4. Send Confirmation to PC
   - URL: http://YOUR_PC_IP:8888/message-sent
   - Method: POST
   - Body: Delivery status and message ID

# URL Scheme Format:
shortcuts://run-shortcut?name=Send%20Message%20from%20PC&input={"recipient":"+1234567890","message":"Hello from PC","requestId":"12345"}

# HTTP Endpoint:
POST http://YOUR_PC_IP:8888/send-message
{
  "recipient": "+1234567890",
  "message": "Hello from Windows PC!",
  "requestId": "unique_id_12345"
}

# Response Format:
{
  "success": true,
  "messageId": "msg_12345",
  "timestamp": "2024-01-01T12:00:00Z",
  "deliveryStatus": "sent",
  "requestId": "unique_id_12345"
}

# Error Handling:
- Invalid recipient: Return error with details
- Message too long: Truncate or split
- Network error: Retry mechanism
- Permission denied: Request user authorization

# Security:
- Validate PC IP address
- Check request signatures
- Rate limiting for message sending
- User confirmation for sensitive messages
