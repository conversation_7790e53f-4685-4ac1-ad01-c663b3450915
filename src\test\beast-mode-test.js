const { BeastPersistence } = require('../src/main/services/BeastPersistence');
const { MessageService } = require('../src/main/services/MessageService');
const path = require('path');
const fs = require('fs');

class BeastModeTest {
  constructor() {
    this.testResults = [];
    this.persistence = null;
    this.messageService = null;
  }

  async runAllTests() {
    console.log('🔥 STARTING BEAST MODE COMPREHENSIVE TESTS 🔥\n');

    try {
      await this.testPersistenceInitialization();
      await this.testMessagePersistence();
      await this.testConversationPersistence();
      await this.testCallPersistence();
      await this.testBackupSystem();
      await this.testAutoReconnect();
      await this.testDataRecovery();
      await this.testPerformance();

      this.printResults();
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    } finally {
      await this.cleanup();
    }
  }

  async testPersistenceInitialization() {
    console.log('📂 Testing BEAST Persistence Initialization...');
    
    try {
      this.persistence = new BeastPersistence();
      await this.persistence.initialize();
      
      this.addResult('Persistence Initialization', true, 'Database created and initialized successfully');
      console.log('✅ Persistence initialization: PASSED\n');
    } catch (error) {
      this.addResult('Persistence Initialization', false, error.message);
      console.log('❌ Persistence initialization: FAILED\n');
    }
  }

  async testMessagePersistence() {
    console.log('💬 Testing Message Persistence...');
    
    try {
      const testMessage = {
        id: 'test-msg-1',
        conversationId: '+1234567890',
        contactName: 'Test Contact',
        phoneNumber: '+1234567890',
        text: 'This is a test message from BEAST mode!',
        timestamp: new Date(),
        isIncoming: true,
        isRead: false,
        attachments: [],
        source: 'beast-test'
      };

      await this.persistence.saveMessage(testMessage);
      
      const savedMessages = await this.persistence.loadAllMessages();
      const foundMessage = savedMessages.find(msg => msg.id === 'test-msg-1');
      
      if (foundMessage && foundMessage.text === testMessage.text) {
        this.addResult('Message Persistence', true, 'Messages saved and loaded correctly');
        console.log('✅ Message persistence: PASSED\n');
      } else {
        throw new Error('Message not found or corrupted');
      }
    } catch (error) {
      this.addResult('Message Persistence', false, error.message);
      console.log('❌ Message persistence: FAILED\n');
    }
  }

  async testConversationPersistence() {
    console.log('💭 Testing Conversation Persistence...');
    
    try {
      const testConversation = {
        id: '+1234567890',
        phoneNumber: '+1234567890',
        contactName: 'Test Contact',
        lastMessage: 'Last message in conversation',
        lastMessageTime: new Date(),
        unreadCount: 3,
        avatar: null
      };

      await this.persistence.saveConversation(testConversation);
      
      const savedConversations = await this.persistence.loadConversations();
      const foundConversation = savedConversations.find(conv => conv.id === '+1234567890');
      
      if (foundConversation && foundConversation.contactName === testConversation.contactName) {
        this.addResult('Conversation Persistence', true, 'Conversations saved and loaded correctly');
        console.log('✅ Conversation persistence: PASSED\n');
      } else {
        throw new Error('Conversation not found or corrupted');
      }
    } catch (error) {
      this.addResult('Conversation Persistence', false, error.message);
      console.log('❌ Conversation persistence: FAILED\n');
    }
  }

  async testCallPersistence() {
    console.log('📞 Testing Call Persistence...');
    
    try {
      const testCall = {
        id: 'test-call-1',
        phoneNumber: '+1234567890',
        contactName: 'Test Contact',
        duration: 120,
        timestamp: new Date(),
        type: 'incoming',
        missed: false
      };

      await this.persistence.saveCall(testCall);
      
      const savedCalls = await this.persistence.loadCalls();
      const foundCall = savedCalls.find(call => call.id === 'test-call-1');
      
      if (foundCall && foundCall.duration === testCall.duration) {
        this.addResult('Call Persistence', true, 'Calls saved and loaded correctly');
        console.log('✅ Call persistence: PASSED\n');
      } else {
        throw new Error('Call not found or corrupted');
      }
    } catch (error) {
      this.addResult('Call Persistence', false, error.message);
      console.log('❌ Call persistence: FAILED\n');
    }
  }

  async testBackupSystem() {
    console.log('💾 Testing Backup System...');
    
    try {
      await this.persistence.createBackup();
      
      // Check if backup was created
      const { app } = require('electron');
      const backupDir = path.join(app.getPath('userData'), 'backups');
      
      if (fs.existsSync(backupDir)) {
        const backups = fs.readdirSync(backupDir).filter(f => f.startsWith('backup-'));
        
        if (backups.length > 0) {
          this.addResult('Backup System', true, `${backups.length} backup(s) created successfully`);
          console.log('✅ Backup system: PASSED\n');
        } else {
          throw new Error('No backups found');
        }
      } else {
        throw new Error('Backup directory not created');
      }
    } catch (error) {
      this.addResult('Backup System', false, error.message);
      console.log('❌ Backup system: FAILED\n');
    }
  }

  async testAutoReconnect() {
    console.log('🔄 Testing Auto-Reconnect System...');
    
    try {
      this.messageService = new MessageService();
      await this.messageService.initialize();
      
      // Test if auto-reconnect interval is set
      if (this.messageService.autoReconnectInterval) {
        this.addResult('Auto-Reconnect', true, 'Auto-reconnect system is active');
        console.log('✅ Auto-reconnect: PASSED\n');
      } else {
        throw new Error('Auto-reconnect interval not set');
      }
    } catch (error) {
      this.addResult('Auto-Reconnect', false, error.message);
      console.log('❌ Auto-reconnect: FAILED\n');
    }
  }

  async testDataRecovery() {
    console.log('🔧 Testing Data Recovery...');
    
    try {
      // Test loading data after restart
      const newPersistence = new BeastPersistence();
      await newPersistence.initialize();
      
      const recoveredMessages = await newPersistence.loadAllMessages();
      const recoveredConversations = await newPersistence.loadConversations();
      const recoveredCalls = await newPersistence.loadCalls();
      
      if (recoveredMessages.length > 0 && recoveredConversations.length > 0 && recoveredCalls.length > 0) {
        this.addResult('Data Recovery', true, 'All data recovered successfully after restart');
        console.log('✅ Data recovery: PASSED\n');
      } else {
        throw new Error('Some data was lost during recovery');
      }
      
      newPersistence.close();
    } catch (error) {
      this.addResult('Data Recovery', false, error.message);
      console.log('❌ Data recovery: FAILED\n');
    }
  }

  async testPerformance() {
    console.log('⚡ Testing Performance...');
    
    try {
      const startTime = Date.now();
      
      // Test bulk operations
      const promises = [];
      for (let i = 0; i < 100; i++) {
        promises.push(this.persistence.saveMessage({
          id: `perf-test-${i}`,
          conversationId: '+1234567890',
          contactName: 'Performance Test',
          phoneNumber: '+1234567890',
          text: `Performance test message ${i}`,
          timestamp: new Date(),
          isIncoming: i % 2 === 0,
          isRead: false,
          attachments: [],
          source: 'performance-test'
        }));
      }
      
      await Promise.all(promises);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (duration < 5000) { // Should complete in under 5 seconds
        this.addResult('Performance', true, `100 messages saved in ${duration}ms`);
        console.log('✅ Performance: PASSED\n');
      } else {
        throw new Error(`Performance too slow: ${duration}ms`);
      }
    } catch (error) {
      this.addResult('Performance', false, error.message);
      console.log('❌ Performance: FAILED\n');
    }
  }

  addResult(testName, passed, details) {
    this.testResults.push({
      test: testName,
      passed,
      details,
      timestamp: new Date()
    });
  }

  printResults() {
    console.log('\n🔥 BEAST MODE TEST RESULTS 🔥\n');
    console.log('=' * 50);
    
    let passedCount = 0;
    let totalCount = this.testResults.length;
    
    this.testResults.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} | ${result.test}`);
      console.log(`     Details: ${result.details}\n`);
      
      if (result.passed) passedCount++;
    });
    
    console.log('=' * 50);
    console.log(`SUMMARY: ${passedCount}/${totalCount} tests passed`);
    
    if (passedCount === totalCount) {
      console.log('🎉 ALL TESTS PASSED! BEAST MODE IS FULLY OPERATIONAL! 🎉');
    } else {
      console.log('⚠️  Some tests failed. Please review the results above.');
    }
  }

  async cleanup() {
    if (this.persistence) {
      this.persistence.close();
    }
    
    if (this.messageService) {
      this.messageService.stop();
    }
  }
}

// Export for use in other files
module.exports = { BeastModeTest };

// Run tests if this file is executed directly
if (require.main === module) {
  const test = new BeastModeTest();
  test.runAllTests();
}
