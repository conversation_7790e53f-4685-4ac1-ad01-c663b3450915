<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Calls - iPhone Companion Pro</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0a0a;
            color: #fff;
            margin: 0;
            padding: 0;
            user-select: none;
        }
        
        .titlebar {
            height: 32px;
            background: #141414;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 12px;
            -webkit-app-region: drag;
            border-bottom: 1px solid #2a2a2a;
        }
        
        .titlebar-title {
            font-size: 13px;
            font-weight: 500;
        }
        
        .titlebar-controls {
            display: flex;
            -webkit-app-region: no-drag;
        }
        
        .titlebar-button {
            width: 46px;
            height: 32px;
            border: none;
            background: transparent;
            color: #999;
            cursor: pointer;
        }
        
        .titlebar-button:hover {
            background: #2a2a2a;
            color: #fff;
        }
        
        .calls-container {
            display: flex;
            height: calc(100vh - 32px);
        }
        
        .sidebar {
            width: 300px;
            background: #141414;
            border-right: 1px solid #2a2a2a;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #2a2a2a;
        }
        
        .sidebar-header h2 {
            margin: 0 0 15px 0;
            font-size: 24px;
        }
        
        .dial-pad-btn {
            width: 100%;
            padding: 12px;
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
        }
        
        .dial-pad-btn:hover {
            background: #0051D5;
        }
        
        .call-history {
            flex: 1;
            overflow-y: auto;
        }
        
        .call-item {
            padding: 15px 20px;
            border-bottom: 1px solid #2a2a2a;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .call-item:hover {
            background: #1a1a1a;
        }
        
        .call-contact {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .call-details {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
        }
        
        .call-direction {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .dialer-container {
            padding: 40px;
            text-align: center;
        }
        
        .phone-input {
            width: 300px;
            padding: 15px;
            font-size: 24px;
            text-align: center;
            background: #1a1a1a;
            border: 2px solid #2a2a2a;
            border-radius: 10px;
            color: #fff;
            margin-bottom: 30px;
        }
        
        .phone-input:focus {
            outline: none;
            border-color: #007AFF;
        }
        
        .dial-pad {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            max-width: 300px;
            margin: 0 auto 30px;
        }
        
        .dial-button {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: #2a2a2a;
            border: none;
            color: #fff;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .dial-button:hover {
            background: #3a3a3a;
            transform: scale(1.05);
        }
        
        .dial-button:active {
            transform: scale(0.95);
        }
        
        .call-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
        }
        
        .call-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .call-btn.call {
            background: #4CAF50;
            color: white;
        }
        
        .call-btn.call:hover {
            background: #45a049;
        }
        
        .call-btn.end {
            background: #f44336;
            color: white;
        }
        
        .call-btn.end:hover {
            background: #da190b;
        }
        
        .active-call {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #1a1a1a;
            border: 2px solid #2a2a2a;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            z-index: 1000;
            min-width: 300px;
        }
        
        .call-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: #007AFF;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
        }
        
        .call-info h3 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        
        .call-status {
            color: #999;
            margin-bottom: 30px;
        }
        
        .call-controls {
            display: flex;
            gap: 20px;
            justify-content: center;
        }
        
        .control-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .control-btn.answer {
            background: #4CAF50;
            color: white;
        }
        
        .control-btn.decline {
            background: #f44336;
            color: white;
        }
        
        .control-btn.mute {
            background: #666;
            color: white;
        }
        
        .control-btn.speaker {
            background: #666;
            color: white;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <!-- Title Bar -->
    <div class="titlebar">
        <div class="titlebar-title">Calls - iPhone Companion Pro</div>
        <div class="titlebar-controls">
            <button class="titlebar-button" onclick="require('electron').ipcRenderer.send('minimize-window')">−</button>
            <button class="titlebar-button" onclick="require('electron').ipcRenderer.send('maximize-window')">□</button>
            <button class="titlebar-button" onclick="require('electron').ipcRenderer.send('close-window')">×</button>
        </div>
    </div>

    <!-- Main Layout -->
    <div class="calls-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>Calls</h2>
                <button class="dial-pad-btn" onclick="showDialer()">📞 Make Call</button>
            </div>
            
            <div class="call-history" id="call-history">
                <!-- Call history will be loaded here -->
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <div class="dialer-container">
                <h1>Phone</h1>
                
                <input type="tel" class="phone-input" id="phone-input" placeholder="Enter phone number">
                
                <div class="dial-pad">
                    <button class="dial-button" onclick="addDigit('1')">1</button>
                    <button class="dial-button" onclick="addDigit('2')">2<br><small>ABC</small></button>
                    <button class="dial-button" onclick="addDigit('3')">3<br><small>DEF</small></button>
                    <button class="dial-button" onclick="addDigit('4')">4<br><small>GHI</small></button>
                    <button class="dial-button" onclick="addDigit('5')">5<br><small>JKL</small></button>
                    <button class="dial-button" onclick="addDigit('6')">6<br><small>MNO</small></button>
                    <button class="dial-button" onclick="addDigit('7')">7<br><small>PQRS</small></button>
                    <button class="dial-button" onclick="addDigit('8')">8<br><small>TUV</small></button>
                    <button class="dial-button" onclick="addDigit('9')">9<br><small>WXYZ</small></button>
                    <button class="dial-button" onclick="addDigit('*')">*</button>
                    <button class="dial-button" onclick="addDigit('0')">0<br><small>+</small></button>
                    <button class="dial-button" onclick="addDigit('#')">#</button>
                </div>
                
                <div class="call-actions">
                    <button class="call-btn call" onclick="makeCall()" title="Make Call">📞</button>
                    <button class="call-btn clear" onclick="clearInput()" title="Clear Input">⌫</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Call Overlay -->
    <div id="active-call" class="active-call hidden">
        <div class="call-avatar" id="call-avatar">👤</div>
        <div class="call-info">
            <h3 id="call-contact">Contact Name</h3>
            <div class="call-status" id="call-status">Calling...</div>
            <div id="call-duration" class="call-status"></div>
        </div>
        <div class="call-controls" id="call-controls">
            <!-- Controls will be dynamically added based on call state -->
        </div>
    </div>

    <script src="scripts/calls.js"></script>

<script>
const { ipcRenderer } = require('electron');

// Listen for calls data
ipcRenderer.on('calls-data', (event, calls) => {
  console.log('Received calls:', calls);
  
  const container = document.querySelector('.calls-container') || document.body;
  
  if (calls && calls.length > 0) {
    container.innerHTML = '<h2>Call History</h2>' + 
      calls.map(call => `
        <div class="call-item">
          <strong>${call.contactName || call.phoneNumber}</strong>
          <p>Duration: ${call.duration}s</p>
          <small>${new Date(call.timestamp).toLocaleString()}</small>
        </div>
      `).join('');
  } else {
    container.innerHTML = '<h2>Call History</h2><p>Loading call history...</p>';
  }
});
</script>

</body>
</html>
