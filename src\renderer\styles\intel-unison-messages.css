/* Intel Unison++ Messages - Modern UI Styles */

/* CSS Variables for theming */
:root {
  /* Colors */
  --primary-color: #007aff;
  --primary-dark: #0056cc;
  --secondary-color: #5856d6;
  --success-color: #34c759;
  --warning-color: #ff9500;
  --danger-color: #ff3b30;
  
  /* Backgrounds */
  --bg-primary: #ffffff;
  --bg-secondary: #f2f2f7;
  --bg-tertiary: #e5e5ea;
  --bg-dark: #1c1c1e;
  --bg-dark-secondary: #2c2c2e;
  --bg-dark-tertiary: #3a3a3c;
  
  /* Text colors */
  --text-primary: #000000;
  --text-secondary: #6d6d70;
  --text-tertiary: #99999c;
  --text-inverse: #ffffff;
  
  /* Borders */
  --border-color: #d1d1d6;
  --border-dark: #38383a;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* Border radius */
  --radius-sm: 6px;
  --radius-md: 12px;
  --radius-lg: 18px;
  --radius-full: 50%;
  
  /* Typography */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 24px;
  
  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.25s ease-out;
  --transition-slow: 0.4s ease-out;
}

/* Dark theme */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: var(--bg-dark);
    --bg-secondary: var(--bg-dark-secondary);
    --bg-tertiary: var(--bg-dark-tertiary);
    --text-primary: var(--text-inverse);
    --text-secondary: #aeaeb2;
    --text-tertiary: #8e8e93;
    --border-color: var(--border-dark);
  }
}

/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  height: 100vh;
  overflow: hidden;
  user-select: none;
}

.app-container {
  display: flex;
  height: 100vh;
  width: 100vw;
}

/* Sidebar */
.sidebar {
  width: 360px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  position: relative;
}

.sidebar-header {
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.avatar {
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-lg);
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  font-size: var(--font-size-md);
  color: var(--text-primary);
}

.sync-status {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.sync-status::before {
  content: '';
  width: 6px;
  height: 6px;
  background: var(--success-color);
  border-radius: var(--radius-full);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.sidebar-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
  color: var(--text-secondary);
}

.action-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.action-btn:active {
  transform: scale(0.95);
}

/* Search */
.search-container {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-primary);
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-wrapper .icon-search {
  position: absolute;
  left: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  z-index: 1;
}

#searchInput {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-xl);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  outline: none;
  transition: var(--transition-fast);
}

#searchInput:focus {
  border-color: var(--primary-color);
  background: var(--bg-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.search-clear {
  position: absolute;
  right: var(--spacing-xs);
  width: 24px;
  height: 24px;
  border: none;
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
}

/* Conversations */
.conversations-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.conversations-header {
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
}

.conversations-title {
  font-weight: 600;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
}

.conversations-count {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  font-weight: 600;
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 20px;
  text-align: center;
}

.conversations-list {
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
}

.conversations-list::-webkit-scrollbar {
  width: 6px;
}

.conversations-list::-webkit-scrollbar-track {
  background: transparent;
}

.conversations-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.conversation-item {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  position: relative;
}

.conversation-item:hover {
  background: var(--bg-tertiary);
}

.conversation-item.active {
  background: var(--primary-color);
  color: white;
}

.conversation-item.active .conversation-name,
.conversation-item.active .conversation-preview,
.conversation-item.active .conversation-time {
  color: white;
}

.conversation-avatar {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: var(--font-size-md);
  flex-shrink: 0;
}

.conversation-content {
  flex: 1;
  min-width: 0;
}

.conversation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.conversation-name {
  font-weight: 600;
  font-size: var(--font-size-md);
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-time {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  white-space: nowrap;
}

.conversation-preview {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
}

.unread-badge {
  background: var(--primary-color);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: 600;
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Welcome Screen */
.welcome-screen {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-primary);
}

.welcome-content {
  text-align: center;
  max-width: 400px;
  padding: var(--spacing-xl);
}

.welcome-icon {
  width: 80px;
  height: 80px;
  background: var(--primary-color);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-lg);
  color: white;
  font-size: 32px;
}

.welcome-content h2 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.welcome-content p {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.5;
}

.feature-highlights {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.feature {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.feature i {
  color: var(--primary-color);
  font-size: var(--font-size-md);
}

/* Chat Container */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-header-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.contact-avatar {
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-md);
}

.contact-details {
  display: flex;
  flex-direction: column;
}

.contact-name {
  font-weight: 600;
  font-size: var(--font-size-md);
  color: var(--text-primary);
}

.contact-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.status-indicator {
  width: 8px;
  height: 8px;
  background: var(--success-color);
  border-radius: var(--radius-full);
}

.chat-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* Messages */
.messages-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--bg-secondary);
}

.messages-scroll {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
}

.messages-scroll::-webkit-scrollbar {
  width: 6px;
}

.messages-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.messages-scroll::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.message {
  margin-bottom: var(--spacing-md);
  display: flex;
  flex-direction: column;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.outgoing {
  align-items: flex-end;
}

.message.incoming {
  align-items: flex-start;
}

.message-bubble {
  max-width: 70%;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  position: relative;
  cursor: pointer;
  transition: var(--transition-fast);
  word-wrap: break-word;
  line-height: 1.4;
}

.message.outgoing .message-bubble {
  background: var(--primary-color);
  color: white;
  border-bottom-right-radius: var(--spacing-xs);
}

.message.incoming .message-bubble {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-bottom-left-radius: var(--spacing-xs);
}

.message-bubble:hover {
  transform: scale(1.01);
  box-shadow: var(--shadow-md);
}

.message-text {
  margin: 0;
  font-size: var(--font-size-md);
}

.message-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-xs);
  opacity: 0.7;
}

.message.outgoing .message-meta {
  justify-content: flex-end;
  color: rgba(255, 255, 255, 0.8);
}

.message.incoming .message-meta {
  justify-content: flex-start;
  color: var(--text-secondary);
}

.message-time {
  white-space: nowrap;
}

.message-status {
  display: flex;
  align-items: center;
  gap: 2px;
}

.status-icon {
  width: 12px;
  height: 12px;
  opacity: 0.6;
}

/* Typing Indicator */
.typing-indicator {
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.typing-dots {
  display: flex;
  gap: 2px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: var(--text-secondary);
  border-radius: var(--radius-full);
  animation: typingDot 1.4s ease-in-out infinite;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingDot {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* Message Input */
.message-input-container {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-md);
}

.message-input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-sm);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm);
}

.text-input-wrapper {
  flex: 1;
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-sm);
  position: relative;
}

#messageInput {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  resize: none;
  font-family: inherit;
  font-size: var(--font-size-md);
  color: var(--text-primary);
  line-height: 1.4;
  max-height: 120px;
  min-height: 24px;
  padding: var(--spacing-xs) 0;
}

#messageInput::placeholder {
  color: var(--text-secondary);
}

.emoji-picker-trigger {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
}

.emoji-picker-trigger:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.attachment-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary);
  transition: var(--transition-fast);
}

.attachment-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.send-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: var(--primary-color);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: var(--transition-fast);
}

.send-btn:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: scale(1.05);
}

.send-btn:active {
  transform: scale(0.95);
}

.send-btn:disabled {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: not-allowed;
  transform: none;
}

.message-actions {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.quick-reply {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-md);
  cursor: pointer;
  transition: var(--transition-fast);
}

.quick-reply:hover {
  background: var(--bg-primary);
  transform: scale(1.1);
}

/* Context Menu */
.context-menu {
  position: fixed;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-xs);
  z-index: 1000;
  min-width: 160px;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  transition: var(--transition-fast);
}

.context-menu-item:hover {
  background: var(--bg-secondary);
}

.context-menu-separator {
  height: 1px;
  background: var(--border-color);
  margin: var(--spacing-xs) 0;
}

/* Loading and Notifications */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  background: var(--bg-primary);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-lg);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--bg-tertiary);
  border-top: 3px solid var(--primary-color);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.notification-toast {
  position: fixed;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-md);
  z-index: 1000;
  max-width: 300px;
  animation: toastSlideIn 0.3s ease-out;
}

@keyframes toastSlideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.toast-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.toast-icon {
  color: var(--primary-color);
  font-size: var(--font-size-md);
}

.toast-message {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.toast-close {
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary);
  margin-left: var(--spacing-sm);
}

.toast-close:hover {
  background: var(--bg-secondary);
}

/* Icons (using simple Unicode or you can replace with an icon font) */
.icon-user::before { content: '👤'; }
.icon-plus::before { content: '+'; }
.icon-search::before { content: '🔍'; }
.icon-settings::before { content: '⚙️'; }
.icon-x::before { content: '✕'; }
.icon-message-circle::before { content: '💬'; }
.icon-smartphone::before { content: '📱'; }
.icon-database::before { content: '🗄️'; }
.icon-zap::before { content: '⚡'; }
.icon-phone::before { content: '📞'; }
.icon-video::before { content: '📹'; }
.icon-info::before { content: 'ℹ️'; }
.icon-paperclip::before { content: '📎'; }
.icon-smile::before { content: '😊'; }
.icon-send::before { content: '➤'; }
.icon-copy::before { content: '📋'; }
.icon-corner-up-left::before { content: '↩️'; }
.icon-share::before { content: '↗️'; }
.icon-trash::before { content: '🗑️'; }

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    max-width: 360px;
  }
  
  .main-content {
    display: none;
  }
  
  .app-container.chat-open .sidebar {
    display: none;
  }
  
  .app-container.chat-open .main-content {
    display: flex;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles */
.action-btn:focus,
.conversation-item:focus,
.message-bubble:focus,
#messageInput:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --bg-tertiary: #e0e0e0;
  }
  
  .message-bubble {
    border-width: 2px;
  }
}