<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>File Manager - iPhone Companion Pro</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/file-manager.css">
</head>
<body>
    <div class="titlebar">
        <div class="titlebar-title">File Manager</div>
        <div class="titlebar-controls">
            <button class="titlebar-button" onclick="window.close()">✕</button>
        </div>
    </div>

    <div class="file-manager-container">
        <!-- Sidebar with device info and quick actions -->
        <div class="file-sidebar">
            <div class="device-info">
                <div class="device-icon">📱</div>
                <div class="device-details">
                    <h3 id="device-name">iPhone</h3>
                    <p id="device-storage">Loading...</p>
                    <div class="storage-bar">
                        <div class="storage-used" id="storage-used" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <div class="quick-actions">
                <h4>Quick Actions</h4>
                <button class="action-btn" onclick="uploadFiles()">
                    <span>📤</span> Upload to iPhone
                </button>
                <button class="action-btn" onclick="downloadSelected()">
                    <span>📥</span> Download Selected
                </button>
                <button class="action-btn" onclick="createFolder()">
                    <span>📁</span> New Folder
                </button>
                <button class="action-btn" onclick="refreshFiles()">
                    <span>🔄</span> Refresh
                </button>
            </div>

            <div class="file-categories">
                <h4>Categories</h4>
                <div class="category-list">
                    <button class="category-btn active" data-category="all" onclick="filterByCategory('all')">
                        <span>📋</span> All Files
                    </button>
                    <button class="category-btn" data-category="photos" onclick="filterByCategory('photos')">
                        <span>🖼️</span> Photos
                    </button>
                    <button class="category-btn" data-category="videos" onclick="filterByCategory('videos')">
                        <span>🎥</span> Videos
                    </button>
                    <button class="category-btn" data-category="documents" onclick="filterByCategory('documents')">
                        <span>📄</span> Documents
                    </button>
                    <button class="category-btn" data-category="music" onclick="filterByCategory('music')">
                        <span>🎵</span> Music
                    </button>
                    <button class="category-btn" data-category="apps" onclick="filterByCategory('apps')">
                        <span>📱</span> Apps
                    </button>
                </div>
            </div>
        </div>

        <!-- Main file browser area -->
        <div class="file-browser">
            <!-- Toolbar -->
            <div class="file-toolbar">
                <div class="toolbar-left">
                    <div class="breadcrumb" id="breadcrumb">
                        <span class="breadcrumb-item active" onclick="navigateToPath('/')">iPhone</span>
                    </div>
                </div>
                
                <div class="toolbar-center">
                    <div class="search-container">
                        <input type="text" id="file-search" placeholder="Search files..." onkeyup="searchFiles()">
                        <button class="search-btn" onclick="searchFiles()">🔍</button>
                    </div>
                </div>
                
                <div class="toolbar-right">
                    <div class="view-controls">
                        <button class="view-btn active" data-view="grid" onclick="setViewMode('grid')" title="Grid View">⊞</button>
                        <button class="view-btn" data-view="list" onclick="setViewMode('list')" title="List View">☰</button>
                    </div>
                    <div class="sort-controls">
                        <select id="sort-by" onchange="sortFiles()">
                            <option value="name">Name</option>
                            <option value="date">Date Modified</option>
                            <option value="size">Size</option>
                            <option value="type">Type</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- File list/grid -->
            <div class="file-content">
                <div class="file-list grid-view" id="file-list">
                    <!-- Files will be loaded here -->
                    <div class="loading-files">
                        <div class="spinner"></div>
                        <p>Loading files...</p>
                    </div>
                </div>
            </div>

            <!-- Transfer progress -->
            <div class="transfer-progress" id="transfer-progress" style="display: none;">
                <div class="progress-header">
                    <h4 id="transfer-title">Transferring files...</h4>
                    <button class="close-btn" onclick="hideTransferProgress()">✕</button>
                </div>
                <div class="progress-list" id="progress-list">
                    <!-- Transfer items will appear here -->
                </div>
            </div>
        </div>
    </div>

    <!-- File Upload Modal -->
    <div class="modal-overlay" id="upload-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Upload Files to iPhone</h2>
                <button class="modal-close" onclick="closeUploadModal()">✕</button>
            </div>
            <div class="modal-body">
                <div class="upload-area" id="upload-area" ondrop="handleFileDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                    <div class="upload-icon">📤</div>
                    <h3>Drag and drop files here</h3>
                    <p>or click to select files</p>
                    <input type="file" id="file-input" multiple style="display: none;" onchange="handleFileSelect(event)">
                    <button class="select-files-btn" onclick="document.getElementById('file-input').click()">Select Files</button>
                </div>
                <div class="upload-options">
                    <label>
                        <input type="checkbox" id="overwrite-existing" checked>
                        Overwrite existing files
                    </label>
                    <label>
                        <input type="checkbox" id="create-backup">
                        Create backup of existing files
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- File Details Modal -->
    <div class="modal-overlay" id="file-details-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>File Details</h2>
                <button class="modal-close" onclick="closeFileDetailsModal()">✕</button>
            </div>
            <div class="modal-body">
                <div class="file-details-content" id="file-details-content">
                    <!-- File details will be loaded here -->
                </div>
                <div class="file-actions">
                    <button class="action-btn primary" onclick="downloadFile()">Download</button>
                    <button class="action-btn secondary" onclick="shareFile()">Share</button>
                    <button class="action-btn secondary" onclick="renameFile()">Rename</button>
                    <button class="action-btn danger" onclick="deleteFile()">Delete</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Folder Modal -->
    <div class="modal-overlay" id="create-folder-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Create New Folder</h2>
                <button class="modal-close" onclick="closeCreateFolderModal()">✕</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="folder-name">Folder Name:</label>
                    <input type="text" id="folder-name" placeholder="Enter folder name..." autofocus>
                </div>
                <div class="form-actions">
                    <button class="action-btn secondary" onclick="closeCreateFolderModal()">Cancel</button>
                    <button class="action-btn primary" onclick="createNewFolder()">Create</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Context Menu -->
    <div class="context-menu" id="context-menu" style="display: none;">
        <div class="context-item" onclick="downloadContextFile()">📥 Download</div>
        <div class="context-item" onclick="shareContextFile()">📤 Share</div>
        <div class="context-item" onclick="renameContextFile()">✏️ Rename</div>
        <div class="context-item" onclick="copyContextFile()">📋 Copy</div>
        <div class="context-item" onclick="moveContextFile()">📁 Move</div>
        <div class="context-separator"></div>
        <div class="context-item" onclick="showContextFileDetails()">ℹ️ Details</div>
        <div class="context-item danger" onclick="deleteContextFile()">🗑️ Delete</div>
    </div>

    <script src="scripts/file-manager.js"></script>
</body>
</html>
