<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>macOS VM Bridge - iPhone Companion Pro</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/vm-bridge.css">
</head>
<body>
    <div class="titlebar">
        <div class="titlebar-title">macOS VM Bridge Configuration</div>
        <div class="titlebar-controls">
            <button class="titlebar-button" onclick="window.close()">✕</button>
        </div>
    </div>

    <div class="main-container">
        <div class="vm-bridge-container">
            <!-- Header Section -->
            <div class="vm-header">
                <div class="vm-icon">🖥️</div>
                <div class="vm-title">
                    <h1>macOS Virtual Machine Bridge</h1>
                    <p>Advanced iPhone integration through macOS virtualization</p>
                </div>
                <div class="vm-status" id="vmStatus">
                    <div class="status-indicator" id="statusIndicator"></div>
                    <span id="statusText">Checking...</span>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <button class="action-btn primary" id="enableBtn" onclick="toggleVMBridge()">
                    <span id="enableBtnText">Enable VM Bridge</span>
                </button>
                <button class="action-btn secondary" id="restartBtn" onclick="restartVMBridge()" disabled>
                    Restart Bridge
                </button>
                <button class="action-btn secondary" onclick="openVMConsole()" disabled id="consoleBtn">
                    VM Console
                </button>
            </div>

            <!-- Configuration Tabs -->
            <div class="config-tabs">
                <div class="tab-buttons">
                    <button class="tab-btn active" onclick="showTab('overview')">Overview</button>
                    <button class="tab-btn" onclick="showTab('configuration')">Configuration</button>
                    <button class="tab-btn" onclick="showTab('monitoring')">Monitoring</button>
                    <button class="tab-btn" onclick="showTab('logs')">Logs</button>
                </div>

                <!-- Overview Tab -->
                <div class="tab-content active" id="overviewTab">
                    <div class="overview-grid">
                        <div class="overview-card">
                            <h3>VM Status</h3>
                            <div class="metric-value" id="vmStatusMetric">Stopped</div>
                            <div class="metric-label">Virtual Machine</div>
                        </div>
                        <div class="overview-card">
                            <h3>Bridge Connection</h3>
                            <div class="metric-value" id="bridgeStatusMetric">Disconnected</div>
                            <div class="metric-label">WebSocket Tunnel</div>
                        </div>
                        <div class="overview-card">
                            <h3>Messages Synced</h3>
                            <div class="metric-value" id="messagesSyncedMetric">0</div>
                            <div class="metric-label">Total Messages</div>
                        </div>
                        <div class="overview-card">
                            <h3>Uptime</h3>
                            <div class="metric-value" id="uptimeMetric">00:00:00</div>
                            <div class="metric-label">Bridge Uptime</div>
                        </div>
                    </div>

                    <div class="feature-status">
                        <h3>Feature Status</h3>
                        <div class="feature-list">
                            <div class="feature-item">
                                <span class="feature-name">Messages Database Access</span>
                                <span class="feature-status-indicator" id="messagesDbStatus">❌</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-name">Real-time Message Sync</span>
                                <span class="feature-status-indicator" id="messageSyncStatus">❌</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-name">Contacts Integration</span>
                                <span class="feature-status-indicator" id="contactsStatus">❌</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-name">AppleScript Automation</span>
                                <span class="feature-status-indicator" id="applescriptStatus">❌</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Configuration Tab -->
                <div class="tab-content" id="configurationTab">
                    <div class="config-section">
                        <h3>VM Configuration</h3>
                        <div class="config-grid">
                            <div class="config-item">
                                <label for="vmMemory">Memory (GB)</label>
                                <select id="vmMemory" onchange="updateVMConfig()">
                                    <option value="4G">4 GB</option>
                                    <option value="6G">6 GB</option>
                                    <option value="8G" selected>8 GB</option>
                                    <option value="12G">12 GB</option>
                                    <option value="16G">16 GB</option>
                                </select>
                            </div>
                            <div class="config-item">
                                <label for="vmCores">CPU Cores</label>
                                <select id="vmCores" onchange="updateVMConfig()">
                                    <option value="2">2 Cores</option>
                                    <option value="4" selected>4 Cores</option>
                                    <option value="6">6 Cores</option>
                                    <option value="8">8 Cores</option>
                                </select>
                            </div>
                            <div class="config-item">
                                <label for="vmDiskSize">Disk Size (GB)</label>
                                <select id="vmDiskSize" onchange="updateVMConfig()">
                                    <option value="60G">60 GB</option>
                                    <option value="80G" selected>80 GB</option>
                                    <option value="120G">120 GB</option>
                                    <option value="160G">160 GB</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="config-section">
                        <h3>Network Configuration</h3>
                        <div class="config-grid">
                            <div class="config-item">
                                <label for="bridgePort">Bridge Port</label>
                                <input type="number" id="bridgePort" value="8080" min="1024" max="65535" onchange="updateVMConfig()">
                            </div>
                            <div class="config-item">
                                <label for="sshPort">SSH Port</label>
                                <input type="number" id="sshPort" value="2222" min="1024" max="65535" onchange="updateVMConfig()">
                            </div>
                            <div class="config-item">
                                <label for="vncPort">VNC Port</label>
                                <input type="number" id="vncPort" value="5900" min="1024" max="65535" onchange="updateVMConfig()">
                            </div>
                        </div>
                    </div>

                    <div class="config-section">
                        <h3>Advanced Options</h3>
                        <div class="config-options">
                            <label class="checkbox-label">
                                <input type="checkbox" id="enableAppleFeatures" onchange="updateVMConfig()">
                                <span class="checkmark"></span>
                                Enable Apple-specific features
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="enableEncryption" checked onchange="updateVMConfig()">
                                <span class="checkmark"></span>
                                Enable WebSocket encryption
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="autoReconnect" checked onchange="updateVMConfig()">
                                <span class="checkmark"></span>
                                Auto-reconnect on failure
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Monitoring Tab -->
                <div class="tab-content" id="monitoringTab">
                    <div class="monitoring-grid">
                        <div class="monitor-card">
                            <h3>Performance Metrics</h3>
                            <div class="metrics-list">
                                <div class="metric-row">
                                    <span>CPU Usage</span>
                                    <div class="metric-bar">
                                        <div class="metric-fill" id="cpuUsageBar" style="width: 0%"></div>
                                    </div>
                                    <span id="cpuUsageText">0%</span>
                                </div>
                                <div class="metric-row">
                                    <span>Memory Usage</span>
                                    <div class="metric-bar">
                                        <div class="metric-fill" id="memoryUsageBar" style="width: 0%"></div>
                                    </div>
                                    <span id="memoryUsageText">0%</span>
                                </div>
                                <div class="metric-row">
                                    <span>Network Latency</span>
                                    <div class="metric-value" id="networkLatencyText">0ms</div>
                                </div>
                            </div>
                        </div>

                        <div class="monitor-card">
                            <h3>Health Status</h3>
                            <div class="health-indicators">
                                <div class="health-item">
                                    <span class="health-label">Overall Health</span>
                                    <span class="health-status" id="overallHealth">Unknown</span>
                                </div>
                                <div class="health-item">
                                    <span class="health-label">Error Count</span>
                                    <span class="health-value" id="errorCount">0</span>
                                </div>
                                <div class="health-item">
                                    <span class="health-label">Warning Count</span>
                                    <span class="health-value" id="warningCount">0</span>
                                </div>
                                <div class="health-item">
                                    <span class="health-label">Last Check</span>
                                    <span class="health-value" id="lastHealthCheck">Never</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="monitoring-actions">
                        <button class="action-btn secondary" onclick="refreshMetrics()">Refresh Metrics</button>
                        <button class="action-btn secondary" onclick="resetCounters()">Reset Counters</button>
                        <button class="action-btn secondary" onclick="exportHealthReport()">Export Report</button>
                    </div>
                </div>

                <!-- Logs Tab -->
                <div class="tab-content" id="logsTab">
                    <div class="logs-header">
                        <h3>System Logs</h3>
                        <div class="log-controls">
                            <select id="logLevel">
                                <option value="all">All Levels</option>
                                <option value="error">Errors Only</option>
                                <option value="warning">Warnings</option>
                                <option value="info">Info</option>
                            </select>
                            <button class="action-btn secondary" onclick="clearLogs()">Clear</button>
                            <button class="action-btn secondary" onclick="exportLogs()">Export</button>
                        </div>
                    </div>
                    <div class="logs-container" id="logsContainer">
                        <div class="log-entry info">
                            <span class="log-time">2024-01-01 12:00:00</span>
                            <span class="log-level">INFO</span>
                            <span class="log-message">VM Bridge system initialized</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="scripts/vm-bridge.js"></script>
</body>
</html>
