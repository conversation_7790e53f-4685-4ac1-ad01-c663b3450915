<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Contacts - iPhone Companion Pro</title>
    <link rel="stylesheet" href="../styles/professional-polish.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #000;
            color: #fff;
            margin: 0;
            padding: 0;
            user-select: none;
        }
        
        .titlebar {
            height: 32px;
            background: #141414;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 12px;
            -webkit-app-region: drag;
            border-bottom: 1px solid #2a2a2a;
        }
        
        .titlebar-title {
            font-size: 13px;
            font-weight: 500;
        }
        
        .titlebar-controls {
            display: flex;
            -webkit-app-region: no-drag;
        }
        
        .titlebar-button {
            width: 46px;
            height: 32px;
            border: none;
            background: transparent;
            color: #999;
            cursor: pointer;
        }
        
        .titlebar-button:hover {
            background: #2a2a2a;
            color: #fff;
        }
        
        .contacts-container {
            display: flex;
            height: calc(100vh - 32px);
        }
        
        .sidebar {
            width: 300px;
            background: #141414;
            border-right: 1px solid #2a2a2a;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #2a2a2a;
        }
        
        .search-box {
            width: 100%;
            padding: 12px;
            background: #1a1a1a;
            border: 1px solid #2a2a2a;
            border-radius: 8px;
            color: #fff;
            font-size: 14px;
            outline: none;
        }
        
        .search-box:focus {
            border-color: #007AFF;
        }
        
        .contacts-list {
            flex: 1;
            overflow-y: auto;
        }
        
        .contact-item {
            padding: 16px 20px;
            border-bottom: 1px solid #2a2a2a;
            cursor: pointer;
            transition: background 0.2s;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .contact-item:hover {
            background: #1a1a1a;
        }
        
        .contact-item.active {
            background: #007AFF;
        }
        
        .contact-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
        }
        
        .contact-info {
            flex: 1;
        }
        
        .contact-name {
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .contact-phone {
            font-size: 12px;
            color: #999;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .contact-details {
            padding: 40px;
            text-align: center;
        }
        
        .contact-details.empty {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
        }
        
        .large-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            font-weight: bold;
            margin: 0 auto 20px;
        }
        
        .contact-name-large {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .contact-details-list {
            background: #1a1a1a;
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
            text-align: left;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 15px 0;
            border-bottom: 1px solid #2a2a2a;
        }
        
        .detail-item:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            color: #999;
            font-weight: 500;
        }
        
        .detail-value {
            color: #fff;
        }
        
        .contact-actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            justify-content: center;
        }
        
        .action-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .action-btn.call {
            background: #4CAF50;
            color: white;
        }
        
        .action-btn.call:hover {
            background: #45a049;
        }
        
        .action-btn.message {
            background: #007AFF;
            color: white;
        }
        
        .action-btn.message:hover {
            background: #0051D5;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #666;
        }
        
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #333;
            border-top: 2px solid #007AFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Title Bar -->
    <div class="titlebar">
        <div class="titlebar-title">Contacts - iPhone Companion Pro</div>
        <div class="titlebar-controls">
            <button class="titlebar-button" onclick="require('electron').ipcRenderer.send('minimize-window')">−</button>
            <button class="titlebar-button" onclick="require('electron').ipcRenderer.send('maximize-window')">□</button>
            <button class="titlebar-button" onclick="require('electron').ipcRenderer.send('close-window')">×</button>
        </div>
    </div>

    <!-- Main Layout -->
    <div class="contacts-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <input type="text" class="search-box" placeholder="Search contacts..." id="search-input">
            </div>
            
            <div class="contacts-list" id="contacts-list">
                <div class="loading">
                    <div class="spinner"></div>
                    Loading contacts...
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <div class="contact-details empty" id="contact-details">
                <div style="font-size: 48px; margin-bottom: 20px;">👥</div>
                <h2>Select a Contact</h2>
                <p>Choose a contact from the list to view details</p>
            </div>
        </div>
    </div>

    <script>
    const { ipcRenderer } = require('electron');

    let contacts = [];
    let currentContact = null;

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
        loadContacts();
        
        // Search functionality
        document.getElementById('search-input').addEventListener('input', (e) => {
            filterContacts(e.target.value);
        });
    });

    async function loadContacts() {
        try {
            console.log('👥 Loading contacts from iPhone...');
            
            const result = await ipcRenderer.invoke('get-contacts');
            if (result.success && result.data && result.data.length > 0) {
                console.log(`👥 Loaded ${result.data.length} contacts`);
                contacts = result.data;
                renderContacts();
            } else {
                showLoadingState();
            }
        } catch (error) {
            console.error('❌ Failed to load contacts:', error);
            showLoadingState();
        }
    }

    function renderContacts(filteredContacts = null) {
        const container = document.getElementById('contacts-list');
        const contactsToRender = filteredContacts || contacts;
        
        if (contactsToRender.length === 0) {
            container.innerHTML = `
                <div class="loading">
                    No contacts found
                </div>
            `;
            return;
        }
        
        container.innerHTML = contactsToRender.map(contact => `
            <div class="contact-item" onclick="selectContact('${contact.id}')" data-id="${contact.id}">
                <div class="contact-avatar">${getInitials(contact.displayName || contact.name)}</div>
                <div class="contact-info">
                    <div class="contact-name">${contact.displayName || contact.name}</div>
                    <div class="contact-phone">${contact.phoneNumber}</div>
                </div>
            </div>
        `).join('');
    }

    function selectContact(contactId) {
        currentContact = contacts.find(c => c.id === contactId);
        
        if (!currentContact) return;
        
        // Update sidebar selection
        document.querySelectorAll('.contact-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-id="${contactId}"]`).classList.add('active');
        
        // Show contact details
        showContactDetails(currentContact);
    }

    function showContactDetails(contact) {
        const container = document.getElementById('contact-details');
        container.className = 'contact-details';
        
        container.innerHTML = `
            <div class="large-avatar">${getInitials(contact.displayName || contact.name)}</div>
            <div class="contact-name-large">${contact.displayName || contact.name}</div>
            
            <div class="contact-details-list">
                <div class="detail-item">
                    <span class="detail-label">Phone</span>
                    <span class="detail-value">${contact.phoneNumber}</span>
                </div>
                ${contact.email ? `
                <div class="detail-item">
                    <span class="detail-label">Email</span>
                    <span class="detail-value">${contact.email}</span>
                </div>
                ` : ''}
                ${contact.company ? `
                <div class="detail-item">
                    <span class="detail-label">Company</span>
                    <span class="detail-value">${contact.company}</span>
                </div>
                ` : ''}
                <div class="detail-item">
                    <span class="detail-label">Last Contact</span>
                    <span class="detail-value">${contact.lastContacted ? new Date(contact.lastContacted).toLocaleDateString() : 'Never'}</span>
                </div>
            </div>
            
            <div class="contact-actions">
                <button class="action-btn call" onclick="callContact('${contact.phoneNumber}')">
                    📞 Call
                </button>
                <button class="action-btn message" onclick="messageContact('${contact.phoneNumber}')">
                    💬 Message
                </button>
            </div>
        `;
    }

    function filterContacts(query) {
        if (!query.trim()) {
            renderContacts();
            return;
        }
        
        const filtered = contacts.filter(contact => {
            const name = (contact.displayName || contact.name || '').toLowerCase();
            const phone = (contact.phoneNumber || '').toLowerCase();
            const email = (contact.email || '').toLowerCase();
            const searchQuery = query.toLowerCase();
            
            return name.includes(searchQuery) || 
                   phone.includes(searchQuery) || 
                   email.includes(searchQuery);
        });
        
        renderContacts(filtered);
    }

    async function callContact(phoneNumber) {
        try {
            const result = await ipcRenderer.invoke('make-call', { phoneNumber });
            if (result.success) {
                console.log('📞 Call initiated to:', phoneNumber);
            } else {
                alert('Failed to make call: ' + result.error);
            }
        } catch (error) {
            console.error('❌ Error making call:', error);
            alert('Error making call');
        }
    }

    async function messageContact(phoneNumber) {
        try {
            // Open messages window and start new conversation
            await ipcRenderer.invoke('open-messages');
            // Could also pass the phone number to pre-fill
        } catch (error) {
            console.error('❌ Error opening messages:', error);
        }
    }

    function showLoadingState() {
        const container = document.getElementById('contacts-list');
        container.innerHTML = `
            <div class="loading">
                <div class="spinner"></div>
                <div>👥 Loading contacts...</div>
                <div style="font-size: 12px; margin-top: 5px;">Waiting for iPhone sync</div>
            </div>
        `;
    }

    function getInitials(name) {
        if (!name) return '?';
        return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }

    // Listen for contacts data updates
    ipcRenderer.on('contacts-data', (event, contactsData) => {
        console.log('👥 Received contacts data:', contactsData);
        contacts = contactsData;
        renderContacts();
    });
    </script>
</body>
</html>