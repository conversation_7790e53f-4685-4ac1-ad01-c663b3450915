<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Messages - iPhone Companion Pro</title>
    <link rel="stylesheet" href="styles/messages.css">
</head>
<body>
    <!-- Title Bar -->
    <div class="titlebar">
        <div class="titlebar-title">Messages - iPhone Companion Pro</div>
        <div class="titlebar-controls">
            <button class="titlebar-button" onclick="require('electron').ipcRenderer.send('minimize-window')">−</button>
            <button class="titlebar-button" onclick="require('electron').ipcRenderer.send('maximize-window')">□</button>
            <button class="titlebar-button" onclick="require('electron').ipcRenderer.send('close-window')">×</button>
        </div>
    </div>

    <!-- Messages Layout -->
    <div class="messages-container">
        <!-- Conversations List -->
        <div class="conversations-panel">
            <div class="conversations-header">
                <h2>Messages</h2>
                <button class="new-message-btn" onclick="newMessage()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M12 5v14m-7-7h14"/>
                    </svg>
                </button>
            </div>
            
            <div class="search-box">
                <input type="text" placeholder="Search messages..." id="search-input" oninput="searchMessages()">
            </div>
            
            <div class="conversations-list" id="conversations-list">
                <!-- NO MOCK DATA - REAL IPHONE DATA ONLY -->
                <div class="loading-state">
                    <div class="spinner"></div>
                    <div class="loading-text">
                        <h3>📱 Connecting to iPhone...</h3>
                        <p>Waiting for real iPhone conversations</p>
                        <p>No mock data - only authentic iPhone connections</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Message Thread -->
        <div class="message-thread-panel">
            <div id="no-conversation" class="no-conversation" style="display: none;">
                <div class="empty-state">
                    <div class="empty-icon">💬</div>
                    <h3>Select a conversation</h3>
                    <p>Choose a conversation from the list to view messages</p>
                </div>
            </div>
            
            <div id="conversation-view" style="display: flex; flex-direction: column; height: 100%;">
                <div class="conversation-header">
                    <div class="contact-info">
                        <div class="contact-avatar">JD</div>
                        <div class="contact-details">
                            <h3 id="contact-name">Connect iPhone</h3>
                            <span id="contact-phone">No iPhone connected</span>
                            <div class="contact-status">Waiting for connection...</div>
                        </div>
                    </div>
                    <div class="conversation-actions">
                        <button class="action-btn" title="Call">📞</button>
                        <button class="action-btn" title="Info">ℹ️</button>
                    </div>
                </div>
                
                <div class="messages-list" id="messages-list">
                    <!-- NO MOCK DATA - REAL IPHONE DATA ONLY -->
                    <div class="loading-state">
                        <div class="spinner"></div>
                        <div class="loading-text">
                            <h3>📱 Ready for real iPhone messages</h3>
                            <p>Connect your iPhone to see messages</p>
                            <p>No sample data - only real conversations</p>
                        </div>
                    </div>
                
                <div class="message-input-container">
                    <input 
                        type="text" 
                        id="message-input" 
                        placeholder="Type a message..." 
                        onkeypress="handleKeyPress(event)"
                    >
                    <button class="send-btn" onclick="sendMessage()">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="scripts/messages.js"></script>

<script>
const { ipcRenderer } = require('electron');

// Listen for messages data
ipcRenderer.on('messages-data', (event, messages) => {
  console.log('Received messages:', messages);
  
  const container = document.querySelector('.messages-container') || document.body;
  
  if (messages && messages.length > 0) {
    container.innerHTML = '<h2>Messages</h2>' + 
      messages.map(msg => `
        <div class="message-item">
          <strong>${msg.contact}</strong>
          <p>${msg.text}</p>
          <small>${new Date(msg.timestamp).toLocaleString()}</small>
        </div>
      `).join('');
  } else {
    container.innerHTML = '<h2>Messages</h2><p>Phone Link messages are stored in the cloud. Open Windows Phone Link to see your messages.</p>';
  }
});

// Handle key press for message input
function handleKeyPress(event) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
}

// Send message function
function sendMessage() {
  const messageInput = document.getElementById('message-input');
  const messageText = messageInput.value.trim();
  
  if (!messageText) {
    console.log('No message text to send');
    return;
  }
  
  console.log('📤 Sending message:', messageText);
  
  // Clear the input immediately for better UX
  messageInput.value = '';
  
  // Send message via IPC
  ipcRenderer.invoke('send-message', {
    text: messageText,
    timestamp: Date.now()
  }).then(result => {
    if (result.success) {
      console.log('✅ Message sent successfully');
    } else {
      console.error('❌ Failed to send message:', result.error);
      // Restore message text if send failed
      messageInput.value = messageText;
    }
  }).catch(error => {
    console.error('❌ Error sending message:', error);
    // Restore message text if send failed
    messageInput.value = messageText;
  });
}

// Enable message input on page load
document.addEventListener('DOMContentLoaded', () => {
  const messageInput = document.getElementById('message-input');
  const sendButton = document.querySelector('.send-btn');
  
  if (messageInput) {
    // Ensure input is enabled
    messageInput.removeAttribute('disabled');
    messageInput.removeAttribute('readonly');
    
    // Add real-time typing feedback
    messageInput.addEventListener('input', (e) => {
      const hasContent = e.target.value.trim().length > 0;
      
      if (sendButton) {
        sendButton.disabled = !hasContent;
        sendButton.style.opacity = hasContent ? '1' : '0.5';
      }
    });
    
    console.log('✅ Message input enabled and ready');
  }
  
  if (sendButton) {
    sendButton.disabled = true;
    sendButton.style.opacity = '0.5';
  }
});
</script>

</body>
</html>