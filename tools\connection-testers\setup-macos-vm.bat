<<<<<<<
<<<<<<<



<<<<<<<















@echo off































































title macOS VM Setup for iPhone Companion Pro































































color 0B































































































































echo.































































echo ========================================































































echo  macOS VM Setup - iPhone Companion Pro































































echo ========================================































































echo.































































































































echo 🍎 Setting up macOS Virtual Machine for iPhone integration...































































echo.































































































































REM Check if virtualization is enabled































































echo [1/8] Checking virtualization support...































































systeminfo | findstr /C:"Hyper-V Requirements" >nul 2>&1































































if %errorlevel% equ 0 (































































    echo ✅ Hyper-V supported































































) else (































































    echo ⚠️ Checking BIOS virtualization...































































    wmic cpu get VirtualizationFirmwareEnabled | findstr True >nul 2>&1































































    if %errorlevel% equ 0 (































































        echo ✅ Virtualization enabled in BIOS































































    ) else (































































        echo ❌ Enable virtualization in BIOS first































































        pause































































        exit /b 1































































    )































































)































































































































REM Check available disk space































































echo.































































echo [2/8] Checking disk space...































































for /f "tokens=3" %%a in ('dir C:\ ^| findstr "bytes free"') do set freespace=%%a































































echo Available space: %freespace% bytes































































echo ⚠️ macOS VM needs at least 100GB free space































































































































REM Download VMware Workstation Pro (trial)































































echo.































































echo [3/8] Setting up VMware Workstation...































































if not exist "VMware-workstation.exe" (































































    echo Downloading VMware Workstation Pro trial...































































    echo ⚠️ Manual step required:































































    echo 1. Go to: https://www.vmware.com/products/workstation-pro/workstation-pro-evaluation.html































































    echo 2. Download VMware Workstation Pro































































    echo 3. Save as VMware-workstation.exe in this folder































































    echo.































































    pause































































)































































































































if exist "VMware-workstation.exe" (































































    echo Installing VMware Workstation...































































    VMware-workstation.exe /S /v/qn































































    echo ✅ VMware Workstation installed































































) else (































































    echo ❌ VMware installer not found































































)































































































































REM Create VM directory































































echo.































































echo [4/8] Creating VM directory...































































set VM_DIR=C:\macOS-VM































































if not exist "%VM_DIR%" (































































    mkdir "%VM_DIR%"































































    echo ✅ Created %VM_DIR%































































)































































































































REM Download macOS installer































































echo.































































echo [5/8] macOS Installer Setup...































































echo ⚠️ Manual steps required:































































echo.































































echo Option A - Download macOS Monterey:































































echo 1. Go to: https://apps.apple.com/us/app/macos-monterey/id1576738294































































echo 2. Download from Mac App Store (if you have a Mac)































































echo 3. Create installer USB or ISO































































echo.































































echo Option B - Use pre-made VM:































































echo 1. Search for "macOS Monterey VMware image"































































echo 2. Download from trusted source































































echo 3. Extract to %VM_DIR%































































echo.































































pause































































































































REM Create VMware configuration































































echo.































































echo [6/8] Creating VMware configuration...































































































































cat > "%VM_DIR%\macOS.vmx" << 'EOF'































































.encoding = "UTF-8"































































config.version = "8"































































virtualHW.version = "19"































































vmci0.present = "TRUE"































































hpet0.present = "TRUE"































































nvram = "macOS.nvram"































































virtualHW.productCompatibility = "hosted"































































powerType.powerOff = "soft"































































powerType.powerOn = "soft"































































powerType.suspend = "soft"































































powerType.reset = "soft"































































displayName = "macOS Monterey"































































guestOS = "darwin21-64"































































numvcpus = "4"































































cpuid.coresPerSocket = "2"































































memsize = "8192"































































sata0.present = "TRUE"































































sata0:0.present = "TRUE"































































sata0:0.fileName = "macOS.vmdk"































































sata0:0.deviceType = "scsi-hardDisk"































































ethernet0.present = "TRUE"































































ethernet0.connectionType = "nat"































































ethernet0.virtualDev = "e1000e"































































ethernet0.wakeOnPcktRcv = "FALSE"































































ethernet0.addressType = "generated"































































usb.present = "TRUE"































































ehci.present = "TRUE"































































usb_xhci.present = "TRUE"































































sound.present = "TRUE"































































sound.virtualDev = "hdaudio"































































sound.fileName = "-1"































































sound.autodetect = "TRUE"































































serial0.present = "TRUE"































































serial0.fileType = "thinprint"































































pciBridge0.present = "TRUE"































































pciBridge4.present = "TRUE"































































pciBridge4.virtualDev = "pcieRootPort"































































pciBridge4.functions = "8"































































pciBridge5.present = "TRUE"































































pciBridge5.virtualDev = "pcieRootPort"































































pciBridge5.functions = "8"































































pciBridge6.present = "TRUE"































































pciBridge6.virtualDev = "pcieRootPort"































































pciBridge6.functions = "8"































































pciBridge7.present = "TRUE"































































pciBridge7.virtualDev = "pcieRootPort"































































pciBridge7.functions = "8"































































vmci0.id = "1861462627"































































monitor.phys_bits_used = "45"































































cleanShutdown = "TRUE"































































softPowerOff = "TRUE"































































unity.wasCapable = "FALSE"































































EOF































































































































echo ✅ VMware configuration created































































































































REM Create network configuration for iPhone bridge































































echo.































































echo [7/8] Configuring network for iPhone bridge...































































































































REM Add port forwarding for bridge































































netsh interface portproxy add v4tov4 listenport=8888 listenaddress=0.0.0.0 connectport=8888 connectaddress=*************































































echo ✅ Port forwarding configured (8888)































































































































REM Create firewall rules































































netsh advfirewall firewall add rule name="macOS VM Bridge" dir=in action=allow protocol=TCP localport=8888































































netsh advfirewall firewall add rule name="macOS VM SSH" dir=in action=allow protocol=TCP localport=22































































echo ✅ Firewall rules added































































































































REM Create startup script for VM































































echo.































































echo [8/8] Creating VM startup script...































































































































cat > "%VM_DIR%\start-macos-vm.bat" << 'EOF'































































@echo off































































echo Starting macOS VM for iPhone Companion Pro...































































































































REM Start VMware if not running































































tasklist | findstr vmware.exe >nul 2>&1































































if %errorlevel% neq 0 (































































    echo Starting VMware Workstation...































































    start "" "C:\Program Files (x86)\VMware\VMware Workstation\vmware.exe"































































    timeout /t 5































































)































































































































REM Start the VM































































echo Starting macOS VM...































































"C:\Program Files (x86)\VMware\VMware Workstation\vmrun.exe" start "C:\macOS-VM\macOS.vmx" nogui































































































































REM Wait for VM to boot































































echo Waiting for VM to boot (60 seconds)...































































timeout /t 60































































































































REM Check if VM is accessible































































echo Testing VM connection...































































ping -n 1 ************* >nul 2>&1































































if %errorlevel% equ 0 (































































    echo ✅ VM is accessible at *************































































) else (































































    echo ⚠️ VM might still be booting, check VMware console































































)































































































































echo.































































echo VM startup complete!































































echo Next: Install bridge in macOS VM































































pause































































EOF































































































































echo ✅ VM startup script created































































































































echo.































































echo ========================================































































echo  macOS VM Setup Complete!































































echo ========================================































































echo.































































echo Next steps:































































echo 1. Install macOS in VMware (use macOS installer)































































echo 2. Configure macOS with Apple ID































































echo 3. Run: %VM_DIR%\start-macos-vm.bat































































echo 4. Copy macos-vm-bridge.sh to VM































































echo 5. Run bridge setup in macOS































































echo.































































echo VM Location: %VM_DIR%































































echo VM IP will be: *************































































echo Bridge Port: 8888































































echo.































































































































pause































































=======















@echo off































title macOS VM Setup for iPhone Companion Pro































color 0B































































echo.































echo ========================================































echo  macOS VM Setup - iPhone Companion Pro































echo ========================================































echo.































































echo 🍎 Setting up macOS Virtual Machine for iPhone integration...































echo.































































REM Check if virtualization is enabled































echo [1/8] Checking virtualization support...































systeminfo | findstr /C:"Hyper-V Requirements" >nul 2>&1































if %errorlevel% equ 0 (































    echo ✅ Hyper-V supported































) else (































    echo ⚠️ Checking BIOS virtualization...































    wmic cpu get VirtualizationFirmwareEnabled | findstr True >nul 2>&1































    if %errorlevel% equ 0 (































        echo ✅ Virtualization enabled in BIOS































    ) else (































        echo ❌ Enable virtualization in BIOS first































        pause































        exit /b 1































    )































)































































REM Check available disk space































echo.































echo [2/8] Checking disk space...































for /f "tokens=3" %%a in ('dir C:\ ^| findstr "bytes free"') do set freespace=%%a































echo Available space: %freespace% bytes































echo ⚠️ macOS VM needs at least 100GB free space































































REM Download VMware Workstation Pro (trial)































echo.































echo [3/8] Setting up VMware Workstation...































if not exist "VMware-workstation.exe" (































    echo Downloading VMware Workstation Pro trial...































    echo ⚠️ Manual step required:































    echo 1. Go to: https://www.vmware.com/products/workstation-pro/workstation-pro-evaluation.html































    echo 2. Download VMware Workstation Pro































    echo 3. Save as VMware-workstation.exe in this folder































    echo.































    pause































)































































if exist "VMware-workstation.exe" (































    echo Installing VMware Workstation...































    VMware-workstation.exe /S /v/qn































    echo ✅ VMware Workstation installed































) else (































    echo ❌ VMware installer not found































)































































REM Create VM directory































echo.































echo [4/8] Creating VM directory...































set VM_DIR=C:\macOS-VM































if not exist "%VM_DIR%" (































    mkdir "%VM_DIR%"































    echo ✅ Created %VM_DIR%































)































































REM Download macOS installer































echo.































echo [5/8] macOS Installer Setup...































echo ⚠️ Manual steps required:































echo.































echo Option A - Download macOS Monterey:































echo 1. Go to: https://apps.apple.com/us/app/macos-monterey/id1576738294































echo 2. Download from Mac App Store (if you have a Mac)































echo 3. Create installer USB or ISO































echo.































echo Option B - Use pre-made VM:































echo 1. Search for "macOS Monterey VMware image"































echo 2. Download from trusted source































echo 3. Extract to %VM_DIR%































echo.































pause































































REM Create VMware configuration































echo.































echo [6/8] Creating VMware configuration...































































cat > "%VM_DIR%\macOS.vmx" << 'EOF'































.encoding = "UTF-8"































config.version = "8"































virtualHW.version = "19"































vmci0.present = "TRUE"































hpet0.present = "TRUE"































nvram = "macOS.nvram"































virtualHW.productCompatibility = "hosted"































powerType.powerOff = "soft"































powerType.powerOn = "soft"































powerType.suspend = "soft"































powerType.reset = "soft"































displayName = "macOS Monterey"































guestOS = "darwin21-64"































numvcpus = "4"































cpuid.coresPerSocket = "2"































memsize = "8192"































sata0.present = "TRUE"































sata0:0.present = "TRUE"































sata0:0.fileName = "macOS.vmdk"































sata0:0.deviceType = "scsi-hardDisk"































ethernet0.present = "TRUE"































ethernet0.connectionType = "nat"































ethernet0.virtualDev = "e1000e"































ethernet0.wakeOnPcktRcv = "FALSE"































ethernet0.addressType = "generated"































usb.present = "TRUE"































ehci.present = "TRUE"































usb_xhci.present = "TRUE"































sound.present = "TRUE"































sound.virtualDev = "hdaudio"































sound.fileName = "-1"































sound.autodetect = "TRUE"































serial0.present = "TRUE"































serial0.fileType = "thinprint"































pciBridge0.present = "TRUE"































pciBridge4.present = "TRUE"































pciBridge4.virtualDev = "pcieRootPort"































pciBridge4.functions = "8"































pciBridge5.present = "TRUE"































pciBridge5.virtualDev = "pcieRootPort"































pciBridge5.functions = "8"































pciBridge6.present = "TRUE"































pciBridge6.virtualDev = "pcieRootPort"































pciBridge6.functions = "8"































pciBridge7.present = "TRUE"































pciBridge7.virtualDev = "pcieRootPort"































pciBridge7.functions = "8"































vmci0.id = "1861462627"































monitor.phys_bits_used = "45"































cleanShutdown = "TRUE"































softPowerOff = "TRUE"































unity.wasCapable = "FALSE"































EOF































































echo ✅ VMware configuration created































































REM Create network configuration for iPhone bridge































echo.































echo [7/8] Configuring network for iPhone bridge...































































REM Add port forwarding for bridge































netsh interface portproxy add v4tov4 listenport=8888 listenaddress=0.0.0.0 connectport=8888 connectaddress=*************































echo ✅ Port forwarding configured (8888)































































REM Create firewall rules































netsh advfirewall firewall add rule name="macOS VM Bridge" dir=in action=allow protocol=TCP localport=8888































netsh advfirewall firewall add rule name="macOS VM SSH" dir=in action=allow protocol=TCP localport=22































echo ✅ Firewall rules added































































REM Create startup script for VM































echo.































echo [8/8] Creating VM startup script...































































cat > "%VM_DIR%\start-macos-vm.bat" << 'EOF'































@echo off































echo Starting macOS VM for iPhone Companion Pro...































































REM Start VMware if not running































tasklist | findstr vmware.exe >nul 2>&1































if %errorlevel% neq 0 (































    echo Starting VMware Workstation...































    start "" "C:\Program Files (x86)\VMware\VMware Workstation\vmware.exe"































    timeout /t 5































)































































REM Start the VM































echo Starting macOS VM...































"C:\Program Files (x86)\VMware\VMware Workstation\vmrun.exe" start "C:\macOS-VM\macOS.vmx" nogui































































REM Wait for VM to boot































echo Waiting for VM to boot (60 seconds)...































timeout /t 60































































REM Check if VM is accessible































echo Testing VM connection...































ping -n 1 ************* >nul 2>&1































if %errorlevel% equ 0 (































    echo ✅ VM is accessible at *************































) else (































    echo ⚠️ VM might still be booting, check VMware console































)































































echo.































echo VM startup complete!































echo Next: Install bridge in macOS VM































pause































EOF































































echo ✅ VM startup script created































































echo.































echo ========================================































echo  macOS VM Setup Complete!































echo ========================================































echo.































echo Next steps:































echo 1. Install macOS in VMware (use macOS installer)































echo 2. Configure macOS with Apple ID































echo 3. Run: %VM_DIR%\start-macos-vm.bat































echo 4. Copy macos-vm-bridge.sh to VM































echo 5. Run bridge setup in macOS































echo.































echo VM Location: %VM_DIR%































echo VM IP will be: *************































echo Bridge Port: 8888































echo.































































pause































>>>>>>>















=======



@echo off







title macOS VM Setup for iPhone Companion Pro







color 0B















echo.







echo ========================================







echo  macOS VM Setup - iPhone Companion Pro







echo ========================================







echo.















echo 🍎 Setting up macOS Virtual Machine for iPhone integration...







echo.















REM Check if virtualization is enabled







echo [1/8] Checking virtualization support...







systeminfo | findstr /C:"Hyper-V Requirements" >nul 2>&1







if %errorlevel% equ 0 (







    echo ✅ Hyper-V supported







) else (







    echo ⚠️ Checking BIOS virtualization...







    wmic cpu get VirtualizationFirmwareEnabled | findstr True >nul 2>&1







    if %errorlevel% equ 0 (







        echo ✅ Virtualization enabled in BIOS







    ) else (







        echo ❌ Enable virtualization in BIOS first







        pause







        exit /b 1







    )







)















REM Check available disk space







echo.







echo [2/8] Checking disk space...







for /f "tokens=3" %%a in ('dir C:\ ^| findstr "bytes free"') do set freespace=%%a







echo Available space: %freespace% bytes







echo ⚠️ macOS VM needs at least 100GB free space















REM Download VMware Workstation Pro (trial)







echo.







echo [3/8] Setting up VMware Workstation...







if not exist "VMware-workstation.exe" (







    echo Downloading VMware Workstation Pro trial...







    echo ⚠️ Manual step required:







    echo 1. Go to: https://www.vmware.com/products/workstation-pro/workstation-pro-evaluation.html







    echo 2. Download VMware Workstation Pro







    echo 3. Save as VMware-workstation.exe in this folder







    echo.







    pause







)















if exist "VMware-workstation.exe" (







    echo Installing VMware Workstation...







    VMware-workstation.exe /S /v/qn







    echo ✅ VMware Workstation installed







) else (







    echo ❌ VMware installer not found







)















REM Create VM directory







echo.







echo [4/8] Creating VM directory...







set VM_DIR=C:\macOS-VM







if not exist "%VM_DIR%" (







    mkdir "%VM_DIR%"







    echo ✅ Created %VM_DIR%







)















REM Download macOS installer







echo.







echo [5/8] macOS Installer Setup...







echo ⚠️ Manual steps required:







echo.







echo Option A - Download macOS Monterey:







echo 1. Go to: https://apps.apple.com/us/app/macos-monterey/id1576738294







echo 2. Download from Mac App Store (if you have a Mac)







echo 3. Create installer USB or ISO







echo.







echo Option B - Use pre-made VM:







echo 1. Search for "macOS Monterey VMware image"







echo 2. Download from trusted source







echo 3. Extract to %VM_DIR%







echo.







pause















REM Create VMware configuration







echo.







echo [6/8] Creating VMware configuration...















cat > "%VM_DIR%\macOS.vmx" << 'EOF'







.encoding = "UTF-8"







config.version = "8"







virtualHW.version = "19"







vmci0.present = "TRUE"







hpet0.present = "TRUE"







nvram = "macOS.nvram"







virtualHW.productCompatibility = "hosted"







powerType.powerOff = "soft"







powerType.powerOn = "soft"







powerType.suspend = "soft"







powerType.reset = "soft"







displayName = "macOS Monterey"







guestOS = "darwin21-64"







numvcpus = "4"







cpuid.coresPerSocket = "2"







memsize = "8192"







sata0.present = "TRUE"







sata0:0.present = "TRUE"







sata0:0.fileName = "macOS.vmdk"







sata0:0.deviceType = "scsi-hardDisk"







ethernet0.present = "TRUE"







ethernet0.connectionType = "nat"







ethernet0.virtualDev = "e1000e"







ethernet0.wakeOnPcktRcv = "FALSE"







ethernet0.addressType = "generated"







usb.present = "TRUE"







ehci.present = "TRUE"







usb_xhci.present = "TRUE"







sound.present = "TRUE"







sound.virtualDev = "hdaudio"







sound.fileName = "-1"







sound.autodetect = "TRUE"







serial0.present = "TRUE"







serial0.fileType = "thinprint"







pciBridge0.present = "TRUE"







pciBridge4.present = "TRUE"







pciBridge4.virtualDev = "pcieRootPort"







pciBridge4.functions = "8"







pciBridge5.present = "TRUE"







pciBridge5.virtualDev = "pcieRootPort"







pciBridge5.functions = "8"







pciBridge6.present = "TRUE"







pciBridge6.virtualDev = "pcieRootPort"







pciBridge6.functions = "8"







pciBridge7.present = "TRUE"







pciBridge7.virtualDev = "pcieRootPort"







pciBridge7.functions = "8"







vmci0.id = "1861462627"







monitor.phys_bits_used = "45"







cleanShutdown = "TRUE"







softPowerOff = "TRUE"







unity.wasCapable = "FALSE"







EOF















echo ✅ VMware configuration created















REM Create network configuration for iPhone bridge







echo.







echo [7/8] Configuring network for iPhone bridge...















REM Add port forwarding for bridge







netsh interface portproxy add v4tov4 listenport=8888 listenaddress=0.0.0.0 connectport=8888 connectaddress=*************







echo ✅ Port forwarding configured (8888)















REM Create firewall rules







netsh advfirewall firewall add rule name="macOS VM Bridge" dir=in action=allow protocol=TCP localport=8888







netsh advfirewall firewall add rule name="macOS VM SSH" dir=in action=allow protocol=TCP localport=22







echo ✅ Firewall rules added















REM Create startup script for VM







echo.







echo [8/8] Creating VM startup script...















cat > "%VM_DIR%\start-macos-vm.bat" << 'EOF'







@echo off







echo Starting macOS VM for iPhone Companion Pro...















REM Start VMware if not running







tasklist | findstr vmware.exe >nul 2>&1







if %errorlevel% neq 0 (







    echo Starting VMware Workstation...







    start "" "C:\Program Files (x86)\VMware\VMware Workstation\vmware.exe"







    timeout /t 5







)















REM Start the VM







echo Starting macOS VM...







"C:\Program Files (x86)\VMware\VMware Workstation\vmrun.exe" start "C:\macOS-VM\macOS.vmx" nogui















REM Wait for VM to boot







echo Waiting for VM to boot (60 seconds)...







timeout /t 60















REM Check if VM is accessible







echo Testing VM connection...







ping -n 1 ************* >nul 2>&1







if %errorlevel% equ 0 (







    echo ✅ VM is accessible at *************







) else (







    echo ⚠️ VM might still be booting, check VMware console







)















echo.







echo VM startup complete!







echo Next: Install bridge in macOS VM







pause







EOF















echo ✅ VM startup script created















echo.







echo ========================================







echo  macOS VM Setup Complete!







echo ========================================







echo.







echo Next steps:







echo 1. Install macOS in VMware (use macOS installer)







echo 2. Configure macOS with Apple ID







echo 3. Run: %VM_DIR%\start-macos-vm.bat







echo 4. Copy macos-vm-bridge.sh to VM







echo 5. Run bridge setup in macOS







echo.







echo VM Location: %VM_DIR%







echo VM IP will be: *************







echo Bridge Port: 8888







echo.















pause







>>>>>>>



=======
@echo off

title macOS VM Setup for iPhone Companion Pro

color 0B



echo.

echo ========================================

echo  macOS VM Setup - iPhone Companion Pro

echo ========================================

echo.



echo 🍎 Setting up macOS Virtual Machine for iPhone integration...

echo.



REM Check if virtualization is enabled

echo [1/8] Checking virtualization support...

systeminfo | findstr /C:"Hyper-V Requirements" >nul 2>&1

if %errorlevel% equ 0 (

    echo ✅ Hyper-V supported

) else (

    echo ⚠️ Checking BIOS virtualization...

    wmic cpu get VirtualizationFirmwareEnabled | findstr True >nul 2>&1

    if %errorlevel% equ 0 (

        echo ✅ Virtualization enabled in BIOS

    ) else (

        echo ❌ Enable virtualization in BIOS first

        pause

        exit /b 1

    )

)



REM Check available disk space

echo.

echo [2/8] Checking disk space...

for /f "tokens=3" %%a in ('dir C:\ ^| findstr "bytes free"') do set freespace=%%a

echo Available space: %freespace% bytes

echo ⚠️ macOS VM needs at least 100GB free space



REM Download VMware Workstation Pro (trial)

echo.

echo [3/8] Setting up VMware Workstation...

if not exist "VMware-workstation.exe" (

    echo Downloading VMware Workstation Pro trial...

    echo ⚠️ Manual step required:

    echo 1. Go to: https://www.vmware.com/products/workstation-pro/workstation-pro-evaluation.html

    echo 2. Download VMware Workstation Pro

    echo 3. Save as VMware-workstation.exe in this folder

    echo.

    pause

)



if exist "VMware-workstation.exe" (

    echo Installing VMware Workstation...

    VMware-workstation.exe /S /v/qn

    echo ✅ VMware Workstation installed

) else (

    echo ❌ VMware installer not found

)



REM Create VM directory

echo.

echo [4/8] Creating VM directory...

set VM_DIR=C:\macOS-VM

if not exist "%VM_DIR%" (

    mkdir "%VM_DIR%"

    echo ✅ Created %VM_DIR%

)



REM Download macOS installer

echo.

echo [5/8] macOS Installer Setup...

echo ⚠️ Manual steps required:

echo.

echo Option A - Download macOS Monterey:

echo 1. Go to: https://apps.apple.com/us/app/macos-monterey/id1576738294

echo 2. Download from Mac App Store (if you have a Mac)

echo 3. Create installer USB or ISO

echo.

echo Option B - Use pre-made VM:

echo 1. Search for "macOS Monterey VMware image"

echo 2. Download from trusted source

echo 3. Extract to %VM_DIR%

echo.

pause



REM Create VMware configuration

echo.

echo [6/8] Creating VMware configuration...



cat > "%VM_DIR%\macOS.vmx" << 'EOF'

.encoding = "UTF-8"

config.version = "8"

virtualHW.version = "19"

vmci0.present = "TRUE"

hpet0.present = "TRUE"

nvram = "macOS.nvram"

virtualHW.productCompatibility = "hosted"

powerType.powerOff = "soft"

powerType.powerOn = "soft"

powerType.suspend = "soft"

powerType.reset = "soft"

displayName = "macOS Monterey"

guestOS = "darwin21-64"

numvcpus = "4"

cpuid.coresPerSocket = "2"

memsize = "8192"

sata0.present = "TRUE"

sata0:0.present = "TRUE"

sata0:0.fileName = "macOS.vmdk"

sata0:0.deviceType = "scsi-hardDisk"

ethernet0.present = "TRUE"

ethernet0.connectionType = "nat"

ethernet0.virtualDev = "e1000e"

ethernet0.wakeOnPcktRcv = "FALSE"

ethernet0.addressType = "generated"

usb.present = "TRUE"

ehci.present = "TRUE"

usb_xhci.present = "TRUE"

sound.present = "TRUE"

sound.virtualDev = "hdaudio"

sound.fileName = "-1"

sound.autodetect = "TRUE"

serial0.present = "TRUE"

serial0.fileType = "thinprint"

pciBridge0.present = "TRUE"

pciBridge4.present = "TRUE"

pciBridge4.virtualDev = "pcieRootPort"

pciBridge4.functions = "8"

pciBridge5.present = "TRUE"

pciBridge5.virtualDev = "pcieRootPort"

pciBridge5.functions = "8"

pciBridge6.present = "TRUE"

pciBridge6.virtualDev = "pcieRootPort"

pciBridge6.functions = "8"

pciBridge7.present = "TRUE"

pciBridge7.virtualDev = "pcieRootPort"

pciBridge7.functions = "8"

vmci0.id = "1861462627"

monitor.phys_bits_used = "45"

cleanShutdown = "TRUE"

softPowerOff = "TRUE"

unity.wasCapable = "FALSE"

EOF



echo ✅ VMware configuration created



REM Create network configuration for iPhone bridge

echo.

echo [7/8] Configuring network for iPhone bridge...



REM Add port forwarding for bridge

netsh interface portproxy add v4tov4 listenport=8888 listenaddress=0.0.0.0 connectport=8888 connectaddress=*************

echo ✅ Port forwarding configured (8888)



REM Create firewall rules

netsh advfirewall firewall add rule name="macOS VM Bridge" dir=in action=allow protocol=TCP localport=8888

netsh advfirewall firewall add rule name="macOS VM SSH" dir=in action=allow protocol=TCP localport=22

echo ✅ Firewall rules added



REM Create startup script for VM

echo.

echo [8/8] Creating VM startup script...



cat > "%VM_DIR%\start-macos-vm.bat" << 'EOF'

@echo off

echo Starting macOS VM for iPhone Companion Pro...



REM Start VMware if not running

tasklist | findstr vmware.exe >nul 2>&1

if %errorlevel% neq 0 (

    echo Starting VMware Workstation...

    start "" "C:\Program Files (x86)\VMware\VMware Workstation\vmware.exe"

    timeout /t 5

)



REM Start the VM

echo Starting macOS VM...

"C:\Program Files (x86)\VMware\VMware Workstation\vmrun.exe" start "C:\macOS-VM\macOS.vmx" nogui



REM Wait for VM to boot

echo Waiting for VM to boot (60 seconds)...

timeout /t 60



REM Check if VM is accessible

echo Testing VM connection...

ping -n 1 ************* >nul 2>&1

if %errorlevel% equ 0 (

    echo ✅ VM is accessible at *************

) else (

    echo ⚠️ VM might still be booting, check VMware console

)



echo.

echo VM startup complete!

echo Next: Install bridge in macOS VM

pause

EOF



echo ✅ VM startup script created



echo.

echo ========================================

echo  macOS VM Setup Complete!

echo ========================================

echo.

echo Next steps:

echo 1. Install macOS in VMware (use macOS installer)

echo 2. Configure macOS with Apple ID

echo 3. Run: %VM_DIR%\start-macos-vm.bat

echo 4. Copy macos-vm-bridge.sh to VM

echo 5. Run bridge setup in macOS

echo.

echo VM Location: %VM_DIR%

echo VM IP will be: *************

echo Bridge Port: 8888

echo.



pause

>>>>>>>
