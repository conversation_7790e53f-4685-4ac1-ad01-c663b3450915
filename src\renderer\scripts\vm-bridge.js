const { ipc<PERSON>ender<PERSON> } = require('electron');

// VM Bridge State
let vmBridgeEnabled = false;
let vmStatus = 'unknown';
let bridgeStatus = 'disconnected';
let healthMetrics = {};
let logs = [];

// Initialize VM Bridge UI
document.addEventListener('DOMContentLoaded', async () => {
    console.log('VM Bridge UI initialized');
    
    // Load initial status
    await refreshStatus();
    
    // Start periodic updates
    startPeriodicUpdates();
    
    // Setup IPC listeners
    setupIPCListeners();
    
    // Load configuration
    loadConfiguration();
});

// Setup IPC event listeners
function setupIPCListeners() {
    // VM Bridge status updates
    ipcRenderer.on('vm-bridge-status', (event, status) => {
        addLog('info', `VM Bridge Status: ${status}`);
        updateStatusDisplay(status);
    });
    
    ipcRenderer.on('vm-bridge-ready', () => {
        addLog('info', 'VM Bridge is ready');
        vmBridgeEnabled = true;
        updateUI();
    });
    
    ipcRenderer.on('vm-bridge-connected', () => {
        addLog('info', 'VM Bridge connected');
        bridgeStatus = 'connected';
        updateUI();
    });
    
    ipcRenderer.on('vm-bridge-disconnected', () => {
        addLog('warning', 'VM Bridge disconnected');
        bridgeStatus = 'disconnected';
        updateUI();
    });
    
    ipcRenderer.on('vm-bridge-error', (event, error) => {
        addLog('error', `VM Bridge Error: ${error}`);
        updateErrorDisplay(error);
    });
    
    ipcRenderer.on('vm-bridge-health-warning', (event, warning) => {
        addLog('warning', `Health Warning: ${warning}`);
    });
}

// Refresh VM Bridge status
async function refreshStatus() {
    try {
        const result = await ipcRenderer.invoke('get-vm-bridge-status');
        
        if (result.success) {
            const status = result.data;
            vmBridgeEnabled = status.enabled;
            
            if (status.enabled) {
                vmStatus = status.vm?.vmStatus || 'unknown';
                bridgeStatus = status.tunnel?.connected ? 'connected' : 'disconnected';
                healthMetrics = status.vm || {};
            }
            
            updateUI();
        }
    } catch (error) {
        console.error('Failed to refresh status:', error);
        addLog('error', `Failed to refresh status: ${error.message}`);
    }
}

// Update UI based on current state
function updateUI() {
    updateStatusIndicator();
    updateQuickActions();
    updateOverviewMetrics();
    updateFeatureStatus();
    updateHealthMetrics();
}

// Update status indicator
function updateStatusIndicator() {
    const indicator = document.getElementById('statusIndicator');
    const statusText = document.getElementById('statusText');
    
    if (!vmBridgeEnabled) {
        indicator.className = 'status-indicator disabled';
        statusText.textContent = 'Disabled';
    } else if (vmStatus === 'running' && bridgeStatus === 'connected') {
        indicator.className = 'status-indicator healthy';
        statusText.textContent = 'Healthy';
    } else if (vmStatus === 'running') {
        indicator.className = 'status-indicator warning';
        statusText.textContent = 'VM Running';
    } else {
        indicator.className = 'status-indicator critical';
        statusText.textContent = 'Stopped';
    }
}

// Update quick action buttons
function updateQuickActions() {
    const enableBtn = document.getElementById('enableBtn');
    const enableBtnText = document.getElementById('enableBtnText');
    const restartBtn = document.getElementById('restartBtn');
    const consoleBtn = document.getElementById('consoleBtn');
    
    if (vmBridgeEnabled) {
        enableBtnText.textContent = 'Disable VM Bridge';
        enableBtn.className = 'action-btn secondary';
        restartBtn.disabled = false;
        consoleBtn.disabled = vmStatus !== 'running';
    } else {
        enableBtnText.textContent = 'Enable VM Bridge';
        enableBtn.className = 'action-btn primary';
        restartBtn.disabled = true;
        consoleBtn.disabled = true;
    }
}

// Update overview metrics
function updateOverviewMetrics() {
    document.getElementById('vmStatusMetric').textContent = 
        vmStatus.charAt(0).toUpperCase() + vmStatus.slice(1);
    
    document.getElementById('bridgeStatusMetric').textContent = 
        bridgeStatus.charAt(0).toUpperCase() + bridgeStatus.slice(1);
    
    // Update other metrics if available
    if (healthMetrics.messageCount) {
        document.getElementById('messagesSyncedMetric').textContent = healthMetrics.messageCount;
    }
    
    if (healthMetrics.uptime) {
        document.getElementById('uptimeMetric').textContent = formatUptime(healthMetrics.uptime);
    }
}

// Update feature status indicators
function updateFeatureStatus() {
    const features = {
        'messagesDbStatus': vmBridgeEnabled && bridgeStatus === 'connected',
        'messageSyncStatus': vmBridgeEnabled && bridgeStatus === 'connected',
        'contactsStatus': vmBridgeEnabled && bridgeStatus === 'connected',
        'applescriptStatus': vmBridgeEnabled && vmStatus === 'running'
    };
    
    Object.entries(features).forEach(([id, enabled]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = enabled ? '✅' : '❌';
        }
    });
}

// Update health metrics
function updateHealthMetrics() {
    if (healthMetrics.cpuUsage !== undefined) {
        updateMetricBar('cpuUsageBar', 'cpuUsageText', healthMetrics.cpuUsage);
    }
    
    if (healthMetrics.memoryUsage !== undefined) {
        updateMetricBar('memoryUsageBar', 'memoryUsageText', healthMetrics.memoryUsage);
    }
    
    if (healthMetrics.networkLatency !== undefined) {
        document.getElementById('networkLatencyText').textContent = `${healthMetrics.networkLatency}ms`;
    }
    
    // Update health status
    const overallHealth = evaluateHealth();
    document.getElementById('overallHealth').textContent = overallHealth;
    document.getElementById('errorCount').textContent = healthMetrics.errorCount || 0;
    document.getElementById('warningCount').textContent = healthMetrics.warningCount || 0;
    
    if (healthMetrics.lastHealthCheck) {
        document.getElementById('lastHealthCheck').textContent = 
            new Date(healthMetrics.lastHealthCheck).toLocaleTimeString();
    }
}

// Update metric bar
function updateMetricBar(barId, textId, value) {
    const bar = document.getElementById(barId);
    const text = document.getElementById(textId);
    
    if (bar && text) {
        bar.style.width = `${Math.min(value, 100)}%`;
        text.textContent = `${Math.round(value)}%`;
    }
}

// Evaluate overall health
function evaluateHealth() {
    if (!vmBridgeEnabled) return 'Disabled';
    if (vmStatus !== 'running') return 'Critical';
    if (bridgeStatus !== 'connected') return 'Warning';
    
    const cpuHigh = (healthMetrics.cpuUsage || 0) > 80;
    const memoryHigh = (healthMetrics.memoryUsage || 0) > 85;
    const latencyHigh = (healthMetrics.networkLatency || 0) > 1000;
    
    if (cpuHigh || memoryHigh || latencyHigh) return 'Warning';
    return 'Healthy';
}

// Toggle VM Bridge
async function toggleVMBridge() {
    try {
        const action = vmBridgeEnabled ? 'disable-vm-bridge' : 'enable-vm-bridge';
        const result = await ipcRenderer.invoke(action);
        
        if (result.success) {
            vmBridgeEnabled = !vmBridgeEnabled;
            addLog('info', `VM Bridge ${vmBridgeEnabled ? 'enabled' : 'disabled'}`);
            updateUI();
        } else {
            addLog('error', `Failed to ${vmBridgeEnabled ? 'disable' : 'enable'} VM Bridge: ${result.error}`);
        }
    } catch (error) {
        addLog('error', `Error toggling VM Bridge: ${error.message}`);
    }
}

// Restart VM Bridge
async function restartVMBridge() {
    try {
        addLog('info', 'Restarting VM Bridge...');
        const result = await ipcRenderer.invoke('restart-vm-bridge');
        
        if (result.success) {
            addLog('info', 'VM Bridge restarted successfully');
            await refreshStatus();
        } else {
            addLog('error', `Failed to restart VM Bridge: ${result.error}`);
        }
    } catch (error) {
        addLog('error', `Error restarting VM Bridge: ${error.message}`);
    }
}

// Open VM Console (VNC)
function openVMConsole() {
    if (vmStatus === 'running') {
        // Open VNC viewer to VM
        const vncUrl = 'vnc://localhost:5900';
        require('electron').shell.openExternal(vncUrl);
        addLog('info', 'Opening VM console via VNC');
    }
}

// Show specific tab
function showTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected tab
    document.getElementById(tabName + 'Tab').classList.add('active');
    event.target.classList.add('active');
}

// Update VM configuration
async function updateVMConfig() {
    const config = {
        memory: document.getElementById('vmMemory').value,
        cores: parseInt(document.getElementById('vmCores').value),
        diskSize: document.getElementById('vmDiskSize').value,
        bridgePort: parseInt(document.getElementById('bridgePort').value),
        sshPort: parseInt(document.getElementById('sshPort').value),
        vncPort: parseInt(document.getElementById('vncPort').value),
        enableAppleFeatures: document.getElementById('enableAppleFeatures').checked,
        enableEncryption: document.getElementById('enableEncryption').checked,
        autoReconnect: document.getElementById('autoReconnect').checked
    };
    
    try {
        const result = await ipcRenderer.invoke('update-vm-config', config);
        
        if (result.success) {
            addLog('info', 'VM configuration updated');
        } else {
            addLog('error', `Failed to update configuration: ${result.error}`);
        }
    } catch (error) {
        addLog('error', `Error updating configuration: ${error.message}`);
    }
}

// Load configuration from storage
function loadConfiguration() {
    // Load saved configuration values
    // This would typically come from the main process
}

// Refresh metrics
async function refreshMetrics() {
    await refreshStatus();
    addLog('info', 'Metrics refreshed');
}

// Reset counters
function resetCounters() {
    // Reset error and warning counters
    addLog('info', 'Counters reset');
}

// Export health report
function exportHealthReport() {
    const report = {
        timestamp: new Date().toISOString(),
        vmBridgeEnabled,
        vmStatus,
        bridgeStatus,
        healthMetrics,
        logs: logs.slice(-100) // Last 100 log entries
    };
    
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `vm-bridge-report-${Date.now()}.json`;
    a.click();
    
    URL.revokeObjectURL(url);
    addLog('info', 'Health report exported');
}

// Clear logs
function clearLogs() {
    logs = [];
    updateLogsDisplay();
    addLog('info', 'Logs cleared');
}

// Export logs
function exportLogs() {
    const logText = logs.map(log => 
        `${log.timestamp} [${log.level.toUpperCase()}] ${log.message}`
    ).join('\n');
    
    const blob = new Blob([logText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `vm-bridge-logs-${Date.now()}.txt`;
    a.click();
    
    URL.revokeObjectURL(url);
    addLog('info', 'Logs exported');
}

// Add log entry
function addLog(level, message) {
    const logEntry = {
        timestamp: new Date().toISOString(),
        level,
        message
    };
    
    logs.push(logEntry);
    
    // Keep only last 1000 entries
    if (logs.length > 1000) {
        logs = logs.slice(-1000);
    }
    
    updateLogsDisplay();
}

// Update logs display
function updateLogsDisplay() {
    const container = document.getElementById('logsContainer');
    const logLevel = document.getElementById('logLevel').value;
    
    const filteredLogs = logLevel === 'all' ? logs : logs.filter(log => log.level === logLevel);
    
    container.innerHTML = filteredLogs.slice(-50).map(log => `
        <div class="log-entry ${log.level}">
            <span class="log-time">${new Date(log.timestamp).toLocaleString()}</span>
            <span class="log-level ${log.level}">${log.level.toUpperCase()}</span>
            <span class="log-message">${log.message}</span>
        </div>
    `).join('');
    
    // Scroll to bottom
    container.scrollTop = container.scrollHeight;
}

// Format uptime
function formatUptime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

// Start periodic updates
function startPeriodicUpdates() {
    // Update status every 30 seconds
    setInterval(refreshStatus, 30000);
    
    // Update logs display when log level changes
    document.getElementById('logLevel').addEventListener('change', updateLogsDisplay);
}

// Update status display
function updateStatusDisplay(status) {
    // Update various status displays based on incoming status
    console.log('Status update:', status);
}

// Update error display
function updateErrorDisplay(error) {
    // Show error in UI
    console.error('VM Bridge Error:', error);
}
