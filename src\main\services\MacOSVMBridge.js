const { EventEmitter } = require('events');
const { spawn, exec } = require('child_process');
const WebSocket = require('ws');
const path = require('path');
const fs = require('fs');
const os = require('os');
const Store = require('electron-store');

class MacOSVMBridge extends EventEmitter {
  constructor() {
    super();
    this.store = new Store();
    this.vmProcess = null;
    this.bridgeSocket = null;
    this.vmConfig = {
      memory: '8G',
      cores: 4,
      diskSize: '80G',
      bridgePort: 8080,
      sshPort: 2222,
      vncPort: 5900
    };
    this.vmStatus = 'stopped';
    this.bridgeConnected = false;
    this.messagesDb = null;
    this.healthCheckInterval = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  // Main setup method
  async setupVMBridge() {
    try {
      this.emit('status', 'Initializing macOS VM Bridge...');
      
      // Check system requirements
      await this.checkSystemRequirements();
      
      // Setup VM environment
      await this.setupVMEnvironment();
      
      // Create or start macOS VM
      const vm = await this.createMacOSVM();
      
      if (vm) {
        // Install and start bridge service in VM
        await this.installBridgeService();
        
        // Create WebSocket tunnel
        await this.createWebSocketTunnel();
        
        // Start health monitoring
        this.startHealthMonitoring();
        
        this.emit('bridge-ready');
        return true;
      }
      
      return false;
    } catch (error) {
      this.emit('error', `VM Bridge setup failed: ${error.message}`);
      return false;
    }
  }

  // Check if system can run macOS VM
  async checkSystemRequirements() {
    this.emit('status', 'Checking system requirements...');
    
    // Check if running on Windows
    if (os.platform() !== 'win32') {
      throw new Error('macOS VM Bridge requires Windows host system');
    }
    
    // Check for virtualization support
    const hasVirtualization = await this.checkVirtualizationSupport();
    if (!hasVirtualization) {
      throw new Error('Hardware virtualization not available. Enable VT-x/AMD-V in BIOS');
    }
    
    // Check available memory (minimum 12GB for host + VM)
    const totalMemory = os.totalmem() / (1024 * 1024 * 1024); // GB
    if (totalMemory < 12) {
      throw new Error(`Insufficient memory. Need at least 12GB, found ${totalMemory.toFixed(1)}GB`);
    }
    
    // Check for QEMU installation
    const hasQemu = await this.checkQemuInstallation();
    if (!hasQemu) {
      throw new Error('QEMU not found. Please install QEMU for Windows');
    }
    
    this.emit('status', 'System requirements check passed');
  }

  // Check if hardware virtualization is available
  async checkVirtualizationSupport() {
    return new Promise((resolve) => {
      exec('wmic cpu get VirtualizationFirmwareEnabled /value', (error, stdout) => {
        if (error) {
          resolve(false);
        } else {
          const enabled = stdout.includes('VirtualizationFirmwareEnabled=TRUE');
          resolve(enabled);
        }
      });
    });
  }

  // Check if QEMU is installed
  async checkQemuInstallation() {
    return new Promise((resolve) => {
      exec('qemu-system-x86_64 --version', (error) => {
        resolve(!error);
      });
    });
  }

  // Setup VM environment and directories
  async setupVMEnvironment() {
    this.emit('status', 'Setting up VM environment...');
    
    const vmDir = path.join(os.homedir(), 'iPhone-Companion-Pro', 'macos-vm');
    
    // Create VM directory structure
    const dirs = [
      vmDir,
      path.join(vmDir, 'disks'),
      path.join(vmDir, 'logs'),
      path.join(vmDir, 'bridge'),
      path.join(vmDir, 'shared')
    ];
    
    for (const dir of dirs) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    }
    
    this.vmDir = vmDir;
    this.diskPath = path.join(vmDir, 'disks', 'macos.qcow2');
    this.logPath = path.join(vmDir, 'logs', 'vm.log');
    
    // Check if VM disk exists
    if (!fs.existsSync(this.diskPath)) {
      await this.createVMDisk();
    }
  }

  // Create VM disk image
  async createVMDisk() {
    this.emit('status', 'Creating VM disk image...');
    
    return new Promise((resolve, reject) => {
      const createCmd = `qemu-img create -f qcow2 "${this.diskPath}" ${this.vmConfig.diskSize}`;
      
      exec(createCmd, (error, stdout, stderr) => {
        if (error) {
          reject(new Error(`Failed to create VM disk: ${error.message}`));
        } else {
          this.emit('status', 'VM disk created successfully');
          resolve();
        }
      });
    });
  }

  // Create and start macOS VM
  async createMacOSVM() {
    this.emit('status', 'Starting macOS VM...');
    
    const qemuArgs = [
      '-enable-kvm',
      '-m', this.vmConfig.memory,
      '-cpu', 'Penryn,vendor=GenuineIntel,+invtsc,vmware-cpuid-freq=on,+pcid,+ssse3,+sse4.2,+popcnt,+avx,+aes,+xsave,+xsaveopt,check',
      '-machine', 'q35,accel=kvm',
      '-smp', `${this.vmConfig.cores},cores=2`,
      '-drive', `file=${this.diskPath},if=virtio,cache=writethrough`,
      '-netdev', `user,id=net0,hostfwd=tcp::${this.vmConfig.bridgePort}-:8080,hostfwd=tcp::${this.vmConfig.sshPort}-:22,hostfwd=tcp::${this.vmConfig.vncPort}-:5900`,
      '-device', 'virtio-net-pci,netdev=net0',
      '-vnc', ':0',
      '-daemonize',
      '-pidfile', path.join(this.vmDir, 'vm.pid')
    ];

    return new Promise((resolve, reject) => {
      this.vmProcess = spawn('qemu-system-x86_64', qemuArgs, {
        stdio: ['ignore', 'pipe', 'pipe'],
        cwd: this.vmDir
      });

      this.vmProcess.on('spawn', () => {
        this.vmStatus = 'running';
        this.emit('vm-started');
        this.emit('status', 'macOS VM started successfully');
        resolve(this.vmProcess);
      });

      this.vmProcess.on('error', (error) => {
        this.vmStatus = 'error';
        this.emit('vm-error', error);
        reject(new Error(`Failed to start VM: ${error.message}`));
      });

      this.vmProcess.on('exit', (code) => {
        this.vmStatus = 'stopped';
        this.emit('vm-stopped', code);
        if (code !== 0) {
          reject(new Error(`VM exited with code ${code}`));
        }
      });

      // Log VM output
      if (this.vmProcess.stdout) {
        this.vmProcess.stdout.on('data', (data) => {
          fs.appendFileSync(this.logPath, data);
        });
      }

      if (this.vmProcess.stderr) {
        this.vmProcess.stderr.on('data', (data) => {
          fs.appendFileSync(this.logPath, data);
        });
      }
    });
  }

  // Install bridge service in VM
  async installBridgeService() {
    this.emit('status', 'Installing bridge service in VM...');
    
    // Wait for VM to boot
    await this.waitForVMBoot();
    
    // Create bridge service files
    await this.createBridgeServiceFiles();
    
    // Execute installation commands in VM
    const installCommands = [
      'cd /tmp/bridge',
      'npm install',
      'npm start &'
    ];
    
    for (const command of installCommands) {
      await this.executeInVM(command);
    }
    
    this.emit('status', 'Bridge service installed and started');
  }

  // Wait for VM to boot completely
  async waitForVMBoot(timeout = 120000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        await this.executeInVM('echo "VM Ready"');
        return true;
      } catch (error) {
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
    
    throw new Error('VM boot timeout');
  }

  // Execute command in VM via SSH
  async executeInVM(command) {
    return new Promise((resolve, reject) => {
      const sshCmd = `ssh -p ${this.vmConfig.sshPort} -o StrictHostKeyChecking=no user@localhost "${command}"`;
      
      exec(sshCmd, (error, stdout, stderr) => {
        if (error) {
          reject(error);
        } else {
          resolve(stdout);
        }
      });
    });
  }

  // Create bridge service files
  async createBridgeServiceFiles() {
    const bridgeDir = path.join(this.vmDir, 'bridge');
    
    // Create package.json
    const packageJson = {
      name: 'macos-bridge',
      version: '1.0.0',
      main: 'index.js',
      dependencies: {
        'ws': '^8.0.0',
        'sqlite3': '^5.0.0',
        'express': '^4.18.0'
      }
    };
    
    fs.writeFileSync(
      path.join(bridgeDir, 'package.json'),
      JSON.stringify(packageJson, null, 2)
    );
    
    // Create main bridge service file
    const bridgeService = this.generateBridgeServiceCode();
    fs.writeFileSync(path.join(bridgeDir, 'index.js'), bridgeService);
  }

  // Generate bridge service code
  generateBridgeServiceCode() {
    return `
const WebSocket = require('ws');
const sqlite3 = require('sqlite3').verbose();
const express = require('express');
const fs = require('fs');
const path = require('path');

class MacOSBridge {
  constructor() {
    this.wss = null;
    this.messagesDb = null;
    this.app = express();
  }

  async start() {
    console.log('Starting macOS Bridge Service...');
    
    // Start WebSocket server
    this.wss = new WebSocket.Server({ port: 8080 });
    
    this.wss.on('connection', (ws) => {
      console.log('Windows client connected');
      
      ws.on('message', async (data) => {
        try {
          const message = JSON.parse(data);
          await this.handleMessage(ws, message);
        } catch (error) {
          console.error('Message handling error:', error);
        }
      });
      
      ws.on('close', () => {
        console.log('Windows client disconnected');
      });
    });
    
    // Connect to Messages database
    await this.connectToMessagesDB();
    
    console.log('macOS Bridge Service started on port 8080');
  }

  async connectToMessagesDB() {
    const dbPath = path.join(process.env.HOME, 'Library/Messages/chat.db');
    
    if (fs.existsSync(dbPath)) {
      this.messagesDb = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY);
      console.log('Connected to Messages database');
    } else {
      console.error('Messages database not found');
    }
  }

  async handleMessage(ws, message) {
    switch (message.type) {
      case 'get_messages':
        await this.getMessages(ws, message);
        break;
      case 'send_message':
        await this.sendMessage(ws, message);
        break;
      case 'ping':
        ws.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
        break;
    }
  }

  async getMessages(ws, message) {
    if (!this.messagesDb) {
      ws.send(JSON.stringify({ type: 'error', message: 'Database not available' }));
      return;
    }

    const query = \`
      SELECT 
        m.ROWID as id,
        m.text,
        m.date,
        m.is_from_me,
        h.id as phone_number
      FROM message m
      JOIN handle h ON m.handle_id = h.ROWID
      ORDER BY m.date DESC
      LIMIT 100
    \`;

    this.messagesDb.all(query, (err, rows) => {
      if (err) {
        ws.send(JSON.stringify({ type: 'error', message: err.message }));
      } else {
        ws.send(JSON.stringify({ type: 'messages', data: rows }));
      }
    });
  }

  async sendMessage(ws, message) {
    // Use AppleScript to send message
    const script = \`
      tell application "Messages"
        set targetService to 1st service whose service type = iMessage
        set targetBuddy to buddy "\${message.phoneNumber}" of targetService
        send "\${message.text}" to targetBuddy
      end tell
    \`;
    
    const { exec } = require('child_process');
    exec(\`osascript -e '\${script}'\`, (error, stdout, stderr) => {
      if (error) {
        ws.send(JSON.stringify({ type: 'error', message: error.message }));
      } else {
        ws.send(JSON.stringify({ type: 'message_sent', success: true }));
      }
    });
  }
}

const bridge = new MacOSBridge();
bridge.start().catch(console.error);
`;
  }

  // Create WebSocket tunnel to VM
  async createWebSocketTunnel() {
    this.emit('status', 'Creating WebSocket tunnel...');

    const maxAttempts = 10;
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait for bridge service

        this.bridgeSocket = new WebSocket(`ws://localhost:${this.vmConfig.bridgePort}`);

        this.bridgeSocket.on('open', () => {
          this.bridgeConnected = true;
          this.reconnectAttempts = 0;
          this.emit('bridge-connected');
          this.emit('status', 'WebSocket tunnel established');
        });

        this.bridgeSocket.on('message', (data) => {
          try {
            const message = JSON.parse(data);
            this.handleBridgeMessage(message);
          } catch (error) {
            console.error('Bridge message parse error:', error);
          }
        });

        this.bridgeSocket.on('close', () => {
          this.bridgeConnected = false;
          this.emit('bridge-disconnected');
          this.attemptReconnect();
        });

        this.bridgeSocket.on('error', (error) => {
          console.error('Bridge socket error:', error);
          attempts++;
          if (attempts >= maxAttempts) {
            throw new Error('Failed to establish WebSocket tunnel');
          }
          setTimeout(() => {
            // Retry connection after delay
          }, 2000);
        });

        // Wait for connection
        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('WebSocket connection timeout'));
          }, 10000);

          this.bridgeSocket.on('open', () => {
            clearTimeout(timeout);
            resolve();
          });
        });

        break;
      } catch (error) {
        attempts++;
        if (attempts >= maxAttempts) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }

  // Handle messages from bridge
  handleBridgeMessage(message) {
    switch (message.type) {
      case 'messages':
        this.emit('messages-received', message.data);
        break;
      case 'message_sent':
        this.emit('message-sent', message);
        break;
      case 'error':
        this.emit('bridge-error', message.message);
        break;
      case 'pong':
        // Health check response
        break;
    }
  }

  // Send message through bridge
  async sendMessage(phoneNumber, text) {
    if (!this.bridgeConnected || !this.bridgeSocket) {
      throw new Error('Bridge not connected');
    }

    const message = {
      type: 'send_message',
      phoneNumber,
      text,
      timestamp: Date.now()
    };

    this.bridgeSocket.send(JSON.stringify(message));
  }

  // Get messages through bridge
  async getMessages() {
    if (!this.bridgeConnected || !this.bridgeSocket) {
      throw new Error('Bridge not connected');
    }

    const message = {
      type: 'get_messages',
      timestamp: Date.now()
    };

    this.bridgeSocket.send(JSON.stringify(message));
  }

  // Start health monitoring
  startHealthMonitoring() {
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, 30000); // Check every 30 seconds
  }

  // Perform health check
  async performHealthCheck() {
    try {
      // Check VM process
      if (!this.vmProcess || this.vmStatus !== 'running') {
        this.emit('health-warning', 'VM not running');
        return;
      }

      // Check bridge connection
      if (!this.bridgeConnected) {
        this.emit('health-warning', 'Bridge not connected');
        this.attemptReconnect();
        return;
      }

      // Send ping to bridge
      if (this.bridgeSocket) {
        this.bridgeSocket.send(JSON.stringify({
          type: 'ping',
          timestamp: Date.now()
        }));
      }

      this.emit('health-ok');
    } catch (error) {
      this.emit('health-error', error.message);
    }
  }

  // Attempt to reconnect bridge
  async attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit('error', 'Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    this.emit('status', `Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    try {
      await new Promise(resolve => setTimeout(resolve, 5000));
      await this.createWebSocketTunnel();
    } catch (error) {
      this.emit('error', `Reconnection failed: ${error.message}`);
    }
  }

  // Stop VM and cleanup
  async stop() {
    this.emit('status', 'Stopping macOS VM Bridge...');

    // Clear health monitoring
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    // Close bridge connection
    if (this.bridgeSocket) {
      this.bridgeSocket.close();
      this.bridgeSocket = null;
      this.bridgeConnected = false;
    }

    // Stop VM
    if (this.vmProcess && this.vmStatus === 'running') {
      try {
        await this.executeInVM('sudo shutdown -h now');

        // Wait for graceful shutdown
        await new Promise((resolve) => {
          const timeout = setTimeout(() => {
            // Force kill if graceful shutdown fails
            if (this.vmProcess) {
              this.vmProcess.kill('SIGKILL');
            }
            resolve();
          }, 30000);

          this.vmProcess.on('exit', () => {
            clearTimeout(timeout);
            resolve();
          });
        });
      } catch (error) {
        // Force kill if shutdown command fails
        if (this.vmProcess) {
          this.vmProcess.kill('SIGKILL');
        }
      }
    }

    this.vmStatus = 'stopped';
    this.emit('bridge-stopped');
    this.emit('status', 'macOS VM Bridge stopped');
  }

  // Get VM status
  getStatus() {
    return {
      vmStatus: this.vmStatus,
      bridgeConnected: this.bridgeConnected,
      reconnectAttempts: this.reconnectAttempts,
      config: this.vmConfig
    };
  }

  // Update VM configuration
  updateConfig(newConfig) {
    this.vmConfig = { ...this.vmConfig, ...newConfig };
    this.store.set('vmConfig', this.vmConfig);
  }

  // Load saved configuration
  loadConfig() {
    const savedConfig = this.store.get('vmConfig');
    if (savedConfig) {
      this.vmConfig = { ...this.vmConfig, ...savedConfig };
    }
  }
}

module.exports = MacOSVMBridge;
