/* Modern Dark Theme */
:root {
  --bg-primary: #0a0a0a;
  --bg-secondary: #141414;
  --bg-tertiary: #1f1f1f;
  --text-primary: #ffffff;
  --text-secondary: #a0a0a0;
  --accent: #007AFF;
  --accent-hover: #0051D5;
  --success: #34C759;
  --warning: #FF9500;
  --danger: #FF3B30;
  --border: #2a2a2a;
  --sidebar-width: 280px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  overflow: hidden;
  user-select: none;
}

/* Title Bar */
.titlebar {
  height: 32px;
  background: var(--bg-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  border-bottom: 1px solid var(--border);
}

.titlebar-drag-region {
  flex: 1;
  height: 100%;
  -webkit-app-region: drag;
  display: flex;
  align-items: center;
  padding-left: 12px;
}

.titlebar-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 500;
}

.titlebar-icon {
  width: 16px;
  height: 16px;
}

.titlebar-controls {
  display: flex;
  -webkit-app-region: no-drag;
}

.titlebar-button {
  width: 46px;
  height: 32px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  cursor: pointer;
}

.titlebar-button:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.titlebar-button.close:hover {
  background: var(--danger);
  color: white;
}

/* Main Layout */
.main-container {
  display: flex;
  height: calc(100vh - 32px);
  margin-top: 32px;
}

/* Sidebar */
.sidebar {
  width: var(--sidebar-width);
  background: var(--bg-secondary);
  border-right: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.connection-status {
  padding: 20px;
  border-bottom: 1px solid var(--border);
  background: var(--bg-tertiary);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 10px;
}

.status-indicator.connected {
  background: var(--success);
  box-shadow: 0 0 8px var(--success);
  animation: pulse 2s infinite;
}

.status-indicator.disconnected {
  background: var(--danger);
}

.status-indicator.connecting {
  background: var(--warning);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

.status-info {
  margin-bottom: 12px;
}

.status-title {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.status-subtitle {
  font-size: 13px;
  color: var(--text-secondary);
}

.connect-btn {
  width: 100%;
  padding: 8px 16px;
  background: var(--accent);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.connect-btn:hover {
  background: var(--accent-hover);
  transform: translateY(-1px);
}

/* Navigation */
.nav-menu {
  flex: 1;
  padding: 20px 12px;
  overflow-y: auto;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  margin-bottom: 4px;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.2s;
  position: relative;
}

.nav-item:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.nav-item.active {
  background: var(--accent);
  color: white;
}

.nav-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  fill: currentColor;
}

.nav-badge {
  position: absolute;
  right: 16px;
  background: var(--danger);
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 600;
}

.nav-badge.pulse {
  animation: pulse-scale 2s infinite;
}

@keyframes pulse-scale {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Content Area */
.content {
  flex: 1;
  overflow-y: auto;
  background: var(--bg-primary);
}

.view {
  display: none;
  padding: 30px;
  animation: fadeIn 0.3s ease;
}

.view.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.view-header {
  margin-bottom: 30px;
}

.view-header h1 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
}

.view-header p {
  color: var(--text-secondary);
  font-size: 16px;
}

/* Cards */
.card {
  background: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s;
}

.card:hover {
  border-color: var(--accent);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 122, 255, 0.1);
}

.card h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.quick-actions {
  grid-column: span 2;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.action-button {
  background: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
}

.action-button:hover {
  background: var(--accent);
  border-color: var(--accent);
  transform: translateY(-2px);
}

.action-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.action-button span {
  display: block;
  font-size: 14px;
  color: var(--text-primary);
}

/* Device Info */
.device-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.info-value {
  font-size: 16px;
  font-weight: 500;
}

/* Battery Widget */
.battery-display {
  display: flex;
  align-items: center;
  gap: 20px;
}

.battery-icon {
  width: 80px;
  height: 40px;
  border: 3px solid var(--text-secondary);
  border-radius: 6px;
  position: relative;
  padding: 3px;
}

.battery-icon::after {
  content: '';
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 16px;
  background: var(--text-secondary);
  border-radius: 0 3px 3px 0;
}

.battery-level {
  height: 100%;
  background: var(--success);
  border-radius: 3px;
  transition: all 0.3s;
}

.battery-percent {
  font-size: 36px;
  font-weight: 700;
}

.battery-status {
  font-size: 14px;
  color: var(--text-secondary);
}

/* Scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border);
}

/* Settings Modal Styles */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #2a2a2a;
  border-radius: 8px;
  border: 1px solid #444;
  cursor: pointer;
  transition: all 0.2s ease;
}

.setting-item:hover {
  background: #3a3a3a;
  border-color: #007AFF;
}

.setting-icon {
  font-size: 32px;
  opacity: 0.8;
}

.setting-info h3 {
  margin: 0 0 5px 0;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}

.setting-info p {
  margin: 0;
  color: #999;
  font-size: 14px;
}