// Settings management
class SettingsManager {
    constructor() {
        this.settings = {};
        this.defaultSettings = {
            // General
            theme: 'auto',
            language: 'en',
            startWithWindows: false,
            minimizeToTray: true,
            autoConnect: true,
            
            // Connection
            connectionMethod: 'usb',
            serverPort: 7000,
            timeout: 30,
            
            // Sync
            syncMessages: true,
            syncContacts: true,
            syncCallHistory: true,
            syncPhotos: false,
            syncInterval: 'realtime',
            
            // Notifications
            enableNotifications: true,
            notificationSound: true,
            showPreview: true,
            notifyMessages: true,
            notifyCalls: true,
            notifyApps: false,
            
            // Performance
            mirrorQuality: 'medium',
            frameRate: '30',
            hardwareAcceleration: true,
            cpuLimit: 80,
            memoryLimit: 1024,
            
            // Security
            requireAuth: false,
            authMethod: 'pin',
            encryptData: true,
            secureConnection: true,
            
            // Advanced
            enableLogging: false,
            logLevel: 'info',
            
            // CRM Integration
            crmPlatform: 'none',
            crmApiKey: '',
            crmUrl: '',
            autoSyncContacts: false,
            autoSyncMessages: false,
            autoSyncCalls: false,
            exportFormat: 'json'
        };
        
        this.init();
    }
    
    async init() {
        await this.loadSettings();
        this.setupEventListeners();
        this.setupNavigation();
        this.updateUI();
    }
    
    async loadSettings() {
        try {
            const result = await window.electronAPI.invoke('get-settings');
            if (result.success) {
                this.settings = { ...this.defaultSettings, ...result.data };
            } else {
                this.settings = { ...this.defaultSettings };
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
            this.settings = { ...this.defaultSettings };
        }
    }
    
    setupEventListeners() {
        // Range inputs
        document.querySelectorAll('input[type="range"]').forEach(range => {
            const valueSpan = range.nextElementSibling;
            
            range.addEventListener('input', (e) => {
                const value = e.target.value;
                const suffix = e.target.id === 'cpuLimit' ? '%' : ' MB';
                valueSpan.textContent = value + suffix;
            });
        });
        
        // Form inputs
        document.querySelectorAll('input, select').forEach(input => {
            input.addEventListener('change', () => {
                this.markAsModified();
            });
        });
    }
    
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        const sections = document.querySelectorAll('.settings-section');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Remove active class from all links and sections
                navLinks.forEach(l => l.classList.remove('active'));
                sections.forEach(s => s.classList.remove('active'));
                
                // Add active class to clicked link
                link.classList.add('active');
                
                // Show corresponding section
                const sectionId = link.dataset.section;
                const section = document.getElementById(sectionId);
                if (section) {
                    section.classList.add('active');
                }
            });
        });
    }
    
    updateUI() {
        // Update all form elements with current settings
        Object.keys(this.settings).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = this.settings[key];
                } else if (element.type === 'radio') {
                    if (element.value === this.settings[key]) {
                        element.checked = true;
                    }
                } else {
                    element.value = this.settings[key];
                }
                
                // Update range value displays
                if (element.type === 'range') {
                    const valueSpan = element.nextElementSibling;
                    if (valueSpan) {
                        const suffix = key === 'cpuLimit' ? '%' : ' MB';
                        valueSpan.textContent = this.settings[key] + suffix;
                    }
                }
            }
        });
        
        // Update radio buttons
        document.querySelectorAll('input[type="radio"]').forEach(radio => {
            if (radio.name === 'connectionMethod' && radio.value === this.settings.connectionMethod) {
                radio.checked = true;
            }
        });
    }
    
    collectSettings() {
        const newSettings = {};
        
        // Collect all form values
        document.querySelectorAll('input, select').forEach(input => {
            if (input.type === 'checkbox') {
                newSettings[input.id] = input.checked;
            } else if (input.type === 'radio') {
                if (input.checked) {
                    newSettings[input.name] = input.value;
                }
            } else if (input.id) {
                newSettings[input.id] = input.value;
            }
        });
        
        return newSettings;
    }
    
    markAsModified() {
        const saveButton = document.querySelector('.btn-primary');
        if (saveButton) {
            saveButton.textContent = 'Save Settings*';
            saveButton.classList.add('modified');
        }
    }
    
    async saveSettings() {
        try {
            const newSettings = this.collectSettings();
            
            const result = await window.electronAPI.invoke('save-settings', newSettings);
            if (result.success) {
                this.settings = newSettings;
                this.showNotification('Settings saved successfully', 'success');
                
                // Reset save button
                const saveButton = document.querySelector('.btn-primary');
                if (saveButton) {
                    saveButton.textContent = 'Save Settings';
                    saveButton.classList.remove('modified');
                }
            } else {
                this.showNotification('Failed to save settings: ' + result.error, 'error');
            }
        } catch (error) {
            console.error('Failed to save settings:', error);
            this.showNotification('Failed to save settings', 'error');
        }
    }
    
    async resetSettings() {
        if (confirm('Are you sure you want to reset all settings to defaults?')) {
            try {
                const result = await window.electronAPI.invoke('reset-settings');
                if (result.success) {
                    this.settings = { ...this.defaultSettings };
                    this.updateUI();
                    this.showNotification('Settings reset to defaults', 'success');
                } else {
                    this.showNotification('Failed to reset settings: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('Failed to reset settings:', error);
                this.showNotification('Failed to reset settings', 'error');
            }
        }
    }
    
    async clearAllData() {
        if (confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
            try {
                const result = await window.electronAPI.invoke('clear-all-data');
                if (result.success) {
                    this.showNotification('All data cleared successfully', 'success');
                    setTimeout(() => {
                        window.close();
                    }, 2000);
                } else {
                    this.showNotification('Failed to clear data: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('Failed to clear data:', error);
                this.showNotification('Failed to clear data', 'error');
            }
        }
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // Hide and remove notification
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Global functions
let settingsManager;

function saveSettings() {
    if (settingsManager) {
        settingsManager.saveSettings();
    }
}

function resetSettings() {
    if (settingsManager) {
        settingsManager.resetSettings();
    }
}

function clearAllData() {
    if (settingsManager) {
        settingsManager.clearAllData();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    settingsManager = new SettingsManager();
    
    // Listen for navigation requests from menu
    const { ipcRenderer } = require('electron');
    ipcRenderer.on('navigate-to-section', (event, section) => {
        console.log('📍 Navigating to section:', section);
        navigateToSection(section);
    });
});

// Navigate to specific settings section
function navigateToSection(sectionName) {
    // Remove active class from all nav links and sections
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
        if (link.dataset.section === sectionName) {
            link.classList.add('active');
            link.click(); // Trigger the existing click handler
        }
    });
}

// CRM Integration Functions
async function testCRMConnection() {
    const platform = document.getElementById('crmPlatform').value;
    const apiKey = document.getElementById('crmApiKey').value;
    const url = document.getElementById('crmUrl').value;
    
    if (platform === 'none') {
        settingsManager.showNotification('Please select a CRM platform first', 'warning');
        return;
    }
    
    if (!apiKey) {
        settingsManager.showNotification('Please enter your CRM API key', 'warning');
        return;
    }
    
    try {
        settingsManager.showNotification('Testing CRM connection...', 'info');
        
        // Test the connection
        const result = await window.electronAPI.invoke('test-crm-connection', {
            platform: platform,
            apiKey: apiKey,
            url: url
        });
        
        if (result.success) {
            settingsManager.showNotification('CRM connection successful!', 'success');
            updateCRMStatus('connected', 'Connected to ' + platform);
        } else {
            settingsManager.showNotification('CRM connection failed: ' + result.error, 'error');
            updateCRMStatus('error', 'Connection failed');
        }
    } catch (error) {
        console.error('CRM connection test failed:', error);
        settingsManager.showNotification('CRM connection test failed', 'error');
        updateCRMStatus('error', 'Connection failed');
    }
}

async function exportToCSV() {
    try {
        settingsManager.showNotification('Exporting messages to CSV...', 'info');
        
        const result = await window.electronAPI.invoke('export-messages-csv');
        
        if (result.success) {
            settingsManager.showNotification(`Messages exported successfully to ${result.filePath}`, 'success');
        } else {
            settingsManager.showNotification('Export failed: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('Export failed:', error);
        settingsManager.showNotification('Export failed', 'error');
    }
}

async function exportCallHistory() {
    try {
        settingsManager.showNotification('Exporting call history...', 'info');
        
        const format = document.getElementById('exportFormat').value;
        const result = await window.electronAPI.invoke('export-call-history', { format: format });
        
        if (result.success) {
            settingsManager.showNotification(`Call history exported successfully to ${result.filePath}`, 'success');
        } else {
            settingsManager.showNotification('Export failed: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('Export failed:', error);
        settingsManager.showNotification('Export failed', 'error');
    }
}

function updateCRMStatus(status, message) {
    const indicator = document.getElementById('crmStatusIndicator');
    const statusText = document.getElementById('crmStatusText');
    const statusDetails = document.getElementById('crmStatusDetails');
    
    if (indicator) {
        indicator.className = `status-indicator ${status}`;
    }
    
    if (statusText) {
        statusText.textContent = message;
    }
    
    if (statusDetails) {
        switch (status) {
            case 'connected':
                statusDetails.textContent = 'CRM integration is active and working';
                break;
            case 'error':
                statusDetails.textContent = 'Check your CRM settings and try again';
                break;
            default:
                statusDetails.textContent = 'Configure CRM settings above';
        }
    }
}

// Handle window close
window.addEventListener('beforeunload', (e) => {
    const saveButton = document.querySelector('.btn-primary');
    if (saveButton && saveButton.classList.contains('modified')) {
        e.preventDefault();
        e.returnValue = 'You have unsaved changes. Are you sure you want to close?';
    }
});
