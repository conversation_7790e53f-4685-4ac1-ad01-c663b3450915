#!/usr/bin/env node

const noble = require('@abandonware/noble');

console.log('🔍 Testing Bluetooth on Windows...');
console.log('Platform:', process.platform);
console.log('Node version:', process.version);

// Check if we're on Windows
if (process.platform !== 'win32') {
    console.log('❌ This test is designed for Windows only');
    process.exit(1);
}

let scanTimeout;

noble.on('stateChange', (state) => {
    console.log(`📡 Bluetooth state: ${state}`);
    
    if (state === 'poweredOn') {
        console.log('✅ Bluetooth is powered on');
        console.log('🔍 Starting scan for devices...');
        
        noble.startScanning([], true); // Allow duplicates to see signal strength changes
        
        // Stop scanning after 30 seconds
        scanTimeout = setTimeout(() => {
            console.log('⏰ Scan timeout - stopping scan');
            noble.stopScanning();
            process.exit(0);
        }, 30000);
        
    } else {
        console.log(`❌ Bluetooth not ready: ${state}`);
        if (state === 'unauthorized') {
            console.log('💡 Try running as administrator');
        }
    }
});

noble.on('discover', (peripheral) => {
    const name = peripheral.advertisement.localName || 'Unknown Device';
    const address = peripheral.address || 'Unknown Address';
    const rssi = peripheral.rssi;
    
    console.log(`📱 Found device: ${name} (${address}) RSSI: ${rssi}dBm`);
    
    // Look for iPhone-like devices
    if (name.toLowerCase().includes('iphone') || 
        name.toLowerCase().includes('apple') ||
        peripheral.advertisement.serviceUuids.some(uuid => 
            uuid.toLowerCase().includes('74b8') || // Apple services
            uuid.toLowerCase().includes('180f')    // Battery service
        )) {
        console.log(`🍎 POTENTIAL IPHONE DETECTED: ${name}`);
        console.log('   Services:', peripheral.advertisement.serviceUuids);
        console.log('   Manufacturer:', peripheral.advertisement.manufacturerData?.toString('hex') || 'N/A');
    }
});

noble.on('scanStart', () => {
    console.log('🔍 Bluetooth scan started');
});

noble.on('scanStop', () => {
    console.log('⏹️ Bluetooth scan stopped');
});

// Handle cleanup
process.on('SIGINT', () => {
    console.log('\n🛑 Stopping scan...');
    if (scanTimeout) clearTimeout(scanTimeout);
    noble.stopScanning();
    process.exit(0);
});

console.log('⚡ Initializing Bluetooth...');
console.log('📱 Make sure your iPhone Bluetooth is enabled and discoverable');
console.log('🔄 Scan will run for 30 seconds...');