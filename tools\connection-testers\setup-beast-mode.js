#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class BeastModeSetup {
  constructor() {
    this.steps = [
      'Checking system requirements',
      'Installing dependencies',
      'Setting up database',
      'Configuring services',
      'Testing connections',
      'Finalizing setup'
    ];
    this.currentStep = 0;
  }

  async run() {
    console.log('🔥 BEAST MODE SETUP WIZARD 🔥\n');
    console.log('Setting up iPhone Companion Pro with full persistence...\n');

    try {
      await this.checkSystemRequirements();
      await this.installDependencies();
      await this.setupDatabase();
      await this.configureServices();
      await this.testConnections();
      await this.finalizeSetup();

      this.showSuccessMessage();
    } catch (error) {
      this.showErrorMessage(error);
    }
  }

  async checkSystemRequirements() {
    this.logStep('Checking system requirements');

    // Check Node.js version
    const nodeVersion = process.version;
    console.log(`   Node.js version: ${nodeVersion}`);

    if (parseInt(nodeVersion.slice(1)) < 16) {
      throw new Error('Node.js 16 or higher is required');
    }

    // Check Windows version
    const os = require('os');
    console.log(`   Operating System: ${os.platform()} ${os.release()}`);

    if (os.platform() !== 'win32') {
      console.log('   ⚠️  This setup is optimized for Windows');
    }

    // Check available disk space
    const stats = fs.statSync('.');
    console.log('   ✅ System requirements met');
  }

  async installDependencies() {
    this.logStep('Installing dependencies');

    try {
      console.log('   Installing SQLite and persistence modules...');
      execSync('npm install sqlite3 better-sqlite3 node-persist chokidar', { stdio: 'inherit' });
      
      console.log('   Installing additional modules...');
      execSync('npm install ws qrcode electron-store', { stdio: 'inherit' });
      
      console.log('   ✅ Dependencies installed successfully');
    } catch (error) {
      throw new Error(`Failed to install dependencies: ${error.message}`);
    }
  }

  async setupDatabase() {
    this.logStep('Setting up database');

    try {
      // Create data directory
      const dataDir = path.join(process.cwd(), 'data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
        console.log('   Created data directory');
      }

      // Create backups directory
      const backupsDir = path.join(dataDir, 'backups');
      if (!fs.existsSync(backupsDir)) {
        fs.mkdirSync(backupsDir, { recursive: true });
        console.log('   Created backups directory');
      }

      console.log('   ✅ Database structure ready');
    } catch (error) {
      throw new Error(`Failed to setup database: ${error.message}`);
    }
  }

  async configureServices() {
    this.logStep('Configuring services');

    try {
      // Create config file
      const config = {
        beastMode: {
          enabled: true,
          persistence: {
            autoBackup: true,
            backupInterval: 300000, // 5 minutes
            maxBackups: 10
          },
          connections: {
            autoReconnect: true,
            reconnectInterval: 10000, // 10 seconds
            syncInterval: 2000 // 2 seconds
          },
          integrations: {
            phoneLink: true,
            airPlay: true,
            companionApp: true,
            webBridge: true,
            vmBridge: false // Advanced feature
          }
        }
      };

      fs.writeFileSync('beast-config.json', JSON.stringify(config, null, 2));
      console.log('   Created configuration file');

      console.log('   ✅ Services configured');
    } catch (error) {
      throw new Error(`Failed to configure services: ${error.message}`);
    }
  }

  async testConnections() {
    this.logStep('Testing connections');

    try {
      // Test if required ports are available
      const net = require('net');
      const ports = [8080, 8888, 7000];

      for (const port of ports) {
        await this.testPort(port);
        console.log(`   Port ${port}: Available`);
      }

      console.log('   ✅ All ports available');
    } catch (error) {
      console.log(`   ⚠️  Port test warning: ${error.message}`);
      console.log('   Continuing setup...');
    }
  }

  async testPort(port) {
    return new Promise((resolve, reject) => {
      const server = net.createServer();
      
      server.listen(port, () => {
        server.close(() => resolve());
      });
      
      server.on('error', (err) => {
        if (err.code === 'EADDRINUSE') {
          reject(new Error(`Port ${port} is in use`));
        } else {
          reject(err);
        }
      });
    });
  }

  async finalizeSetup() {
    this.logStep('Finalizing setup');

    try {
      // Create startup script
      const startupScript = `@echo off
echo 🔥 Starting iPhone Companion Pro BEAST MODE 🔥
echo.
npm start
pause`;

      fs.writeFileSync('start-beast-mode.bat', startupScript);
      console.log('   Created startup script');

      // Create test script
      const testScript = `@echo off
echo 🧪 Running BEAST MODE Tests 🧪
echo.
node tests/beast-mode-test.js
pause`;

      fs.writeFileSync('test-beast-mode.bat', testScript);
      console.log('   Created test script');

      console.log('   ✅ Setup finalized');
    } catch (error) {
      throw new Error(`Failed to finalize setup: ${error.message}`);
    }
  }

  logStep(stepName) {
    this.currentStep++;
    console.log(`\n[${this.currentStep}/${this.steps.length}] ${stepName}...`);
  }

  showSuccessMessage() {
    console.log('\n' + '='.repeat(60));
    console.log('🎉 BEAST MODE SETUP COMPLETE! 🎉');
    console.log('='.repeat(60));
    console.log('\n✅ iPhone Companion Pro is ready with:');
    console.log('   • Full data persistence (SQLite)');
    console.log('   • Auto-reconnect system');
    console.log('   • Multiple integration methods');
    console.log('   • Professional UI design');
    console.log('   • Automatic backups');
    console.log('\n🚀 Quick Start:');
    console.log('   1. Run: start-beast-mode.bat');
    console.log('   2. Click "Connect iPhone" in the app');
    console.log('   3. Choose your preferred connection method');
    console.log('\n🧪 Test Everything:');
    console.log('   Run: test-beast-mode.bat');
    console.log('\n📖 Documentation:');
    console.log('   Read: BEAST-MODE-README.md');
    console.log('\n🔥 BEAST MODE: Your iPhone, Perfectly Mirrored! 🔥\n');
  }

  showErrorMessage(error) {
    console.log('\n' + '='.repeat(60));
    console.log('❌ SETUP FAILED');
    console.log('='.repeat(60));
    console.log(`\nError: ${error.message}`);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Ensure you have Node.js 16+ installed');
    console.log('   2. Run as Administrator if needed');
    console.log('   3. Check your internet connection');
    console.log('   4. Close other applications using ports 8080, 8888, 7000');
    console.log('\n💬 Need help? Check BEAST-MODE-README.md\n');
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  const setup = new BeastModeSetup();
  setup.run();
}

module.exports = { BeastModeSetup };
