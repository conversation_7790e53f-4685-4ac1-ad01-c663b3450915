<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>iPhone Companion Pro</title>
    <link rel="stylesheet" href="styles/unified.css">
    <link rel="stylesheet" href="styles/logs.css">
</head>
<body>
    <!-- Clean Header -->
    <div class="app-header">
        <div class="logo">
            <img src="../../assets/icon.png" alt="Logo">
            <h1>iPhone Companion Pro</h1>
        </div>
        <div class="connection-status">
            <span class="status-dot"></span>
            <span id="connection-text">Not Connected</span>
        </div>
    </div>

    <!-- Sidebar Navigation -->
    <div class="app-container">
        <nav class="sidebar">
            <div class="nav-section">
                <h3>Device</h3>
                <button class="nav-btn active" data-view="dashboard">
                    <i>📱</i> Dashboard
                </button>
                <button class="nav-btn" data-view="connection">
                    <i>🔗</i> Connection
                </button>
            </div>
            
            <div class="nav-section">
                <h3>Features</h3>
                <button class="nav-btn" data-view="messages">
                    <i>💬</i> Messages
                    <span class="badge" id="msg-count">0</span>
                </button>
                <button class="nav-btn" data-view="calls">
                    <i>📞</i> Calls
                </button>
                <button class="nav-btn" data-view="mirror">
                    <i>📺</i> Screen Mirror
                </button>
                <button class="nav-btn" data-view="files">
                    <i>📁</i> Files
                </button>
            </div>
            
            <div class="nav-section">
                <h3>System</h3>
                <button class="nav-btn" data-view="settings">
                    <i>⚙️</i> Settings
                </button>
                <button class="nav-btn" data-view="logs">
                    <i>📋</i> Logs
                </button>
            </div>
        </nav>

        <!-- Main Content Area -->
        <main class="content">
            <!-- Dashboard View -->
            <div class="view active" id="dashboard-view">
                <div class="view-header">
                    <h2>Dashboard</h2>
                    <p>Your iPhone at a glance</p>
                </div>
                
                <div class="dashboard-grid">
                    <!-- Device Card -->
                    <div class="card device-card">
                        <h3>📱 Device Info</h3>
                        <div class="device-icon">📱</div>
                        <div class="device-details">
                            <p><strong>Name:</strong> <span id="device-name" class="device-value">iPhone 15 Pro</span></p>
                            <p><strong>Model:</strong> <span id="device-model" class="device-value">iPhone15,2</span></p>
                            <p><strong>iOS:</strong> <span id="device-ios" class="device-value">17.2.1</span></p>
                            <p><strong>Battery:</strong> <span id="device-battery" class="device-value battery-indicator">87%</span></p>
                            <p><strong>Quality:</strong> <span id="connection-quality" class="device-value quality-indicator">Excellent</span></p>
                            <p><strong>Time:</strong> <span id="current-time" class="device-value">-</span></p>
                        </div>
                    </div>
                    
                    <!-- Quick Stats -->
                    <div class="card stats-card">
                        <h3>Activity</h3>
                        <div class="stat-grid">
                            <div class="stat">
                                <div class="stat-value" id="stat-messages">0</div>
                                <div class="stat-label">Messages Today</div>
                            </div>
                            <div class="stat">
                                <div class="stat-value" id="stat-calls">0</div>
                                <div class="stat-label">Calls Today</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="card actions-card">
                        <h3>Quick Actions</h3>
                        <div class="action-buttons">
                            <button class="action-btn" onclick="quickAction('mirror')">
                                <i>📺</i>
                                <span>Start Mirroring</span>
                            </button>
                            <button class="action-btn" onclick="quickAction('sync')">
                                <i>🔄</i>
                                <span>Sync Now</span>
                            </button>
                            <button class="action-btn" onclick="quickAction('backup')">
                                <i>💾</i>
                                <span>Backup</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Connection View -->
            <div class="view" id="connection-view">
                <div class="view-header">
                    <h2>Connection Center</h2>
                    <p>Choose how to connect your iPhone</p>
                </div>
                
                <div class="connection-methods">
                    <div class="method-card" onclick="connectMethod('usb')">
                        <div class="method-icon">🔌</div>
                        <h4>USB Cable</h4>
                        <p>Most reliable, fastest</p>
                        <div class="method-status" id="usb-status">Not Connected</div>
                    </div>
                    
                    <div class="method-card" onclick="connectMethod('wifi')">
                        <div class="method-icon">📶</div>
                        <h4>WiFi/AirPlay</h4>
                        <p>Wireless screen mirroring</p>
                        <div class="method-status" id="wifi-status">Not Connected</div>
                    </div>
                    
                    <div class="method-card" onclick="connectMethod('bluetooth')">
                        <div class="method-icon">🔵</div>
                        <h4>Bluetooth</h4>
                        <p>Limited features</p>
                        <div class="method-status" id="bt-status">Not Connected</div>
                    </div>
                    
                    <div class="method-card" onclick="connectMethod('phonelink')">
                        <div class="method-icon">🔗</div>
                        <h4>Phone Link</h4>
                        <p>Use Windows Phone Link</p>
                        <div class="method-status" id="pl-status">Not Detected</div>
                    </div>
                </div>
                
                <div class="connection-log">
                    <h3>Connection Log</h3>
                    <div id="connection-log-content">
                        <p class="log-entry">Waiting for connection...</p>
                    </div>
                </div>
            </div>

            <!-- Calls View -->
            <div class="view" id="calls-view">
                <div class="view-header">
                    <h2>Calls</h2>
                    <p>Manage your calls and call history</p>
                </div>

                <div class="calls-container">
                    <!-- Call History Panel -->
                    <div class="calls-panel">
                        <div class="calls-header">
                            <h3>Recent Calls</h3>
                            <button class="new-call-btn" onclick="showDialer()">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                </svg>
                            </button>
                        </div>

                        <div class="search-box">
                            <input type="text" placeholder="Search calls..." id="call-search" oninput="searchCalls()">
                        </div>

                        <div class="calls-list" id="calls-list">
                            <!-- Sample call entries -->
                            <div class="call-entry missed">
                                <div class="call-avatar">
                                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face" alt="Contact">
                                    <div class="online-indicator"></div>
                                </div>
                                <div class="call-info">
                                    <div class="call-name">Sarah Johnson</div>
                                    <div class="call-details">
                                        <span class="call-type">📞 Missed</span>
                                        <span class="call-time">2 min ago</span>
                                    </div>
                                </div>
                                <div class="call-actions">
                                    <button class="call-btn" onclick="callContact('sarah')">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                        </svg>
                                    </button>
                                    <button class="video-btn" onclick="videoCall('sarah')">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <polygon points="23 7 16 12 23 17 23 7"/>
                                            <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <div class="call-entry outgoing">
                                <div class="call-avatar">
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face" alt="Contact">
                                </div>
                                <div class="call-info">
                                    <div class="call-name">Mike Chen</div>
                                    <div class="call-details">
                                        <span class="call-type">📞 Outgoing</span>
                                        <span class="call-time">5 min ago • 2:34</span>
                                    </div>
                                </div>
                                <div class="call-actions">
                                    <button class="call-btn" onclick="callContact('mike')">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                        </svg>
                                    </button>
                                    <button class="video-btn" onclick="videoCall('mike')">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <polygon points="23 7 16 12 23 17 23 7"/>
                                            <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <div class="call-entry incoming">
                                <div class="call-avatar">
                                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face" alt="Contact">
                                </div>
                                <div class="call-info">
                                    <div class="call-name">Emma Wilson</div>
                                    <div class="call-details">
                                        <span class="call-type">📞 Incoming</span>
                                        <span class="call-time">1 hour ago • 12:45</span>
                                    </div>
                                </div>
                                <div class="call-actions">
                                    <button class="call-btn" onclick="callContact('emma')">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                        </svg>
                                    </button>
                                    <button class="video-btn" onclick="videoCall('emma')">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <polygon points="23 7 16 12 23 17 23 7"/>
                                            <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Dialer Panel -->
                    <div class="dialer-panel">
                        <div class="dialer-header">
                            <h3>Dialer</h3>
                        </div>

                        <div class="phone-display">
                            <input type="tel" class="phone-input" id="phone-input" placeholder="Enter number" readonly>
                            <button class="clear-btn" onclick="clearNumber()">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M18 6L6 18M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>

                        <div class="dialer-grid">
                            <button class="dial-btn" onclick="addDigit('1')">
                                <span class="digit">1</span>
                                <span class="letters"></span>
                            </button>
                            <button class="dial-btn" onclick="addDigit('2')">
                                <span class="digit">2</span>
                                <span class="letters">ABC</span>
                            </button>
                            <button class="dial-btn" onclick="addDigit('3')">
                                <span class="digit">3</span>
                                <span class="letters">DEF</span>
                            </button>
                            <button class="dial-btn" onclick="addDigit('4')">
                                <span class="digit">4</span>
                                <span class="letters">GHI</span>
                            </button>
                            <button class="dial-btn" onclick="addDigit('5')">
                                <span class="digit">5</span>
                                <span class="letters">JKL</span>
                            </button>
                            <button class="dial-btn" onclick="addDigit('6')">
                                <span class="digit">6</span>
                                <span class="letters">MNO</span>
                            </button>
                            <button class="dial-btn" onclick="addDigit('7')">
                                <span class="digit">7</span>
                                <span class="letters">PQRS</span>
                            </button>
                            <button class="dial-btn" onclick="addDigit('8')">
                                <span class="digit">8</span>
                                <span class="letters">TUV</span>
                            </button>
                            <button class="dial-btn" onclick="addDigit('9')">
                                <span class="digit">9</span>
                                <span class="letters">WXYZ</span>
                            </button>
                            <button class="dial-btn" onclick="addDigit('*')">
                                <span class="digit">*</span>
                                <span class="letters"></span>
                            </button>
                            <button class="dial-btn" onclick="addDigit('0')">
                                <span class="digit">0</span>
                                <span class="letters">+</span>
                            </button>
                            <button class="dial-btn" onclick="addDigit('#')">
                                <span class="digit">#</span>
                                <span class="letters"></span>
                            </button>
                        </div>

                        <div class="call-controls">
                            <button class="call-action-btn" onclick="makeCall()">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                </svg>
                                <span>Call</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Messages View -->
            <div class="view" id="messages-view">
                <div class="view-header">
                    <h2>Messages</h2>
                    <p>Send and receive messages from your iPhone</p>
                </div>

                <div class="messages-container">
                    <!-- Conversations Panel -->
                    <div class="conversations-panel">
                        <div class="conversations-header">
                            <h3>Messages</h3>
                            <button class="new-message-btn" onclick="newMessage()">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M12 5v14m-7-7h14"/>
                                </svg>
                            </button>
                        </div>

                        <div class="search-box">
                            <input type="text" placeholder="Search messages..." id="message-search" oninput="searchMessages()">
                        </div>

                        <div class="conversations-list" id="conversations-list">
                            <!-- Sample conversations -->
                            <div class="conversation-item active" onclick="selectConversation('sarah')">
                                <div class="conversation-avatar">
                                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face" alt="Sarah">
                                    <div class="online-indicator"></div>
                                </div>
                                <div class="conversation-info">
                                    <div class="conversation-header">
                                        <div class="conversation-name">Sarah Johnson</div>
                                        <div class="conversation-time">2m</div>
                                    </div>
                                    <div class="conversation-preview">
                                        <span class="preview-text">Hey! Are we still on for dinner tonight? 🍽️</span>
                                        <div class="unread-badge">2</div>
                                    </div>
                                </div>
                            </div>

                            <div class="conversation-item" onclick="selectConversation('mike')">
                                <div class="conversation-avatar">
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face" alt="Mike">
                                </div>
                                <div class="conversation-info">
                                    <div class="conversation-header">
                                        <div class="conversation-name">Mike Chen</div>
                                        <div class="conversation-time">1h</div>
                                    </div>
                                    <div class="conversation-preview">
                                        <span class="preview-text">Thanks for the help with the project! �</span>
                                    </div>
                                </div>
                            </div>

                            <div class="conversation-item" onclick="selectConversation('emma')">
                                <div class="conversation-avatar">
                                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face" alt="Emma">
                                </div>
                                <div class="conversation-info">
                                    <div class="conversation-header">
                                        <div class="conversation-name">Emma Wilson</div>
                                        <div class="conversation-time">3h</div>
                                    </div>
                                    <div class="conversation-preview">
                                        <span class="preview-text">The meeting went great! Let's catch up soon.</span>
                                    </div>
                                </div>
                            </div>

                            <div class="conversation-item" onclick="selectConversation('team')">
                                <div class="conversation-avatar group">
                                    <div class="group-avatar">
                                        <span>👥</span>
                                    </div>
                                </div>
                                <div class="conversation-info">
                                    <div class="conversation-header">
                                        <div class="conversation-name">Team Chat</div>
                                        <div class="conversation-time">5h</div>
                                    </div>
                                    <div class="conversation-preview">
                                        <span class="preview-text">Alex: Don't forget about tomorrow's standup!</span>
                                        <div class="unread-badge">5</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Area -->
                    <div class="chat-area">
                        <div class="chat-header">
                            <div class="chat-contact">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face" alt="Select a conversation" class="chat-contact-avatar">
                                <div class="chat-contact-info">
                                    <h4>Select a conversation</h4>
                                    <p class="chat-contact-status">Choose a contact to start messaging</p>
                                </div>
                            </div>
                            <div class="chat-actions">
                                <button class="chat-action-btn">📞</button>
                                <button class="chat-action-btn">📹</button>
                                <button class="chat-action-btn">ℹ️</button>
                            </div>
                        </div>

                        <div class="messages-area">
                            <div class="empty-state">
                                <div class="empty-icon">💬</div>
                                <h3>No conversation selected</h3>
                                <p>Choose a conversation from the sidebar to start messaging</p>
                            </div>
                        </div>

                        <div class="message-input-area">
                            <div class="message-input-container">
                                <textarea class="message-input" placeholder="Type a message..." rows="1" onkeypress="if(event.key==='Enter' && !event.shiftKey){event.preventDefault(); sendMessage();}"></textarea>
                                <div class="input-actions">
                                    <button class="input-action-btn" title="Attach file">📎</button>
                                    <button class="input-action-btn" title="Add emoji">😊</button>
                                    <button class="input-action-btn send-btn" onclick="sendMessage()" title="Send">➤</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Screen Mirror View -->
            <div class="view" id="mirror-view">
                <div class="view-header">
                    <h2>Screen Mirror</h2>
                    <p>Mirror your iPhone screen wirelessly</p>
                </div>

                <div class="mirror-container">
                    <!-- Mirror Controls -->
                    <div class="mirror-controls">
                        <div class="connection-panel">
                            <h3>AirPlay Connection</h3>
                            <div class="connection-status-indicator">
                                <div class="status-dot disconnected"></div>
                                <span id="airplay-status">Not Connected</span>
                            </div>
                            <button class="connect-btn" onclick="toggleAirPlay()">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M5 17H4a2 2 0 01-2-2V5a2 2 0 012-2h16a2 2 0 012 2v10a2 2 0 01-2 2h-1"/>
                                    <polygon points="12 15 17 21 7 21 12 15"/>
                                </svg>
                                <span id="connect-text">Connect AirPlay</span>
                            </button>
                        </div>

                        <div class="mirror-settings">
                            <h4>Display Settings</h4>
                            <div class="setting-group">
                                <label>Quality:</label>
                                <select id="quality-select" onchange="updateQuality()">
                                    <option value="auto">Auto</option>
                                    <option value="high">High (1080p)</option>
                                    <option value="medium">Medium (720p)</option>
                                    <option value="low">Low (480p)</option>
                                </select>
                            </div>
                            <div class="setting-group">
                                <label>Frame Rate:</label>
                                <select id="framerate-select" onchange="updateFrameRate()">
                                    <option value="60">60 FPS</option>
                                    <option value="30">30 FPS</option>
                                    <option value="15">15 FPS</option>
                                </select>
                            </div>
                            <div class="setting-group">
                                <label>
                                    <input type="checkbox" id="audio-mirror" onchange="toggleAudio()">
                                    Mirror Audio
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Mirror Display -->
                    <div class="mirror-display">
                        <div class="device-frame">
                            <div class="device-screen" id="mirror-screen">
                                <div class="mirror-placeholder">
                                    <div class="placeholder-icon">�</div>
                                    <h3>iPhone Screen Mirror</h3>
                                    <p>Connect your iPhone via AirPlay to see your screen here</p>
                                    <div class="connection-steps">
                                        <div class="step">
                                            <span class="step-number">1</span>
                                            <span>Open Control Center on your iPhone</span>
                                        </div>
                                        <div class="step">
                                            <span class="step-number">2</span>
                                            <span>Tap Screen Mirroring</span>
                                        </div>
                                        <div class="step">
                                            <span class="step-number">3</span>
                                            <span>Select "iPhone Companion Pro"</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Device Controls -->
                            <div class="device-controls">
                                <button class="control-btn" onclick="simulateHomeButton()" title="Home">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z"/>
                                    </svg>
                                </button>
                                <button class="control-btn" onclick="simulateVolumeUp()" title="Volume Up">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/>
                                        <path d="M15.54 8.46a5 5 0 010 7.07"/>
                                        <path d="M19.07 4.93a10 10 0 010 14.14"/>
                                    </svg>
                                </button>
                                <button class="control-btn" onclick="simulateVolumeDown()" title="Volume Down">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/>
                                        <path d="M15.54 8.46a5 5 0 010 7.07"/>
                                    </svg>
                                </button>
                                <button class="control-btn" onclick="takeScreenshot()" title="Screenshot">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M23 19a2 2 0 01-2 2H3a2 2 0 01-2-2V8a2 2 0 012-2h4l2-3h6l2 3h4a2 2 0 012 2z"/>
                                        <circle cx="12" cy="13" r="4"/>
                                    </svg>
                                </button>
                                <button class="control-btn" onclick="toggleFullscreen()" title="Fullscreen">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M8 3H5a2 2 0 00-2 2v3m18 0V5a2 2 0 00-2-2h-3m0 18h3a2 2 0 002-2v-3M3 16v3a2 2 0 002 2h3"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Files View -->
            <div class="view" id="files-view">
                <div class="view-header">
                    <h2>Files</h2>
                    <p>Transfer files between your iPhone and PC</p>
                </div>

                <div class="files-container">
                    <div class="empty-state">
                        <div class="empty-icon">📁</div>
                        <h3>File Transfer</h3>
                        <p>File management features coming soon</p>
                    </div>
                </div>
            </div>

            <!-- Settings View -->
            <div class="view" id="settings-view">
                <div class="view-header">
                    <h2>Settings</h2>
                    <p>Configure your iPhone Companion Pro</p>
                </div>

                <div class="settings-container">
                    <div class="empty-state">
                        <div class="empty-icon">⚙️</div>
                        <h3>Settings</h3>
                        <p>Configuration options coming soon</p>
                    </div>
                </div>
            </div>

            <!-- Logs View -->
            <div class="view" id="logs-view">
                <div class="view-header">
                    <h2>System Logs</h2>
                    <p>Real-time debugging and system monitoring</p>
                </div>

                <div class="logs-container">
                    <!-- Log Controls -->
                    <div class="logs-controls">
                        <div class="log-filters">
                            <button class="filter-btn active" data-level="all" onclick="filterLogs('all')">
                                <span class="filter-dot all"></span>All
                            </button>
                            <button class="filter-btn" data-level="info" onclick="filterLogs('info')">
                                <span class="filter-dot info"></span>Info
                            </button>
                            <button class="filter-btn" data-level="warn" onclick="filterLogs('warn')">
                                <span class="filter-dot warn"></span>Warnings
                            </button>
                            <button class="filter-btn" data-level="error" onclick="filterLogs('error')">
                                <span class="filter-dot error"></span>Errors
                            </button>
                            <button class="filter-btn" data-level="debug" onclick="filterLogs('debug')">
                                <span class="filter-dot debug"></span>Debug
                            </button>
                        </div>
                        
                        <div class="log-actions">
                            <button class="log-action-btn" onclick="clearLogs()" title="Clear Logs">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M3 6h18M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6"/>
                                </svg>
                                Clear
                            </button>
                            <button class="log-action-btn" onclick="exportLogs()" title="Export Logs">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4M7 10l5 5 5-5M12 15V3"/>
                                </svg>
                                Export
                            </button>
                            <button class="log-action-btn" onclick="toggleAutoScroll()" title="Auto Scroll" id="autoscroll-btn">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M12 22l-3-3h6l-3 3zM12 2l3 3H9l3-3z"/>
                                </svg>
                                Auto
                            </button>
                            <button class="log-action-btn" onclick="pauseLogs()" title="Pause/Resume" id="pause-btn">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <rect x="6" y="4" width="4" height="16"/>
                                    <rect x="14" y="4" width="4" height="16"/>
                                </svg>
                                Pause
                            </button>
                        </div>
                    </div>

                    <!-- Search Bar -->
                    <div class="log-search">
                        <input type="text" id="log-search-input" placeholder="Search logs..." oninput="searchLogs()">
                        <div class="search-stats" id="search-stats"></div>
                    </div>

                    <!-- Log Display -->
                    <div class="log-display" id="log-display">
                        <div class="log-entry info" data-timestamp="1753075218052">
                            <div class="log-time">15:26:58.052</div>
                            <div class="log-level info">INFO</div>
                            <div class="log-source">Intel Unison Core</div>
                            <div class="log-message">🔥 Initializing Intel Unison Core...</div>
                        </div>
                        <div class="log-entry success" data-timestamp="1753075218153">
                            <div class="log-time">15:26:58.153</div>
                            <div class="log-level info">INFO</div>
                            <div class="log-source">Database</div>
                            <div class="log-message">✅ Database initialized successfully</div>
                        </div>
                        <div class="log-entry info" data-timestamp="1753075218254">
                            <div class="log-time">15:26:58.254</div>
                            <div class="log-level info">INFO</div>
                            <div class="log-source">Connection Manager</div>
                            <div class="log-message">🔗 Initializing Connection Manager...</div>
                        </div>
                        <div class="log-entry warn" data-timestamp="1753075218355">
                            <div class="log-time">15:26:58.355</div>
                            <div class="log-level warn">WARN</div>
                            <div class="log-source">Bluetooth Discovery</div>
                            <div class="log-message">⚠️ Bluetooth not available in this environment - using network protocols only</div>
                        </div>
                        <div class="log-entry success" data-timestamp="1753075218456">
                            <div class="log-time">15:26:58.456</div>
                            <div class="log-level info">INFO</div>
                            <div class="log-source">UDP Discovery</div>
                            <div class="log-message">✅ UDP discovery listening on port 26817</div>
                        </div>
                    </div>
                </div>

                <!-- Developer Console -->
                <div class="developer-console">
                    <div class="console-header">
                        <h3>Developer Console</h3>
                        <div class="console-controls">
                            <button class="console-btn" onclick="toggleConsole()" id="console-toggle">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M7 8l-4 4 4 4M17 8l4 4-4 4M14 4l-4 16"/>
                                </svg>
                                Console
                            </button>
                            <button class="console-btn" onclick="clearConsole()">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M3 6h18M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6"/>
                                </svg>
                                Clear
                            </button>
                        </div>
                    </div>
                    
                    <div class="console-output" id="console-output">
                        <div class="console-line">
                            <span class="console-prompt">></span>
                            <span class="console-text">Intel Unison++ Developer Console</span>
                        </div>
                        <div class="console-line">
                            <span class="console-prompt">></span>
                            <span class="console-text">Type 'help' for available commands</span>
                        </div>
                    </div>
                    
                    <div class="console-input-container">
                        <span class="console-prompt">></span>
                        <input type="text" class="console-input" id="console-input" placeholder="Enter command..." onkeypress="handleConsoleInput(event)">
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="scripts/app-clean.js"></script>
    <script src="scripts/logs.js"></script>
</body>
</html>
