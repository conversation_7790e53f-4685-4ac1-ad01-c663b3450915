<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Intel Unison++ Messages</title>
    <link rel="stylesheet" href="../styles/intel-unison-messages.css">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="user-profile">
                    <div class="avatar">
                        <i class="icon-user"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-name">iPhone Companion</div>
                        <div class="sync-status" id="syncStatus">Syncing...</div>
                    </div>
                </div>
                <div class="sidebar-actions">
                    <button class="action-btn" id="newMessageBtn" title="New Message">
                        <i class="icon-plus"></i>
                    </button>
                    <button class="action-btn" id="searchBtn" title="Search">
                        <i class="icon-search"></i>
                    </button>
                    <button class="action-btn" id="settingsBtn" title="Settings">
                        <i class="icon-settings"></i>
                    </button>
                </div>
            </div>

            <div class="search-container" id="searchContainer" style="display: none;">
                <div class="search-input-wrapper">
                    <i class="icon-search"></i>
                    <input type="text" id="searchInput" placeholder="Search messages..." autocomplete="off">
                    <button class="search-clear" id="searchClear" style="display: none;">
                        <i class="icon-x"></i>
                    </button>
                </div>
            </div>

            <div class="conversations-container">
                <div class="conversations-header">
                    <span class="conversations-title">Messages</span>
                    <span class="conversations-count" id="conversationsCount">0</span>
                </div>
                <div class="conversations-list" id="conversationsList">
                    <!-- Conversations will be populated here -->
                </div>
            </div>
        </div>

        <!-- Main Chat Area -->
        <div class="main-content">
            <div class="welcome-screen" id="welcomeScreen">
                <div class="welcome-content">
                    <div class="welcome-icon">
                        <i class="icon-message-circle"></i>
                    </div>
                    <h2>Intel Unison++ Messages</h2>
                    <p>Select a conversation to start messaging from your iPhone</p>
                    <div class="feature-highlights">
                        <div class="feature">
                            <i class="icon-smartphone"></i>
                            <span>Real-time iPhone sync</span>
                        </div>
                        <div class="feature">
                            <i class="icon-database"></i>
                            <span>Persistent message storage</span>
                        </div>
                        <div class="feature">
                            <i class="icon-zap"></i>
                            <span>Lightning-fast search</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="chat-container" id="chatContainer" style="display: none;">
                <div class="chat-header">
                    <div class="chat-header-info">
                        <div class="contact-avatar" id="contactAvatar">
                            <i class="icon-user"></i>
                        </div>
                        <div class="contact-details">
                            <div class="contact-name" id="contactName">Select a contact</div>
                            <div class="contact-status" id="contactStatus">
                                <span class="status-indicator"></span>
                                <span class="status-text">iPhone Connected</span>
                            </div>
                        </div>
                    </div>
                    <div class="chat-actions">
                        <button class="action-btn" id="callBtn" title="Call">
                            <i class="icon-phone"></i>
                        </button>
                        <button class="action-btn" id="videoChatBtn" title="Video Call">
                            <i class="icon-video"></i>
                        </button>
                        <button class="action-btn" id="infoBtn" title="Contact Info">
                            <i class="icon-info"></i>
                        </button>
                    </div>
                </div>

                <div class="messages-container" id="messagesContainer">
                    <div class="messages-scroll" id="messagesScroll">
                        <!-- Messages will be populated here -->
                    </div>
                    <div class="typing-indicator" id="typingIndicator" style="display: none;">
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <span class="typing-text">Typing...</span>
                    </div>
                </div>

                <div class="message-input-container">
                    <div class="message-input-wrapper">
                        <button class="attachment-btn" id="attachmentBtn" title="Attach File">
                            <i class="icon-paperclip"></i>
                        </button>
                        <div class="text-input-wrapper">
                            <textarea 
                                id="messageInput" 
                                placeholder="Type a message..." 
                                rows="1"
                                maxlength="1000"
                            ></textarea>
                            <div class="emoji-picker-trigger" id="emojiBtn" title="Emoji">
                                <i class="icon-smile"></i>
                            </div>
                        </div>
                        <button class="send-btn" id="sendBtn" disabled title="Send Message">
                            <i class="icon-send"></i>
                        </button>
                    </div>
                    <div class="message-actions" id="messageActions" style="display: none;">
                        <button class="action-btn quick-reply" data-reply="👍">👍</button>
                        <button class="action-btn quick-reply" data-reply="👎">👎</button>
                        <button class="action-btn quick-reply" data-reply="❤️">❤️</button>
                        <button class="action-btn quick-reply" data-reply="😂">😂</button>
                        <button class="action-btn quick-reply" data-reply="😮">😮</button>
                        <button class="action-btn quick-reply" data-reply="😢">😢</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Context Menu -->
    <div class="context-menu" id="contextMenu" style="display: none;">
        <div class="context-menu-item" data-action="copy">
            <i class="icon-copy"></i>
            <span>Copy</span>
        </div>
        <div class="context-menu-item" data-action="reply">
            <i class="icon-corner-up-left"></i>
            <span>Reply</span>
        </div>
        <div class="context-menu-item" data-action="forward">
            <i class="icon-share"></i>
            <span>Forward</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="delete" style="color: var(--danger-color);">
            <i class="icon-trash"></i>
            <span>Delete</span>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">Syncing messages...</div>
        </div>
    </div>

    <!-- Notification Toast -->
    <div class="notification-toast" id="notificationToast" style="display: none;">
        <div class="toast-content">
            <i class="toast-icon"></i>
            <div class="toast-message"></div>
        </div>
        <button class="toast-close">
            <i class="icon-x"></i>
        </button>
    </div>

    <!-- Scripts -->
    <script src="../js/intel-unison-messages.js"></script>
</body>
</html>