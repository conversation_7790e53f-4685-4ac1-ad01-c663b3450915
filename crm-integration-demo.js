const http = require('http');

// Simple HTTP client for testing
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 7777,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        try {
          resolve(JSON.parse(responseData));
        } catch (error) {
          resolve(responseData);
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function demonstrateCRMIntegration() {
  console.log('🚀 iPhone Companion Pro - CRM Integration Demo');
  console.log('================================================\n');

  try {
    // 1. Check system health
    console.log('1️⃣ Checking system health...');
    const health = await makeRequest('GET', '/api/crm/health');
    console.log('   Status:', health.status);
    console.log('   Integrations:', health.integrations);
    console.log('   ✅ System is healthy\n');

    // 2. Get integration status
    console.log('2️⃣ Getting integration status...');
    const status = await makeRequest('GET', '/api/crm/status');
    console.log('   Phone Link:', status.status.phoneLink ? '✅ Connected' : '❌ Disconnected');
    console.log('   Companion App:', status.status.companionApp ? '✅ Connected' : '❌ Disconnected');
    console.log('   Connected Clients:', status.status.connectedClients);
    console.log('');

    // 3. Get all contacts
    console.log('3️⃣ Retrieving contacts...');
    const contacts = await makeRequest('GET', '/api/crm/contacts');
    console.log(`   Found ${contacts.count} contacts`);
    if (contacts.contacts.length > 0) {
      contacts.contacts.slice(0, 3).forEach(contact => {
        console.log(`   📞 ${contact.name} - ${contact.phoneNumber}`);
      });
    } else {
      console.log('   📞 No contacts found (Phone Link not connected or no data)');
    }
    console.log('');

    // 4. Get all messages
    console.log('4️⃣ Retrieving messages...');
    const messages = await makeRequest('GET', '/api/crm/messages');
    console.log(`   Found ${messages.count} messages in local database`);
    console.log('');

    // 5. Get conversation threads
    console.log('5️⃣ Getting conversation threads...');
    const conversations = await makeRequest('GET', '/api/crm/conversations');
    console.log(`   Found ${conversations.count} conversation threads`);
    console.log('');

    // 6. Demo: Send a test message (commented out for safety)
    console.log('6️⃣ Message sending capability...');
    console.log('   📱 Ready to send messages via iPhone');
    console.log('   📝 Example API call:');
    console.log('   POST /api/crm/send');
    console.log('   Body: { "phone": "+**********", "text": "Hello from CRM!", "campaignId": "demo" }');
    console.log('   ⚠️  Uncomment the code below to actually send a test message');
    console.log('');

    /*
    // Uncomment this section to actually send a test message
    console.log('📤 Sending test message...');
    const sendResult = await makeRequest('POST', '/api/crm/send', {
      phone: '+**********',
      text: 'Test message from CRM integration demo',
      campaignId: 'demo-campaign'
    });
    console.log('   Send result:', sendResult);
    */

    // 7. Demo: Get messages for specific contact
    console.log('7️⃣ Contact-specific message retrieval...');
    console.log('   📱 Ready to get message history for any contact');
    console.log('   📝 Example API call:');
    console.log('   GET /api/crm/messages/+**********');
    console.log('');

    console.log('🎉 CRM Integration Demo Complete!');
    console.log('\n📋 Summary of what your CRM can do:');
    console.log('   ✅ Check system health and status');
    console.log('   ✅ Get all contacts from iPhone');
    console.log('   ✅ Retrieve all message history');
    console.log('   ✅ Get conversation threads');
    console.log('   ✅ Send messages via iPhone');
    console.log('   ✅ Get message history for specific contacts');
    console.log('   ✅ Track campaign IDs for sent messages');
    console.log('   ✅ Real-time data persistence (Intel Unison style)');

    console.log('\n🔗 API Endpoints for your CRM:');
    console.log('   GET  http://localhost:7777/api/crm/health');
    console.log('   GET  http://localhost:7777/api/crm/status');
    console.log('   GET  http://localhost:7777/api/crm/contacts');
    console.log('   GET  http://localhost:7777/api/crm/messages');
    console.log('   GET  http://localhost:7777/api/crm/conversations');
    console.log('   GET  http://localhost:7777/api/crm/messages/:phone');
    console.log('   POST http://localhost:7777/api/crm/send');

    console.log('\n💡 Next Steps:');
    console.log('   1. Connect your iPhone via Phone Link or companion app');
    console.log('   2. Your CRM can start making API calls immediately');
    console.log('   3. All messages are stored locally like Intel Unison');
    console.log('   4. Data persists even after app restarts');

  } catch (error) {
    console.error('❌ Demo failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Make sure iPhone Companion Pro is running (npm start)');
    console.log('   2. Check if port 7777 is available');
    console.log('   3. Verify the CRM API started successfully');
  }
}

// Run the demo
demonstrateCRMIntegration();
