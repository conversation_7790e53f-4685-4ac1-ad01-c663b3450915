// Integration Script for iPhone Companion Pro - All Connection Methods
const fs = require('fs');
const path = require('path');

class ConnectionIntegrator {
  constructor() {
    this.config = this.loadConnectionConfig();
    this.mainAppPath = './src/main/services/MasterConnectionManager.js';
    this.rendererPath = './src/renderer/components/ConnectionStatus.js';
  }

  loadConnectionConfig() {
    try {
      const configPath = './connection-config.json';
      if (fs.existsSync(configPath)) {
        return JSON.parse(fs.readFileSync(configPath, 'utf8'));
      }
    } catch (error) {
      console.log('⚠️  No connection config found, using defaults');
    }
    
    return {
      methods: { usb: false, phoneLink: false, airplay: false, macosVM: false, bluetooth: false },
      errors: []
    };
  }

  async integrateAll() {
    console.log('🔧 iPhone Companion Pro - CONNECTION INTEGRATION\n');
    
    // Update main connection manager
    await this.updateMasterConnectionManager();
    
    // Update renderer components
    await this.updateConnectionStatus();
    
    // Create connection service files
    await this.createConnectionServices();
    
    // Update package.json with new dependencies
    await this.updatePackageJson();
    
    console.log('\n✅ Integration completed successfully!');
    console.log('📝 Next steps:');
    console.log('   1. Restart the application');
    console.log('   2. Test each connection method');
    console.log('   3. Check the connection dashboard');
  }

  async updateMasterConnectionManager() {
    console.log('📱 Updating MasterConnectionManager...');
    
    const template = `// Master Connection Manager - Updated with all iPhone connection methods
const EventEmitter = require('events');
const USBConnectionService = require('./USBConnectionService');
const PhoneLinkService = require('./PhoneLinkService');
const AirPlayService = require('./AirPlayService');
const MacOSVMService = require('./MacOSVMService');
const BluetoothService = require('./BluetoothService');

class MasterConnectionManager extends EventEmitter {
  constructor() {
    super();
    this.connections = new Map();
    this.activeConnections = new Set();
    this.config = ${JSON.stringify(this.config, null, 4)};
    
    this.initializeServices();
  }

  initializeServices() {
    // Initialize all available connection services
    if (this.config.methods.usb) {
      this.connections.set('usb', new USBConnectionService());
    }
    
    if (this.config.methods.phoneLink) {
      this.connections.set('phoneLink', new PhoneLinkService());
    }
    
    if (this.config.methods.airplay) {
      this.connections.set('airplay', new AirPlayService());
    }
    
    if (this.config.methods.macosVM) {
      this.connections.set('macosVM', new MacOSVMService());
    }
    
    if (this.config.methods.bluetooth) {
      this.connections.set('bluetooth', new BluetoothService());
    }

    // Set up event listeners
    this.connections.forEach((service, type) => {
      service.on('connected', () => {
        this.activeConnections.add(type);
        this.emit('connectionChanged', { type, status: 'connected' });
      });
      
      service.on('disconnected', () => {
        this.activeConnections.delete(type);
        this.emit('connectionChanged', { type, status: 'disconnected' });
      });
      
      service.on('data', (data) => {
        this.emit('data', { source: type, data });
      });
    });
  }

  async startAllConnections() {
    console.log('🚀 Starting all available connections...');
    
    const promises = Array.from(this.connections.entries()).map(async ([type, service]) => {
      try {
        await service.connect();
        console.log(\`✅ \${type} connection started\`);
        return { type, success: true };
      } catch (error) {
        console.log(\`❌ \${type} connection failed: \${error.message}\`);
        return { type, success: false, error: error.message };
      }
    });

    const results = await Promise.allSettled(promises);
    return results.map(result => result.value || result.reason);
  }

  async stopAllConnections() {
    const promises = Array.from(this.connections.values()).map(service => 
      service.disconnect().catch(err => console.log('Disconnect error:', err))
    );
    
    await Promise.allSettled(promises);
    this.activeConnections.clear();
  }

  getConnectionStatus() {
    const status = {};
    this.connections.forEach((service, type) => {
      status[type] = {
        available: true,
        connected: this.activeConnections.has(type),
        lastSeen: service.lastSeen || null
      };
    });
    return status;
  }

  async sendMessage(message, preferredMethod = null) {
    if (preferredMethod && this.activeConnections.has(preferredMethod)) {
      const service = this.connections.get(preferredMethod);
      return await service.sendMessage(message);
    }

    // Try all active connections in priority order
    const priorityOrder = ['usb', 'phoneLink', 'airplay', 'macosVM', 'bluetooth'];
    
    for (const method of priorityOrder) {
      if (this.activeConnections.has(method)) {
        try {
          const service = this.connections.get(method);
          return await service.sendMessage(message);
        } catch (error) {
          console.log(\`Failed to send via \${method}: \${error.message}\`);
        }
      }
    }
    
    throw new Error('No active connections available');
  }
}

module.exports = MasterConnectionManager;`;

    // Ensure directory exists
    const dir = path.dirname(this.mainAppPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(this.mainAppPath, template);
    console.log('✅ MasterConnectionManager updated');
  }

  async updateConnectionStatus() {
    console.log('🖥️  Updating ConnectionStatus component...');
    
    const template = `// Connection Status Component - Updated with all methods
import React, { useState, useEffect } from 'react';
import './ConnectionStatus.css';

const ConnectionStatus = () => {
  const [connections, setConnections] = useState({
    usb: { connected: false, available: ${this.config.methods.usb} },
    phoneLink: { connected: false, available: ${this.config.methods.phoneLink} },
    airplay: { connected: false, available: ${this.config.methods.airplay} },
    macosVM: { connected: false, available: ${this.config.methods.macosVM} },
    bluetooth: { connected: false, available: ${this.config.methods.bluetooth} }
  });

  useEffect(() => {
    // Listen for connection status updates
    window.electronAPI.onConnectionChange((event, data) => {
      setConnections(prev => ({
        ...prev,
        [data.type]: {
          ...prev[data.type],
          connected: data.status === 'connected'
        }
      }));
    });

    // Request initial status
    window.electronAPI.getConnectionStatus().then(status => {
      setConnections(prev => {
        const updated = { ...prev };
        Object.keys(status).forEach(type => {
          if (updated[type]) {
            updated[type] = { ...updated[type], ...status[type] };
          }
        });
        return updated;
      });
    });
  }, []);

  const getStatusIcon = (connection) => {
    if (!connection.available) return '⚫';
    return connection.connected ? '🟢' : '🔴';
  };

  const getStatusText = (connection) => {
    if (!connection.available) return 'Not Available';
    return connection.connected ? 'Connected' : 'Disconnected';
  };

  return (
    <div className="connection-status">
      <h3>📱 iPhone Connections</h3>
      <div className="connection-grid">
        <div className="connection-item">
          <span className="icon">{getStatusIcon(connections.usb)}</span>
          <span className="label">USB (iTunes)</span>
          <span className="status">{getStatusText(connections.usb)}</span>
        </div>
        
        <div className="connection-item">
          <span className="icon">{getStatusIcon(connections.phoneLink)}</span>
          <span className="label">Phone Link</span>
          <span className="status">{getStatusText(connections.phoneLink)}</span>
        </div>
        
        <div className="connection-item">
          <span className="icon">{getStatusIcon(connections.airplay)}</span>
          <span className="label">AirPlay</span>
          <span className="status">{getStatusText(connections.airplay)}</span>
        </div>
        
        <div className="connection-item">
          <span className="icon">{getStatusIcon(connections.macosVM)}</span>
          <span className="label">macOS VM</span>
          <span className="status">{getStatusText(connections.macosVM)}</span>
        </div>
        
        <div className="connection-item">
          <span className="icon">{getStatusIcon(connections.bluetooth)}</span>
          <span className="label">Bluetooth</span>
          <span className="status">{getStatusText(connections.bluetooth)}</span>
        </div>
      </div>
    </div>
  );
};

export default ConnectionStatus;`;

    // Ensure directory exists
    const dir = path.dirname(this.rendererPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(this.rendererPath, template);
    console.log('✅ ConnectionStatus component updated');
  }

  async createConnectionServices() {
    console.log('🔧 Creating connection service files...');
    
    const services = [
      'USBConnectionService',
      'PhoneLinkService', 
      'AirPlayService',
      'MacOSVMService',
      'BluetoothService'
    ];

    const serviceDir = './src/main/services';
    if (!fs.existsSync(serviceDir)) {
      fs.mkdirSync(serviceDir, { recursive: true });
    }

    services.forEach(serviceName => {
      const template = this.generateServiceTemplate(serviceName);
      const filePath = path.join(serviceDir, `${serviceName}.js`);
      fs.writeFileSync(filePath, template);
      console.log(`✅ ${serviceName} created`);
    });
  }

  generateServiceTemplate(serviceName) {
    const type = serviceName.replace('Service', '').replace('Connection', '').toLowerCase();
    
    return `// ${serviceName} - iPhone connection via ${type}
const EventEmitter = require('events');

class ${serviceName} extends EventEmitter {
  constructor() {
    super();
    this.connected = false;
    this.lastSeen = null;
  }

  async connect() {
    console.log('🔌 Connecting via ${type}...');
    
    try {
      // TODO: Implement actual ${type} connection logic
      await this.establishConnection();
      
      this.connected = true;
      this.lastSeen = new Date();
      this.emit('connected');
      
      console.log('✅ ${type} connection established');
    } catch (error) {
      console.log('❌ ${type} connection failed:', error.message);
      throw error;
    }
  }

  async disconnect() {
    if (this.connected) {
      // TODO: Implement disconnection logic
      this.connected = false;
      this.emit('disconnected');
      console.log('🔌 ${type} disconnected');
    }
  }

  async establishConnection() {
    // TODO: Implement specific connection logic for ${type}
    // This is a placeholder that should be replaced with actual implementation
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Simulate connection attempt
        if (Math.random() > 0.3) {
          resolve();
        } else {
          reject(new Error('${type} connection failed'));
        }
      }, 1000);
    });
  }

  async sendMessage(message) {
    if (!this.connected) {
      throw new Error('${type} not connected');
    }

    // TODO: Implement message sending logic
    console.log(\`📤 Sending message via ${type}:\`, message);
    
    // Simulate message sending
    return { success: true, method: '${type}' };
  }

  isConnected() {
    return this.connected;
  }
}

module.exports = ${serviceName};`;
  }

  async updatePackageJson() {
    console.log('📦 Updating package.json dependencies...');
    
    try {
      const packagePath = './package.json';
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      
      // Add required dependencies
      const newDependencies = {
        'bonjour-service': '^1.0.14',
        'ws': '^8.14.2',
        '@abandonware/noble': '^1.9.2-15'
      };

      packageJson.dependencies = { ...packageJson.dependencies, ...newDependencies };
      
      fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
      console.log('✅ package.json updated with new dependencies');
    } catch (error) {
      console.log('⚠️  Could not update package.json:', error.message);
    }
  }
}

// Run integration if called directly
if (require.main === module) {
  const integrator = new ConnectionIntegrator();
  integrator.integrateAll().catch(console.error);
}

module.exports = ConnectionIntegrator;
