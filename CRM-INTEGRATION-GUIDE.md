# iPhone Companion Pro - CRM Integration Guide

## 🎉 Intel Unison-Style Integration Complete!

Your iPhone Companion Pro now works exactly like Intel Unison with persistent local storage and a full CRM API. Here's everything your CRM needs to know:

## 🚀 Quick Start

1. **Start iPhone Companion Pro**: `npm start`
2. **CRM API runs on**: `http://localhost:7777`
3. **Test the integration**: `node crm-integration-demo.js`

## 📊 CRM API Endpoints

### Health & Status
```bash
GET /api/crm/health          # System health check
GET /api/crm/status          # Integration status
```

### Contacts
```bash
GET /api/crm/contacts        # Get all contacts from iPhone
```

### Messages
```bash
GET /api/crm/messages                # Get all messages
GET /api/crm/messages/:phone         # Get messages for specific contact
GET /api/crm/conversations           # Get conversation threads
POST /api/crm/send                   # Send message via iPhone
```

### Webhooks
```bash
POST /api/crm/webhook        # Register your CRM webhook
```

## 📱 Sending Messages

**Endpoint**: `POST /api/crm/send`

**Request Body**:
```json
{
  "phone": "+**********",
  "text": "Hello from your CRM!",
  "campaignId": "summer-campaign-2024"
}
```

**Response**:
```json
{
  "success": true,
  "phone": "+**********",
  "campaignId": "summer-campaign-2024",
  "timestamp": 1752391588942
}
```

## 📞 Getting Contact Messages

**Endpoint**: `GET /api/crm/messages/+**********`

**Response**:
```json
{
  "success": true,
  "phone": "+**********",
  "messages": [
    {
      "id": 1,
      "phone_number": "+**********",
      "contact_name": "John Doe",
      "message_text": "Hello!",
      "timestamp": 1752391588942,
      "is_outgoing": 0,
      "is_read": 1
    }
  ],
  "count": 1
}
```

## 🔄 Real-Time Data Persistence

### Intel Unison-Style Features:
- ✅ **Local SQLite Database**: All messages stored locally
- ✅ **Never Lose Data**: Messages persist even after restart
- ✅ **Real-Time Sync**: Monitors Phone Link for changes
- ✅ **CRM-Friendly Format**: All data formatted for easy CRM integration

### Database Location:
- **Windows**: `%APPDATA%\iPhone-Companion-Pro\unison-data.db`
- **Tables**: `messages`, `message_threads`, `contacts`

## 🔗 Integration Examples

### JavaScript/Node.js
```javascript
const axios = require('axios');

// Send message
const response = await axios.post('http://localhost:7777/api/crm/send', {
  phone: '+**********',
  text: 'Hello from CRM!',
  campaignId: 'my-campaign'
});

// Get contacts
const contacts = await axios.get('http://localhost:7777/api/crm/contacts');
```

### Python
```python
import requests

# Send message
response = requests.post('http://localhost:7777/api/crm/send', json={
    'phone': '+**********',
    'text': 'Hello from CRM!',
    'campaignId': 'my-campaign'
})

# Get contacts
contacts = requests.get('http://localhost:7777/api/crm/contacts')
```

### cURL
```bash
# Send message
curl -X POST http://localhost:7777/api/crm/send \
  -H "Content-Type: application/json" \
  -d '{"phone":"+**********","text":"Hello!","campaignId":"test"}'

# Get contacts
curl http://localhost:7777/api/crm/contacts
```

## 🔧 Configuration

### Port Configuration
The CRM API runs on port 7777 by default. To change:

1. Edit `src/main/services/CRMBridge.js`
2. Change the port in `crmBridge.start(7777)` in `main.js`

### Database Path
The local database is stored in:
- `%APPDATA%\iPhone-Companion-Pro\unison-data.db`

## 🛠️ Troubleshooting

### API Not Responding
1. Check if iPhone Companion Pro is running: `npm start`
2. Verify port 7777 is available
3. Check console for error messages

### No Contacts/Messages
1. Ensure Phone Link is installed and connected
2. Check Phone Link database path in `PhoneLinkBridge.js`
3. Try connecting iPhone via companion app

### Database Issues
1. Check if `%APPDATA%\iPhone-Companion-Pro\` directory exists
2. Verify SQLite database permissions
3. Check console for database initialization errors

## 📈 Monitoring & Logging

### Health Check
```bash
curl http://localhost:7777/api/crm/health
```

### Integration Status
```bash
curl http://localhost:7777/api/crm/status
```

### Console Logs
- ✅ Database initialization
- 📱 Phone Link connection status
- 📊 Message sync statistics
- 🚀 CRM API startup

## 🎯 Best Practices for Your CRM

1. **Health Checks**: Monitor `/api/crm/health` regularly
2. **Error Handling**: Always check `success` field in responses
3. **Rate Limiting**: Be respectful with API calls
4. **Campaign Tracking**: Use meaningful `campaignId` values
5. **Data Backup**: The local database is your source of truth

## 🔮 Future Enhancements

When you're ready, we can add:
- Webhook notifications for new messages
- Message read receipts
- Attachment support
- Group message handling
- Advanced filtering and search

## 🎉 You're All Set!

Your iPhone Companion Pro now works exactly like Intel Unison:
- ✅ Persistent local storage
- ✅ Never loses messages
- ✅ Full CRM API
- ✅ Real-time sync
- ✅ Professional-grade reliability

Your CRM can start integrating immediately using the API endpoints above!
