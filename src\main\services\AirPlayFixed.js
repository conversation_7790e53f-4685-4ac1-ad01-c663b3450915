const http = require('http');
const { EventEmitter } = require('events');

// Try to load bonjour, fallback to mock if not available
let bonjour;
try {
  bonjour = require('bonjour-service')();
} catch (error) {
  console.log('⚠️ Bonjour service not available, using mock');
  bonjour = {
    publish: () => ({ destroy: () => {} })
  };
}

class AirPlayFixed extends EventEmitter {
  constructor() {
    super();
    this.server = null;
    this.advertisement = null;
  }

  start() {
    console.log('🚀 Starting AirPlay server...');
    
    // Create HTTP server for AirPlay protocol
    this.server = http.createServer((req, res) => {
      console.log('📡 AirPlay request:', req.method, req.url);
      
      if (req.url === '/server-info') {
        res.writeHead(200, { 
          'Content-Type': 'text/x-apple-plist+xml',
          'Access-Control-Allow-Origin': '*'
        });
        res.end(`<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>deviceid</key>
  <string>AA:BB:CC:DD:EE:FF</string>
  <key>features</key>
  <integer>0x5A7FFEE6</integer>
  <key>model</key>
  <string>AppleTV3,1</string>
  <key>protovers</key>
  <string>1.0</string>
  <key>srcvers</key>
  <string>220.68</string>
  <key>vv</key>
  <integer>2</integer>
</dict>
</plist>`);
      } else if (req.url === '/photo') {
        // Handle photo streaming
        this.emit('photo', req);
        res.writeHead(200);
        res.end();
      } else if (req.url === '/play') {
        // Handle video streaming
        this.emit('video', req);
        res.writeHead(200);
        res.end();
      }
    });
    
    // Try different ports if 7000 is in use
    const tryPort = (port) => {
      this.server.listen(port, '0.0.0.0', () => {
        console.log(`✅ AirPlay HTTP server running on port ${port}`);

        // Advertise service with Bonjour
        this.advertisement = bonjour.publish({
          name: 'iPhone Companion Pro',
          type: 'airplay',
          port: port,
          txt: {
            deviceid: 'AA:BB:CC:DD:EE:FF',
            features: '0x5A7FFEE6',
            model: 'AppleTV3,1',
            srcvers: '220.68',
            vv: '2'
          }
        });

        console.log('📡 AirPlay service advertised');
        this.emit('ready');
      }).on('error', (err) => {
        if (err.code === 'EADDRINUSE' && port < 7010) {
          console.log(`⚠️ Port ${port} in use, trying ${port + 1}...`);
          tryPort(port + 1);
        } else {
          console.error('❌ AirPlay server error:', err.message);
          this.emit('error', err);
        }
      });
    };

    tryPort(7000);
  }

  stop() {
    if (this.server) {
      this.server.close();
    }
    if (this.advertisement) {
      this.advertisement.destroy();
    }
  }
}

module.exports = { AirPlayFixed };