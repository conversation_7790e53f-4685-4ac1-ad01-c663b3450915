const EventEmitter = require('events');
const { exec } = require('child_process');
const { promisify } = require('util');
const path = require('path');
const fs = require('fs');

const execAsync = promisify(exec);

class FocusedConnectionManager extends EventEmitter {
  constructor() {
    super();
    this.connected = false;
    this.connectionType = null;
    this.deviceInfo = null;
    this.connectionMethods = new Map();
    this.activeConnections = new Set();
    this.connectionAttempts = 0;
    this.maxRetries = 3;
    
    // Initialize connection methods
    this.initializeConnectionMethods();
  }

  initializeConnectionMethods() {
    console.log('🔧 Initializing Real iPhone Connection Methods...');
    
    // Priority order: AirPlay (most visual) -> Phone Link -> USB -> macOS VM
    this.connectionMethods.set('airplay', {
      name: 'AirPlay Screen Mirroring',
      priority: 1,
      available: false,
      service: null,
      description: 'Real-time iPhone screen mirroring',
      requirements: ['iPhone on same WiFi network', 'AirPlay enabled on iPhone']
    });
    
    this.connectionMethods.set('phonelink', {
      name: 'Windows Phone Link',
      priority: 2,
      available: false,
      service: null,
      description: 'Microsoft Phone Link integration',
      requirements: ['Phone Link app installed', 'iPhone paired with Windows']
    });
    
    this.connectionMethods.set('usb', {
      name: 'USB Connection',
      priority: 3,
      available: false,
      service: null,
      description: 'Direct USB connection via iTunes',
      requirements: ['iPhone connected via USB', 'iTunes or Apple Mobile Device Support installed', 'iPhone unlocked and trusted']
    });
    
    this.connectionMethods.set('macosvm', {
      name: 'macOS VM Bridge',
      priority: 4,
      available: false,
      service: null,
      description: 'Messages.app access via macOS virtualization',
      requirements: ['macOS VM running', 'iPhone signed into same Apple ID', 'Messages.app configured']
    });
  }

  async connect() {
    console.log('🚀 Starting Real iPhone Connection Process...');
    this.emit('status', 'Scanning for iPhone connection methods...');
    
    try {
      // Check availability of all methods
      await this.checkAllMethods();
      
      // Get available methods sorted by priority
      const availableMethods = Array.from(this.connectionMethods.entries())
        .filter(([_, method]) => method.available)
        .sort((a, b) => a[1].priority - b[1].priority);
      
      if (availableMethods.length === 0) {
        throw new Error('No iPhone connection methods available. Please check requirements.');
      }
      
      // Try each method in priority order
      for (const [methodName, method] of availableMethods) {
        this.emit('status', `Attempting ${method.name}...`);
        
        try {
          const result = await this.attemptConnection(methodName);
          if (result.success) {
            this.connected = true;
            this.connectionType = methodName;
            this.deviceInfo = result.deviceInfo;
            
            this.emit('connected', {
              method: methodName,
              methodName: method.name,
              deviceInfo: result.deviceInfo
            });
            
            console.log(`✅ Connected via ${method.name}`);
            return { success: true, method: methodName, deviceInfo: result.deviceInfo };
          }
        } catch (error) {
          console.log(`❌ ${method.name} failed: ${error.message}`);
          this.emit('method-failed', { method: methodName, error: error.message });
        }
      }
      
      throw new Error('All connection methods failed');
      
    } catch (error) {
      this.emit('error', error.message);
      return { success: false, error: error.message };
    }
  }

  async checkAllMethods() {
    console.log('🔍 Checking availability of connection methods...');
    
    // Check AirPlay availability
    await this.checkAirPlayAvailability();
    
    // Check Phone Link availability
    await this.checkPhoneLinkAvailability();
    
    // Check USB availability
    await this.checkUSBAvailability();
    
    // Check macOS VM availability
    await this.checkMacOSVMAvailability();
  }

  async checkAirPlayAvailability() {
    try {
      // Check if AirPlay receiver is available on network
      const result = await execAsync('netsh wlan show profiles');
      const hasWiFi = result.stdout.includes('Profile');
      
      if (hasWiFi) {
        // Check for AirPlay devices on network
        const airplayCheck = await this.scanForAirPlayDevices();
        this.connectionMethods.get('airplay').available = airplayCheck.found;
        
        if (airplayCheck.found) {
          console.log('✅ AirPlay devices detected on network');
        }
      }
    } catch (error) {
      console.log('❌ AirPlay check failed:', error.message);
    }
  }

  async scanForAirPlayDevices() {
    try {
      // Use DNS-SD to scan for AirPlay devices
      const result = await execAsync('dns-sd -B _airplay._tcp local.', { timeout: 5000 });
      return { found: result.stdout.includes('_airplay._tcp') };
    } catch (error) {
      // Fallback: assume AirPlay is available if on WiFi
      return { found: true };
    }
  }

  async checkPhoneLinkAvailability() {
    try {
      // Check if Phone Link is installed
      const phoneLinkPath = path.join(process.env.LOCALAPPDATA, 'Microsoft', 'WindowsApps');
      const phoneLinkExists = fs.existsSync(phoneLinkPath);
      
      if (phoneLinkExists) {
        // Check if Phone Link service is running
        const result = await execAsync('tasklist /FI "IMAGENAME eq PhoneExperienceHost.exe"');
        const isRunning = result.stdout.includes('PhoneExperienceHost.exe');
        
        this.connectionMethods.get('phonelink').available = isRunning;
        
        if (isRunning) {
          console.log('✅ Phone Link service detected');
        }
      }
    } catch (error) {
      console.log('❌ Phone Link check failed:', error.message);
    }
  }

  async checkUSBAvailability() {
    try {
      // Check for iTunes or Apple Mobile Device Support
      const itunesCheck = await this.checkForItunes();
      
      if (itunesCheck.found) {
        // Check for connected iPhone
        const deviceCheck = await this.checkForUSBiPhone();
        this.connectionMethods.get('usb').available = deviceCheck.found;
        
        if (deviceCheck.found) {
          console.log('✅ iPhone detected via USB');
        }
      }
    } catch (error) {
      console.log('❌ USB check failed:', error.message);
    }
  }

  async checkForItunes() {
    try {
      // Check for iTunes installation
      const result = await execAsync('reg query "HKLM\\SOFTWARE\\Apple Inc.\\iTunes" /v Version');
      return { found: true, version: result.stdout };
    } catch (error) {
      // Check for Apple Mobile Device Support
      try {
        const amdsResult = await execAsync('sc query "Apple Mobile Device Service"');
        return { found: amdsResult.stdout.includes('RUNNING') };
      } catch (amdsError) {
        return { found: false };
      }
    }
  }

  async checkForUSBiPhone() {
    try {
      // Use Windows Device Manager to check for iPhone
      const result = await execAsync('wmic path Win32_PnPEntity where "Name like \'%iPhone%\'" get Name');
      return { found: result.stdout.includes('iPhone') };
    } catch (error) {
      return { found: false };
    }
  }

  async checkMacOSVMAvailability() {
    try {
      // Check for running macOS VM (VMware or VirtualBox)
      const vmwareCheck = await execAsync('tasklist /FI "IMAGENAME eq vmware.exe"');
      const vboxCheck = await execAsync('tasklist /FI "IMAGENAME eq VirtualBox.exe"');
      
      const hasVM = vmwareCheck.stdout.includes('vmware.exe') || vboxCheck.stdout.includes('VirtualBox.exe');
      
      if (hasVM) {
        // Check if macOS VM is accessible
        const macosCheck = await this.checkMacOSVMConnection();
        this.connectionMethods.get('macosvm').available = macosCheck.accessible;
        
        if (macosCheck.accessible) {
          console.log('✅ macOS VM bridge available');
        }
      }
    } catch (error) {
      console.log('❌ macOS VM check failed:', error.message);
    }
  }

  async checkMacOSVMConnection() {
    try {
      // Try to connect to macOS VM bridge service
      const net = require('net');
      
      return new Promise((resolve) => {
        const socket = net.createConnection({ host: '127.0.0.1', port: 8080 }, () => {
          socket.end();
          resolve({ accessible: true });
        });
        
        socket.on('error', () => {
          resolve({ accessible: false });
        });
        
        setTimeout(() => {
          socket.destroy();
          resolve({ accessible: false });
        }, 3000);
      });
    } catch (error) {
      return { accessible: false };
    }
  }

  async attemptConnection(methodName) {
    switch (methodName) {
      case 'airplay':
        return await this.connectViaAirPlay();
      case 'phonelink':
        return await this.connectViaPhoneLink();
      case 'usb':
        return await this.connectViaUSB();
      case 'macosvm':
        return await this.connectViaMacOSVM();
      default:
        throw new Error(`Unknown connection method: ${methodName}`);
    }
  }

  async connectViaAirPlay() {
    // Implementation for AirPlay connection
    this.emit('status', 'Establishing AirPlay connection...');

    // Start AirPlay receiver service
    const AirPlayService = require('./AirPlayService');
    const airplayService = new AirPlayService();

    return new Promise((resolve, reject) => {
      airplayService.on('device-connected', (deviceInfo) => {
        resolve({
          success: true,
          deviceInfo: {
            name: deviceInfo.name || 'iPhone',
            model: deviceInfo.model || 'iPhone',
            connection: 'AirPlay',
            capabilities: ['screen-mirroring', 'audio', 'video']
          }
        });
      });

      airplayService.on('error', (error) => {
        reject(new Error(`AirPlay connection failed: ${error.message}`));
      });

      airplayService.start().catch(reject);

      // Timeout after 30 seconds
      setTimeout(() => {
        reject(new Error('AirPlay connection timeout'));
      }, 30000);
    });
  }

  async connectViaPhoneLink() {
    this.emit('status', 'Connecting via Windows Phone Link...');

    try {
      // Launch Phone Link and attempt connection
      await execAsync('start ms-yourphone:');

      // Wait for Phone Link to establish connection
      await new Promise(resolve => setTimeout(resolve, 5000));

      // Check if connection was established
      const result = await execAsync('tasklist /FI "IMAGENAME eq PhoneExperienceHost.exe"');

      if (result.stdout.includes('PhoneExperienceHost.exe')) {
        return {
          success: true,
          deviceInfo: {
            name: 'iPhone via Phone Link',
            model: 'iPhone',
            connection: 'Phone Link',
            capabilities: ['notifications', 'messages', 'calls']
          }
        };
      } else {
        throw new Error('Phone Link connection not established');
      }
    } catch (error) {
      throw new Error(`Phone Link connection failed: ${error.message}`);
    }
  }

  async connectViaUSB() {
    this.emit('status', 'Connecting via USB...');

    try {
      // Check for connected iPhone
      const deviceResult = await execAsync('wmic path Win32_PnPEntity where "Name like \'%iPhone%\'" get Name,DeviceID');

      if (!deviceResult.stdout.includes('iPhone')) {
        throw new Error('No iPhone detected via USB');
      }

      // Try to get device info using iTunes/Apple Mobile Device Support
      const deviceInfo = await this.getUSBDeviceInfo();

      return {
        success: true,
        deviceInfo: {
          name: deviceInfo.name || 'iPhone',
          model: deviceInfo.model || 'iPhone',
          connection: 'USB',
          capabilities: ['file-transfer', 'backup', 'sync']
        }
      };
    } catch (error) {
      throw new Error(`USB connection failed: ${error.message}`);
    }
  }

  async getUSBDeviceInfo() {
    try {
      // Try to get device info from Windows registry
      const result = await execAsync('reg query "HKLM\\SYSTEM\\CurrentControlSet\\Enum\\USB" /s /f "iPhone"');

      return {
        name: 'iPhone',
        model: 'iPhone',
        serialNumber: 'Unknown'
      };
    } catch (error) {
      return {
        name: 'iPhone',
        model: 'iPhone'
      };
    }
  }

  async connectViaMacOSVM() {
    this.emit('status', 'Connecting via macOS VM Bridge...');

    try {
      // Connect to macOS VM bridge service
      const net = require('net');

      return new Promise((resolve, reject) => {
        const socket = net.createConnection({ host: '127.0.0.1', port: 8080 }, () => {
          // Send handshake
          socket.write(JSON.stringify({
            type: 'handshake',
            client: 'iPhone Companion Pro'
          }));
        });

        socket.on('data', (data) => {
          try {
            const message = JSON.parse(data.toString());

            if (message.type === 'handshake-response' && message.success) {
              socket.end();
              resolve({
                success: true,
                deviceInfo: {
                  name: message.deviceInfo?.name || 'iPhone via macOS',
                  model: message.deviceInfo?.model || 'iPhone',
                  connection: 'macOS VM Bridge',
                  capabilities: ['messages', 'notifications', 'contacts']
                }
              });
            }
          } catch (parseError) {
            reject(new Error('Invalid response from macOS VM'));
          }
        });

        socket.on('error', (error) => {
          reject(new Error(`macOS VM connection failed: ${error.message}`));
        });

        setTimeout(() => {
          socket.destroy();
          reject(new Error('macOS VM connection timeout'));
        }, 10000);
      });
    } catch (error) {
      throw new Error(`macOS VM connection failed: ${error.message}`);
    }
  }

  disconnect() {
    if (this.connected) {
      this.emit('status', 'Disconnecting...');
      
      // Clean up active connections
      this.activeConnections.clear();
      this.connected = false;
      this.connectionType = null;
      this.deviceInfo = null;
      
      this.emit('disconnected');
      console.log('📱 iPhone disconnected');
    }
  }

  getConnectionStatus() {
    return {
      connected: this.connected,
      connectionType: this.connectionType,
      deviceInfo: this.deviceInfo,
      availableMethods: Array.from(this.connectionMethods.entries())
        .map(([name, method]) => ({
          name,
          displayName: method.name,
          available: method.available,
          description: method.description,
          requirements: method.requirements
        }))
    };
  }

  async connectPhoneLink() {
    console.log('📱 Connecting to Phone Link...');
    
    try {
      const { PhoneLinkBridge } = require('./PhoneLinkBridge');
      this.phoneLinkBridge = new PhoneLinkBridge();
      await this.phoneLinkBridge.connect();
      
      this.activeConnections.set('phoneLink', this.phoneLinkBridge);
      this.emit('connected', 'phoneLink');
      
      return true;
    } catch (error) {
      console.error('Phone Link connection failed:', error);
      return false;
    }
  }
  
  async getContacts() {
    if (this.phoneLinkBridge) {
      return await this.phoneLinkBridge.getContacts();
    }
    return [];
  }
  
  async getCalls() {
    if (this.phoneLinkBridge) {
      return await this.phoneLinkBridge.getCallHistory();
    }
    return [];
  }

}

module.exports = FocusedConnectionManager;
