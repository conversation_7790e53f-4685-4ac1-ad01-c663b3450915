import Foundation
import UIKit

class ShortcutsManager: ObservableObject {
    @Published var shortcutsInstalled = false
    @Published var automationEnabled = false
    
    private let requiredShortcuts = [
        "SendMessageFromPC",
        "GetRecentMessages", 
        "HandleIncomingCall",
        "SyncNotifications"
    ]
    
    // MARK: - Shortcut Installation
    
    func generateShortcutFiles() -> [String: String] {
        var shortcuts: [String: String] = [:]
        
        // Send Message Shortcut
        shortcuts["SendMessageFromPC"] = generateSendMessageShortcut()
        
        // Get Recent Messages Shortcut
        shortcuts["GetRecentMessages"] = generateGetMessagesShortcut()
        
        // Handle Incoming Call Shortcut
        shortcuts["HandleIncomingCall"] = generateCallHandlerShortcut()
        
        // Sync Notifications Shortcut
        shortcuts["SyncNotifications"] = generateNotificationShortcut()
        
        return shortcuts
    }
    
    private func generateSendMessageShortcut() -> String {
        return """
        {
            "WFWorkflowActions": [
                {
                    "WFWorkflowActionIdentifier": "is.workflow.actions.text.split",
                    "WFWorkflowActionParameters": {
                        "WFTextSeparator": "|"
                    }
                },
                {
                    "WFWorkflowActionIdentifier": "is.workflow.actions.getitemfromlist",
                    "WFWorkflowActionParameters": {
                        "WFItemIndex": 1
                    }
                },
                {
                    "WFWorkflowActionIdentifier": "is.workflow.actions.setvariable",
                    "WFWorkflowActionParameters": {
                        "WFVariableName": "PhoneNumber"
                    }
                },
                {
                    "WFWorkflowActionIdentifier": "is.workflow.actions.getitemfromlist",
                    "WFWorkflowActionParameters": {
                        "WFItemIndex": 2
                    }
                },
                {
                    "WFWorkflowActionIdentifier": "is.workflow.actions.setvariable",
                    "WFWorkflowActionParameters": {
                        "WFVariableName": "MessageText"
                    }
                },
                {
                    "WFWorkflowActionIdentifier": "is.workflow.actions.sendmessage",
                    "WFWorkflowActionParameters": {
                        "WFSendMessageActionRecipients": {
                            "Value": {
                                "WFVariableName": "PhoneNumber"
                            },
                            "WFSerializationType": "WFTextTokenAttachment"
                        },
                        "WFSendMessageContent": {
                            "Value": {
                                "WFVariableName": "MessageText"
                            },
                            "WFSerializationType": "WFTextTokenAttachment"
                        }
                    }
                }
            ],
            "WFWorkflowName": "SendMessageFromPC",
            "WFWorkflowIcon": {
                "WFWorkflowIconStartColor": 431817727,
                "WFWorkflowIconGlyphNumber": 61440
            }
        }
        """
    }
    
    private func generateGetMessagesShortcut() -> String {
        return """
        {
            "WFWorkflowActions": [
                {
                    "WFWorkflowActionIdentifier": "is.workflow.actions.gettext",
                    "WFWorkflowActionParameters": {
                        "WFTextActionText": "Getting recent messages..."
                    }
                },
                {
                    "WFWorkflowActionIdentifier": "is.workflow.actions.url",
                    "WFWorkflowActionParameters": {
                        "WFURLActionURL": "http://localhost:8888/messages"
                    }
                },
                {
                    "WFWorkflowActionIdentifier": "is.workflow.actions.downloadurl",
                    "WFWorkflowActionParameters": {
                        "WFHTTPMethod": "POST",
                        "WFHTTPBodyType": "JSON",
                        "WFJSONValues": {
                            "type": "message_sync",
                            "timestamp": {
                                "Value": {
                                    "WFVariableName": "Current Date"
                                },
                                "WFSerializationType": "WFTextTokenAttachment"
                            }
                        }
                    }
                }
            ],
            "WFWorkflowName": "GetRecentMessages",
            "WFWorkflowIcon": {
                "WFWorkflowIconStartColor": 946986751,
                "WFWorkflowIconGlyphNumber": 61440
            }
        }
        """
    }
    
    private func generateCallHandlerShortcut() -> String {
        return """
        {
            "WFWorkflowActions": [
                {
                    "WFWorkflowActionIdentifier": "is.workflow.actions.gettext",
                    "WFWorkflowActionParameters": {
                        "WFTextActionText": "Handling incoming call..."
                    }
                },
                {
                    "WFWorkflowActionIdentifier": "is.workflow.actions.url",
                    "WFWorkflowActionParameters": {
                        "WFURLActionURL": "http://localhost:8888/call"
                    }
                },
                {
                    "WFWorkflowActionIdentifier": "is.workflow.actions.downloadurl",
                    "WFWorkflowActionParameters": {
                        "WFHTTPMethod": "POST",
                        "WFHTTPBodyType": "JSON",
                        "WFJSONValues": {
                            "type": "incoming_call",
                            "caller": "Unknown",
                            "timestamp": {
                                "Value": {
                                    "WFVariableName": "Current Date"
                                },
                                "WFSerializationType": "WFTextTokenAttachment"
                            }
                        }
                    }
                }
            ],
            "WFWorkflowName": "HandleIncomingCall",
            "WFWorkflowIcon": {
                "WFWorkflowIconStartColor": 2071128575,
                "WFWorkflowIconGlyphNumber": 61442
            }
        }
        """
    }
    
    private func generateNotificationShortcut() -> String {
        return """
        {
            "WFWorkflowActions": [
                {
                    "WFWorkflowActionIdentifier": "is.workflow.actions.gettext",
                    "WFWorkflowActionParameters": {
                        "WFTextActionText": "Syncing notifications..."
                    }
                },
                {
                    "WFWorkflowActionIdentifier": "is.workflow.actions.url",
                    "WFWorkflowActionParameters": {
                        "WFURLActionURL": "http://localhost:8888/notifications"
                    }
                },
                {
                    "WFWorkflowActionIdentifier": "is.workflow.actions.downloadurl",
                    "WFWorkflowActionParameters": {
                        "WFHTTPMethod": "POST",
                        "WFHTTPBodyType": "JSON",
                        "WFJSONValues": {
                            "type": "notification_sync",
                            "timestamp": {
                                "Value": {
                                    "WFVariableName": "Current Date"
                                },
                                "WFSerializationType": "WFTextTokenAttachment"
                            }
                        }
                    }
                }
            ],
            "WFWorkflowName": "SyncNotifications",
            "WFWorkflowIcon": {
                "WFWorkflowIconStartColor": 4282601983,
                "WFWorkflowIconGlyphNumber": 61440
            }
        }
        """
    }
    
    // MARK: - Installation URLs
    
    func getShortcutInstallationURLs() -> [String: String] {
        return [
            "SendMessageFromPC": "https://www.icloud.com/shortcuts/your-shortcut-id-1",
            "GetRecentMessages": "https://www.icloud.com/shortcuts/your-shortcut-id-2", 
            "HandleIncomingCall": "https://www.icloud.com/shortcuts/your-shortcut-id-3",
            "SyncNotifications": "https://www.icloud.com/shortcuts/your-shortcut-id-4"
        ]
    }
    
    // MARK: - Automation Setup
    
    func getAutomationInstructions() -> [String] {
        return [
            "1. Open iOS Shortcuts app",
            "2. Go to 'Automation' tab",
            "3. Tap '+' to create new automation",
            "4. Choose 'App' trigger",
            "5. Select 'Messages' app",
            "6. Choose 'Is Opened'",
            "7. Add 'GetRecentMessages' shortcut",
            "8. Disable 'Ask Before Running'",
            "9. Save automation"
        ]
    }
    
    // MARK: - Validation
    
    func validateShortcutInstallation() {
        // Check if shortcuts are installed by attempting to run them
        for shortcut in requiredShortcuts {
            let url = URL(string: "shortcuts://run-shortcut?name=\(shortcut)")!
            
            UIApplication.shared.open(url) { success in
                DispatchQueue.main.async {
                    if success {
                        print("Shortcut \(shortcut) is installed")
                    } else {
                        print("Shortcut \(shortcut) is NOT installed")
                    }
                }
            }
        }
    }
}
