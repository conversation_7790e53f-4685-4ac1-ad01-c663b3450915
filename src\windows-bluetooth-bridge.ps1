# Windows Bluetooth Bridge for Intel Unison++
# This script provides Bluetooth functionality on Windows

param(
    [string]$Action = "discover",
    [int]$Duration = 30
)

Write-Host "🌉 Windows Bluetooth Bridge Starting..." -ForegroundColor Cyan
Write-Host "Platform: $($env:OS)" -ForegroundColor Gray
Write-Host "PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor Gray

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
if (-not $isAdmin) {
    Write-Host "⚠️ Not running as Administrator - some Bluetooth features may be limited" -ForegroundColor Yellow
}

# Function to discover Bluetooth devices
function Start-BluetoothDiscovery {
    param([int]$ScanDuration = 30)
    
    Write-Host "🔍 Starting Bluetooth device discovery for $ScanDuration seconds..." -ForegroundColor Green
    
    try {
        # Try to use Windows.Devices.Bluetooth APIs
        Add-Type -AssemblyName System.Runtime.WindowsRuntime
        
        $asTaskGeneric = ([System.WindowsRuntimeSystemExtensions].GetMethods() | Where-Object { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1 -and $_.GetParameters()[0].ParameterType.Name -eq 'IAsyncOperation`1' })[0]
        
        function Await($WinRtTask, $ResultType) {
            $asTask = $asTaskGeneric.MakeGenericMethod($ResultType)
            $netTask = $asTask.Invoke($null, @($WinRtTask))
            $netTask.Wait(-1) | Out-Null
            $netTask.Result
        }
        
        # Get Bluetooth adapter
        [Windows.Devices.Bluetooth.BluetoothAdapter, Windows.Devices.Bluetooth, ContentType = WindowsRuntime] | Out-Null
        $adapterTask = [Windows.Devices.Bluetooth.BluetoothAdapter]::GetDefaultAsync()
        $adapter = Await $adapterTask ([Windows.Devices.Bluetooth.BluetoothAdapter])
        
        if ($adapter -eq $null) {
            Write-Host "❌ No Bluetooth adapter found" -ForegroundColor Red
            return
        }
        
        Write-Host "✅ Bluetooth adapter found: $($adapter.DeviceId)" -ForegroundColor Green
        Write-Host "📡 Radio state: $($adapter.IsClassicSupported), LE: $($adapter.IsLowEnergySupported)" -ForegroundColor Gray
        
        # Start discovery using WMI as fallback
        Write-Host "🔍 Scanning for devices using WMI..." -ForegroundColor Yellow
        
        $devices = Get-WmiObject -Class Win32_PnPEntity | Where-Object { 
            $_.Name -match "Bluetooth|iPhone|Apple" -and $_.Status -eq "OK" 
        }
        
        if ($devices) {
            Write-Host "📱 Found Bluetooth devices:" -ForegroundColor Green
            foreach ($device in $devices) {
                $deviceInfo = @{
                    Name = $device.Name
                    DeviceID = $device.DeviceID
                    Status = $device.Status
                    Manufacturer = $device.Manufacturer
                }
                
                Write-Host "  📍 $($device.Name)" -ForegroundColor Cyan
                Write-Host "     ID: $($device.DeviceID)" -ForegroundColor Gray
                Write-Host "     Status: $($device.Status)" -ForegroundColor Gray
                
                # Output as JSON for Node.js to parse
                $json = $deviceInfo | ConvertTo-Json -Compress
                Write-Output "DEVICE_JSON:$json"
            }
        } else {
            Write-Host "❌ No Bluetooth devices found" -ForegroundColor Red
        }
        
        # Also try to get paired devices
        Write-Host "🔗 Checking paired devices..." -ForegroundColor Yellow
        $pairedDevices = Get-PnpDevice | Where-Object { 
            $_.FriendlyName -match "iPhone|Apple|Bluetooth" -and $_.Status -eq "OK" 
        }
        
        if ($pairedDevices) {
            Write-Host "📱 Found paired devices:" -ForegroundColor Green
            foreach ($device in $pairedDevices) {
                Write-Host "  🔗 $($device.FriendlyName)" -ForegroundColor Cyan
                Write-Host "     Status: $($device.Status)" -ForegroundColor Gray
                
                $pairedInfo = @{
                    Name = $device.FriendlyName
                    InstanceId = $device.InstanceId
                    Status = $device.Status
                    Class = $device.Class
                    Type = "Paired"
                }
                
                $json = $pairedInfo | ConvertTo-Json -Compress
                Write-Output "PAIRED_JSON:$json"
            }
        }
        
    } catch {
        Write-Host "❌ Error during Bluetooth discovery: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "💡 Try running as Administrator or check if Bluetooth is enabled" -ForegroundColor Yellow
    }
}

# Function to get Bluetooth status
function Get-BluetoothStatus {
    try {
        $bluetoothService = Get-Service -Name "bthserv" -ErrorAction SilentlyContinue
        if ($bluetoothService) {
            Write-Host "📡 Bluetooth service status: $($bluetoothService.Status)" -ForegroundColor Green
            
            $statusInfo = @{
                ServiceStatus = $bluetoothService.Status.ToString()
                ServiceName = $bluetoothService.Name
                DisplayName = $bluetoothService.DisplayName
            }
            
            $json = $statusInfo | ConvertTo-Json -Compress
            Write-Output "STATUS_JSON:$json"
        } else {
            Write-Host "❌ Bluetooth service not found" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Error checking Bluetooth status: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Main execution
switch ($Action.ToLower()) {
    "discover" {
        Get-BluetoothStatus
        Start-BluetoothDiscovery -ScanDuration $Duration
    }
    "status" {
        Get-BluetoothStatus
    }
    default {
        Write-Host "❌ Unknown action: $Action" -ForegroundColor Red
        Write-Host "Available actions: discover, status" -ForegroundColor Yellow
    }
}

Write-Host "🏁 Bluetooth Bridge completed" -ForegroundColor Green