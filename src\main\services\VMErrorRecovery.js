const { EventEmitter } = require('events');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

class VMErrorRecovery extends EventEmitter {
  constructor(vmBridge, vmManager, healthMonitor) {
    super();
    this.vmBridge = vmBridge;
    this.vmManager = vmManager;
    this.healthMonitor = healthMonitor;
    this.recoveryStrategies = new Map();
    this.recoveryHistory = [];
    this.isRecovering = false;
    this.maxRecoveryAttempts = 5;
    this.recoveryTimeout = 300000; // 5 minutes
    this.backoffMultiplier = 2;
    this.baseDelay = 5000; // 5 seconds
    
    this.initializeRecoveryStrategies();
    this.setupEventListeners();
  }

  // Initialize recovery strategies for different error types
  initializeRecoveryStrategies() {
    // VM startup failures
    this.recoveryStrategies.set('VM_STARTUP_FAILED', [
      { name: 'restart_vm', priority: 1, timeout: 60000 },
      { name: 'recreate_vm_config', priority: 2, timeout: 30000 },
      { name: 'reset_vm_disk', priority: 3, timeout: 120000 },
      { name: 'fallback_to_basic_mode', priority: 4, timeout: 10000 }
    ]);

    // Bridge connection failures
    this.recoveryStrategies.set('BRIDGE_CONNECTION_FAILED', [
      { name: 'restart_bridge_service', priority: 1, timeout: 30000 },
      { name: 'reset_network_config', priority: 2, timeout: 45000 },
      { name: 'restart_vm_networking', priority: 3, timeout: 60000 },
      { name: 'recreate_bridge_tunnel', priority: 4, timeout: 90000 }
    ]);

    // Database access failures
    this.recoveryStrategies.set('DATABASE_ACCESS_FAILED', [
      { name: 'restart_database_service', priority: 1, timeout: 20000 },
      { name: 'repair_database_permissions', priority: 2, timeout: 30000 },
      { name: 'recreate_database_connection', priority: 3, timeout: 45000 },
      { name: 'fallback_to_cached_data', priority: 4, timeout: 5000 }
    ]);

    // Resource exhaustion
    this.recoveryStrategies.set('RESOURCE_EXHAUSTION', [
      { name: 'optimize_vm_resources', priority: 1, timeout: 30000 },
      { name: 'cleanup_temporary_files', priority: 2, timeout: 15000 },
      { name: 'restart_with_reduced_resources', priority: 3, timeout: 60000 },
      { name: 'enable_resource_monitoring', priority: 4, timeout: 10000 }
    ]);

    // Network connectivity issues
    this.recoveryStrategies.set('NETWORK_CONNECTIVITY_FAILED', [
      { name: 'reset_network_stack', priority: 1, timeout: 30000 },
      { name: 'reconfigure_port_forwarding', priority: 2, timeout: 20000 },
      { name: 'restart_vm_network_interface', priority: 3, timeout: 45000 },
      { name: 'fallback_to_alternative_ports', priority: 4, timeout: 15000 }
    ]);
  }

  // Setup event listeners for error detection
  setupEventListeners() {
    if (this.vmBridge) {
      this.vmBridge.on('error', (error) => this.handleError('VM_BRIDGE_ERROR', error));
      this.vmBridge.on('bridge-disconnected', () => this.handleError('BRIDGE_CONNECTION_FAILED'));
      this.vmBridge.on('vm-error', (error) => this.handleError('VM_STARTUP_FAILED', error));
    }

    if (this.healthMonitor) {
      this.healthMonitor.on('health-critical', (data) => this.handleCriticalHealth(data));
      this.healthMonitor.on('recovery-failed', () => this.handleRecoveryFailure());
      this.healthMonitor.on('high-cpu-usage', () => this.handleError('RESOURCE_EXHAUSTION'));
      this.healthMonitor.on('high-memory-usage', () => this.handleError('RESOURCE_EXHAUSTION'));
      this.healthMonitor.on('network-connectivity-failed', () => this.handleError('NETWORK_CONNECTIVITY_FAILED'));
    }
  }

  // Handle different types of errors
  async handleError(errorType, errorData = null) {
    if (this.isRecovering) {
      console.log(`Recovery already in progress, queuing error: ${errorType}`);
      return;
    }

    console.log(`Handling error: ${errorType}`, errorData);
    this.emit('error-detected', { type: errorType, data: errorData });

    try {
      await this.executeRecoveryPlan(errorType, errorData);
    } catch (recoveryError) {
      console.error('Recovery failed:', recoveryError);
      this.emit('recovery-failed', { errorType, recoveryError });
    }
  }

  // Handle critical health status
  async handleCriticalHealth(healthData) {
    const issues = this.analyzeHealthIssues(healthData);
    
    for (const issue of issues) {
      await this.handleError(issue.type, issue.data);
    }
  }

  // Analyze health data to determine specific issues
  analyzeHealthIssues(healthData) {
    const issues = [];
    const metrics = healthData.metrics;

    if (metrics.vmStatus !== 'running') {
      issues.push({ type: 'VM_STARTUP_FAILED', data: { status: metrics.vmStatus } });
    }

    if (!metrics.bridgeConnected) {
      issues.push({ type: 'BRIDGE_CONNECTION_FAILED', data: { connected: false } });
    }

    if (metrics.cpuUsage > 90 || metrics.memoryUsage > 95) {
      issues.push({ type: 'RESOURCE_EXHAUSTION', data: { cpu: metrics.cpuUsage, memory: metrics.memoryUsage } });
    }

    if (metrics.networkLatency > 5000 || metrics.networkLatency === -1) {
      issues.push({ type: 'NETWORK_CONNECTIVITY_FAILED', data: { latency: metrics.networkLatency } });
    }

    return issues;
  }

  // Execute recovery plan for specific error type
  async executeRecoveryPlan(errorType, errorData) {
    this.isRecovering = true;
    const startTime = Date.now();

    try {
      const strategies = this.recoveryStrategies.get(errorType) || [];
      
      if (strategies.length === 0) {
        throw new Error(`No recovery strategies defined for error type: ${errorType}`);
      }

      this.emit('recovery-started', { errorType, strategiesCount: strategies.length });

      // Sort strategies by priority
      const sortedStrategies = strategies.sort((a, b) => a.priority - b.priority);

      for (const strategy of sortedStrategies) {
        const attemptCount = this.getAttemptCount(errorType, strategy.name);
        
        if (attemptCount >= this.maxRecoveryAttempts) {
          console.log(`Max attempts reached for strategy: ${strategy.name}`);
          continue;
        }

        try {
          console.log(`Executing recovery strategy: ${strategy.name} (attempt ${attemptCount + 1})`);
          this.emit('recovery-strategy-started', { strategy: strategy.name, attempt: attemptCount + 1 });

          const success = await this.executeRecoveryStrategy(strategy, errorData);
          
          if (success) {
            this.recordRecoverySuccess(errorType, strategy.name, Date.now() - startTime);
            this.emit('recovery-success', { errorType, strategy: strategy.name });
            return;
          }

        } catch (strategyError) {
          console.error(`Recovery strategy ${strategy.name} failed:`, strategyError);
          this.recordRecoveryFailure(errorType, strategy.name, strategyError);
          this.emit('recovery-strategy-failed', { strategy: strategy.name, error: strategyError });
        }

        // Apply backoff delay between strategies
        const delay = this.calculateBackoffDelay(attemptCount);
        await this.sleep(delay);
      }

      throw new Error(`All recovery strategies failed for error type: ${errorType}`);

    } finally {
      this.isRecovering = false;
    }
  }

  // Execute individual recovery strategy
  async executeRecoveryStrategy(strategy, errorData) {
    const timeout = strategy.timeout || 60000;
    
    return new Promise(async (resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Recovery strategy ${strategy.name} timed out`));
      }, timeout);

      try {
        let result = false;

        switch (strategy.name) {
          case 'restart_vm':
            result = await this.restartVM();
            break;
          case 'recreate_vm_config':
            result = await this.recreateVMConfig();
            break;
          case 'reset_vm_disk':
            result = await this.resetVMDisk();
            break;
          case 'restart_bridge_service':
            result = await this.restartBridgeService();
            break;
          case 'reset_network_config':
            result = await this.resetNetworkConfig();
            break;
          case 'restart_vm_networking':
            result = await this.restartVMNetworking();
            break;
          case 'recreate_bridge_tunnel':
            result = await this.recreateBridgeTunnel();
            break;
          case 'restart_database_service':
            result = await this.restartDatabaseService();
            break;
          case 'repair_database_permissions':
            result = await this.repairDatabasePermissions();
            break;
          case 'recreate_database_connection':
            result = await this.recreateDatabaseConnection();
            break;
          case 'optimize_vm_resources':
            result = await this.optimizeVMResources();
            break;
          case 'cleanup_temporary_files':
            result = await this.cleanupTemporaryFiles();
            break;
          case 'restart_with_reduced_resources':
            result = await this.restartWithReducedResources();
            break;
          case 'reset_network_stack':
            result = await this.resetNetworkStack();
            break;
          case 'reconfigure_port_forwarding':
            result = await this.reconfigurePortForwarding();
            break;
          case 'restart_vm_network_interface':
            result = await this.restartVMNetworkInterface();
            break;
          case 'fallback_to_basic_mode':
            result = await this.fallbackToBasicMode();
            break;
          case 'fallback_to_cached_data':
            result = await this.fallbackToCachedData();
            break;
          case 'fallback_to_alternative_ports':
            result = await this.fallbackToAlternativePorts();
            break;
          case 'enable_resource_monitoring':
            result = await this.enableResourceMonitoring();
            break;
          default:
            throw new Error(`Unknown recovery strategy: ${strategy.name}`);
        }

        clearTimeout(timer);
        resolve(result);

      } catch (error) {
        clearTimeout(timer);
        reject(error);
      }
    });
  }

  // Recovery strategy implementations
  async restartVM() {
    if (this.vmManager) {
      await this.vmManager.stopVM();
      await this.sleep(5000);
      const vm = await this.vmManager.createOptimizedMacOSVM();
      return !!vm;
    }
    return false;
  }

  async recreateVMConfig() {
    if (this.vmBridge) {
      await this.vmBridge.setupVMEnvironment();
      return true;
    }
    return false;
  }

  async resetVMDisk() {
    // This would restore from a known good snapshot
    if (this.vmManager) {
      try {
        await this.vmManager.restoreSnapshot('clean-install');
        return true;
      } catch (error) {
        console.error('Failed to restore snapshot:', error);
        return false;
      }
    }
    return false;
  }

  async restartBridgeService() {
    if (this.vmBridge) {
      try {
        await this.vmBridge.createWebSocketTunnel();
        return true;
      } catch (error) {
        console.error('Failed to restart bridge service:', error);
        return false;
      }
    }
    return false;
  }

  async resetNetworkConfig() {
    // Reset network configuration
    try {
      await this.vmBridge.setupVMEnvironment();
      return true;
    } catch (error) {
      console.error('Failed to reset network config:', error);
      return false;
    }
  }

  async restartVMNetworking() {
    // Restart VM networking
    if (this.vmBridge) {
      try {
        await this.vmBridge.executeInVM('sudo ifconfig en0 down && sudo ifconfig en0 up');
        return true;
      } catch (error) {
        console.error('Failed to restart VM networking:', error);
        return false;
      }
    }
    return false;
  }

  async recreateBridgeTunnel() {
    if (this.vmBridge) {
      try {
        await this.vmBridge.createWebSocketTunnel();
        return true;
      } catch (error) {
        console.error('Failed to recreate bridge tunnel:', error);
        return false;
      }
    }
    return false;
  }

  async restartDatabaseService() {
    // Restart database service in VM
    if (this.vmBridge) {
      try {
        await this.vmBridge.executeInVM('pkill -f "node.*bridge" && cd /tmp/bridge && npm start &');
        await this.sleep(10000); // Wait for service to start
        return true;
      } catch (error) {
        console.error('Failed to restart database service:', error);
        return false;
      }
    }
    return false;
  }

  async repairDatabasePermissions() {
    // Repair database permissions
    if (this.vmBridge) {
      try {
        await this.vmBridge.executeInVM('chmod 644 ~/Library/Messages/chat.db');
        return true;
      } catch (error) {
        console.error('Failed to repair database permissions:', error);
        return false;
      }
    }
    return false;
  }

  async recreateDatabaseConnection() {
    // Recreate database connection
    return await this.restartDatabaseService();
  }

  async optimizeVMResources() {
    // Optimize VM resource usage
    if (this.vmBridge) {
      const currentConfig = this.vmBridge.getStatus().config;
      const optimizedConfig = {
        ...currentConfig,
        memory: Math.max(parseInt(currentConfig.memory) - 1, 4) + 'G',
        cores: Math.max(currentConfig.cores - 1, 2)
      };
      
      this.vmBridge.updateConfig(optimizedConfig);
      return true;
    }
    return false;
  }

  async cleanupTemporaryFiles() {
    // Cleanup temporary files
    try {
      const vmDir = path.join(require('os').homedir(), 'iPhone-Companion-Pro', 'macos-vm');
      const tempDir = path.join(vmDir, 'temp');
      
      if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
      }
      
      return true;
    } catch (error) {
      console.error('Failed to cleanup temporary files:', error);
      return false;
    }
  }

  async restartWithReducedResources() {
    await this.optimizeVMResources();
    return await this.restartVM();
  }

  async resetNetworkStack() {
    // Reset network stack
    return new Promise((resolve) => {
      exec('netsh winsock reset', (error) => {
        resolve(!error);
      });
    });
  }

  async reconfigurePortForwarding() {
    // Reconfigure port forwarding
    if (this.vmBridge) {
      try {
        const config = this.vmBridge.getStatus().config;
        config.bridgePort = config.bridgePort === 8080 ? 8081 : 8080;
        this.vmBridge.updateConfig(config);
        return true;
      } catch (error) {
        console.error('Failed to reconfigure port forwarding:', error);
        return false;
      }
    }
    return false;
  }

  async restartVMNetworkInterface() {
    return await this.restartVMNetworking();
  }

  async fallbackToBasicMode() {
    // Fallback to basic mode without VM
    this.emit('fallback-to-basic-mode');
    return true;
  }

  async fallbackToCachedData() {
    // Use cached data instead of live data
    this.emit('fallback-to-cached-data');
    return true;
  }

  async fallbackToAlternativePorts() {
    return await this.reconfigurePortForwarding();
  }

  async enableResourceMonitoring() {
    // Enable enhanced resource monitoring
    if (this.healthMonitor) {
      this.healthMonitor.updateThresholds({
        cpuUsage: 70,
        memoryUsage: 75,
        networkLatency: 500
      });
      return true;
    }
    return false;
  }

  // Utility methods
  getAttemptCount(errorType, strategyName) {
    return this.recoveryHistory.filter(
      entry => entry.errorType === errorType && 
               entry.strategy === strategyName &&
               Date.now() - entry.timestamp < 3600000 // Last hour
    ).length;
  }

  calculateBackoffDelay(attemptCount) {
    return Math.min(this.baseDelay * Math.pow(this.backoffMultiplier, attemptCount), 60000);
  }

  recordRecoverySuccess(errorType, strategy, duration) {
    this.recoveryHistory.push({
      timestamp: Date.now(),
      errorType,
      strategy,
      success: true,
      duration
    });
  }

  recordRecoveryFailure(errorType, strategy, error) {
    this.recoveryHistory.push({
      timestamp: Date.now(),
      errorType,
      strategy,
      success: false,
      error: error.message
    });
  }

  handleRecoveryFailure() {
    this.emit('all-recovery-failed');
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get recovery statistics
  getRecoveryStats() {
    const recent = this.recoveryHistory.filter(
      entry => Date.now() - entry.timestamp < 86400000 // Last 24 hours
    );

    return {
      totalAttempts: recent.length,
      successfulAttempts: recent.filter(entry => entry.success).length,
      failedAttempts: recent.filter(entry => !entry.success).length,
      averageDuration: recent.filter(entry => entry.success)
        .reduce((sum, entry) => sum + entry.duration, 0) / recent.filter(entry => entry.success).length || 0,
      mostCommonErrors: this.getMostCommonErrors(recent),
      mostSuccessfulStrategies: this.getMostSuccessfulStrategies(recent)
    };
  }

  getMostCommonErrors(history) {
    const errorCounts = {};
    history.forEach(entry => {
      errorCounts[entry.errorType] = (errorCounts[entry.errorType] || 0) + 1;
    });
    
    return Object.entries(errorCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);
  }

  getMostSuccessfulStrategies(history) {
    const strategyCounts = {};
    history.filter(entry => entry.success).forEach(entry => {
      strategyCounts[entry.strategy] = (strategyCounts[entry.strategy] || 0) + 1;
    });
    
    return Object.entries(strategyCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);
  }
}

module.exports = VMErrorRecovery;
