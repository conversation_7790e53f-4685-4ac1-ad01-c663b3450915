// Master Connection Manager - Updated with all iPhone connection methods
const EventEmitter = require('events');
const USBConnectionService = require('./USBConnectionService');
const PhoneLinkService = require('./PhoneLinkService');
const AirPlayService = require('./AirPlayService');
const MacOSVMService = require('./MacOSVMService');
const BluetoothService = require('./BluetoothService');

class MasterConnectionManager extends EventEmitter {
  constructor() {
    super();
    this.connections = new Map();
    this.activeConnections = new Set();
    this.config = {
    "methods": {
        "usb": false,
        "phoneLink": false,
        "airplay": false,
        "macosVM": false,
        "bluetooth": false
    },
    "errors": []
};
    
    this.initializeServices();
  }

  initializeServices() {
    // Initialize all available connection services
    if (this.config.methods.usb) {
      this.connections.set('usb', new USBConnectionService());
    }
    
    if (this.config.methods.phoneLink) {
      this.connections.set('phoneLink', new PhoneLinkService());
    }
    
    if (this.config.methods.airplay) {
      this.connections.set('airplay', new AirPlayService());
    }
    
    if (this.config.methods.macosVM) {
      this.connections.set('macosVM', new MacOSVMService());
    }
    
    if (this.config.methods.bluetooth) {
      this.connections.set('bluetooth', new BluetoothService());
    }

    // Set up event listeners
    this.connections.forEach((service, type) => {
      service.on('connected', () => {
        this.activeConnections.add(type);
        this.emit('connectionChanged', { type, status: 'connected' });
      });
      
      service.on('disconnected', () => {
        this.activeConnections.delete(type);
        this.emit('connectionChanged', { type, status: 'disconnected' });
      });
      
      service.on('data', (data) => {
        this.emit('data', { source: type, data });
      });
    });
  }

  async startAllConnections() {
    console.log('🚀 Starting all available connections...');
    
    const promises = Array.from(this.connections.entries()).map(async ([type, service]) => {
      try {
        await service.connect();
        console.log(`✅ ${type} connection started`);
        return { type, success: true };
      } catch (error) {
        console.log(`❌ ${type} connection failed: ${error.message}`);
        return { type, success: false, error: error.message };
      }
    });

    const results = await Promise.allSettled(promises);
    return results.map(result => result.value || result.reason);
  }

  async stopAllConnections() {
    const promises = Array.from(this.connections.values()).map(service => 
      service.disconnect().catch(err => console.log('Disconnect error:', err))
    );
    
    await Promise.allSettled(promises);
    this.activeConnections.clear();
  }

  getConnectionStatus() {
    const status = {};
    this.connections.forEach((service, type) => {
      status[type] = {
        available: true,
        connected: this.activeConnections.has(type),
        lastSeen: service.lastSeen || null
      };
    });
    return status;
  }

  async sendMessage(message, preferredMethod = null) {
    if (preferredMethod && this.activeConnections.has(preferredMethod)) {
      const service = this.connections.get(preferredMethod);
      return await service.sendMessage(message);
    }

    // Try all active connections in priority order
    const priorityOrder = ['usb', 'phoneLink', 'airplay', 'macosVM', 'bluetooth'];
    
    for (const method of priorityOrder) {
      if (this.activeConnections.has(method)) {
        try {
          const service = this.connections.get(method);
          return await service.sendMessage(message);
        } catch (error) {
          console.log(`Failed to send via ${method}: ${error.message}`);
        }
      }
    }
    
    throw new Error('No active connections available');
  }
}

module.exports = MasterConnectionManager;