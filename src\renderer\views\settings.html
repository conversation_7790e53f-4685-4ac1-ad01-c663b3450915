<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - iPhone Companion Pro</title>
    <link rel="stylesheet" href="styles/settings.css">
    <link rel="stylesheet" href="styles/common.css">
</head>
<body>
    <div class="settings-container">
        <header class="settings-header">
            <h1>Settings</h1>
            <button class="close-btn" onclick="window.close()">×</button>
        </header>

        <div class="settings-content">
            <nav class="settings-nav">
                <ul>
                    <li><a href="#general" class="nav-link active" data-section="general">General</a></li>
                    <li><a href="#connection" class="nav-link" data-section="connection">Connection</a></li>
                    <li><a href="#sync" class="nav-link" data-section="sync">Sync</a></li>
                    <li><a href="#notifications" class="nav-link" data-section="notifications">Notifications</a></li>
                    <li><a href="#performance" class="nav-link" data-section="performance">Performance</a></li>
                    <li><a href="#crm" class="nav-link" data-section="crm">CRM Integration</a></li>
                    <li><a href="#security" class="nav-link" data-section="security">Security</a></li>
                    <li><a href="#advanced" class="nav-link" data-section="advanced">Advanced</a></li>
                </ul>
            </nav>

            <main class="settings-main">
                <!-- General Settings -->
                <section id="general" class="settings-section active">
                    <h2>General Settings</h2>
                    
                    <div class="setting-group">
                        <h3>Appearance</h3>
                        <div class="setting-item">
                            <label for="theme">Theme</label>
                            <select id="theme">
                                <option value="auto">Auto</option>
                                <option value="light">Light</option>
                                <option value="dark">Dark</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label for="language">Language</label>
                            <select id="language">
                                <option value="en">English</option>
                                <option value="es">Spanish</option>
                                <option value="fr">French</option>
                                <option value="de">German</option>
                            </select>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3>Startup</h3>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="startWithWindows">
                                <span class="checkmark"></span>
                                Start with Windows
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="minimizeToTray">
                                <span class="checkmark"></span>
                                Minimize to system tray
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="autoConnect">
                                <span class="checkmark"></span>
                                Auto-connect to last device
                            </label>
                        </div>
                    </div>
                </section>

                <!-- Connection Settings -->
                <section id="connection" class="settings-section">
                    <h2>Connection Settings</h2>
                    
                    <div class="setting-group">
                        <h3>Connection Method</h3>
                        <div class="setting-item">
                            <label class="radio-label">
                                <input type="radio" name="connectionMethod" value="usb" checked>
                                <span class="radio-mark"></span>
                                USB Connection
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="radio-label">
                                <input type="radio" name="connectionMethod" value="wifi">
                                <span class="radio-mark"></span>
                                Wi-Fi Connection
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="radio-label">
                                <input type="radio" name="connectionMethod" value="vm">
                                <span class="radio-mark"></span>
                                VM Bridge
                            </label>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3>Network Settings</h3>
                        <div class="setting-item">
                            <label for="serverPort">Server Port</label>
                            <input type="number" id="serverPort" value="7000" min="1024" max="65535">
                        </div>
                        <div class="setting-item">
                            <label for="timeout">Connection Timeout (seconds)</label>
                            <input type="number" id="timeout" value="30" min="5" max="300">
                        </div>
                    </div>
                </section>

                <!-- Sync Settings -->
                <section id="sync" class="settings-section">
                    <h2>Sync Settings</h2>
                    
                    <div class="setting-group">
                        <h3>Data Sync</h3>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="syncMessages" checked>
                                <span class="checkmark"></span>
                                Messages
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="syncContacts" checked>
                                <span class="checkmark"></span>
                                Contacts
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="syncCallHistory" checked>
                                <span class="checkmark"></span>
                                Call History
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="syncPhotos">
                                <span class="checkmark"></span>
                                Photos
                            </label>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3>Sync Frequency</h3>
                        <div class="setting-item">
                            <label for="syncInterval">Sync Interval</label>
                            <select id="syncInterval">
                                <option value="realtime">Real-time</option>
                                <option value="5">Every 5 minutes</option>
                                <option value="15">Every 15 minutes</option>
                                <option value="30">Every 30 minutes</option>
                                <option value="60">Every hour</option>
                            </select>
                        </div>
                    </div>
                </section>

                <!-- Notifications Settings -->
                <section id="notifications" class="settings-section">
                    <h2>Notification Settings</h2>
                    
                    <div class="setting-group">
                        <h3>Desktop Notifications</h3>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="enableNotifications" checked>
                                <span class="checkmark"></span>
                                Enable desktop notifications
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="notificationSound" checked>
                                <span class="checkmark"></span>
                                Play notification sound
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="showPreview" checked>
                                <span class="checkmark"></span>
                                Show message preview
                            </label>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3>Notification Types</h3>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="notifyMessages" checked>
                                <span class="checkmark"></span>
                                Messages
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="notifyCalls" checked>
                                <span class="checkmark"></span>
                                Calls
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="notifyApps">
                                <span class="checkmark"></span>
                                App Notifications
                            </label>
                        </div>
                    </div>
                </section>

                <!-- Performance Settings -->
                <section id="performance" class="settings-section">
                    <h2>Performance Settings</h2>
                    
                    <div class="setting-group">
                        <h3>Screen Mirroring</h3>
                        <div class="setting-item">
                            <label for="mirrorQuality">Video Quality</label>
                            <select id="mirrorQuality">
                                <option value="low">Low (720p)</option>
                                <option value="medium" selected>Medium (1080p)</option>
                                <option value="high">High (1440p)</option>
                                <option value="ultra">Ultra (4K)</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label for="frameRate">Frame Rate</label>
                            <select id="frameRate">
                                <option value="15">15 FPS</option>
                                <option value="30" selected>30 FPS</option>
                                <option value="60">60 FPS</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="hardwareAcceleration" checked>
                                <span class="checkmark"></span>
                                Hardware acceleration
                            </label>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3>Resource Usage</h3>
                        <div class="setting-item">
                            <label for="cpuLimit">CPU Usage Limit (%)</label>
                            <input type="range" id="cpuLimit" min="10" max="100" value="80">
                            <span class="range-value">80%</span>
                        </div>
                        <div class="setting-item">
                            <label for="memoryLimit">Memory Usage Limit (MB)</label>
                            <input type="range" id="memoryLimit" min="256" max="4096" value="1024" step="256">
                            <span class="range-value">1024 MB</span>
                        </div>
                    </div>
                </section>

                <!-- CRM Integration Settings -->
                <section id="crm" class="settings-section">
                    <h2>CRM Integration Settings</h2>
                    
                    <div class="setting-group">
                        <h3>CRM Platform</h3>
                        <div class="setting-item">
                            <label for="crmPlatform">Select CRM Platform</label>
                            <select id="crmPlatform">
                                <option value="none">No CRM Integration</option>
                                <option value="salesforce">Salesforce</option>
                                <option value="hubspot">HubSpot</option>
                                <option value="pipedrive">Pipedrive</option>
                                <option value="zoho">Zoho CRM</option>
                                <option value="custom">Custom API</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label for="crmApiKey">API Key</label>
                            <input type="password" id="crmApiKey" placeholder="Enter your CRM API key">
                        </div>
                        <div class="setting-item">
                            <label for="crmUrl">CRM URL (for custom API)</label>
                            <input type="url" id="crmUrl" placeholder="https://your-crm.com/api">
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3>Auto-Sync Settings</h3>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="autoSyncContacts">
                                <span class="checkmark"></span>
                                Auto-sync contacts to CRM
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="autoSyncMessages">
                                <span class="checkmark"></span>
                                Auto-sync message history to CRM
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="autoSyncCalls">
                                <span class="checkmark"></span>
                                Auto-sync call history to CRM
                            </label>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3>Export Options</h3>
                        <div class="setting-item">
                            <label for="exportFormat">Export Format</label>
                            <select id="exportFormat">
                                <option value="json">JSON</option>
                                <option value="csv">CSV</option>
                                <option value="xml">XML</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <button class="btn btn-primary" onclick="exportToCSV()">Export All Messages</button>
                            <button class="btn btn-primary" onclick="exportCallHistory()">Export Call History</button>
                        </div>
                        <div class="setting-item">
                            <button class="btn btn-secondary" onclick="testCRMConnection()">Test CRM Connection</button>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3>Current CRM Status</h3>
                        <div class="setting-item">
                            <div class="crm-status-display">
                                <div class="status-indicator" id="crmStatusIndicator"></div>
                                <div class="status-info">
                                    <div class="status-text" id="crmStatusText">Not Connected</div>
                                    <div class="status-details" id="crmStatusDetails">Configure CRM settings above</div>
                                </div>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="crm-stats">
                                <div class="stat-item">
                                    <span class="stat-label">Synced Contacts:</span>
                                    <span class="stat-value" id="syncedContacts">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Synced Messages:</span>
                                    <span class="stat-value" id="syncedMessages">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Last Sync:</span>
                                    <span class="stat-value" id="lastSync">Never</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Security Settings -->
                <section id="security" class="settings-section">
                    <h2>Security Settings</h2>
                    
                    <div class="setting-group">
                        <h3>Authentication</h3>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="requireAuth">
                                <span class="checkmark"></span>
                                Require authentication
                            </label>
                        </div>
                        <div class="setting-item">
                            <label for="authMethod">Authentication Method</label>
                            <select id="authMethod">
                                <option value="pin">PIN Code</option>
                                <option value="password">Password</option>
                                <option value="biometric">Biometric</option>
                            </select>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3>Data Protection</h3>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="encryptData" checked>
                                <span class="checkmark"></span>
                                Encrypt stored data
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="secureConnection" checked>
                                <span class="checkmark"></span>
                                Use secure connection (TLS)
                            </label>
                        </div>
                    </div>
                </section>

                <!-- Advanced Settings -->
                <section id="advanced" class="settings-section">
                    <h2>Advanced Settings</h2>
                    
                    <div class="setting-group">
                        <h3>Debugging</h3>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="enableLogging">
                                <span class="checkmark"></span>
                                Enable debug logging
                            </label>
                        </div>
                        <div class="setting-item">
                            <label for="logLevel">Log Level</label>
                            <select id="logLevel">
                                <option value="error">Error</option>
                                <option value="warn">Warning</option>
                                <option value="info" selected>Info</option>
                                <option value="debug">Debug</option>
                            </select>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3>Reset</h3>
                        <div class="setting-item">
                            <button class="btn btn-secondary" onclick="resetSettings()">Reset to Defaults</button>
                        </div>
                        <div class="setting-item">
                            <button class="btn btn-danger" onclick="clearAllData()">Clear All Data</button>
                        </div>
                    </div>
                </section>
            </main>
        </div>

        <footer class="settings-footer">
            <button class="btn btn-secondary" onclick="window.close()">Cancel</button>
            <button class="btn btn-primary" onclick="saveSettings()">Save Settings</button>
        </footer>
    </div>

    <script src="scripts/settings.js"></script>
</body>
</html>
