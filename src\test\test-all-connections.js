// test-all-connections.js - Comprehensive iPhone Connection Testing
const { MasterConnectionManager } = require('./src/main/services/MasterConnectionManager');
const fs = require('fs');
const path = require('path');

class ConnectionTester {
  constructor() {
    this.manager = new MasterConnectionManager();
    this.testResults = {
      bluetooth: { status: 'pending', details: [] },
      airplay: { status: 'pending', details: [] },
      vm: { status: 'pending', details: [] },
      phoneLink: { status: 'pending', details: [] },
      usb: { status: 'pending', details: [] }
    };
  }

  async runAllTests() {
    console.log('🧪 iPhone Companion Pro - Connection Testing Suite\n');
    console.log('Testing ALL iPhone connection methods...\n');

    // Setup event listeners
    this.setupEventListeners();

    // Run individual tests
    await this.testBluetooth();
    await this.testAirPlay();
    await this.testVMBridge();
    await this.testPhoneLink();
    await this.testUSB();

    // Run master manager test
    await this.testMasterManager();

    // Generate report
    this.generateReport();
  }

  setupEventListeners() {
    this.manager.on('connectionAdded', (name) => {
      console.log(`✅ ${name.toUpperCase()} connection established!`);
      this.testResults[name].status = 'connected';
    });

    this.manager.on('data', (dataEvent) => {
      console.log(`📨 Data received from ${dataEvent.source}`);
      this.testResults[dataEvent.source].details.push({
        type: 'data_received',
        timestamp: dataEvent.timestamp,
        data: dataEvent.data
      });
    });
  }

  async testBluetooth() {
    console.log('📱 TESTING BLUETOOTH CONNECTION:');
    
    try {
      const { BluetoothEnhanced } = require('./src/main/services/BluetoothEnhanced');
      const bt = new BluetoothEnhanced();
      
      console.log('🔍 Scanning for iPhone via Bluetooth...');
      this.testResults.bluetooth.details.push('Scanning started');
      
      // Set timeout for Bluetooth scan
      const timeout = new Promise((resolve) => {
        setTimeout(() => {
          console.log('⏰ Bluetooth scan timeout (30s)');
          resolve(null);
        }, 30000);
      });
      
      const scan = bt.findIPhone();
      const result = await Promise.race([scan, timeout]);
      
      if (result) {
        this.testResults.bluetooth.status = 'success';
        this.testResults.bluetooth.details.push('iPhone found and connected');
        console.log('✅ Bluetooth test PASSED\n');
      } else {
        this.testResults.bluetooth.status = 'timeout';
        this.testResults.bluetooth.details.push('No iPhone found within timeout');
        console.log('⚠️ Bluetooth test TIMEOUT - Check iPhone Bluetooth settings\n');
      }
    } catch (error) {
      this.testResults.bluetooth.status = 'error';
      this.testResults.bluetooth.details.push(`Error: ${error.message}`);
      console.log('❌ Bluetooth test FAILED:', error.message, '\n');
    }
  }

  async testAirPlay() {
    console.log('📺 TESTING AIRPLAY CONNECTION:');
    
    try {
      const { AirPlayFixed } = require('./src/main/services/AirPlayFixed');
      const airplay = new AirPlayFixed();
      
      console.log('🚀 Starting AirPlay server...');
      this.testResults.airplay.details.push('Server starting');
      
      airplay.on('ready', () => {
        console.log('✅ AirPlay server ready');
        this.testResults.airplay.status = 'ready';
        this.testResults.airplay.details.push('Server ready for connections');
      });
      
      airplay.on('photo', () => {
        console.log('📸 Photo stream received!');
        this.testResults.airplay.status = 'success';
        this.testResults.airplay.details.push('Photo streaming active');
      });
      
      airplay.on('video', () => {
        console.log('🎥 Video stream received!');
        this.testResults.airplay.status = 'success';
        this.testResults.airplay.details.push('Video streaming active');
      });
      
      airplay.start();
      
      // Wait for server to be ready
      await new Promise((resolve) => setTimeout(resolve, 3000));
      
      if (this.testResults.airplay.status === 'ready') {
        console.log('✅ AirPlay test PASSED - Server running');
        console.log('📱 On iPhone: Control Center > Screen Mirroring > "iPhone Companion Pro"');
      } else {
        this.testResults.airplay.status = 'partial';
        console.log('⚠️ AirPlay server started but no connections yet');
      }
      
      console.log('');
    } catch (error) {
      this.testResults.airplay.status = 'error';
      this.testResults.airplay.details.push(`Error: ${error.message}`);
      console.log('❌ AirPlay test FAILED:', error.message, '\n');
    }
  }

  async testVMBridge() {
    console.log('🖥️ TESTING MACOS VM BRIDGE:');
    
    try {
      const { VMBridge } = require('./src/main/services/VMBridge');
      const vm = new VMBridge();
      
      console.log('🔍 Searching for macOS VM...');
      this.testResults.vm.details.push('VM search started');
      
      vm.on('connected', () => {
        console.log('✅ VM Bridge connected!');
        this.testResults.vm.status = 'success';
        this.testResults.vm.details.push('Connected to macOS VM');
      });
      
      vm.on('data', (data) => {
        console.log('📨 VM data received:', data.type);
        this.testResults.vm.details.push(`Data: ${data.type}`);
      });
      
      // Try to connect with timeout
      const connectPromise = vm.connect();
      const timeout = new Promise((resolve) => {
        setTimeout(() => {
          console.log('⏰ VM connection timeout (15s)');
          resolve(null);
        }, 15000);
      });
      
      await Promise.race([connectPromise, timeout]);
      
      if (this.testResults.vm.status === 'success') {
        console.log('✅ VM Bridge test PASSED\n');
      } else {
        this.testResults.vm.status = 'timeout';
        this.testResults.vm.details.push('No VM found or connection failed');
        console.log('⚠️ VM Bridge test TIMEOUT - Check if macOS VM is running\n');
      }
    } catch (error) {
      this.testResults.vm.status = 'error';
      this.testResults.vm.details.push(`Error: ${error.message}`);
      console.log('❌ VM Bridge test FAILED:', error.message, '\n');
    }
  }

  async testPhoneLink() {
    console.log('🔗 TESTING PHONE LINK BRIDGE:');
    
    try {
      const { PhoneLinkBridge } = require('./src/main/services/PhoneLinkBridge');
      const phoneLink = new PhoneLinkBridge();
      
      console.log('🔍 Searching for Phone Link databases...');
      this.testResults.phoneLink.details.push('Database search started');
      
      phoneLink.on('messages', (messages) => {
        console.log(`📨 Found ${messages.length} messages in Phone Link!`);
        this.testResults.phoneLink.status = 'success';
        this.testResults.phoneLink.details.push(`Found ${messages.length} messages`);
      });
      
      await phoneLink.findDatabase();
      
      if (this.testResults.phoneLink.status === 'success') {
        console.log('✅ Phone Link test PASSED\n');
      } else {
        this.testResults.phoneLink.status = 'not_found';
        this.testResults.phoneLink.details.push('No Phone Link databases found');
        console.log('⚠️ Phone Link test - No databases found');
        console.log('   Make sure Phone Link is installed and iPhone is paired\n');
      }
    } catch (error) {
      this.testResults.phoneLink.status = 'error';
      this.testResults.phoneLink.details.push(`Error: ${error.message}`);
      console.log('❌ Phone Link test FAILED:', error.message, '\n');
    }
  }

  async testUSB() {
    console.log('🔌 TESTING USB CONNECTION:');
    
    try {
      const { USBConnection } = require('./src/main/services/USBConnection');
      const usb = new USBConnection();
      
      console.log('🔍 Scanning for iPhone via USB...');
      this.testResults.usb.details.push('USB scan started');
      
      usb.on('connected', (device) => {
        console.log('📱 iPhone connected via USB:', device.id);
        this.testResults.usb.status = 'success';
        this.testResults.usb.details.push(`Connected: ${device.id}`);
      });
      
      const deviceId = await usb.detectiPhone();
      
      if (deviceId) {
        this.testResults.usb.status = 'success';
        this.testResults.usb.details.push(`Device detected: ${deviceId}`);
        console.log('✅ USB test PASSED\n');
      } else {
        this.testResults.usb.status = 'not_found';
        this.testResults.usb.details.push('No iPhone detected');
        console.log('⚠️ USB test - No iPhone detected');
        console.log('   Make sure iPhone is connected and "Trust This Computer" is accepted\n');
      }
    } catch (error) {
      this.testResults.usb.status = 'error';
      this.testResults.usb.details.push(`Error: ${error.message}`);
      console.log('❌ USB test FAILED:', error.message, '\n');
    }
  }

  async testMasterManager() {
    console.log('🎯 TESTING MASTER CONNECTION MANAGER:');

    try {
      console.log('🚀 Initializing all connections...');
      const activeConnections = await this.manager.initializeAll();

      console.log(`\n📊 Connection Summary:`);
      console.log(`   Active: ${activeConnections.size}/5 connections`);
      console.log(`   Methods: ${Array.from(activeConnections).join(', ')}`);

      if (activeConnections.size > 0) {
        console.log('✅ Master Manager test PASSED\n');
      } else {
        console.log('⚠️ Master Manager test - No connections established\n');
      }
    } catch (error) {
      console.log('❌ Master Manager test FAILED:', error.message, '\n');
    }
  }

  generateReport() {
    console.log('📋 FINAL TEST REPORT:');
    console.log('=' .repeat(50));

    const statusEmojis = {
      success: '✅',
      ready: '🟡',
      partial: '🟡',
      timeout: '⏰',
      not_found: '⚠️',
      error: '❌',
      pending: '⏳'
    };

    let successCount = 0;
    const totalTests = Object.keys(this.testResults).length;

    Object.entries(this.testResults).forEach(([method, result]) => {
      const emoji = statusEmojis[result.status] || '❓';
      console.log(`${emoji} ${method.toUpperCase()}: ${result.status}`);

      if (result.details.length > 0) {
        result.details.forEach(detail => {
          console.log(`   • ${detail}`);
        });
      }

      if (result.status === 'success' || result.status === 'ready') {
        successCount++;
      }

      console.log('');
    });

    console.log('=' .repeat(50));
    console.log(`📊 OVERALL RESULTS: ${successCount}/${totalTests} methods working`);

    if (successCount === 0) {
      console.log('❌ NO CONNECTIONS WORKING');
      console.log('\n🔧 TROUBLESHOOTING STEPS:');
      console.log('1. Check iPhone Bluetooth is on and discoverable');
      console.log('2. Ensure iPhone and PC are on same WiFi network');
      console.log('3. Install iTunes for USB support');
      console.log('4. Setup Phone Link app and pair iPhone');
      console.log('5. Setup macOS VM with bridge script');
    } else if (successCount < totalTests) {
      console.log('⚠️ PARTIAL SUCCESS - Some methods working');
      console.log('\n💡 RECOMMENDATIONS:');
      console.log('• Use working methods for now');
      console.log('• Troubleshoot failed methods individually');
      console.log('• Check iPhone settings for each method');
    } else {
      console.log('🎉 ALL METHODS WORKING PERFECTLY!');
      console.log('\n🚀 READY TO USE:');
      console.log('• Run: npm start');
      console.log('• All iPhone data will sync automatically');
    }

    // Save report to file
    const reportData = {
      timestamp: new Date().toISOString(),
      results: this.testResults,
      summary: {
        total: totalTests,
        working: successCount,
        percentage: Math.round((successCount / totalTests) * 100)
      }
    };

    fs.writeFileSync('connection-test-report.json', JSON.stringify(reportData, null, 2));
    console.log('\n💾 Report saved to: connection-test-report.json');

    // iPhone setup instructions
    console.log('\n📱 IPHONE SETUP CHECKLIST:');
    console.log('□ Bluetooth: Settings > Bluetooth > ON');
    console.log('□ AirPlay: Control Center > Screen Mirroring');
    console.log('□ USB: Connect cable + "Trust This Computer"');
    console.log('□ Phone Link: Install app + pair with Windows');
    console.log('□ VM Bridge: No iPhone setup needed');
  }
}

// Run the tests
const tester = new ConnectionTester();
tester.runAllTests().catch(console.error);
