import Foundation
import Network
import Combine
import CoreBluetooth
import Messages

class ConnectionManager: NSObject, ObservableObject {
    @Published var isConnected = false
    @Published var connectedPC = ""
    @Published var callsEnabled = false
    @Published var connectionStatus = "Disconnected"
    @Published var unisonProtocolActive = false
    
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession?
    private let encoder = JSONEncoder()
    private let decoder = JSONDecoder()
    
    // Network browser for discovering PCs
    private var browser: NWBrowser?
    private var discoveredPCs: [NWEndpoint] = []
    
    // Intel Unison++ Protocol Support
    private var centralManager: CBCentralManager?
    private var wifiDirectConnection: NWConnection?
    private var udpListener: NWListener?
    private var tcpListener: NWListener?
    
    // User's phone number for message sending
    private var phoneNumber: String?
    
    // Intel Unison protocol constants
    private let UNISON_SERVICE_UUID = CBUUID(string: "00001101-0000-1000-8000-00805f9b34fb")
    private let UNISON_CHARACTERISTIC_UUID = CBUUID(string: "00002a05-0000-1000-8000-00805f9b34fb")
    private let WIFI_DIRECT_PORT: UInt16 = 8888
    private let DISCOVERY_PORT: UInt16 = 8889
    
    override init() {
        super.init()
        setupURLSession()
        setupUnisonProtocol()
    }
    
    private func setupURLSession() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 60
        urlSession = URLSession(configuration: config)
    }
    
    private func setupUnisonProtocol() {
        // Initialize Bluetooth Central Manager
        centralManager = CBCentralManager(delegate: self, queue: nil)
        
        // Get user's phone number
        if let phoneNumber = getPhoneNumber() {
            self.phoneNumber = phoneNumber
            print("📱 User phone number: \(phoneNumber)")
        }
        
        // Start UDP discovery listener
        startUDPDiscoveryListener()
        
        // Start TCP server for WiFi Direct
        startTCPServer()
        
        // Broadcast discovery message
        broadcastDiscoveryMessage()
    }
    
    private func getPhoneNumber() -> String? {
        // Try to get phone number from various sources
        if let phoneNumber = UserDefaults.standard.string(forKey: "PhoneNumber") {
            return phoneNumber
        }
        
        // Could also try to detect from carrier info, but that's more complex
        return nil
    }
    
    // MARK: - Intel Unison Protocol Methods
    
    private func startUDPDiscoveryListener() {
        let parameters = NWParameters.udp
        parameters.allowLocalEndpointReuse = true
        
        do {
            udpListener = try NWListener(using: parameters, on: NWEndpoint.Port(integerLiteral: DISCOVERY_PORT))
            
            udpListener?.newConnectionHandler = { connection in
                connection.start(queue: .main)
                
                connection.receive(minimumIncompleteLength: 1, maximumLength: 1024) { data, _, isComplete, error in
                    if let data = data, !data.isEmpty {
                        self.handleDiscoveryMessage(data, from: connection)
                    }
                }
            }
            
            udpListener?.start(queue: .main)
            print("🔊 UDP Discovery listener started on port \(DISCOVERY_PORT)")
        } catch {
            print("❌ Failed to start UDP listener: \(error)")
        }
    }
    
    private func startTCPServer() {
        let parameters = NWParameters.tcp
        parameters.allowLocalEndpointReuse = true
        
        do {
            tcpListener = try NWListener(using: parameters, on: NWEndpoint.Port(integerLiteral: WIFI_DIRECT_PORT))
            
            tcpListener?.newConnectionHandler = { connection in
                print("📡 New WiFi Direct connection from PC")
                self.wifiDirectConnection = connection
                connection.start(queue: .main)
                
                // Send device info
                self.sendDeviceInfo(via: connection)
                
                // Start receiving messages
                self.receiveWiFiDirectMessage(connection)
                
                DispatchQueue.main.async {
                    self.unisonProtocolActive = true
                    self.connectionStatus = "Connected via WiFi Direct"
                    self.isConnected = true
                }
            }
            
            tcpListener?.start(queue: .main)
            print("🔊 TCP server started on port \(WIFI_DIRECT_PORT)")
        } catch {
            print("❌ Failed to start TCP server: \(error)")
        }
    }
    
    private func broadcastDiscoveryMessage() {
        let deviceInfo = UnisonDeviceInfo(
            type: "UNISON_IPHONE",
            name: UIDevice.current.name,
            version: "2.0",
            capabilities: ["messages", "calls", "notifications", "sync"],
            port: Int(WIFI_DIRECT_PORT)
        )
        
        guard let data = try? encoder.encode(deviceInfo) else { return }
        
        let connection = NWConnection(
            to: .hostPort(host: "***************", port: NWEndpoint.Port(integerLiteral: DISCOVERY_PORT)),
            using: .udp
        )
        
        connection.start(queue: .main)
        connection.send(content: data, completion: .contentProcessed { error in
            if let error = error {
                print("❌ Discovery broadcast failed: \(error)")
            } else {
                print("📡 Discovery message broadcast")
            }
            connection.cancel()
        })
    }
    
    private func handleDiscoveryMessage(_ data: Data, from connection: NWConnection) {
        guard let message = try? decoder.decode(UnisonDeviceInfo.self, from: data) else { return }
        
        if message.type == "UNISON_PC" {
            print("💻 Discovered PC: \(message.name)")
            
            // Connect to PC via WiFi Direct
            if let host = connection.endpoint.host {
                connectToPC(host: host, port: message.port ?? Int(WIFI_DIRECT_PORT))
            }
        }
    }
    
    private func connectToPC(host: NWEndpoint.Host, port: Int) {
        let connection = NWConnection(
            to: .hostPort(host: host, port: NWEndpoint.Port(integerLiteral: UInt16(port))),
            using: .tcp
        )
        
        connection.start(queue: .main)
        
        connection.stateUpdateHandler = { state in
            DispatchQueue.main.async {
                switch state {
                case .ready:
                    print("✅ Connected to PC via WiFi Direct")
                    self.wifiDirectConnection = connection
                    self.unisonProtocolActive = true
                    self.connectionStatus = "Connected via WiFi Direct"
                    self.isConnected = true
                    
                    // Send device info
                    self.sendDeviceInfo(via: connection)
                    
                    // Start receiving messages
                    self.receiveWiFiDirectMessage(connection)
                    
                case .failed(let error):
                    print("❌ WiFi Direct connection failed: \(error)")
                    self.connectionStatus = "Connection Failed"
                    
                default:
                    break
                }
            }
        }
    }
    
    private func sendDeviceInfo(via connection: NWConnection) {
        let deviceInfo = UnisonPhoneInfo(
            type: "phone_info",
            phoneNumber: phoneNumber,
            deviceName: UIDevice.current.name,
            systemVersion: UIDevice.current.systemVersion,
            capabilities: ["messages", "calls", "notifications", "sync"]
        )
        
        guard let data = try? encoder.encode(deviceInfo) else { return }
        
        connection.send(content: data, completion: .contentProcessed { error in
            if let error = error {
                print("❌ Failed to send device info: \(error)")
            } else {
                print("✅ Sent device info to PC")
            }
        })
    }
    
    private func receiveWiFiDirectMessage(_ connection: NWConnection) {
        connection.receive(minimumIncompleteLength: 1, maximumLength: 4096) { data, _, isComplete, error in
            if let data = data, !data.isEmpty {
                self.handleWiFiDirectMessage(data, connection: connection)
            }
            
            if !isComplete {
                self.receiveWiFiDirectMessage(connection)
            }
        }
    }
    
    private func handleWiFiDirectMessage(_ data: Data, connection: NWConnection) {
        guard let message = try? decoder.decode(UnisonMessage.self, from: data) else { return }
        
        DispatchQueue.main.async {
            switch message.type {
            case "send_message":
                if let phoneNumber = message.to, let text = message.text {
                    print("📤 Sending message via iPhone to \(phoneNumber): \(text)")
                    MessageBridge.shared.sendMessage(to: phoneNumber, text: text)
                }
                
            case "sync_request":
                print("🔄 Received sync request from PC")
                self.syncMessagesWithPC(connection: connection)
                
            default:
                print("Unknown WiFi Direct message type: \(message.type)")
            }
        }
    }
    
    private func syncMessagesWithPC(connection: NWConnection) {
        // Get recent messages from Messages app
        MessageBridge.shared.getRecentMessages { messages in
            let syncResponse = UnisonSyncResponse(
                type: "sync_response",
                messages: messages,
                timestamp: Date().timeIntervalSince1970
            )
            
            guard let data = try? self.encoder.encode(syncResponse) else { return }
            
            connection.send(content: data, completion: .contentProcessed { error in
                if let error = error {
                    print("❌ Failed to send sync response: \(error)")
                } else {
                    print("✅ Sent sync response to PC")
                }
            })
        }
    }
    
    // MARK: - Connection Methods
    
    func connectToPC(url: String) {
        guard let wsURL = URL(string: url) else {
            print("Invalid WebSocket URL")
            return
        }
        
        connectionStatus = "Connecting..."
        
        webSocketTask = urlSession?.webSocketTask(with: wsURL)
        webSocketTask?.resume()
        
        // Start listening for messages
        receiveMessage()
        
        // Send initial handshake
        sendHandshake()
        
        // Monitor connection
        monitorConnection()
    }
    
    func disconnect() {
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        
        DispatchQueue.main.async {
            self.isConnected = false
            self.connectedPC = ""
            self.connectionStatus = "Disconnected"
            self.callsEnabled = false
        }
    }
    
    // MARK: - Network Discovery
    
    func searchForPC() {
        let parameters = NWParameters()
        parameters.includePeerToPeer = true
        
        let browserDescriptor = NWBrowser.Descriptor.bonjourWithTXTRecord(
            type: "_iphone-companion._tcp",
            domain: nil
        )
        
        browser = NWBrowser(for: browserDescriptor, using: parameters)
        
        browser?.stateUpdateHandler = { state in
            switch state {
            case .ready:
                print("Browser ready")
            case .failed(let error):
                print("Browser failed: \(error)")
            default:
                break
            }
        }
        
        browser?.browseResultsChangedHandler = { results, changes in
            self.discoveredPCs = Array(results.map { $0.endpoint })
            
            // Auto-connect to first discovered PC
            if let firstPC = self.discoveredPCs.first,
               case .service(let name, let type, let domain, _) = firstPC {
                let url = "ws://\(name).\(domain):8080"
                self.connectToPC(url: url)
            }
        }
        
        browser?.start(queue: .main)
    }
    
    // MARK: - WebSocket Communication
    
    private func sendHandshake() {
        let handshake = HandshakeMessage(
            type: "handshake",
            deviceInfo: DeviceInfo(
                name: UIDevice.current.name,
                model: UIDevice.current.model,
                systemVersion: UIDevice.current.systemVersion,
                capabilities: ["messages", "calls", "notifications", "files"]
            )
        )
        
        sendMessage(handshake)
    }
    
    private func receiveMessage() {
        webSocketTask?.receive { [weak self] result in
            switch result {
            case .success(let message):
                switch message {
                case .string(let text):
                    self?.handleTextMessage(text)
                case .data(let data):
                    self?.handleDataMessage(data)
                @unknown default:
                    break
                }
                
                // Continue receiving
                self?.receiveMessage()
                
            case .failure(let error):
                print("WebSocket receive error: \(error)")
                DispatchQueue.main.async {
                    self?.connectionStatus = "Connection Error"
                    self?.isConnected = false
                }
            }
        }
    }
    
    private func handleTextMessage(_ text: String) {
        guard let data = text.data(using: .utf8) else { return }
        
        do {
            let message = try decoder.decode(PCMessage.self, from: data)
            handlePCMessage(message)
        } catch {
            print("Failed to decode message: \(error)")
        }
    }
    
    private func handleDataMessage(_ data: Data) {
        // Handle binary data if needed
        print("Received binary data: \(data.count) bytes")
    }
    
    private func handlePCMessage(_ message: PCMessage) {
        DispatchQueue.main.async {
            switch message.type {
            case "connection_confirmed":
                self.isConnected = true
                self.connectedPC = message.pcName ?? "Unknown PC"
                self.connectionStatus = "Connected"
                
            case "send_message":
                if let phoneNumber = message.phoneNumber,
                   let text = message.text {
                    MessageBridge.shared.sendMessage(to: phoneNumber, text: text)
                }
                
            case "unison_send_message":
                if let phoneNumber = message.phoneNumber,
                   let text = message.text {
                    print("📤 Unison Protocol: Sending message to \(phoneNumber)")
                    MessageBridge.shared.sendMessage(to: phoneNumber, text: text)
                }
                
            case "enable_calls":
                self.callsEnabled = true
                
            case "disable_calls":
                self.callsEnabled = false
                
            default:
                print("Unknown message type: \(message.type)")
            }
        }
    }
    
    func sendMessage<T: Codable>(_ message: T) {
        do {
            let data = try encoder.encode(message)
            let string = String(data: data, encoding: .utf8) ?? ""
            
            webSocketTask?.send(.string(string)) { error in
                if let error = error {
                    print("WebSocket send error: \(error)")
                }
            }
        } catch {
            print("Failed to encode message: \(error)")
        }
    }
    
    private func monitorConnection() {
        // Send ping every 30 seconds to keep connection alive
        Timer.scheduledTimer(withTimeInterval: 30, repeats: true) { _ in
            if self.isConnected {
                let ping = PingMessage(type: "ping", timestamp: Date().timeIntervalSince1970)
                self.sendMessage(ping)
            }
        }
    }
}

// MARK: - Message Types

struct HandshakeMessage: Codable {
    let type: String
    let deviceInfo: DeviceInfo
}

struct DeviceInfo: Codable {
    let name: String
    let model: String
    let systemVersion: String
    let capabilities: [String]
}

struct PCMessage: Codable {
    let type: String
    let pcName: String?
    let phoneNumber: String?
    let text: String?
    let timestamp: Double?
}

struct PingMessage: Codable {
    let type: String
    let timestamp: Double
}

// MARK: - Intel Unison Protocol Message Types

struct UnisonDeviceInfo: Codable {
    let type: String
    let name: String
    let version: String
    let capabilities: [String]
    let port: Int?
}

struct UnisonPhoneInfo: Codable {
    let type: String
    let phoneNumber: String?
    let deviceName: String
    let systemVersion: String
    let capabilities: [String]
}

struct UnisonMessage: Codable {
    let type: String
    let to: String?
    let text: String?
    let from: String?
    let timestamp: Double?
}

struct UnisonSyncResponse: Codable {
    let type: String
    let messages: [MessageData]
    let timestamp: Double
}

struct MessageData: Codable {
    let from: String
    let text: String
    let timestamp: Double
    let isOutgoing: Bool
}

// MARK: - Bluetooth LE Support

extension ConnectionManager: CBCentralManagerDelegate, CBPeripheralDelegate {
    
    func centralManagerDidUpdateState(_ central: CBCentralManager) {
        switch central.state {
        case .poweredOn:
            print("🔵 Bluetooth LE is ready")
            // Start advertising as Intel Unison device
            startBluetoothAdvertising()
            
        case .poweredOff:
            print("🔴 Bluetooth LE is powered off")
            
        case .unsupported:
            print("❌ Bluetooth LE not supported")
            
        default:
            print("⚠️ Bluetooth LE state: \(central.state.rawValue)")
        }
    }
    
    private func startBluetoothAdvertising() {
        // This would require implementing CBPeripheralManager
        // For now, we'll focus on the WiFi Direct connection
        print("📡 Would start Bluetooth LE advertising here")
    }
}
