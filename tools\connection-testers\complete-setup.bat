<<<<<<<
<<<<<<<



<<<<<<<















@echo off































































title iPhone Companion Pro - Complete Setup































































color 0A































































































































echo.































































echo ========================================































































echo  iPhone Companion Pro - Complete Setup































































echo ========================================































































echo.































































































































echo 🔥 Setting up EVERYTHING for iPhone integration...































































echo This will configure ALL connection methods!































































echo.































































































































REM Check admin privileges































































net session >nul 2>&1































































if %errorlevel% neq 0 (































































    echo ❌ This script requires administrator privileges































































    echo Right-click and select "Run as administrator"































































    pause































































    exit /b 1































































)































































































































echo ✅ Running with administrator privileges































































echo.































































































































REM Step 1: Install Node.js dependencies































































echo [1/7] Installing dependencies...































































if not exist node_modules (































































    npm install ws sqlite3 better-sqlite3 bonjour-service @abandonware/noble electron-store































































)































































echo ✅ Dependencies installed































































































































REM Step 2: Fix all connection issues































































echo.































































echo [2/7] Fixing connection issues...































































node fix-all-connections.js































































































































REM Step 3: Setup macOS VM































































echo.































































echo [3/7] Setting up macOS VM...































































choice /C YN /M "Do you want to set up macOS VM for full iPhone access"































































if %errorlevel% equ 1 (































































    call setup-macos-vm.bat































































)































































































































REM Step 4: Configure Windows services































































echo.































































echo [4/7] Configuring Windows services...































































































































REM Start required services































































echo Starting Bluetooth service...































































net start bthserv >nul 2>&1































































































































echo Starting Windows Audio service...































































net start audiosrv >nul 2>&1































































































































echo Starting SSDP Discovery service...































































net start SSDPSRV >nul 2>&1































































































































echo Starting UPnP Device Host...































































net start upnphost >nul 2>&1































































































































echo ✅ Windows services configured































































































































REM Step 5: Configure firewall































































echo.































































echo [5/7] Configuring firewall...































































































































REM Add firewall rules for all connection methods































































netsh advfirewall firewall add rule name="iPhone Companion AirPlay" dir=in action=allow protocol=TCP localport=7000 >nul 2>&1































































netsh advfirewall firewall add rule name="iPhone Companion Bonjour" dir=in action=allow protocol=UDP localport=5353 >nul 2>&1































































netsh advfirewall firewall add rule name="iPhone Companion WebSocket" dir=in action=allow protocol=TCP localport=8080 >nul 2>&1































































netsh advfirewall firewall add rule name="iPhone Companion VM Bridge" dir=in action=allow protocol=TCP localport=8888 >nul 2>&1































































netsh advfirewall firewall add rule name="iPhone Companion HTTP" dir=in action=allow protocol=TCP localport=3000 >nul 2>&1































































































































echo ✅ Firewall configured































































































































REM Step 6: Create startup scripts































































echo.































































echo [6/7] Creating startup scripts...































































































































REM Create enhanced startup script































































cat > start-iphone-companion.bat << 'EOF'































































@echo off































































title iPhone Companion Pro - Starting All Services































































color 0A































































































































echo 🔥 Starting iPhone Companion Pro with ALL connections...































































echo.































































































































REM Start VM if available































































if exist "C:\macOS-VM\start-macos-vm.bat" (































































    echo 🖥️ Starting macOS VM...































































    start /min "macOS VM" "C:\macOS-VM\start-macos-vm.bat"































































    timeout /t 10































































)































































































































REM Start Phone Link if available































































echo 📱 Starting Phone Link...































































start YourPhone: >nul 2>&1































































































































REM Start the main application































































echo 🚀 Starting iPhone Companion Pro...































































npm start































































































































pause































































EOF































































































































echo ✅ Startup scripts created































































































































REM Step 7: Final test































































echo.































































echo [7/7] Running final connection test...































































node complete-connection-test.js































































































































echo.































































echo ========================================































































echo  Setup Complete!































































echo ========================================































































echo.































































































































echo 🎉 iPhone Companion Pro is ready!































































echo.































































echo 📱 iPhone Setup Required:































































echo 1. Connect iPhone via USB cable































































echo 2. Tap "Trust This Computer" when prompted































































echo 3. Enable Personal Hotspot in Settings































































echo 4. Open Control Center and check Screen Mirroring































































echo 5. Look for "iPhone Companion Pro" in AirPlay list































































echo.































































































































echo 🚀 To start the application:































































echo   start-iphone-companion.bat































































echo.































































































































echo 🔧 If you need macOS VM:































































echo 1. Install VMware Workstation Pro































































echo 2. Create macOS Monterey VM































































echo 3. Run macos-vm-bridge.sh in the VM































































echo.































































































































echo 📊 Connection Methods Available:































































echo ✅ USB (via iTunes drivers)































































echo ✅ Phone Link (Windows built-in)































































echo ✅ AirPlay (wireless screen mirroring)































































echo ✅ Bluetooth (basic connection)































































echo ⚠️ macOS VM (requires setup)































































echo.































































































































choice /C YN /M "Start iPhone Companion Pro now"































































if %errorlevel% equ 1 (































































    start-iphone-companion.bat































































)































































































































pause































































=======















@echo off































title iPhone Companion Pro - Complete Setup































color 0A































































echo.































echo ========================================































echo  iPhone Companion Pro - Complete Setup































echo ========================================































echo.































































echo 🔥 Setting up EVERYTHING for iPhone integration...































echo This will configure ALL connection methods!































echo.































































REM Check admin privileges































net session >nul 2>&1































if %errorlevel% neq 0 (































    echo ❌ This script requires administrator privileges































    echo Right-click and select "Run as administrator"































    pause































    exit /b 1































)































































echo ✅ Running with administrator privileges































echo.































































REM Step 1: Install Node.js dependencies































echo [1/7] Installing dependencies...































if not exist node_modules (































    npm install ws sqlite3 better-sqlite3 bonjour-service @abandonware/noble electron-store































)































echo ✅ Dependencies installed































































REM Step 2: Fix all connection issues































echo.































echo [2/7] Fixing connection issues...































node fix-all-connections.js































































REM Step 3: Setup macOS VM































echo.































echo [3/7] Setting up macOS VM...































choice /C YN /M "Do you want to set up macOS VM for full iPhone access"































if %errorlevel% equ 1 (































    call setup-macos-vm.bat































)































































REM Step 4: Configure Windows services































echo.































echo [4/7] Configuring Windows services...































































REM Start required services































echo Starting Bluetooth service...































net start bthserv >nul 2>&1































































echo Starting Windows Audio service...































net start audiosrv >nul 2>&1































































echo Starting SSDP Discovery service...































net start SSDPSRV >nul 2>&1































































echo Starting UPnP Device Host...































net start upnphost >nul 2>&1































































echo ✅ Windows services configured































































REM Step 5: Configure firewall































echo.































echo [5/7] Configuring firewall...































































REM Add firewall rules for all connection methods































netsh advfirewall firewall add rule name="iPhone Companion AirPlay" dir=in action=allow protocol=TCP localport=7000 >nul 2>&1































netsh advfirewall firewall add rule name="iPhone Companion Bonjour" dir=in action=allow protocol=UDP localport=5353 >nul 2>&1































netsh advfirewall firewall add rule name="iPhone Companion WebSocket" dir=in action=allow protocol=TCP localport=8080 >nul 2>&1































netsh advfirewall firewall add rule name="iPhone Companion VM Bridge" dir=in action=allow protocol=TCP localport=8888 >nul 2>&1































netsh advfirewall firewall add rule name="iPhone Companion HTTP" dir=in action=allow protocol=TCP localport=3000 >nul 2>&1































































echo ✅ Firewall configured































































REM Step 6: Create startup scripts































echo.































echo [6/7] Creating startup scripts...































































REM Create enhanced startup script































cat > start-iphone-companion.bat << 'EOF'































@echo off































title iPhone Companion Pro - Starting All Services































color 0A































































echo 🔥 Starting iPhone Companion Pro with ALL connections...































echo.































































REM Start VM if available































if exist "C:\macOS-VM\start-macos-vm.bat" (































    echo 🖥️ Starting macOS VM...































    start /min "macOS VM" "C:\macOS-VM\start-macos-vm.bat"































    timeout /t 10































)































































REM Start Phone Link if available































echo 📱 Starting Phone Link...































start YourPhone: >nul 2>&1































































REM Start the main application































echo 🚀 Starting iPhone Companion Pro...































npm start































































pause































EOF































































echo ✅ Startup scripts created































































REM Step 7: Final test































echo.































echo [7/7] Running final connection test...































node complete-connection-test.js































































echo.































echo ========================================































echo  Setup Complete!































echo ========================================































echo.































































echo 🎉 iPhone Companion Pro is ready!































echo.































echo 📱 iPhone Setup Required:































echo 1. Connect iPhone via USB cable































echo 2. Tap "Trust This Computer" when prompted































echo 3. Enable Personal Hotspot in Settings































echo 4. Open Control Center and check Screen Mirroring































echo 5. Look for "iPhone Companion Pro" in AirPlay list































echo.































































echo 🚀 To start the application:































echo   start-iphone-companion.bat































echo.































































echo 🔧 If you need macOS VM:































echo 1. Install VMware Workstation Pro































echo 2. Create macOS Monterey VM































echo 3. Run macos-vm-bridge.sh in the VM































echo.































































echo 📊 Connection Methods Available:































echo ✅ USB (via iTunes drivers)































echo ✅ Phone Link (Windows built-in)































echo ✅ AirPlay (wireless screen mirroring)































echo ✅ Bluetooth (basic connection)































echo ⚠️ macOS VM (requires setup)































echo.































































choice /C YN /M "Start iPhone Companion Pro now"































if %errorlevel% equ 1 (































    start-iphone-companion.bat































)































































pause































>>>>>>>















=======



@echo off







title iPhone Companion Pro - Complete Setup







color 0A















echo.







echo ========================================







echo  iPhone Companion Pro - Complete Setup







echo ========================================







echo.















echo 🔥 Setting up EVERYTHING for iPhone integration...







echo This will configure ALL connection methods!







echo.















REM Check admin privileges







net session >nul 2>&1







if %errorlevel% neq 0 (







    echo ❌ This script requires administrator privileges







    echo Right-click and select "Run as administrator"







    pause







    exit /b 1







)















echo ✅ Running with administrator privileges







echo.















REM Step 1: Install Node.js dependencies







echo [1/7] Installing dependencies...







if not exist node_modules (







    npm install ws sqlite3 better-sqlite3 bonjour-service @abandonware/noble electron-store







)







echo ✅ Dependencies installed















REM Step 2: Fix all connection issues







echo.







echo [2/7] Fixing connection issues...







node fix-all-connections.js















REM Step 3: Setup macOS VM







echo.







echo [3/7] Setting up macOS VM...







choice /C YN /M "Do you want to set up macOS VM for full iPhone access"







if %errorlevel% equ 1 (







    call setup-macos-vm.bat







)















REM Step 4: Configure Windows services







echo.







echo [4/7] Configuring Windows services...















REM Start required services







echo Starting Bluetooth service...







net start bthserv >nul 2>&1















echo Starting Windows Audio service...







net start audiosrv >nul 2>&1















echo Starting SSDP Discovery service...







net start SSDPSRV >nul 2>&1















echo Starting UPnP Device Host...







net start upnphost >nul 2>&1















echo ✅ Windows services configured















REM Step 5: Configure firewall







echo.







echo [5/7] Configuring firewall...















REM Add firewall rules for all connection methods







netsh advfirewall firewall add rule name="iPhone Companion AirPlay" dir=in action=allow protocol=TCP localport=7000 >nul 2>&1







netsh advfirewall firewall add rule name="iPhone Companion Bonjour" dir=in action=allow protocol=UDP localport=5353 >nul 2>&1







netsh advfirewall firewall add rule name="iPhone Companion WebSocket" dir=in action=allow protocol=TCP localport=8080 >nul 2>&1







netsh advfirewall firewall add rule name="iPhone Companion VM Bridge" dir=in action=allow protocol=TCP localport=8888 >nul 2>&1







netsh advfirewall firewall add rule name="iPhone Companion HTTP" dir=in action=allow protocol=TCP localport=3000 >nul 2>&1















echo ✅ Firewall configured















REM Step 6: Create startup scripts







echo.







echo [6/7] Creating startup scripts...















REM Create enhanced startup script







cat > start-iphone-companion.bat << 'EOF'







@echo off







title iPhone Companion Pro - Starting All Services







color 0A















echo 🔥 Starting iPhone Companion Pro with ALL connections...







echo.















REM Start VM if available







if exist "C:\macOS-VM\start-macos-vm.bat" (







    echo 🖥️ Starting macOS VM...







    start /min "macOS VM" "C:\macOS-VM\start-macos-vm.bat"







    timeout /t 10







)















REM Start Phone Link if available







echo 📱 Starting Phone Link...







start YourPhone: >nul 2>&1















REM Start the main application







echo 🚀 Starting iPhone Companion Pro...







npm start















pause







EOF















echo ✅ Startup scripts created















REM Step 7: Final test







echo.







echo [7/7] Running final connection test...







node complete-connection-test.js















echo.







echo ========================================







echo  Setup Complete!







echo ========================================







echo.















echo 🎉 iPhone Companion Pro is ready!







echo.







echo 📱 iPhone Setup Required:







echo 1. Connect iPhone via USB cable







echo 2. Tap "Trust This Computer" when prompted







echo 3. Enable Personal Hotspot in Settings







echo 4. Open Control Center and check Screen Mirroring







echo 5. Look for "iPhone Companion Pro" in AirPlay list







echo.















echo 🚀 To start the application:







echo   start-iphone-companion.bat







echo.















echo 🔧 If you need macOS VM:







echo 1. Install VMware Workstation Pro







echo 2. Create macOS Monterey VM







echo 3. Run macos-vm-bridge.sh in the VM







echo.















echo 📊 Connection Methods Available:







echo ✅ USB (via iTunes drivers)







echo ✅ Phone Link (Windows built-in)







echo ✅ AirPlay (wireless screen mirroring)







echo ✅ Bluetooth (basic connection)







echo ⚠️ macOS VM (requires setup)







echo.















choice /C YN /M "Start iPhone Companion Pro now"







if %errorlevel% equ 1 (







    start-iphone-companion.bat







)















pause







>>>>>>>



=======
@echo off

title iPhone Companion Pro - Complete Setup

color 0A



echo.

echo ========================================

echo  iPhone Companion Pro - Complete Setup

echo ========================================

echo.



echo 🔥 Setting up EVERYTHING for iPhone integration...

echo This will configure ALL connection methods!

echo.



REM Check admin privileges

net session >nul 2>&1

if %errorlevel% neq 0 (

    echo ❌ This script requires administrator privileges

    echo Right-click and select "Run as administrator"

    pause

    exit /b 1

)



echo ✅ Running with administrator privileges

echo.



REM Step 1: Install Node.js dependencies

echo [1/7] Installing dependencies...

if not exist node_modules (

    npm install ws sqlite3 better-sqlite3 bonjour-service @abandonware/noble electron-store

)

echo ✅ Dependencies installed



REM Step 2: Fix all connection issues

echo.

echo [2/7] Fixing connection issues...

node fix-all-connections.js



REM Step 3: Setup macOS VM

echo.

echo [3/7] Setting up macOS VM...

choice /C YN /M "Do you want to set up macOS VM for full iPhone access"

if %errorlevel% equ 1 (

    call setup-macos-vm.bat

)



REM Step 4: Configure Windows services

echo.

echo [4/7] Configuring Windows services...



REM Start required services

echo Starting Bluetooth service...

net start bthserv >nul 2>&1



echo Starting Windows Audio service...

net start audiosrv >nul 2>&1



echo Starting SSDP Discovery service...

net start SSDPSRV >nul 2>&1



echo Starting UPnP Device Host...

net start upnphost >nul 2>&1



echo ✅ Windows services configured



REM Step 5: Configure firewall

echo.

echo [5/7] Configuring firewall...



REM Add firewall rules for all connection methods

netsh advfirewall firewall add rule name="iPhone Companion AirPlay" dir=in action=allow protocol=TCP localport=7000 >nul 2>&1

netsh advfirewall firewall add rule name="iPhone Companion Bonjour" dir=in action=allow protocol=UDP localport=5353 >nul 2>&1

netsh advfirewall firewall add rule name="iPhone Companion WebSocket" dir=in action=allow protocol=TCP localport=8080 >nul 2>&1

netsh advfirewall firewall add rule name="iPhone Companion VM Bridge" dir=in action=allow protocol=TCP localport=8888 >nul 2>&1

netsh advfirewall firewall add rule name="iPhone Companion HTTP" dir=in action=allow protocol=TCP localport=3000 >nul 2>&1



echo ✅ Firewall configured



REM Step 6: Create startup scripts

echo.

echo [6/7] Creating startup scripts...



REM Create enhanced startup script

cat > start-iphone-companion.bat << 'EOF'

@echo off

title iPhone Companion Pro - Starting All Services

color 0A



echo 🔥 Starting iPhone Companion Pro with ALL connections...

echo.



REM Start VM if available

if exist "C:\macOS-VM\start-macos-vm.bat" (

    echo 🖥️ Starting macOS VM...

    start /min "macOS VM" "C:\macOS-VM\start-macos-vm.bat"

    timeout /t 10

)



REM Start Phone Link if available

echo 📱 Starting Phone Link...

start YourPhone: >nul 2>&1



REM Start the main application

echo 🚀 Starting iPhone Companion Pro...

npm start



pause

EOF



echo ✅ Startup scripts created



REM Step 7: Final test

echo.

echo [7/7] Running final connection test...

node complete-connection-test.js



echo.

echo ========================================

echo  Setup Complete!

echo ========================================

echo.



echo 🎉 iPhone Companion Pro is ready!

echo.

echo 📱 iPhone Setup Required:

echo 1. Connect iPhone via USB cable

echo 2. Tap "Trust This Computer" when prompted

echo 3. Enable Personal Hotspot in Settings

echo 4. Open Control Center and check Screen Mirroring

echo 5. Look for "iPhone Companion Pro" in AirPlay list

echo.



echo 🚀 To start the application:

echo   start-iphone-companion.bat

echo.



echo 🔧 If you need macOS VM:

echo 1. Install VMware Workstation Pro

echo 2. Create macOS Monterey VM

echo 3. Run macos-vm-bridge.sh in the VM

echo.



echo 📊 Connection Methods Available:

echo ✅ USB (via iTunes drivers)

echo ✅ Phone Link (Windows built-in)

echo ✅ AirPlay (wireless screen mirroring)

echo ✅ Bluetooth (basic connection)

echo ⚠️ macOS VM (requires setup)

echo.



choice /C YN /M "Start iPhone Companion Pro now"

if %errorlevel% equ 1 (

    start-iphone-companion.bat

)



pause

>>>>>>>
