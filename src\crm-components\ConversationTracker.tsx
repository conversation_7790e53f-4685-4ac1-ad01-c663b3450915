import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  TrendingUp, 
  TrendingDown, 
  MessageSquare, 
  Phone, 
  Clock, 
  Target, 
  AlertTriangle, 
  CheckCircle,
  Bot,
  BarChart3,
  Users,
  Calendar,
  Filter
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';

interface ConversationMetrics {
  totalMessages: number;
  responseTime: number;
  sentimentScore: number;
  leadScore: number;
  lastActivity: Date;
  conversationStage: 'initial' | 'qualified' | 'negotiation' | 'closed';
  priority: 'high' | 'medium' | 'low';
}

interface ConversationInsight {
  phoneNumber: string;
  contactName: string;
  contactType: 'client' | 'lead';
  metrics: ConversationMetrics;
  recentMessages: Array<{
    id: string;
    content: string;
    direction: 'inbound' | 'outbound';
    timestamp: Date;
    sentiment: {
      score: number;
      label: 'positive' | 'negative' | 'neutral';
    };
    intent: {
      category: string;
      confidence: number;
    };
  }>;
  aiRecommendations: Array<{
    type: string;
    urgency: 'high' | 'medium' | 'low';
    message: string;
    action?: string;
  }>;
  timeline: Array<{
    event: string;
    timestamp: Date;
    details: string;
  }>;
}

interface ConversationTrackerProps {
  clientId?: string;
  leadId?: string;
  showAI?: boolean;
  height?: string;
}

export const ConversationTracker: React.FC<ConversationTrackerProps> = ({
  clientId,
  leadId,
  showAI = true,
  height = '800px'
}) => {
  const { user } = useUser();
  const [conversations, setConversations] = useState<ConversationInsight[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<ConversationInsight | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [filter, setFilter] = useState<'all' | 'high_priority' | 'needs_followup' | 'negative_sentiment'>('all');
  const [timeRange, setTimeRange] = useState<'today' | 'week' | 'month' | 'all'>('week');
  const [aggregateMetrics, setAggregateMetrics] = useState<any>(null);

  useEffect(() => {
    loadConversationData();
  }, [user, filter, timeRange]);

  const loadConversationData = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Load conversation insights
      const response = await fetch(`/api/conversations/insights?filter=${filter}&timeRange=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${user.id}`,
          'Content-Type': 'application/json'
        }
      });
      
      const data = await response.json();
      
      if (data.success) {
        setConversations(data.conversations);
        setAggregateMetrics(data.aggregateMetrics);
        
        // Auto-select first conversation if none selected
        if (!selectedConversation && data.conversations.length > 0) {
          setSelectedConversation(data.conversations[0]);
        }
      }
    } catch (error) {
      console.error('Error loading conversation data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getSentimentColor = (score: number) => {
    if (score > 0.5) return 'text-green-600';
    if (score < -0.5) return 'text-red-600';
    return 'text-gray-600';
  };

  const getLeadScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'initial': return 'bg-blue-100 text-blue-800';
      case 'qualified': return 'bg-purple-100 text-purple-800';
      case 'negotiation': return 'bg-orange-100 text-orange-800';
      case 'closed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderMetricsOverview = () => {
    if (!aggregateMetrics) return null;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Conversations</p>
                <p className="text-2xl font-bold">{aggregateMetrics.totalConversations}</p>
              </div>
              <MessageSquare className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Response Time</p>
                <p className="text-2xl font-bold">{Math.round(aggregateMetrics.avgResponseTime / 60)}m</p>
              </div>
              <Clock className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">High Priority</p>
                <p className="text-2xl font-bold">{aggregateMetrics.highPriorityCount}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Sentiment</p>
                <p className={`text-2xl font-bold ${getSentimentColor(aggregateMetrics.avgSentiment)}`}>
                  {aggregateMetrics.avgSentiment > 0 ? '+' : ''}{(aggregateMetrics.avgSentiment * 100).toFixed(0)}%
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderConversationList = () => (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Conversations</CardTitle>
          <div className="flex items-center space-x-2">
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="px-3 py-1 border rounded text-sm"
            >
              <option value="all">All</option>
              <option value="high_priority">High Priority</option>
              <option value="needs_followup">Needs Follow-up</option>
              <option value="negative_sentiment">Negative Sentiment</option>
            </select>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as any)}
              className="px-3 py-1 border rounded text-sm"
            >
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="all">All Time</option>
            </select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-96">
          <div className="space-y-3">
            {conversations.map((conversation) => (
              <div
                key={conversation.phoneNumber}
                className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                  selectedConversation?.phoneNumber === conversation.phoneNumber
                    ? 'bg-blue-50 border-blue-200'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => setSelectedConversation(conversation)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback>
                        {conversation.contactName.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div>
                      <h4 className="font-medium">{conversation.contactName}</h4>
                      <p className="text-sm text-gray-500">{conversation.phoneNumber}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className={getPriorityColor(conversation.metrics.priority)}>
                      {conversation.metrics.priority}
                    </Badge>
                    <Badge variant="outline" className={getStageColor(conversation.metrics.conversationStage)}>
                      {conversation.metrics.conversationStage}
                    </Badge>
                  </div>
                </div>
                
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span className="flex items-center space-x-1">
                      <MessageSquare className="h-4 w-4" />
                      <span>{conversation.metrics.totalMessages}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{Math.round(conversation.metrics.responseTime / 60)}m</span>
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center space-x-1">
                      <div className={`w-2 h-2 rounded-full ${getLeadScoreColor(conversation.metrics.leadScore)}`} />
                      <span className="text-sm">{conversation.metrics.leadScore}</span>
                    </div>
                    <span className="text-sm text-gray-500">
                      {formatDistanceToNow(conversation.metrics.lastActivity, { addSuffix: true })}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );

  const renderConversationDetails = () => {
    if (!selectedConversation) return null;

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Conversation Details</CardTitle>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Phone className="h-4 w-4 mr-2" />
                Call
              </Button>
              <Button variant="outline" size="sm">
                <MessageSquare className="h-4 w-4 mr-2" />
                Message
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="messages">Messages</TabsTrigger>
              <TabsTrigger value="ai-insights">AI Insights</TabsTrigger>
              <TabsTrigger value="timeline">Timeline</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Lead Score</label>
                  <div className="flex items-center space-x-2">
                    <Progress value={selectedConversation.metrics.leadScore} className="flex-1" />
                    <span className="text-sm font-medium">{selectedConversation.metrics.leadScore}</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Sentiment Score</label>
                  <div className="flex items-center space-x-2">
                    <Progress 
                      value={Math.abs(selectedConversation.metrics.sentimentScore) * 100} 
                      className="flex-1"
                    />
                    <span className={`text-sm font-medium ${getSentimentColor(selectedConversation.metrics.sentimentScore)}`}>
                      {(selectedConversation.metrics.sentimentScore * 100).toFixed(0)}%
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Response Time</label>
                  <p className="text-lg font-semibold">{Math.round(selectedConversation.metrics.responseTime / 60)} minutes</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Total Messages</label>
                  <p className="text-lg font-semibold">{selectedConversation.metrics.totalMessages}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Stage</label>
                  <Badge className={getStageColor(selectedConversation.metrics.conversationStage)}>
                    {selectedConversation.metrics.conversationStage}
                  </Badge>
                </div>
                <div>
                  <label className="text-sm font-medium">Priority</label>
                  <Badge className={getPriorityColor(selectedConversation.metrics.priority)}>
                    {selectedConversation.metrics.priority}
                  </Badge>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="messages" className="space-y-4">
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {selectedConversation.recentMessages.map((message) => (
                    <div
                      key={message.id}
                      className={`p-3 rounded-lg ${
                        message.direction === 'outbound' ? 'bg-blue-50 ml-12' : 'bg-gray-50 mr-12'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className={
                            message.direction === 'outbound' ? 'bg-blue-100' : 'bg-gray-100'
                          }>
                            {message.direction}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {format(message.timestamp, 'MMM d, HH:mm')}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <div className={`w-2 h-2 rounded-full ${
                            message.sentiment.label === 'positive' ? 'bg-green-500' :
                            message.sentiment.label === 'negative' ? 'bg-red-500' : 'bg-gray-500'
                          }`} />
                          <Badge variant="outline" className="text-xs">
                            {message.intent.category}
                          </Badge>
                        </div>
                      </div>
                      
                      <p className="text-sm">{message.content}</p>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
            
            <TabsContent value="ai-insights" className="space-y-4">
              <div className="space-y-3">
                <h4 className="font-medium flex items-center space-x-2">
                  <Bot className="h-4 w-4 text-blue-500" />
                  <span>AI Recommendations</span>
                </h4>
                
                {selectedConversation.aiRecommendations.map((recommendation, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg border-l-4 ${
                      recommendation.urgency === 'high' ? 'border-red-500 bg-red-50' :
                      recommendation.urgency === 'medium' ? 'border-yellow-500 bg-yellow-50' :
                      'border-green-500 bg-green-50'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="outline" className={
                        recommendation.urgency === 'high' ? 'bg-red-100 text-red-800' :
                        recommendation.urgency === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }>
                        {recommendation.urgency} priority
                      </Badge>
                      <span className="text-xs text-gray-500">{recommendation.type}</span>
                    </div>
                    
                    <p className="text-sm">{recommendation.message}</p>
                    
                    {recommendation.action && (
                      <Button variant="outline" size="sm" className="mt-2">
                        {recommendation.action}
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="timeline" className="space-y-4">
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {selectedConversation.timeline.map((event, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h5 className="font-medium">{event.event}</h5>
                          <span className="text-xs text-gray-500">
                            {format(event.timestamp, 'MMM d, HH:mm')}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">{event.details}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6" style={{ height }}>
      {renderMetricsOverview()}
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {renderConversationList()}
        {renderConversationDetails()}
      </div>
    </div>
  );
};

export default ConversationTracker;