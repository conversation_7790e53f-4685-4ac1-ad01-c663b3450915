/* 
 * INTEL UNISON++ <PERSON><PERSON><PERSON><PERSON> MESSAGING UI 
 * WhatsApp/Telegram inspired interface
 * 60fps smooth animations
 */

/* CSS Variables - Modern WhatsApp/Telegram theme */
:root {
  /* Primary Colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2a2a2a;
  --bg-quaternary: #3a3a3a;
  
  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-tertiary: #808080;
  --text-quaternary: #595959;
  
  /* Message Bubble Colors */
  --bubble-sent: #005c4b;
  --bubble-received: #262d31;
  --bubble-sent-hover: #006c5a;
  --bubble-received-hover: #2d3439;
  
  /* Accent Colors */
  --accent-primary: #00a884;
  --accent-secondary: #128c7e;
  --accent-tertiary: #25d366;
  --accent-blue: #0084ff;
  
  /* Status Colors */
  --status-online: #00d25b;
  --status-offline: #8696a0;
  --status-typing: #ffa500;
  --status-delivered: #4fc3f7;
  --status-read: #53bdeb;
  
  /* Borders and Shadows */
  --border-primary: #2a2a2a;
  --border-secondary: #404040;
  --shadow-primary: 0 2px 8px rgba(0, 0, 0, 0.3);
  --shadow-secondary: 0 4px 16px rgba(0, 0, 0, 0.2);
  --shadow-bubble: 0 1px 2px rgba(0, 0, 0, 0.15);
  
  /* Animations */
  --animation-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --animation-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --animation-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Measurements */
  --sidebar-width: 360px;
  --titlebar-height: 32px;
  --message-input-height: 60px;
  --conversation-header-height: 70px;
  --max-bubble-width: 460px;
  --border-radius: 12px;
  --border-radius-sm: 6px;
  --border-radius-lg: 18px;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  overflow: hidden;
  user-select: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--bg-tertiary);
  border-radius: 10px;
  transition: background var(--animation-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--bg-quaternary);
}

/* Title Bar */
.titlebar {
  height: var(--titlebar-height);
  background: var(--bg-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  border-bottom: 1px solid var(--border-primary);
  -webkit-app-region: drag;
}

.titlebar-title {
  flex: 1;
  display: flex;
  align-items: center;
  padding-left: 16px;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-primary);
}

.titlebar-controls {
  display: flex;
  -webkit-app-region: no-drag;
}

.titlebar-button {
  width: 46px;
  height: var(--titlebar-height);
  border: none;
  background: transparent;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--animation-fast);
}

.titlebar-button:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.titlebar-button:last-child:hover {
  background: #ff5f5f;
  color: white;
}

/* Main Messages Container */
.messages-container {
  display: flex;
  height: calc(100vh - var(--titlebar-height));
  margin-top: var(--titlebar-height);
  background: var(--bg-primary);
}

/* Conversations Panel (Left Sidebar) */
.conversations-panel {
  width: var(--sidebar-width);
  background: var(--bg-secondary);
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--border-primary);
  position: relative;
  overflow: hidden;
}

.conversations-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-secondary);
}

.conversations-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
}

.new-message-btn {
  width: 44px;
  height: 44px;
  border: none;
  background: var(--accent-primary);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--animation-fast);
  box-shadow: var(--shadow-primary);
}

.new-message-btn:hover {
  background: var(--accent-secondary);
  transform: scale(1.05);
}

.new-message-btn:active {
  transform: scale(0.95);
}

/* Search Box */
.search-box {
  padding: 12px 20px;
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.search-box input {
  width: 100%;
  padding: 12px 16px;
  background: var(--bg-tertiary);
  border: none;
  border-radius: var(--border-radius-sm);
  color: var(--text-primary);
  font-size: 14px;
  transition: all var(--animation-fast);
}

.search-box input::placeholder {
  color: var(--text-tertiary);
}

.search-box input:focus {
  outline: none;
  background: var(--bg-quaternary);
  box-shadow: 0 0 0 2px var(--accent-primary);
}

/* Conversations List */
.conversations-list {
  flex: 1;
  overflow-y: auto;
  background: var(--bg-secondary);
}

.conversation-item {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-primary);
  cursor: pointer;
  transition: all var(--animation-fast);
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.conversation-item:hover {
  background: var(--bg-tertiary);
}

.conversation-item.active {
  background: var(--accent-primary);
  color: white;
}

.conversation-item.active .conversation-info .conversation-name {
  color: white;
}

.conversation-item.active .conversation-info .conversation-preview {
  color: rgba(255, 255, 255, 0.8);
}

.conversation-item.active .conversation-meta .conversation-time {
  color: rgba(255, 255, 255, 0.8);
}

.conversation-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--accent-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: 600;
  flex-shrink: 0;
  position: relative;
}

.conversation-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.conversation-avatar::after {
  content: '';
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--status-online);
  border: 2px solid var(--bg-secondary);
}

.conversation-info {
  flex: 1;
  overflow: hidden;
}

.conversation-name {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-preview {
  font-size: 14px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 6px;
}

.conversation-time {
  font-size: 12px;
  color: var(--text-tertiary);
}

.conversation-badge {
  background: var(--accent-primary);
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

/* Message Thread Panel (Right Side) */
.message-thread-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  position: relative;
  overflow: hidden;
}

/* No Conversation State */
.no-conversation {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-primary);
}

.empty-state {
  text-align: center;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 20px;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.empty-state p {
  font-size: 14px;
  color: var(--text-tertiary);
}

/* Conversation View */
#conversation-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Conversation Header */
.conversation-header {
  height: var(--conversation-header-height);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  position: relative;
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.contact-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--accent-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.contact-details h3 {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.contact-details span {
  font-size: 13px;
  color: var(--text-secondary);
}

.contact-status {
  font-size: 12px;
  color: var(--status-online);
  margin-top: 2px;
}

.conversation-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--animation-fast);
}

.action-btn:hover {
  background: var(--bg-quaternary);
  color: var(--text-primary);
}

/* Messages List */
.messages-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  background: var(--bg-primary);
  scroll-behavior: smooth;
}

/* Message Bubbles */
.message-bubble {
  max-width: var(--max-bubble-width);
  padding: 8px 12px 6px 12px;
  border-radius: var(--border-radius);
  word-wrap: break-word;
  position: relative;
  margin-bottom: 2px;
  animation: messageSlideIn var(--animation-normal) ease-out;
  box-shadow: var(--shadow-bubble);
  transition: all var(--animation-fast);
}

.message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Sent Messages (Right aligned) */
.message-bubble.sent {
  background: var(--bubble-sent);
  color: white;
  align-self: flex-end;
  border-bottom-right-radius: 4px;
}

.message-bubble.sent:hover {
  background: var(--bubble-sent-hover);
}

/* Received Messages (Left aligned) */
.message-bubble.received {
  background: var(--bubble-received);
  color: var(--text-primary);
  align-self: flex-start;
  border-bottom-left-radius: 4px;
}

.message-bubble.received:hover {
  background: var(--bubble-received-hover);
}

/* Message Content */
.message-content {
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 4px;
  word-break: break-word;
}

/* Message Meta */
.message-meta {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 4px;
}

.message-bubble.received .message-meta {
  color: var(--text-tertiary);
}

.message-time {
  font-size: 11px;
  opacity: 0.8;
}

.message-status {
  display: flex;
  align-items: center;
  gap: 2px;
}

.message-status.delivered {
  color: var(--status-delivered);
}

.message-status.read {
  color: var(--status-read);
}

/* Message Status Icons */
.status-icon {
  width: 14px;
  height: 14px;
  fill: currentColor;
}

/* Date Divider */
.date-divider {
  text-align: center;
  padding: 8px 0;
  margin: 8px 0;
  position: relative;
}

.date-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--border-primary);
}

.date-divider-text {
  background: var(--bg-primary);
  padding: 0 12px;
  font-size: 12px;
  color: var(--text-tertiary);
  position: relative;
  z-index: 1;
}

/* Message Input Container */
.message-input-container {
  height: var(--message-input-height);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  padding: 8px 16px;
  gap: 12px;
}

.message-input-container input {
  flex: 1;
  padding: 12px 16px;
  background: var(--bg-tertiary);
  border: none;
  border-radius: var(--border-radius-lg);
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
  transition: all var(--animation-fast);
}

.message-input-container input::placeholder {
  color: var(--text-tertiary);
}

.message-input-container input:focus {
  background: var(--bg-quaternary);
  box-shadow: 0 0 0 2px var(--accent-primary);
}

.send-btn {
  width: 44px;
  height: 44px;
  border: none;
  background: var(--accent-primary);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--animation-fast);
  box-shadow: var(--shadow-primary);
}

.send-btn:hover {
  background: var(--accent-secondary);
  transform: scale(1.05);
}

.send-btn:active {
  transform: scale(0.95);
}

.send-btn:disabled {
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  cursor: not-allowed;
  transform: none;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 13px;
  color: var(--status-typing);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  animation: fadeIn var(--animation-normal) ease-out;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--status-typing);
  animation: typingPulse 1.5s ease-in-out infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

/* Animations */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes typingPulse {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  30% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Selection highlighting */
.message-bubble.selected {
  background: var(--accent-primary) !important;
  color: white !important;
}

/* Context menu */
.context-menu {
  position: absolute;
  background: var(--bg-quaternary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-secondary);
  z-index: 1000;
  min-width: 180px;
  animation: fadeIn var(--animation-fast) ease-out;
}

.context-menu-item {
  padding: 12px 16px;
  cursor: pointer;
  color: var(--text-primary);
  font-size: 14px;
  border-bottom: 1px solid var(--border-primary);
  transition: all var(--animation-fast);
}

.context-menu-item:hover {
  background: var(--bg-tertiary);
}

.context-menu-item:last-child {
  border-bottom: none;
}

.context-menu-item.danger {
  color: #ff5f5f;
}

.context-menu-item.danger:hover {
  background: rgba(255, 95, 95, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .conversations-panel {
    width: 100%;
    position: absolute;
    z-index: 100;
    transform: translateX(-100%);
    transition: transform var(--animation-normal);
  }
  
  .conversations-panel.mobile-open {
    transform: translateX(0);
  }
  
  .message-thread-panel {
    width: 100%;
  }
  
  .message-bubble {
    max-width: calc(100% - 80px);
  }
}

/* Performance optimizations */
.messages-list {
  contain: layout style paint;
  will-change: scroll-position;
}

.message-bubble {
  contain: layout style paint;
  will-change: transform, opacity;
}

.conversation-item {
  contain: layout style paint;
  will-change: background-color;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    --bg-primary: #000000;
    --bg-secondary: #1a1a1a;
    --text-primary: #ffffff;
    --border-primary: #ffffff;
    --accent-primary: #00ff00;
  }
}

/* Message Delete Button Styles */
.message-actions {
  position: absolute;
  top: 6px;
  right: 6px;
  opacity: 0;
  transition: opacity var(--animation-fast);
}

.message:hover .message-actions {
  opacity: 1;
}

.message-delete-btn {
  background: rgba(255, 59, 48, 0.8);
  border: none;
  border-radius: 4px;
  padding: 4px;
  cursor: pointer;
  color: white;
  transition: all var(--animation-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-delete-btn:hover {
  background: rgba(255, 59, 48, 1);
  transform: scale(1.1);
}

.message-delete-btn svg {
  transition: transform var(--animation-fast);
}

.message-delete-btn:hover svg {
  transform: scale(1.1);
}

/* Position message bubble relatively for delete button */
.message-bubble {
  position: relative;
}