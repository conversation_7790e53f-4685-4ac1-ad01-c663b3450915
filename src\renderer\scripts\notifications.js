const { ipc<PERSON><PERSON><PERSON> } = require('electron');

let notifications = [];
let filteredNotifications = [];
let currentFilter = 'all';
let selectedNotification = null;
let notificationSettings = {
    enableNotifications: true,
    showPreviews: true,
    playSounds: true,
    autoDismiss: false,
    dndEnabled: false,
    dndStart: '22:00',
    dndEnd: '07:00',
    appSettings: {}
};

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    loadNotifications();
    loadNotificationSettings();
    setupEventListeners();
    
    // Listen for new notifications
    ipcRenderer.on('new-notification', (event, notification) => {
        handleNewNotification(notification);
    });
    
    // Listen for notification updates
    ipcRenderer.on('notifications-updated', (event, data) => {
        notifications = data;
        applyCurrentFilter();
        updateNotificationCounts();
    });
    
    // Listen for notification settings updates
    ipcRenderer.on('notification-settings-updated', (event, settings) => {
        notificationSettings = settings;
        updateSettingsUI();
    });
});

function setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('notification-search');
    if (searchInput) {
        searchInput.addEventListener('input', searchNotifications);
    }
    
    // Keyboard shortcuts
    document.addEventListener('keydown', (event) => {
        if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
                case 'r':
                    event.preventDefault();
                    refreshNotifications();
                    break;
                case 'a':
                    event.preventDefault();
                    markAllAsRead();
                    break;
                case 'Delete':
                    event.preventDefault();
                    clearAllNotifications();
                    break;
            }
        }
    });
}

async function loadNotifications() {
    try {
        const result = await ipcRenderer.invoke('get-notifications');
        if (result.success) {
            notifications = result.data || [];
        } else {
            // Generate sample notifications for demo
            notifications = generateSampleNotifications();
        }
        
        applyCurrentFilter();
        updateNotificationCounts();
        
    } catch (error) {
        console.error('Failed to load notifications:', error);
        notifications = generateSampleNotifications();
        applyCurrentFilter();
        updateNotificationCounts();
    }
}

function generateSampleNotifications() {
    return [
        {
            id: '1',
            appName: 'Messages',
            appIcon: '💬',
            title: 'iPhone Contact',
            body: 'Hey, are you free for lunch today?',
            timestamp: Date.now() - 300000, // 5 minutes ago
            isRead: false,
            category: 'messages',
            actions: ['Reply', 'Mark as Read']
        },
        {
            id: '2',
            appName: 'Phone',
            appIcon: '📞',
            title: 'Missed Call',
            body: 'Sarah Wilson called you',
            timestamp: Date.now() - 900000, // 15 minutes ago
            isRead: false,
            category: 'calls',
            actions: ['Call Back', 'Message']
        },
        {
            id: '3',
            appName: 'Mail',
            appIcon: '📧',
            title: 'New Email',
            body: 'Meeting reminder: Team standup at 2 PM',
            timestamp: Date.now() - 1800000, // 30 minutes ago
            isRead: true,
            category: 'apps',
            actions: ['Reply', 'Archive']
        },
        {
            id: '4',
            appName: 'Calendar',
            appIcon: '📅',
            title: 'Upcoming Event',
            body: 'Doctor appointment in 1 hour',
            timestamp: Date.now() - 3600000, // 1 hour ago
            isRead: false,
            category: 'apps',
            actions: ['View', 'Snooze']
        },
        {
            id: '5',
            appName: 'Instagram',
            appIcon: '📷',
            title: 'New Follower',
            body: 'alex_photographer started following you',
            timestamp: Date.now() - 7200000, // 2 hours ago
            isRead: true,
            category: 'apps',
            actions: ['View Profile', 'Follow Back']
        },
        {
            id: '6',
            appName: 'Messages',
            appIcon: '💬',
            title: 'Family Group',
            body: 'Mom: Don\'t forget about dinner on Sunday!',
            timestamp: Date.now() - 10800000, // 3 hours ago
            isRead: false,
            category: 'messages',
            actions: ['Reply', 'Mark as Read']
        }
    ];
}

function renderNotifications() {
    const container = document.getElementById('notifications-list');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (filteredNotifications.length === 0) {
        container.innerHTML = `
            <div class="empty-notifications">
                <div class="empty-icon">🔔</div>
                <h3>No notifications</h3>
                <p>You're all caught up!</p>
            </div>
        `;
        return;
    }
    
    // Group notifications by date
    const groupedNotifications = groupNotificationsByDate(filteredNotifications);
    
    Object.entries(groupedNotifications).forEach(([date, notifs]) => {
        // Add date header
        const dateHeader = document.createElement('div');
        dateHeader.className = 'date-header';
        dateHeader.innerHTML = `<h3>${date}</h3>`;
        container.appendChild(dateHeader);
        
        // Add notifications for this date
        notifs.forEach(notification => {
            const notificationElement = createNotificationElement(notification);
            container.appendChild(notificationElement);
        });
    });
}

function createNotificationElement(notification) {
    const div = document.createElement('div');
    div.className = `notification-item ${notification.isRead ? 'read' : 'unread'}`;
    div.dataset.notificationId = notification.id;
    
    div.innerHTML = `
        <div class="notification-icon">
            <span class="app-icon">${notification.appIcon}</span>
            ${!notification.isRead ? '<div class="unread-indicator"></div>' : ''}
        </div>
        <div class="notification-content">
            <div class="notification-header">
                <span class="app-name">${escapeHtml(notification.appName)}</span>
                <span class="notification-time">${formatTime(notification.timestamp)}</span>
            </div>
            <div class="notification-title">${escapeHtml(notification.title)}</div>
            <div class="notification-body">${escapeHtml(notification.body)}</div>
        </div>
        <div class="notification-actions">
            <button class="action-btn small" onclick="viewNotificationDetails('${notification.id}')" title="View Details">
                👁️
            </button>
            <button class="action-btn small" onclick="toggleNotificationRead('${notification.id}')" title="${notification.isRead ? 'Mark as Unread' : 'Mark as Read'}">
                ${notification.isRead ? '📭' : '📬'}
            </button>
            <button class="action-btn small" onclick="deleteNotificationItem('${notification.id}')" title="Delete">
                🗑️
            </button>
        </div>
    `;
    
    // Add click handler for the main notification area
    const contentArea = div.querySelector('.notification-content');
    contentArea.addEventListener('click', () => {
        viewNotificationDetails(notification.id);
    });
    
    return div;
}

function groupNotificationsByDate(notifications) {
    const groups = {};
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    notifications.forEach(notification => {
        const notificationDate = new Date(notification.timestamp);
        let dateKey;
        
        if (notificationDate.toDateString() === today.toDateString()) {
            dateKey = 'Today';
        } else if (notificationDate.toDateString() === yesterday.toDateString()) {
            dateKey = 'Yesterday';
        } else {
            dateKey = notificationDate.toLocaleDateString();
        }
        
        if (!groups[dateKey]) {
            groups[dateKey] = [];
        }
        groups[dateKey].push(notification);
    });
    
    return groups;
}

function filterNotifications(filter) {
    currentFilter = filter;
    
    // Update filter button states
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
    
    applyCurrentFilter();
}

function applyCurrentFilter() {
    switch (currentFilter) {
        case 'all':
            filteredNotifications = [...notifications];
            break;
        case 'unread':
            filteredNotifications = notifications.filter(n => !n.isRead);
            break;
        case 'messages':
            filteredNotifications = notifications.filter(n => n.category === 'messages');
            break;
        case 'calls':
            filteredNotifications = notifications.filter(n => n.category === 'calls');
            break;
        case 'apps':
            filteredNotifications = notifications.filter(n => n.category === 'apps');
            break;
        default:
            filteredNotifications = [...notifications];
    }
    
    // Sort by timestamp (newest first)
    filteredNotifications.sort((a, b) => b.timestamp - a.timestamp);
    
    renderNotifications();
}

function searchNotifications() {
    const searchTerm = document.getElementById('notification-search').value.toLowerCase();
    
    if (!searchTerm) {
        applyCurrentFilter();
        return;
    }
    
    filteredNotifications = notifications.filter(notification => 
        notification.title.toLowerCase().includes(searchTerm) ||
        notification.body.toLowerCase().includes(searchTerm) ||
        notification.appName.toLowerCase().includes(searchTerm)
    );
    
    renderNotifications();
}

function updateNotificationCounts() {
    const counts = {
        all: notifications.length,
        unread: notifications.filter(n => !n.isRead).length,
        messages: notifications.filter(n => n.category === 'messages').length,
        calls: notifications.filter(n => n.category === 'calls').length,
        apps: notifications.filter(n => n.category === 'apps').length
    };
    
    Object.entries(counts).forEach(([filter, count]) => {
        const countElement = document.getElementById(`count-${filter}`);
        if (countElement) {
            countElement.textContent = count;
        }
    });
}

function viewNotificationDetails(notificationId) {
    const notification = notifications.find(n => n.id === notificationId);
    if (!notification) return;
    
    selectedNotification = notification;
    
    const modal = document.getElementById('notification-details-modal');
    const content = document.getElementById('notification-details-content');
    
    content.innerHTML = `
        <div class="notification-details-header">
            <div class="app-info">
                <span class="app-icon-large">${notification.appIcon}</span>
                <div class="app-details">
                    <h3>${escapeHtml(notification.appName)}</h3>
                    <p class="notification-timestamp">${formatFullTime(notification.timestamp)}</p>
                </div>
            </div>
            <div class="notification-status">
                <span class="status-badge ${notification.isRead ? 'read' : 'unread'}">
                    ${notification.isRead ? 'Read' : 'Unread'}
                </span>
            </div>
        </div>
        <div class="notification-details-body">
            <h4>${escapeHtml(notification.title)}</h4>
            <p>${escapeHtml(notification.body)}</p>
        </div>
        <div class="notification-details-actions">
            ${notification.actions.map(action => 
                `<button class="action-btn secondary" onclick="performNotificationAction('${action}')">${action}</button>`
            ).join('')}
        </div>
    `;
    
    modal.style.display = 'flex';
    
    // Mark as read when viewed
    if (!notification.isRead) {
        toggleNotificationRead(notificationId);
    }
}

function closeNotificationDetailsModal() {
    document.getElementById('notification-details-modal').style.display = 'none';
    selectedNotification = null;
}

function performNotificationAction(action) {
    if (!selectedNotification) return;
    
    switch (action) {
        case 'Reply':
            openQuickReply();
            break;
        case 'Mark as Read':
            toggleNotificationRead(selectedNotification.id);
            closeNotificationDetailsModal();
            break;
        case 'Call Back':
            ipcRenderer.invoke('make-call', {
                phoneNumber: selectedNotification.phoneNumber || 'Unknown',
                contactName: selectedNotification.title
            });
            break;
        case 'Message':
            ipcRenderer.invoke('open-messages');
            break;
        default:
            console.log(`Performing action: ${action}`);
    }
}

function openQuickReply() {
    if (!selectedNotification) return;
    
    const modal = document.getElementById('quick-reply-modal');
    const context = document.getElementById('reply-context');
    
    context.innerHTML = `
        <div class="reply-notification">
            <span class="app-icon">${selectedNotification.appIcon}</span>
            <div class="reply-details">
                <strong>${escapeHtml(selectedNotification.title)}</strong>
                <p>${escapeHtml(selectedNotification.body)}</p>
            </div>
        </div>
    `;
    
    modal.style.display = 'flex';
    document.getElementById('reply-text').focus();
}

function closeQuickReplyModal() {
    document.getElementById('quick-reply-modal').style.display = 'none';
    document.getElementById('reply-text').value = '';
}

function sendQuickReply() {
    const replyText = document.getElementById('reply-text').value.trim();
    if (!replyText || !selectedNotification) return;
    
    // Send reply through appropriate channel
    if (selectedNotification.category === 'messages') {
        ipcRenderer.invoke('send-message', {
            phoneNumber: selectedNotification.phoneNumber || 'Unknown',
            text: replyText
        });
    }
    
    closeQuickReplyModal();
    closeNotificationDetailsModal();
}

function toggleNotificationRead(notificationId) {
    const notification = notifications.find(n => n.id === notificationId);
    if (!notification) return;
    
    notification.isRead = !notification.isRead;
    
    // Update UI
    applyCurrentFilter();
    updateNotificationCounts();
    
    // Send to main process
    ipcRenderer.invoke('update-notification-status', {
        id: notificationId,
        isRead: notification.isRead
    });
}

function deleteNotificationItem(notificationId) {
    notifications = notifications.filter(n => n.id !== notificationId);
    applyCurrentFilter();
    updateNotificationCounts();
    
    // Send to main process
    ipcRenderer.invoke('delete-notification', { id: notificationId });
}

async function refreshNotifications() {
    const refreshBtn = document.querySelector('[onclick="refreshNotifications()"]');
    if (refreshBtn) {
        refreshBtn.innerHTML = '<span>🔄</span> Refreshing...';
        refreshBtn.disabled = true;
    }
    
    await loadNotifications();
    
    if (refreshBtn) {
        refreshBtn.innerHTML = '<span>🔄</span> Refresh';
        refreshBtn.disabled = false;
    }
}

function markAllAsRead() {
    notifications.forEach(notification => {
        notification.isRead = true;
    });
    
    applyCurrentFilter();
    updateNotificationCounts();
    
    // Send to main process
    ipcRenderer.invoke('mark-all-notifications-read');
}

function clearAllNotifications() {
    if (confirm('Are you sure you want to clear all notifications?')) {
        notifications = [];
        applyCurrentFilter();
        updateNotificationCounts();
        
        // Send to main process
        ipcRenderer.invoke('clear-all-notifications');
    }
}

function handleNewNotification(notification) {
    // Add to notifications array
    notifications.unshift(notification);
    
    // Update UI
    applyCurrentFilter();
    updateNotificationCounts();
    
    // Show Windows notification if enabled
    if (notificationSettings.enableNotifications && Notification.permission === 'granted') {
        new Notification(notification.title, {
            body: notification.body,
            icon: '../../assets/icon.png',
            tag: notification.id
        });
    }
    
    // Play sound if enabled
    if (notificationSettings.playSounds) {
        // Play notification sound
        const audio = new Audio('../../assets/notification.mp3');
        audio.play().catch(() => {
            // Ignore audio play errors
        });
    }
}

// Notification Settings
function openNotificationSettings() {
    loadNotificationSettings();
    document.getElementById('notification-settings-modal').style.display = 'flex';
}

function closeNotificationSettingsModal() {
    document.getElementById('notification-settings-modal').style.display = 'none';
}

function loadNotificationSettings() {
    // Load settings from storage or use defaults
    const savedSettings = localStorage.getItem('notificationSettings');
    if (savedSettings) {
        notificationSettings = { ...notificationSettings, ...JSON.parse(savedSettings) };
    }
    
    updateSettingsUI();
}

function updateSettingsUI() {
    document.getElementById('enable-notifications').checked = notificationSettings.enableNotifications;
    document.getElementById('show-previews').checked = notificationSettings.showPreviews;
    document.getElementById('play-sounds').checked = notificationSettings.playSounds;
    document.getElementById('auto-dismiss').checked = notificationSettings.autoDismiss;
    document.getElementById('dnd-enabled').checked = notificationSettings.dndEnabled;
    document.getElementById('dnd-start').value = notificationSettings.dndStart;
    document.getElementById('dnd-end').value = notificationSettings.dndEnd;
}

function saveNotificationSettings() {
    // Collect settings from UI
    notificationSettings.enableNotifications = document.getElementById('enable-notifications').checked;
    notificationSettings.showPreviews = document.getElementById('show-previews').checked;
    notificationSettings.playSounds = document.getElementById('play-sounds').checked;
    notificationSettings.autoDismiss = document.getElementById('auto-dismiss').checked;
    notificationSettings.dndEnabled = document.getElementById('dnd-enabled').checked;
    notificationSettings.dndStart = document.getElementById('dnd-start').value;
    notificationSettings.dndEnd = document.getElementById('dnd-end').value;
    
    // Save to storage
    localStorage.setItem('notificationSettings', JSON.stringify(notificationSettings));
    
    // Send to main process
    ipcRenderer.invoke('update-notification-settings', notificationSettings);
    
    closeNotificationSettingsModal();
}

function resetNotificationSettings() {
    if (confirm('Reset all notification settings to default?')) {
        notificationSettings = {
            enableNotifications: true,
            showPreviews: true,
            playSounds: true,
            autoDismiss: false,
            dndEnabled: false,
            dndStart: '22:00',
            dndEnd: '07:00',
            appSettings: {}
        };
        
        updateSettingsUI();
        saveNotificationSettings();
    }
}

// Utility functions
function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'Now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    
    return date.toLocaleDateString();
}

function formatFullTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString();
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
