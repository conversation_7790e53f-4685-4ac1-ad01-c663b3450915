const { exec } = require('child_process');
const { EventEmitter } = require('events');

class USBConnection extends EventEmitter {
  constructor() {
    super();
    this.connectedDevices = [];
    this.isMonitoring = false;
  }

  async detectiPhone() {
    console.log('🔍 Scanning for iPhone via USB...');

    return new Promise((resolve) => {
      // Use Windows WMI to detect Apple devices
      const wmiCmd = `
        wmic path Win32_PnPEntity where "Name like '%Apple%' OR Name like '%iPhone%'" get Name,Status,DeviceID
      `;

      exec(wmiCmd, (err, stdout) => {
        if (!err && stdout.includes('Apple')) {
          console.log('✅ Apple device detected via USB!');
          console.log(stdout);

          // Try to get device info with libimobiledevice
          exec('idevice_id -l', (err2, stdout2) => {
            if (!err2 && stdout2.trim()) {
              const deviceId = stdout2.trim();
              console.log('📱 Device UDID:', deviceId);

              // Get device info
              exec(`ideviceinfo -u ${deviceId}`, (err3, stdout3) => {
                if (!err3) {
                  console.log('📋 Device info retrieved');
                  this.emit('connected', { id: deviceId, info: stdout3 });
                }
              });

              resolve(deviceId);
            } else {
              console.log('⚠️ libimobiledevice not found - install for full USB support');
              resolve(null);
            }
          });
        } else {
          console.log('❌ No iPhone detected via USB');
          console.log('   Make sure iPhone is connected and "Trust This Computer" is accepted');
          resolve(null);
        }
      });
    });
  }

  startMonitoring() {
    if (!this.isMonitoring) {
      this.isMonitoring = true;
      console.log('👀 Starting USB device monitoring...');

      // Poll for device changes every 5 seconds
      setInterval(() => {
        this.detectiPhone();
      }, 5000);
    }
  }

  stopMonitoring() {
    this.isMonitoring = false;
  }
}

module.exports = { USBConnection };