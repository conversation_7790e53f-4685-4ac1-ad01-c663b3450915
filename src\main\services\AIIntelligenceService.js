const { EventEmitter } = require('events');
const winston = require('winston');
const axios = require('axios');

/**
 * AIIntelligenceService - AI-powered message intelligence and business insights
 * Provides sentiment analysis, lead scoring, conversation intelligence, and predictive analytics
 */
class AIIntelligenceService extends EventEmitter {
  constructor(persistence, messageService) {
    super();
    this.persistence = persistence;
    this.messageService = messageService;
    this.isInitialized = false;
    this.aiModels = {
      sentiment: null,
      leadScoring: null,
      intentRecognition: null,
      conversationSummarization: null
    };
    
    // AI-powered insights cache
    this.insightsCache = new Map();
    this.leadScores = new Map();
    this.conversationInsights = new Map();
    
    // Configure logger
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ level, message, timestamp }) => {
          return `${timestamp} [${level.toUpperCase()}] AIIntelligence: ${message}`;
        })
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'ai-intelligence.log' })
      ]
    });

    // AI processing rules
    this.processingRules = {
      sentiment: {
        positive: ['great', 'excellent', 'love', 'amazing', 'perfect', 'thank you', 'yes', 'interested'],
        negative: ['bad', 'terrible', 'hate', 'awful', 'disappointed', 'no', 'cancel', 'stop', 'unsubscribe'],
        urgent: ['urgent', 'asap', 'immediately', 'emergency', 'critical', 'help'],
        buying: ['buy', 'purchase', 'order', 'price', 'cost', 'quote', 'proposal', 'contract']
      },
      leadQuality: {
        hot: ['ready to buy', 'when can we start', 'send contract', 'lets do this', 'how much', 'budget'],
        warm: ['interested', 'tell me more', 'send info', 'call me', 'schedule', 'meeting'],
        cold: ['maybe', 'thinking', 'consider', 'later', 'not now', 'busy']
      },
      intent: {
        sales: ['buy', 'purchase', 'price', 'cost', 'quote', 'proposal', 'demo'],
        support: ['help', 'problem', 'issue', 'broken', 'error', 'bug', 'support'],
        billing: ['invoice', 'payment', 'bill', 'charge', 'refund', 'account'],
        information: ['how', 'what', 'when', 'where', 'why', 'info', 'details']
      }
    };
  }

  async initialize() {
    this.logger.info('🤖 INITIALIZING AI INTELLIGENCE SERVICE 🤖');
    
    // Initialize AI models (using local processing for privacy)
    await this.initializeAIModels();
    
    // Set up real-time message processing
    this.setupMessageProcessing();
    
    // Load historical data for training
    await this.loadHistoricalData();
    
    this.isInitialized = true;
    this.logger.info('✅ AI Intelligence Service initialized');
  }

  async initializeAIModels() {
    // Initialize lightweight AI models for real-time processing
    // In a production environment, you might use TensorFlow.js or similar
    
    this.aiModels.sentiment = {
      analyze: (text) => this.analyzeSentiment(text),
      confidence: 0.85
    };
    
    this.aiModels.leadScoring = {
      score: (conversation) => this.calculateLeadScore(conversation),
      model: 'behavioral_scoring_v1'
    };
    
    this.aiModels.intentRecognition = {
      classify: (text) => this.classifyIntent(text),
      categories: ['sales', 'support', 'billing', 'information']
    };
    
    this.aiModels.conversationSummarization = {
      summarize: (messages) => this.summarizeConversation(messages),
      maxLength: 200
    };
    
    this.logger.info('🧠 AI models initialized');
  }

  setupMessageProcessing() {
    // Process messages in real-time as they arrive
    this.messageService.on('message-received', async (message) => {
      await this.processMessageIntelligence(message);
    });
    
    this.messageService.on('message-sent', async (message) => {
      await this.processMessageIntelligence(message);
    });
  }

  async loadHistoricalData() {
    try {
      // Load historical messages for pattern analysis
      const messages = await this.persistence.loadAllMessages(1000);
      
      // Build conversation patterns
      const conversations = this.groupMessagesByConversation(messages);
      
      // Analyze patterns for better AI training
      for (const [phoneNumber, convMessages] of conversations) {
        const insights = await this.analyzeConversationPattern(convMessages);
        this.conversationInsights.set(phoneNumber, insights);
      }
      
      this.logger.info(`📊 Analyzed ${conversations.size} conversations for AI training`);
    } catch (error) {
      this.logger.error(`❌ Error loading historical data: ${error.message}`);
    }
  }

  async processMessageIntelligence(message) {
    try {
      const phoneNumber = message.phoneNumber;
      const messageText = message.messageText || message.text;
      
      // Sentiment Analysis
      const sentiment = await this.analyzeSentiment(messageText);
      
      // Intent Recognition
      const intent = await this.classifyIntent(messageText);
      
      // Lead Scoring Update
      await this.updateLeadScore(phoneNumber, message, sentiment, intent);
      
      // Conversation Intelligence
      await this.updateConversationIntelligence(phoneNumber, message, sentiment, intent);
      
      // Store AI insights
      await this.storeAIInsights(message.id, {
        sentiment,
        intent,
        messageText,
        timestamp: message.timestamp,
        phoneNumber
      });
      
      // Emit intelligence events
      this.emit('intelligence-processed', {
        messageId: message.id,
        phoneNumber,
        sentiment,
        intent,
        leadScore: this.leadScores.get(phoneNumber)
      });
      
    } catch (error) {
      this.logger.error(`❌ Error processing message intelligence: ${error.message}`);
    }
  }

  async analyzeSentiment(text) {
    if (!text) return { score: 0, label: 'neutral', confidence: 0 };
    
    const textLower = text.toLowerCase();
    const words = textLower.split(/\s+/);
    
    let positiveScore = 0;
    let negativeScore = 0;
    let urgencyScore = 0;
    let buyingScore = 0;
    
    // Analyze sentiment using keyword matching
    words.forEach(word => {
      if (this.processingRules.sentiment.positive.includes(word)) {
        positiveScore++;
      }
      if (this.processingRules.sentiment.negative.includes(word)) {
        negativeScore++;
      }
      if (this.processingRules.sentiment.urgent.includes(word)) {
        urgencyScore++;
      }
      if (this.processingRules.sentiment.buying.includes(word)) {
        buyingScore++;
      }
    });
    
    // Calculate overall sentiment
    const totalScore = positiveScore - negativeScore;
    let label = 'neutral';
    let score = 0;
    
    if (totalScore > 0) {
      label = 'positive';
      score = Math.min(totalScore / words.length * 10, 1);
    } else if (totalScore < 0) {
      label = 'negative';
      score = Math.max(totalScore / words.length * 10, -1);
    }
    
    return {
      score,
      label,
      confidence: 0.8,
      details: {
        positive: positiveScore,
        negative: negativeScore,
        urgent: urgencyScore > 0,
        buying_intent: buyingScore > 0
      }
    };
  }

  async classifyIntent(text) {
    if (!text) return { category: 'unknown', confidence: 0 };
    
    const textLower = text.toLowerCase();
    const words = textLower.split(/\s+/);
    
    const intentScores = {};
    
    // Calculate scores for each intent category
    Object.entries(this.processingRules.intent).forEach(([category, keywords]) => {
      let score = 0;
      keywords.forEach(keyword => {
        if (textLower.includes(keyword)) {
          score += 1;
        }
      });
      intentScores[category] = score;
    });
    
    // Find the highest scoring intent
    const topIntent = Object.entries(intentScores)
      .sort(([,a], [,b]) => b - a)[0];
    
    return {
      category: topIntent[1] > 0 ? topIntent[0] : 'general',
      confidence: topIntent[1] / words.length,
      scores: intentScores
    };
  }

  async updateLeadScore(phoneNumber, message, sentiment, intent) {
    const currentScore = this.leadScores.get(phoneNumber) || {
      score: 50, // Start at neutral
      factors: {},
      lastUpdated: Date.now(),
      history: []
    };
    
    let scoreChange = 0;
    const factors = { ...currentScore.factors };
    
    // Sentiment impact
    if (sentiment.label === 'positive') {
      scoreChange += 10;
      factors.sentiment = 'positive';
    } else if (sentiment.label === 'negative') {
      scoreChange -= 5;
      factors.sentiment = 'negative';
    }
    
    // Intent impact
    if (intent.category === 'sales') {
      scoreChange += 15;
      factors.sales_intent = true;
    } else if (intent.category === 'support') {
      scoreChange += 5;
      factors.support_engagement = true;
    }
    
    // Urgency impact
    if (sentiment.details.urgent) {
      scoreChange += 20;
      factors.urgency = true;
    }
    
    // Buying intent impact
    if (sentiment.details.buying_intent) {
      scoreChange += 25;
      factors.buying_signals = true;
    }
    
    // Response time impact (if outgoing message)
    if (!message.isIncoming) {
      const responseTime = this.calculateResponseTime(phoneNumber);
      if (responseTime < 300000) { // 5 minutes
        scoreChange += 5;
        factors.quick_response = true;
      }
    }
    
    // Update score
    const newScore = Math.max(0, Math.min(100, currentScore.score + scoreChange));
    
    const updatedScore = {
      score: newScore,
      factors,
      lastUpdated: Date.now(),
      history: [...currentScore.history, {
        timestamp: Date.now(),
        change: scoreChange,
        reason: this.getScoreChangeReason(scoreChange, sentiment, intent),
        messageId: message.id
      }].slice(-10) // Keep last 10 changes
    };
    
    this.leadScores.set(phoneNumber, updatedScore);
    
    // Emit score change event
    this.emit('lead-score-updated', {
      phoneNumber,
      oldScore: currentScore.score,
      newScore,
      change: scoreChange,
      factors
    });
  }

  async updateConversationIntelligence(phoneNumber, message, sentiment, intent) {
    const currentInsights = this.conversationInsights.get(phoneNumber) || {
      totalMessages: 0,
      sentimentTrend: [],
      intentDistribution: {},
      keyTopics: [],
      responsePattern: [],
      lastActivity: null
    };
    
    // Update metrics
    currentInsights.totalMessages++;
    currentInsights.lastActivity = message.timestamp;
    
    // Update sentiment trend
    currentInsights.sentimentTrend.push({
      timestamp: message.timestamp,
      score: sentiment.score,
      label: sentiment.label
    });
    
    // Keep only last 20 sentiment readings
    if (currentInsights.sentimentTrend.length > 20) {
      currentInsights.sentimentTrend.shift();
    }
    
    // Update intent distribution
    if (!currentInsights.intentDistribution[intent.category]) {
      currentInsights.intentDistribution[intent.category] = 0;
    }
    currentInsights.intentDistribution[intent.category]++;
    
    // Extract key topics (simple keyword extraction)
    const topics = this.extractKeyTopics(message.messageText || message.text);
    topics.forEach(topic => {
      if (!currentInsights.keyTopics.includes(topic)) {
        currentInsights.keyTopics.push(topic);
      }
    });
    
    // Update response pattern
    currentInsights.responsePattern.push({
      timestamp: message.timestamp,
      isIncoming: message.isIncoming,
      length: (message.messageText || message.text).length
    });
    
    // Keep only last 50 messages for pattern analysis
    if (currentInsights.responsePattern.length > 50) {
      currentInsights.responsePattern.shift();
    }
    
    this.conversationInsights.set(phoneNumber, currentInsights);
  }

  async storeAIInsights(messageId, insights) {
    try {
      // Store in database for historical analysis
      await this.persistence.run(`
        INSERT OR REPLACE INTO ai_insights (
          message_id, sentiment_score, sentiment_label, intent_category, 
          phone_number, processed_at, raw_insights
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        messageId,
        insights.sentiment.score,
        insights.sentiment.label,
        insights.intent.category,
        insights.phoneNumber,
        Date.now(),
        JSON.stringify(insights)
      ]);
    } catch (error) {
      // Table might not exist, create it
      await this.createAIInsightsTable();
      await this.storeAIInsights(messageId, insights);
    }
  }

  async createAIInsightsTable() {
    await this.persistence.run(`
      CREATE TABLE IF NOT EXISTS ai_insights (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        message_id TEXT NOT NULL,
        sentiment_score REAL,
        sentiment_label TEXT,
        intent_category TEXT,
        phone_number TEXT,
        processed_at INTEGER,
        raw_insights TEXT,
        created_at INTEGER DEFAULT (strftime('%s', 'now'))
      )
    `);
    
    await this.persistence.run(`
      CREATE INDEX IF NOT EXISTS idx_ai_insights_phone ON ai_insights(phone_number)
    `);
  }

  // Business Intelligence Methods
  async generateBusinessInsights() {
    const insights = {
      leadScoring: await this.getLeadScoringInsights(),
      sentimentAnalysis: await this.getSentimentInsights(),
      conversationMetrics: await this.getConversationMetrics(),
      predictiveAnalytics: await this.getPredictiveInsights(),
      recommendations: await this.getActionableRecommendations()
    };
    
    return insights;
  }

  async getLeadScoringInsights() {
    const scores = Array.from(this.leadScores.entries());
    
    const hotLeads = scores.filter(([_, score]) => score.score >= 80);
    const warmLeads = scores.filter(([_, score]) => score.score >= 60 && score.score < 80);
    const coldLeads = scores.filter(([_, score]) => score.score < 60);
    
    return {
      total: scores.length,
      hot: hotLeads.length,
      warm: warmLeads.length,
      cold: coldLeads.length,
      averageScore: scores.reduce((sum, [_, score]) => sum + score.score, 0) / scores.length,
      topLeads: hotLeads.slice(0, 10).map(([phone, score]) => ({ phone, score: score.score })),
      scoringFactors: this.getTopScoringFactors()
    };
  }

  async getSentimentInsights() {
    const allInsights = Array.from(this.conversationInsights.values());
    
    const totalSentiments = allInsights.reduce((acc, insight) => {
      insight.sentimentTrend.forEach(s => {
        acc[s.label] = (acc[s.label] || 0) + 1;
      });
      return acc;
    }, {});
    
    const avgSentiment = allInsights.reduce((sum, insight) => {
      const avgScore = insight.sentimentTrend.reduce((s, t) => s + t.score, 0) / insight.sentimentTrend.length;
      return sum + (avgScore || 0);
    }, 0) / allInsights.length;
    
    return {
      distribution: totalSentiments,
      averageScore: avgSentiment,
      trendAnalysis: this.calculateSentimentTrend()
    };
  }

  async getConversationMetrics() {
    const conversations = Array.from(this.conversationInsights.values());
    
    return {
      totalConversations: conversations.length,
      averageMessagesPerConversation: conversations.reduce((sum, c) => sum + c.totalMessages, 0) / conversations.length,
      intentDistribution: this.aggregateIntentDistribution(conversations),
      responseTimeAnalysis: this.calculateResponseTimeMetrics(conversations),
      topTopics: this.getTopTopics(conversations)
    };
  }

  async getPredictiveInsights() {
    // Simple predictive analytics based on patterns
    const leadScores = Array.from(this.leadScores.values());
    const conversations = Array.from(this.conversationInsights.values());
    
    return {
      likelyConversions: this.predictConversions(leadScores, conversations),
      churningContacts: this.predictChurn(conversations),
      optimalContactTimes: this.findOptimalContactTimes(conversations),
      nextBestActions: this.suggestNextActions(leadScores, conversations)
    };
  }

  async getActionableRecommendations() {
    const recommendations = [];
    
    // High-priority leads
    const hotLeads = Array.from(this.leadScores.entries())
      .filter(([_, score]) => score.score >= 80)
      .slice(0, 5);
    
    hotLeads.forEach(([phone, score]) => {
      recommendations.push({
        priority: 'high',
        type: 'contact_immediately',
        contact: phone,
        reason: `High lead score (${score.score}) - contact immediately`,
        action: 'Call or send personalized message'
      });
    });
    
    // Negative sentiment alerts
    const negativeContacts = Array.from(this.conversationInsights.entries())
      .filter(([_, insights]) => {
        const recentSentiment = insights.sentimentTrend.slice(-3);
        return recentSentiment.every(s => s.label === 'negative');
      });
    
    negativeContacts.forEach(([phone, insights]) => {
      recommendations.push({
        priority: 'medium',
        type: 'customer_service',
        contact: phone,
        reason: 'Consistently negative sentiment detected',
        action: 'Schedule customer service call'
      });
    });
    
    return recommendations;
  }

  // Helper methods
  groupMessagesByConversation(messages) {
    const conversations = new Map();
    
    messages.forEach(message => {
      const phoneNumber = message.phoneNumber;
      if (!conversations.has(phoneNumber)) {
        conversations.set(phoneNumber, []);
      }
      conversations.get(phoneNumber).push(message);
    });
    
    return conversations;
  }

  calculateResponseTime(phoneNumber) {
    const insights = this.conversationInsights.get(phoneNumber);
    if (!insights || insights.responsePattern.length < 2) return 0;
    
    const recent = insights.responsePattern.slice(-10);
    const responseTimes = [];
    
    for (let i = 1; i < recent.length; i++) {
      if (recent[i-1].isIncoming && !recent[i].isIncoming) {
        responseTimes.push(recent[i].timestamp - recent[i-1].timestamp);
      }
    }
    
    return responseTimes.length > 0 ? 
      responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : 0;
  }

  getScoreChangeReason(change, sentiment, intent) {
    if (change > 0) {
      if (sentiment.details.buying_intent) return 'Buying intent detected';
      if (sentiment.details.urgent) return 'Urgent message';
      if (intent.category === 'sales') return 'Sales inquiry';
      return 'Positive engagement';
    } else {
      return 'Negative sentiment';
    }
  }

  extractKeyTopics(text) {
    if (!text) return [];
    
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3);
    
    // Simple keyword extraction (in production, use more sophisticated NLP)
    const commonWords = ['this', 'that', 'with', 'have', 'will', 'from', 'they', 'know', 'want', 'been', 'good', 'much', 'some', 'time', 'very', 'when', 'come', 'here', 'just', 'like', 'long', 'make', 'many', 'over', 'such', 'take', 'than', 'them', 'well', 'were'];
    
    return words
      .filter(word => !commonWords.includes(word))
      .slice(0, 5);
  }

  getTopScoringFactors() {
    const factors = {};
    
    Array.from(this.leadScores.values()).forEach(score => {
      Object.keys(score.factors).forEach(factor => {
        factors[factor] = (factors[factor] || 0) + 1;
      });
    });
    
    return Object.entries(factors)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([factor, count]) => ({ factor, count }));
  }

  calculateSentimentTrend() {
    // Calculate sentiment trend over time
    const allTrends = Array.from(this.conversationInsights.values())
      .map(insight => insight.sentimentTrend)
      .flat()
      .sort((a, b) => a.timestamp - b.timestamp);
    
    return allTrends.slice(-30); // Last 30 sentiment readings
  }

  aggregateIntentDistribution(conversations) {
    const distribution = {};
    
    conversations.forEach(conv => {
      Object.entries(conv.intentDistribution).forEach(([intent, count]) => {
        distribution[intent] = (distribution[intent] || 0) + count;
      });
    });
    
    return distribution;
  }

  calculateResponseTimeMetrics(conversations) {
    const responseTimes = [];
    
    conversations.forEach(conv => {
      const avgResponseTime = this.calculateResponseTime(conv.phoneNumber);
      if (avgResponseTime > 0) {
        responseTimes.push(avgResponseTime);
      }
    });
    
    return {
      average: responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length,
      median: responseTimes.sort()[Math.floor(responseTimes.length / 2)],
      fastest: Math.min(...responseTimes),
      slowest: Math.max(...responseTimes)
    };
  }

  getTopTopics(conversations) {
    const topicCounts = {};
    
    conversations.forEach(conv => {
      conv.keyTopics.forEach(topic => {
        topicCounts[topic] = (topicCounts[topic] || 0) + 1;
      });
    });
    
    return Object.entries(topicCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .map(([topic, count]) => ({ topic, count }));
  }

  predictConversions(leadScores, conversations) {
    return leadScores
      .filter(score => score.score >= 70)
      .sort((a, b) => b.score - a.score)
      .slice(0, 10)
      .map(score => ({
        phoneNumber: score.phoneNumber,
        conversionProbability: (score.score / 100) * 0.8, // 80% max probability
        score: score.score,
        factors: score.factors
      }));
  }

  predictChurn(conversations) {
    const now = Date.now();
    const weekAgo = now - (7 * 24 * 60 * 60 * 1000);
    
    return conversations
      .filter(conv => conv.lastActivity < weekAgo)
      .filter(conv => conv.totalMessages > 5)
      .sort((a, b) => a.lastActivity - b.lastActivity)
      .slice(0, 10)
      .map(conv => ({
        phoneNumber: conv.phoneNumber,
        daysSinceLastContact: Math.floor((now - conv.lastActivity) / (24 * 60 * 60 * 1000)),
        totalMessages: conv.totalMessages,
        churnRisk: conv.lastActivity < (now - 14 * 24 * 60 * 60 * 1000) ? 'high' : 'medium'
      }));
  }

  findOptimalContactTimes(conversations) {
    const hourCounts = new Array(24).fill(0);
    
    conversations.forEach(conv => {
      conv.responsePattern.forEach(pattern => {
        const hour = new Date(pattern.timestamp).getHours();
        hourCounts[hour]++;
      });
    });
    
    const optimalHours = hourCounts
      .map((count, hour) => ({ hour, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);
    
    return optimalHours;
  }

  suggestNextActions(leadScores, conversations) {
    const actions = [];
    
    // High-value leads needing immediate attention
    Array.from(this.leadScores.entries())
      .filter(([_, score]) => score.score >= 80)
      .forEach(([phone, score]) => {
        actions.push({
          phoneNumber: phone,
          action: 'immediate_followup',
          priority: 'high',
          reason: `Lead score: ${score.score}`,
          suggestedMessage: this.generateSuggestedMessage(phone, score)
        });
      });
    
    return actions;
  }

  generateSuggestedMessage(phoneNumber, leadScore) {
    const insights = this.conversationInsights.get(phoneNumber);
    
    if (leadScore.factors.buying_signals) {
      return "I noticed you were interested in our pricing. Would you like to schedule a quick call to discuss your specific needs?";
    } else if (leadScore.factors.urgency) {
      return "I understand this is urgent for you. Let me prioritize this and get back to you within the hour.";
    } else if (insights && insights.intentDistribution.sales) {
      return "Based on our conversation, I think we can help you achieve your goals. Would you like to see a personalized demo?";
    } else {
      return "Hope you're doing well! I wanted to follow up on our recent conversation. Do you have any questions I can help with?";
    }
  }

  // Public API methods
  async getLeadScore(phoneNumber) {
    return this.leadScores.get(phoneNumber) || { score: 50, factors: {}, lastUpdated: null };
  }

  async getConversationInsights(phoneNumber) {
    return this.conversationInsights.get(phoneNumber) || null;
  }

  async getAIInsights(phoneNumber) {
    const leadScore = await this.getLeadScore(phoneNumber);
    const convInsights = await this.getConversationInsights(phoneNumber);
    
    return {
      leadScore,
      conversationInsights: convInsights,
      recommendations: await this.getPersonalizedRecommendations(phoneNumber)
    };
  }

  async getPersonalizedRecommendations(phoneNumber) {
    const leadScore = await this.getLeadScore(phoneNumber);
    const insights = await this.getConversationInsights(phoneNumber);
    
    const recommendations = [];
    
    if (leadScore.score >= 80) {
      recommendations.push({
        type: 'contact_immediately',
        urgency: 'high',
        message: 'This is a hot lead - contact immediately!'
      });
    }
    
    if (insights && insights.sentimentTrend.slice(-3).every(s => s.label === 'negative')) {
      recommendations.push({
        type: 'customer_service',
        urgency: 'medium',
        message: 'Recent negative sentiment - consider customer service outreach'
      });
    }
    
    return recommendations;
  }

  getServiceStatus() {
    return {
      isInitialized: this.isInitialized,
      modelsLoaded: Object.keys(this.aiModels).length,
      insightsCached: this.insightsCache.size,
      leadScoresTracked: this.leadScores.size,
      conversationsAnalyzed: this.conversationInsights.size
    };
  }
}

module.exports = { AIIntelligenceService };