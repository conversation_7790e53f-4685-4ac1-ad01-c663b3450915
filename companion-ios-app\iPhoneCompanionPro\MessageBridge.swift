import Foundation
import Messages
import Contacts
import Combine

class MessageBridge: NSObject, ObservableObject {
    static let shared = MessageBridge()
    
    @Published var isEnabled = false
    @Published var messageCount = 0
    @Published var conversations: [ConversationData] = []
    
    private var connectionManager: ConnectionManager?
    private let contactStore = CNContactStore()
    
    override init() {
        super.init()
        requestPermissions()
    }
    
    func setConnectionManager(_ manager: ConnectionManager) {
        self.connectionManager = manager
    }
    
    // MARK: - Permissions
    
    private func requestPermissions() {
        // Request contacts permission
        contactStore.requestAccess(for: .contacts) { granted, error in
            if granted {
                print("Contacts permission granted")
            } else {
                print("Contacts permission denied: \(error?.localizedDescription ?? "Unknown error")")
            }
        }
        
        // Note: iOS doesn't allow direct access to Messages app
        // This would require a Messages app extension or user interaction
        // For now, we'll simulate the capability
        DispatchQueue.main.async {
            self.isEnabled = true
        }
    }
    
    // MARK: - Message Handling
    
    func sendMessage(to phoneNumber: String, text: String) {
        print("📤 Intel Unison++: Sending message to \(phoneNumber): \(text)")
        
        // Use iOS Shortcuts integration for legitimate message sending
        triggerMessageShortcut(phoneNumber: phoneNumber, text: text)
        
        // Also try webhook method for Intel Unison++ protocol
        sendViaWebhook(phoneNumber: phoneNumber, text: text)
        
        // Notify that message was queued for sending
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.notifyMessageSent(phoneNumber: phoneNumber, text: text)
        }
    }
    
    private func sendViaWebhook(phoneNumber: String, text: String) {
        // Send to Intel Unison++ webhook endpoint
        guard let url = URL(string: "http://YOUR_PC_IP:7778/shortcuts/send-message") else { return }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let payload = [
            "phoneNumber": phoneNumber,
            "message": text,
            "userPhone": getUserPhoneNumber() ?? "unknown",
            "timestamp": Date().timeIntervalSince1970
        ] as [String: Any]
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: payload)
            
            URLSession.shared.dataTask(with: request) { data, response, error in
                if let error = error {
                    print("❌ Webhook send failed: \(error)")
                } else {
                    print("✅ Message sent via webhook")
                }
            }.resume()
        } catch {
            print("❌ Failed to encode webhook payload: \(error)")
        }
    }
    
    private func getUserPhoneNumber() -> String? {
        // Try to get user's phone number from various sources
        return UserDefaults.standard.string(forKey: "PhoneNumber")
    }

    private func triggerMessageShortcut(phoneNumber: String, text: String) {
        // Create URL scheme to trigger iOS Shortcut for Intel Unison++
        let shortcutName = "IntelUnisonSendMessage"
        let encodedText = text.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let encodedNumber = phoneNumber.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""

        let shortcutURL = "shortcuts://run-shortcut?name=\(shortcutName)&input=text&text=\(encodedNumber)|\(encodedText)"

        if let url = URL(string: shortcutURL) {
            DispatchQueue.main.async {
                UIApplication.shared.open(url) { success in
                    if success {
                        print("✅ Successfully triggered Intel Unison++ message shortcut")
                    } else {
                        print("❌ Failed to trigger message shortcut - Intel Unison++ shortcut may not be installed")
                        self.showShortcutInstallationPrompt()
                    }
                }
            }
        }
    }

    private func showShortcutInstallationPrompt() {
        // Notify the UI to show Intel Unison++ shortcut installation instructions
        DispatchQueue.main.async {
            NotificationCenter.default.post(
                name: NSNotification.Name("ShowShortcutInstallation"),
                object: nil,
                userInfo: ["type": "intel_unison_message"]
            )
        }
    }
    
    // MARK: - Intel Unison++ Protocol Methods
    
    func getRecentMessages(completion: @escaping ([MessageData]) -> Void) {
        // Since we can't directly access Messages app, we'll return simulated data
        // In a real implementation, this would interface with Messages app extension
        
        let mockMessages = [
            MessageData(
                from: "+1234567890",
                text: "Hello from iPhone!",
                timestamp: Date().timeIntervalSince1970 - 3600,
                isOutgoing: false
            ),
            MessageData(
                from: "+1234567891",
                text: "This is a test message",
                timestamp: Date().timeIntervalSince1970 - 1800,
                isOutgoing: true
            )
        ]
        
        completion(mockMessages)
    }
    
    func startMessageMonitoring() {
        // Start monitoring for new messages
        // This would require a Messages app extension in a real implementation
        print("📱 Started Intel Unison++ message monitoring")
        
        // Simulate receiving messages
        Timer.scheduledTimer(withTimeInterval: 30, repeats: true) { _ in
            self.simulateIncomingMessage()
        }
    }
    
    private func simulateIncomingMessage() {
        // This simulates receiving a message for testing
        let message = MessageData(
            from: "+1234567890",
            text: "Test message from iPhone at \(Date())",
            timestamp: Date().timeIntervalSince1970,
            isOutgoing: false
        )
        
        // Notify PC via webhook
        sendMessageToPC(message: message)
    }
    
    private func sendMessageToPC(message: MessageData) {
        guard let url = URL(string: "http://YOUR_PC_IP:7778/shortcuts/receive-message") else { return }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let payload = [
            "from": message.from,
            "text": message.text,
            "timestamp": message.timestamp,
            "contactName": getContactName(for: message.from) ?? "Unknown"
        ] as [String: Any]
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: payload)
            
            URLSession.shared.dataTask(with: request) { data, response, error in
                if let error = error {
                    print("❌ Failed to send message to PC: \(error)")
                } else {
                    print("✅ Message sent to PC via webhook")
                }
            }.resume()
        } catch {
            print("❌ Failed to encode message payload: \(error)")
        }
    }
    
    private func getContactName(for phoneNumber: String) -> String? {
        // Try to get contact name from phone number
        let keys = [CNContactGivenNameKey, CNContactFamilyNameKey]
        let request = CNContactFetchRequest(keysToFetch: keys as [CNKeyDescriptor])
        
        do {
            try contactStore.enumerateContacts(with: request) { contact, _ in
                for phone in contact.phoneNumbers {
                    if phone.value.stringValue == phoneNumber {
                        return contact.givenName + " " + contact.familyName
                    }
                }
            }
        } catch {
            print("Failed to fetch contacts: \(error)")
        }
        
        return nil
    }
    
    private func notifyMessageSent(phoneNumber: String, text: String) {
        let message = OutgoingMessage(
            type: "message_sent",
            phoneNumber: phoneNumber,
            text: text,
            timestamp: Date().timeIntervalSince1970,
            messageId: UUID().uuidString
        )
        
        connectionManager?.sendMessage(message)
    }
    
    // MARK: - Conversation Management
    
    func loadConversations() {
        // Load real conversations from Messages database
        // NO MORE SAMPLE DATA - only real iPhone messages

        DispatchQueue.main.async {
            self.conversations = []
            self.messageCount = 0
        }

        // Try to load real conversations via shortcuts or Messages framework
        loadRealConversations()
    }

    private func loadRealConversations() {
        // This will be implemented to access real Messages data
        // For now, conversations remain empty until real data is available
        print("Waiting for real iPhone message data...")
    }
    
    func getContactName(for phoneNumber: String) -> String {
        let request = CNContactFetchRequest(keysToFetch: [CNContactPhoneNumbersKey, CNContactGivenNameKey, CNContactFamilyNameKey])
        
        do {
            try contactStore.enumerateContacts(with: request) { contact, _ in
                for phoneNumberValue in contact.phoneNumbers {
                    let contactPhoneNumber = phoneNumberValue.value.stringValue
                    if contactPhoneNumber.contains(phoneNumber) || phoneNumber.contains(contactPhoneNumber) {
                        let fullName = "\(contact.givenName) \(contact.familyName)".trimmingCharacters(in: .whitespaces)
                        return fullName.isEmpty ? phoneNumber : fullName
                    }
                }
            }
        } catch {
            print("Error fetching contacts: \(error)")
        }
        
        return phoneNumber
    }
    
    // MARK: - Message Monitoring
    
    func startMessageMonitoring() {
        // Set up iOS Shortcuts automation for message monitoring
        setupShortcutAutomation()

        // Start local HTTP server to receive shortcut data
        startShortcutReceiver()

        // Real message monitoring - no more demo timers
        startRealMessageMonitoring()
    }

    private func setupShortcutAutomation() {
        // This will guide users to set up automation shortcuts
        print("Setting up iOS Shortcuts automation for message monitoring")

        // Post notification to show setup instructions
        DispatchQueue.main.async {
            NotificationCenter.default.post(
                name: NSNotification.Name("ShowShortcutSetup"),
                object: nil,
                userInfo: ["type": "automation"]
            )
        }
    }

    private func startShortcutReceiver() {
        // Start local server to receive data from iOS Shortcuts
        // This would be implemented using a simple HTTP server
        // that shortcuts can POST to with new message data

        let port = 8888
        print("Starting shortcut receiver on port \(port)")

        // In a real implementation, you'd start an HTTP server here
        // that iOS Shortcuts can send data to when new messages arrive
    }

    private func startRealMessageMonitoring() {
        // Monitor for real iPhone messages - no more demo messages
        print("Starting real message monitoring...")
        // This will be implemented to monitor actual Messages app
    }

    private func checkForNewMessages() {
        // NO MORE DEMO MESSAGES - only process real iPhone data
        // This function now waits for real message notifications
    }
    
    // MARK: - Integration with Messages App
    
    func openMessagesApp(for phoneNumber: String) {
        // Open Messages app to specific conversation
        let urlString = "sms:\(phoneNumber)"
        if let url = URL(string: urlString) {
            UIApplication.shared.open(url)
        }
    }
    
    func composeMessage(to phoneNumber: String, text: String) {
        // This would typically use MFMessageComposeViewController
        // For demonstration, we'll just open Messages app
        openMessagesApp(for: phoneNumber)
    }
}

// MARK: - Message Data Types

struct ConversationData: Identifiable, Codable {
    let id = UUID()
    let phoneNumber: String
    let contactName: String
    let lastMessage: String
    let timestamp: Date
    let unreadCount: Int
}

struct IncomingMessage: Codable {
    let type: String
    let phoneNumber: String
    let contactName: String
    let text: String
    let timestamp: Double
    let messageId: String
}

struct OutgoingMessage: Codable {
    let type: String
    let phoneNumber: String
    let text: String
    let timestamp: Double
    let messageId: String
}

// MARK: - Messages App Extension Helper

/*
 For full Messages integration, you would need to create a Messages App Extension:
 
 1. Add a new target: Messages Extension
 2. Implement MSMessagesAppViewController
 3. Handle message composition and sending
 4. Sync with main app via App Groups
 
 Example structure:
 
 class MessagesViewController: MSMessagesAppViewController {
     override func willBecomeActive(with conversation: MSConversation) {
         // Handle becoming active
         syncWithMainApp(conversation: conversation)
     }
     
     override func didReceive(_ message: MSMessage, conversation: MSConversation) {
         // Handle received message
         forwardToMainApp(message: message, conversation: conversation)
     }
 }
 */
