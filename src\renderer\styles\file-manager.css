/* File Manager Interface Styles */
.file-manager-container {
    display: flex;
    height: calc(100vh - 40px);
    background: #0a0a0a;
}

/* Sidebar */
.file-sidebar {
    width: 280px;
    background: #1a1a1a;
    border-right: 1px solid #333;
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.device-info {
    background: #2a2a2a;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #444;
}

.device-info .device-icon {
    font-size: 32px;
    text-align: center;
    margin-bottom: 10px;
}

.device-details h3 {
    margin: 0 0 5px 0;
    color: #fff;
    font-size: 16px;
    text-align: center;
}

.device-details p {
    margin: 0 0 10px 0;
    color: #999;
    font-size: 13px;
    text-align: center;
}

.storage-bar {
    width: 100%;
    height: 6px;
    background: #444;
    border-radius: 3px;
    overflow: hidden;
}

.storage-used {
    height: 100%;
    background: linear-gradient(90deg, #34C759, #FF9500, #FF3B30);
    transition: width 0.3s ease;
}

.quick-actions {
    margin-bottom: 20px;
}

.quick-actions h4 {
    margin: 0 0 10px 0;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
}

.action-btn {
    width: 100%;
    padding: 10px 15px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 8px;
    color: #fff;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn:hover {
    background: #3a3a3a;
    border-color: #555;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.file-categories h4 {
    margin: 0 0 10px 0;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
}

.category-list {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.category-btn {
    padding: 8px 12px;
    background: transparent;
    border: none;
    border-radius: 6px;
    color: #999;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 8px;
}

.category-btn:hover {
    background: #2a2a2a;
    color: #fff;
}

.category-btn.active {
    background: #007AFF;
    color: white;
}

/* File Browser */
.file-browser {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #0a0a0a;
}

.file-toolbar {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    background: #1a1a1a;
    border-bottom: 1px solid #333;
    gap: 20px;
}

.toolbar-left {
    flex: 1;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #999;
    font-size: 14px;
}

.breadcrumb-item {
    color: #999;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.breadcrumb-item:hover {
    background: #2a2a2a;
    color: #fff;
}

.breadcrumb-item.active {
    color: #007AFF;
}

.breadcrumb-separator {
    color: #666;
    margin: 0 2px;
}

.toolbar-center {
    flex: 2;
}

.search-container {
    display: flex;
    gap: 8px;
}

.search-container input {
    flex: 1;
    padding: 8px 12px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 6px;
    color: #fff;
    font-size: 14px;
}

.search-container input:focus {
    outline: none;
    border-color: #007AFF;
}

.search-btn {
    padding: 8px 12px;
    background: #007AFF;
    border: none;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    font-size: 14px;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.view-controls {
    display: flex;
    gap: 5px;
}

.view-btn {
    width: 32px;
    height: 32px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 6px;
    color: #999;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.view-btn:hover {
    color: #fff;
    border-color: #555;
}

.view-btn.active {
    background: #007AFF;
    border-color: #007AFF;
    color: white;
}

.sort-controls select {
    padding: 6px 10px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 6px;
    color: #fff;
    font-size: 13px;
}

/* File Content */
.file-content {
    flex: 1;
    overflow: auto;
    padding: 20px;
}

.file-list {
    display: grid;
    gap: 15px;
}

.file-list.grid-view {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
}

.file-list.list-view {
    grid-template-columns: 1fr;
    gap: 5px;
}

.file-item {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.file-item:hover {
    background: #2a2a2a;
    border-color: #444;
}

.file-item.selected {
    background: rgba(0, 122, 255, 0.1);
    border-color: #007AFF;
}

/* Grid View */
.grid-view .file-item {
    padding: 15px;
    text-align: center;
    aspect-ratio: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.file-icon {
    font-size: 32px;
    margin-bottom: 8px;
    position: relative;
}

.file-icon img {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 4px;
}

.file-type-badge {
    position: absolute;
    bottom: -5px;
    right: -5px;
    background: #007AFF;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}

.file-name {
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 4px;
}

.file-size {
    color: #999;
    font-size: 11px;
}

.file-checkbox {
    position: absolute;
    top: 8px;
    right: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.file-item:hover .file-checkbox,
.file-item.selected .file-checkbox {
    opacity: 1;
}

.file-checkbox input {
    width: 16px;
    height: 16px;
}

/* List View */
.list-view .file-item {
    padding: 12px 15px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.file-icon-small {
    font-size: 20px;
    width: 24px;
    text-align: center;
}

.file-details {
    flex: 1;
    min-width: 0;
}

.list-view .file-name {
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 2px;
}

.file-meta {
    display: flex;
    gap: 15px;
    color: #999;
    font-size: 12px;
}

.file-actions {
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.file-item:hover .file-actions {
    opacity: 1;
}

.action-btn.small {
    width: 28px;
    height: 28px;
    padding: 0;
    background: #3a3a3a;
    border: 1px solid #555;
    border-radius: 4px;
    color: #fff;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn.small:hover {
    background: #4a4a4a;
}

/* Empty State */
.empty-files {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #666;
    text-align: center;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-files h3 {
    margin: 0 0 10px 0;
    color: #999;
}

.empty-files p {
    margin: 0;
    color: #666;
}

/* Loading State */
.loading-files {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #666;
}

.spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #333;
    border-top: 2px solid #007AFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Transfer Progress */
.transfer-progress {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.progress-header {
    padding: 15px 20px;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-header h4 {
    margin: 0;
    color: #fff;
    font-size: 14px;
}

.close-btn {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 16px;
    padding: 2px;
}

.close-btn:hover {
    color: #fff;
}

.progress-list {
    max-height: 200px;
    overflow-y: auto;
}

.transfer-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    border-bottom: 1px solid #2a2a2a;
}

.transfer-item:last-child {
    border-bottom: none;
}

.transfer-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
}

.transfer-icon {
    font-size: 14px;
}

.transfer-name {
    color: #fff;
    font-size: 13px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.transfer-status {
    display: flex;
    align-items: center;
    gap: 5px;
}

.status-icon {
    font-size: 14px;
}

.error-message {
    color: #FF3B30;
    font-size: 11px;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: #1a1a1a;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 80%;
    overflow: hidden;
    border: 1px solid #333;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    color: #fff;
    font-size: 18px;
}

.modal-close {
    background: none;
    border: none;
    color: #999;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
}

.modal-close:hover {
    color: #fff;
}

.modal-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

/* Upload Modal */
.upload-area {
    border: 2px dashed #444;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.upload-area.drag-over {
    border-color: #007AFF;
    background: rgba(0, 122, 255, 0.1);
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.7;
}

.upload-area h3 {
    margin: 0 0 10px 0;
    color: #fff;
}

.upload-area p {
    margin: 0 0 20px 0;
    color: #999;
}

.select-files-btn {
    padding: 10px 20px;
    background: #007AFF;
    border: none;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    font-size: 14px;
}

.upload-options {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.upload-options label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
}

/* Context Menu */
.context-menu {
    position: fixed;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
    z-index: 1001;
    min-width: 150px;
}

.context-item {
    padding: 10px 15px;
    color: #fff;
    font-size: 13px;
    cursor: pointer;
    transition: background 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.context-item:hover {
    background: #3a3a3a;
}

.context-item.danger {
    color: #FF3B30;
}

.context-separator {
    height: 1px;
    background: #444;
    margin: 5px 0;
}

/* File Details Modal */
.file-details-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.file-icon-large {
    font-size: 48px;
}

.file-info h3 {
    margin: 0;
    color: #fff;
    font-size: 18px;
}

.file-path {
    margin: 5px 0 0 0;
    color: #999;
    font-size: 13px;
}

.file-properties {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.property-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #333;
}

.property-row:last-child {
    border-bottom: none;
}

.property-label {
    color: #999;
    font-weight: 500;
}

.property-value {
    color: #fff;
}

.file-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.action-btn.primary {
    background: #007AFF;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
}

.action-btn.secondary {
    background: #2a2a2a;
    color: #fff;
    border: 1px solid #444;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
}

.action-btn.danger {
    background: #FF3B30;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #fff;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 10px 12px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 6px;
    color: #fff;
    font-size: 14px;
}

.form-group input:focus {
    outline: none;
    border-color: #007AFF;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Responsive Design */
@media (max-width: 768px) {
    .file-manager-container {
        flex-direction: column;
    }
    
    .file-sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid #333;
    }
    
    .file-toolbar {
        flex-direction: column;
        gap: 10px;
    }
    
    .toolbar-left,
    .toolbar-center,
    .toolbar-right {
        width: 100%;
    }
    
    .file-list.grid-view {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
    
    .transfer-progress {
        width: calc(100% - 40px);
        left: 20px;
        right: 20px;
    }
}
