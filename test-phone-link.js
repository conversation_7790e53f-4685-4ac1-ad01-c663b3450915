
const path = require('path');
const fs = require('fs');

console.log('🔍 Searching for Phone Link data...\n');

// Check for Phone Link installation
const packagesDir = path.join(process.env.LOCALAPPDATA, 'Packages');
const phoneLinkDirs = fs.readdirSync(packagesDir).filter(dir => 
    dir.includes('YourPhone') || dir.includes('Phone') || dir.includes('Link')
);

console.log('Found directories:', phoneLinkDirs);

// Look for databases
phoneLinkDirs.forEach(dir => {
    const fullPath = path.join(packagesDir, dir);
    try {
        const findDBFiles = (dir) => {
            const items = fs.readdirSync(dir);
            items.forEach(item => {
                const itemPath = path.join(dir, item);
                const stat = fs.statSync(itemPath);
                if (stat.isDirectory() && !item.startsWith('.')) {
                    findDBFiles(itemPath);
                } else if (item.endsWith('.db') || item.endsWith('.sqlite')) {
                    console.log('\n✅ Found database:', itemPath);
                    console.log('   Size:', (stat.size / 1024 / 1024).toFixed(2), 'MB');
                }
            });
        };
        findDBFiles(fullPath);
    } catch (e) {
        // Permission denied
    }
});

// Also check iTunes backup location
const backupPath = path.join(process.env.APPDATA, '../Roaming/Apple Computer/MobileSync/Backup');
if (fs.existsSync(backupPath)) {
    console.log('\n📱 iTunes backup folder found:', backupPath);
    const backups = fs.readdirSync(backupPath);
    console.log('   Backups found:', backups.length);
}
