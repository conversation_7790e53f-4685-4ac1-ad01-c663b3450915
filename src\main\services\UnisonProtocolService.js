const { EventEmitter } = require('events');
// const noble = require('@abandonware/noble'); // Disabled for now - needs rebuild
const { exec } = require('child_process');
const { promisify } = require('util');
const axios = require('axios');
const WebSocket = require('ws');
const crypto = require('crypto');
const dgram = require('dgram');
const winston = require('winston');

const execAsync = promisify(exec);

class UnisonProtocolService extends EventEmitter {
  constructor(persistence, messageService) {
    super();
    this.persistence = persistence;
    this.messageService = messageService;
    this.isConnected = false;
    this.deviceInfo = null;
    this.bleDevice = null;
    this.wifiDirectConnection = null;
    this.phoneNumber = null;
    this.connectionMethods = new Map();
    
    // Configure logger
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(({ level, message, timestamp }) => {
          return `${timestamp} [${level.toUpperCase()}] UnisonProtocol: ${message}`;
        })
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'unison-protocol.log' })
      ]
    });
    
    // Intel Unison-style protocol constants
    this.UNISON_SERVICE_UUID = '00001101-0000-1000-8000-00805f9b34fb'; // Serial Port Profile
    this.UNISON_CHARACTERISTIC_UUID = '00002a05-0000-1000-8000-00805f9b34fb';
    this.WIFI_DIRECT_PORT = 8888;
    this.DISCOVERY_PORT = 8889;
    
    // Message sending methods priority
    this.sendingMethods = [
      { name: 'phonelink_api', handler: this.sendViaPhoneLinkAPI.bind(this) },
      { name: 'shortcuts_webhook', handler: this.sendViaShortcutsWebhook.bind(this) },
      { name: 'wifi_direct', handler: this.sendViaWiFiDirect.bind(this) },
      { name: 'bluetooth_le', handler: this.sendViaBluetooth.bind(this) },
      { name: 'pushbullet_api', handler: this.sendViaPushbullet.bind(this) }
    ];
  }

  async initialize() {
    this.logger.info('🔥 INITIALIZING INTEL UNISON++ PROTOCOL SERVICE 🔥');
    
    // Initialize all connection methods
    await this.initializeBluetooth();
    await this.initializeWiFiDirect();
    await this.initializePhoneLinkAPI();
    await this.setupShortcutsWebhook();
    
    this.logger.info('✅ Intel Unison++ Protocol Service initialized');
    return true;
  }

  // BLUETOOTH LE DISCOVERY (Intel Unison style) - Temporarily disabled
  async initializeBluetooth() {
    this.logger.info('🔵 Bluetooth LE discovery temporarily disabled (noble needs rebuild)');
    
    try {
      // TODO: Reinstall noble with proper native bindings
      // For now, we'll rely on WiFi Direct and other methods
      
      this.connectionMethods.set('bluetooth', { status: 'disabled', reason: 'noble needs rebuild' });
    } catch (error) {
      this.logger.error(`❌ Bluetooth initialization failed: ${error.message}`);
      this.connectionMethods.set('bluetooth', { status: 'failed', error: error.message });
    }
  }

  async startBluetoothScan() {
    // Temporarily disabled
    this.logger.info('🔵 Bluetooth scan disabled - noble needs rebuild');
  }

  async handleBluetoothDevice(peripheral) {
    // Temporarily disabled
    this.logger.info('🔵 Bluetooth device handling disabled - noble needs rebuild');
  }

  // WIFI DIRECT CONNECTION (Intel Unison style)
  async initializeWiFiDirect() {
    this.logger.info('📶 Initializing WiFi Direct connection...');
    
    try {
      // Create UDP discovery server
      this.discoveryServer = dgram.createSocket('udp4');
      
      this.discoveryServer.on('message', (msg, rinfo) => {
        const message = msg.toString();
        if (message.startsWith('UNISON_DEVICE:')) {
          const deviceInfo = JSON.parse(message.substring(14));
          this.handleWiFiDirectDevice(deviceInfo, rinfo);
        }
      });
      
      this.discoveryServer.bind(this.DISCOVERY_PORT);
      this.logger.info(`✅ WiFi Direct discovery server listening on port ${this.DISCOVERY_PORT}`);
      
      // Broadcast discovery message
      this.broadcastDiscovery();
      
      // Create TCP server for data transfer
      this.createWiFiDirectServer();
      
      this.connectionMethods.set('wifi_direct', { status: 'initialized', connected: false });
    } catch (error) {
      this.logger.error(`❌ WiFi Direct initialization failed: ${error.message}`);
      this.connectionMethods.set('wifi_direct', { status: 'failed', error: error.message });
    }
  }

  broadcastDiscovery() {
    const message = Buffer.from(JSON.stringify({
      type: 'UNISON_PC',
      name: 'iPhone Companion Pro',
      version: '2.0',
      capabilities: ['messages', 'calls', 'notifications', 'files']
    }));
    
    const client = dgram.createSocket('udp4');
    client.bind(() => {
      client.setBroadcast(true);
      client.send(message, 0, message.length, this.DISCOVERY_PORT, '***************', () => {
        client.close();
      });
    });
    
    this.logger.info('📡 Broadcast discovery message sent');
  }

  createWiFiDirectServer() {
    const net = require('net');
    
    this.wifiServer = net.createServer((socket) => {
      this.logger.info('📱 iPhone connected via WiFi Direct');
      this.wifiDirectConnection = socket;
      
      socket.on('data', (data) => {
        this.handleWiFiDirectData(data);
      });
      
      socket.on('close', () => {
        this.logger.info('📱 WiFi Direct connection closed');
        this.wifiDirectConnection = null;
        this.connectionMethods.set('wifi_direct', { status: 'disconnected' });
      });
    });
    
    this.wifiServer.listen(this.WIFI_DIRECT_PORT, () => {
      this.logger.info(`✅ WiFi Direct server listening on port ${this.WIFI_DIRECT_PORT}`);
    });
  }

  handleWiFiDirectDevice(deviceInfo, rinfo) {
    if (deviceInfo.type === 'UNISON_IPHONE') {
      this.logger.info(`📱 Found iPhone via WiFi Direct: ${deviceInfo.name} at ${rinfo.address}`);
      
      // Connect to iPhone
      const net = require('net');
      const client = net.createConnection(deviceInfo.port || this.WIFI_DIRECT_PORT, rinfo.address, () => {
        this.logger.info('✅ Connected to iPhone via WiFi Direct');
        this.wifiDirectConnection = client;
        this.connectionMethods.set('wifi_direct', { status: 'connected', device: deviceInfo.name });
        this.emit('wifi-direct-connected', deviceInfo);
      });
      
      client.on('error', (error) => {
        this.logger.error(`❌ WiFi Direct connection failed: ${error.message}`);
      });
    }
  }

  handleWiFiDirectData(data) {
    try {
      const message = JSON.parse(data.toString());
      
      switch (message.type) {
        case 'message':
          this.handleIncomingMessage(message);
          break;
        case 'sync_request':
          this.handleSyncRequest(message);
          break;
        case 'phone_info':
          this.phoneNumber = message.phoneNumber;
          this.deviceInfo = message;
          this.logger.info(`✅ Got phone number: ${this.phoneNumber}`);
          break;
      }
    } catch (error) {
      this.logger.error(`❌ Error handling WiFi Direct data: ${error.message}`);
    }
  }

  // PHONE LINK HIDDEN API
  async initializePhoneLinkAPI() {
    this.logger.info('🔌 Discovering Phone Link hidden APIs...');
    
    try {
      // Check for Phone Link process
      const { stdout } = await execAsync('tasklist /FI "IMAGENAME eq YourPhone.exe" /FO CSV');
      if (stdout.includes('YourPhone.exe')) {
        this.logger.info('✅ Phone Link process found');
        
        // Try to find Phone Link's internal API port
        const portCheck = await execAsync('netstat -ano | findstr "YourPhone"');
        this.logger.info('📊 Phone Link network connections:', portCheck.stdout);
        
        // Look for local API endpoints
        await this.discoverPhoneLinkEndpoints();
        
        this.connectionMethods.set('phonelink_api', { status: 'discovered' });
      }
    } catch (error) {
      this.logger.warn(`⚠️ Phone Link API discovery failed: ${error.message}`);
      this.connectionMethods.set('phonelink_api', { status: 'not_found' });
    }
  }

  async discoverPhoneLinkEndpoints() {
    const possiblePorts = [5000, 5001, 8080, 8081, 9000, 9001, 10000];
    
    for (const port of possiblePorts) {
      try {
        const response = await axios.get(`http://localhost:${port}/api/status`, { timeout: 1000 });
        if (response.data) {
          this.logger.info(`✅ Found Phone Link API on port ${port}`);
          this.phoneLinkAPIPort = port;
          break;
        }
      } catch (error) {
        // Continue checking other ports
      }
    }
  }

  // iOS SHORTCUTS WEBHOOK
  async setupShortcutsWebhook() {
    this.logger.info('🔗 Setting up iOS Shortcuts webhook server...');
    
    try {
      const express = require('express');
      const app = express();
      app.use(express.json());
      
      // Webhook endpoint for iOS Shortcuts
      app.post('/shortcuts/send-message', async (req, res) => {
        const { phoneNumber, message, userPhone } = req.body;
        
        this.logger.info(`📲 Received message request via Shortcuts: ${phoneNumber} - ${message}`);
        
        // Store the user's phone number
        if (userPhone) {
          this.phoneNumber = userPhone;
        }
        
        // Send via Phone Link or other methods
        const result = await this.sendMessage(phoneNumber, message);
        
        res.json({ success: result.success, messageId: result.messageId });
      });
      
      // Webhook for receiving messages
      app.post('/shortcuts/receive-message', async (req, res) => {
        const messageData = req.body;
        
        // Save to database
        await this.persistence.saveMessage({
          threadId: messageData.from,
          phoneNumber: messageData.from,
          contactName: messageData.contactName || 'Unknown',
          messageText: messageData.text,
          timestamp: new Date(),
          isOutgoing: false,
          isDelivered: true,
          isRead: false,
          source: 'shortcuts_webhook'
        });
        
        this.emit('message-received', messageData);
        res.json({ success: true });
      });
      
      const WEBHOOK_PORT = 7778;
      app.listen(WEBHOOK_PORT, () => {
        this.logger.info(`✅ iOS Shortcuts webhook server running on port ${WEBHOOK_PORT}`);
        this.logger.info('📱 Configure your iPhone Shortcuts to use:');
        this.logger.info(`   Send: http://YOUR_PC_IP:${WEBHOOK_PORT}/shortcuts/send-message`);
        this.logger.info(`   Receive: http://YOUR_PC_IP:${WEBHOOK_PORT}/shortcuts/receive-message`);
      });
      
      this.connectionMethods.set('shortcuts_webhook', { status: 'active', port: WEBHOOK_PORT });
    } catch (error) {
      this.logger.error(`❌ Shortcuts webhook setup failed: ${error.message}`);
      this.connectionMethods.set('shortcuts_webhook', { status: 'failed', error: error.message });
    }
  }

  // MESSAGE SENDING IMPLEMENTATION
  async sendMessage(phoneNumber, text) {
    this.logger.info(`📤 Sending message to ${phoneNumber}: ${text}`);
    
    // Try each sending method in priority order
    for (const method of this.sendingMethods) {
      try {
        const result = await method.handler(phoneNumber, text);
        if (result.success) {
          this.logger.info(`✅ Message sent successfully via ${method.name}`);
          
          // Save to database as sent
          await this.persistence.saveMessage({
            threadId: phoneNumber,
            phoneNumber: phoneNumber,
            contactName: await this.getContactName(phoneNumber),
            messageText: text,
            timestamp: new Date(),
            isOutgoing: true,
            isDelivered: true,
            isRead: true,
            source: method.name
          });
          
          return result;
        }
      } catch (error) {
        this.logger.warn(`⚠️ ${method.name} failed: ${error.message}`);
      }
    }
    
    this.logger.error('❌ All message sending methods failed');
    return { success: false, error: 'All sending methods failed' };
  }

  async sendViaPhoneLinkAPI(phoneNumber, text) {
    if (!this.phoneLinkAPIPort) {
      throw new Error('Phone Link API not discovered');
    }
    
    try {
      // Try various Phone Link API endpoints
      const endpoints = [
        `/api/messages/send`,
        `/api/sms/send`,
        `/api/v1/messages`,
        `/messages/send`
      ];
      
      for (const endpoint of endpoints) {
        try {
          const response = await axios.post(
            `http://localhost:${this.phoneLinkAPIPort}${endpoint}`,
            {
              to: phoneNumber,
              message: text,
              from: this.phoneNumber
            },
            { timeout: 5000 }
          );
          
          if (response.data && response.data.success) {
            return { success: true, messageId: response.data.messageId };
          }
        } catch (error) {
          // Try next endpoint
        }
      }
      
      // Try PowerShell command to interact with Phone Link
      const psCommand = `
        Add-Type -AssemblyName System.Windows.Forms
        [System.Windows.Forms.SendKeys]::SendWait("%{TAB}")
        Start-Sleep -Milliseconds 500
        [System.Windows.Forms.SendKeys]::SendWait("${phoneNumber}")
        Start-Sleep -Milliseconds 500
        [System.Windows.Forms.SendKeys]::SendWait("{TAB}")
        [System.Windows.Forms.SendKeys]::SendWait("${text}")
        Start-Sleep -Milliseconds 500
        [System.Windows.Forms.SendKeys]::SendWait("^{ENTER}")
      `;
      
      await execAsync(`powershell -Command "${psCommand}"`);
      return { success: true, messageId: Date.now().toString() };
      
    } catch (error) {
      throw new Error(`Phone Link API failed: ${error.message}`);
    }
  }

  async sendViaShortcutsWebhook(phoneNumber, text) {
    // This requires the iPhone to have a Shortcut that polls our webhook
    // The shortcut will pick up the message and send it
    
    const messageId = crypto.randomBytes(16).toString('hex');
    
    // Queue message for pickup by iPhone Shortcut
    this.messageQueue = this.messageQueue || new Map();
    this.messageQueue.set(messageId, {
      to: phoneNumber,
      text: text,
      timestamp: Date.now(),
      status: 'pending'
    });
    
    // Wait for confirmation (timeout after 10 seconds)
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        this.messageQueue.delete(messageId);
        resolve({ success: false, error: 'Shortcut did not pick up message' });
      }, 10000);
      
      // Set up listener for confirmation
      const checkInterval = setInterval(() => {
        const msg = this.messageQueue.get(messageId);
        if (msg && msg.status === 'sent') {
          clearTimeout(timeout);
          clearInterval(checkInterval);
          this.messageQueue.delete(messageId);
          resolve({ success: true, messageId });
        }
      }, 500);
    });
  }

  async sendViaWiFiDirect(phoneNumber, text) {
    if (!this.wifiDirectConnection) {
      throw new Error('WiFi Direct not connected');
    }
    
    const message = JSON.stringify({
      type: 'send_message',
      to: phoneNumber,
      text: text,
      from: this.phoneNumber,
      timestamp: Date.now()
    });
    
    return new Promise((resolve, reject) => {
      this.wifiDirectConnection.write(message, (error) => {
        if (error) {
          reject(error);
        } else {
          resolve({ success: true, messageId: Date.now().toString() });
        }
      });
    });
  }

  async sendViaBluetooth(phoneNumber, text) {
    // Temporarily disabled
    throw new Error('Bluetooth sending disabled - noble needs rebuild');
  }

  async sendViaPushbullet(phoneNumber, text) {
    // Fallback option using Pushbullet API
    if (!process.env.PUSHBULLET_API_KEY) {
      throw new Error('Pushbullet API key not configured');
    }
    
    try {
      const response = await axios.post(
        'https://api.pushbullet.com/v2/texts',
        {
          data: {
            target_device_iden: process.env.PUSHBULLET_DEVICE_ID,
            addresses: [phoneNumber],
            message: text,
            guid: crypto.randomBytes(16).toString('hex')
          }
        },
        {
          headers: {
            'Access-Token': process.env.PUSHBULLET_API_KEY
          }
        }
      );
      
      return { success: true, messageId: response.data.iden };
    } catch (error) {
      throw new Error(`Pushbullet API failed: ${error.message}`);
    }
  }

  // REAL-TIME SYNC
  async startRealtimeSync() {
    this.logger.info('🔄 Starting real-time sync...');
    
    // Monitor for new messages every second (Intel Unison style)
    this.syncInterval = setInterval(async () => {
      if (this.isConnected) {
        await this.syncMessages();
      }
    }, 1000);
    
    // Also sync on connection
    this.on('bluetooth-connected', () => this.syncMessages());
    this.on('wifi-direct-connected', () => this.syncMessages());
  }

  async syncMessages() {
    try {
      // Request sync from iPhone
      if (this.wifiDirectConnection) {
        this.wifiDirectConnection.write(JSON.stringify({
          type: 'sync_request',
          lastSync: await this.getLastSyncTime()
        }));
      }
    } catch (error) {
      this.logger.error(`❌ Sync failed: ${error.message}`);
    }
  }

  async getLastSyncTime() {
    const lastSync = await this.persistence.getLastSync('realtime');
    return lastSync ? lastSync.last_sync : 0;
  }

  async getContactName(phoneNumber) {
    const contacts = await this.persistence.loadContacts();
    const contact = contacts.find(c => c.phoneNumber === phoneNumber);
    return contact ? contact.displayName : phoneNumber;
  }

  handleIncomingMessage(message) {
    this.logger.info(`📨 Received message from ${message.from}: ${message.text}`);
    
    // Save to database
    this.persistence.saveMessage({
      threadId: message.from,
      phoneNumber: message.from,
      contactName: message.contactName || 'Unknown',
      messageText: message.text,
      timestamp: new Date(message.timestamp),
      isOutgoing: false,
      isDelivered: true,
      isRead: false,
      hasAttachment: message.hasAttachment || false,
      source: 'unison_sync'
    });
    
    // Update sync status
    this.persistence.updateSyncStatus('realtime', 'success', this.deviceInfo?.name, 1);
    
    // Emit event
    this.emit('message-received', message);
  }

  handleSyncRequest(request) {
    // Send all messages since last sync
    this.persistence.loadAllMessages().then(messages => {
      const newMessages = messages.filter(m => m.timestamp > request.lastSync);
      
      if (this.wifiDirectConnection) {
        this.wifiDirectConnection.write(JSON.stringify({
          type: 'sync_response',
          messages: newMessages
        }));
      }
    });
  }

  getStatus() {
    return {
      isConnected: this.isConnected,
      phoneNumber: this.phoneNumber,
      deviceInfo: this.deviceInfo,
      connectionMethods: Object.fromEntries(this.connectionMethods),
      activeMethods: Array.from(this.connectionMethods.entries())
        .filter(([_, status]) => status.connected || status.status === 'active')
        .map(([name, _]) => name)
    };
  }

  close() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    
    // Bluetooth cleanup disabled
    
    if (this.wifiDirectConnection) {
      this.wifiDirectConnection.end();
    }
    
    if (this.discoveryServer) {
      this.discoveryServer.close();
    }
    
    if (this.wifiServer) {
      this.wifiServer.close();
    }
    
    this.logger.info('🔌 Intel Unison++ Protocol Service closed');
  }
}

module.exports = { UnisonProtocolService };